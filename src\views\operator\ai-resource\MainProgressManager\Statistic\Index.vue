<template>
  <div class="speech-statistic-container">
    <el-tabs v-model="active">
      <el-tab-pane label="挂机统计" :name="1"><HookStatistic v-if="active==1" :scriptId="editId"/></el-tab-pane>
      <el-tab-pane label="命中统计" :name="2"><HitStatistic v-if="active==2" :scriptId="editId"/></el-tab-pane>
      <!-- <el-tab-pane label="分类统计" :name="3"><IntentionStatistic v-if="active==3" :scriptId="editId"/></el-tab-pane> -->
    </el-tabs>
    <el-select v-model="editId" placeholder="请选择话术版本" class="version-select">
      <el-option
        v-for="item in scriptList"
        :key="item.id"
        class="option-script-item"
        :label="`${item.scriptName.slice(0,4)}-版本${item.version}${!item.isDeleted && item.status===SpeechCraftStatusEnum['生效中'] ? '(生效中)':''}`"
        :value="item.id"
      >
        <div class="title">
          <span class="tw-grow tw-truncate">{{ item.scriptName || '' }}</span>
          <span class="tw-grow-0 tw-ml-[8px]">{{ !item.isDeleted && item.status===SpeechCraftStatusEnum['生效中'] ? '生效中': ('版本' + item.version || '') }}</span>
        </div>
        <div class="tips">{{ item.createTime || '' }}</div>
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { useScriptStore } from '@/store/script'
import { scriptStatisticsModel } from '@/api/speech-craft'
import { SpeechCraftInfoItem, SpeechCraftStatusEnum } from '@/type/speech-craft'

const HookStatistic = defineAsyncComponent({loader: () => import('./HookStatistic.vue')})
const HitStatistic = defineAsyncComponent({loader: () => import('./HitStatistic.vue')})
// const IntentionStatistic = defineAsyncComponent({loader: () => import('./IntentionStatistic.vue')})

const scriptStore = useScriptStore()
const editId = ref(scriptStore.id)
const active = ref(1)
const scriptList = ref<SpeechCraftInfoItem[]>([])
// 执行区
onMounted(async() => {
  scriptList.value = await scriptStatisticsModel.getScriptList({ scriptId: scriptStore.id }) || []
});

</script>

<style scoped lang="postcss" type="text/postcss">
.speech-statistic-container {
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  background-color: #fff;
  height: calc(100vh - 160px);
  .el-tabs :deep(.el-tabs__item){
    padding: 8px 0 4px;
    height: 36px;
    line-height: 22px;
    width: 80px;
    font-size: 14px;
    color: var(--primary-black-color-400);
    &.is-active {
      color: var(--el-color-primary);
    }
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    border: none;
    background-color: #e5e7eb;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
  :deep(.el-checkbox-group .el-checkbox__label) {
    font-size: 13px;
  }
  .version-select {
    position: absolute;
    top: 4px;
    right: 24px;
    z-index: 9;
  }
}
.option-script-item {
  text-align: left;
  height: 48px;
  position: relative;
  line-height: 20px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  margin: 4px;
  padding: 4px 8px;
  .title {
    font-size: 13px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    color: #313233;
  }
  .tips {
    font-size: 12px;
    color: #969799;
  }
  &.selected{
    background-color: #165DFF;
    border-radius: 4px;
    .title, .tips{
      color: #fff;
    }
    .tips {
      opacity: 0.7;
    }
  }
}
</style>