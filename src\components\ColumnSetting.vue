<template>
   <el-popover v-model:visible="visible" placement="right-end" :width="400" trigger="click" @before-enter="handleEnter">
    <template #reference>
      <el-button :link="!visible" type="primary" class="column-setting">
        <el-icon :size="15"><SvgIcon name="setting1"/></el-icon>
        <span>配置展示字段</span>
      </el-button>
    </template>
    <div class="tw-h-[450px] tw-flex tw-flex-col dialog-form">
      <div class="tw-text-left info-content tw-font-[600] tw-mb-[8px]">配置展示字段</div>
      <div class="tw-h-[400px] tw-w-full">
        <TransferBox
          v-model:leftList="unselectedList"
          checkAllVisible
          unit="项"
          :width="5"
          leftTitle="未配置"
          name="name"
          value="name"
          v-model:rightList="selectedList"
          rightTitle="已配置"
        ></TransferBox>
      </div>
      
      <span class="tw-flex tw-justify-end tw-mt-[8px]">
        <el-button link @click="visible = false">取消</el-button>
        <el-button type="primary" link @click="confirm" >确认</el-button>
      </span>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref,watch, reactive, computed,} from 'vue'
import TransferBox from '@/components/TransferBox.vue'
import { useUserStore } from '@/store/user'

const emits = defineEmits(['update:selectList'])
const props = defineProps<{
  totalList: string[]
  disabledList?: string[]
  defaultList?: string[]
  name: string
}>();
const visible = ref(false)
const userInfo = useUserStore()
const selectedList = ref<{name: string, disabled?: boolean}[]>([])
const unselectedList = ref<{name: string, disabled?: boolean}[]>([])
const handleEnter = () => {
  if (userInfo.colInfo[props.name] && userInfo.colInfo[props.name].length > 0) {
    const list1: {name: string, disabled?: boolean}[] = []
    const list2: {name: string, disabled?: boolean}[] = []
    const arr = [...new Set(userInfo.colInfo[props.name])]
    arr.forEach(item => {
      if (props.totalList.includes(item)) {
        props.disabledList?.includes(item)
        ? list1.push({
          name: item,
          disabled: true,
        }) 
        : list2.push({
          name: item,
          disabled: false,
        })
      }
    })
    selectedList.value = [...list1, ...list2]
  } else {
    const list1: {name: string, disabled?: boolean}[] = []
    const list2: {name: string, disabled?: boolean}[] = []
    const arr = [...new Set(props.defaultList)]
    arr?.forEach(item => {
      if (props.totalList.includes(item)) {
        props.disabledList?.includes(item)
        ? list1.push({
          name: item,
          disabled: true,
        }) 
        : list2.push({
          name: item,
          disabled: false,
        })
      }
    })
    selectedList.value = [...list1, ...list2]
    userInfo.colInfo[props.name] = props.defaultList || []
  }
  const ulist1: {name: string, disabled?: boolean}[] = []
  const ulist2: {name: string, disabled?: boolean}[] = []
  const arr = selectedList.value.map(item => item.name) || []
  props.totalList?.forEach(item => {
    if (!arr.includes(item)) {
      props.disabledList?.includes(item)
      ? ulist1.push({
        name: item,
        disabled: props.disabledList?.includes(item),
      }) 
      : ulist2.push({
        name: item,
        disabled: props.disabledList?.includes(item),
      })
    }
  })
  unselectedList.value = [...ulist1, ...ulist2]
}
const confirm = () => {
  visible.value = false
  userInfo.colInfo[props.name] = selectedList.value.map(item => item.name)
}
handleEnter()
</script>

<style lang="postcss" type="text/postcss" scoped>
.column-setting {
  width: 116px;
  /* height: 28px; */
  &.el-button--primary {
    padding: 4px;
    border-radius: 2px;
  }
}
.column-popover {
  :deep(.el-button) {
    font-size: 13px;
  }
}

</style>
