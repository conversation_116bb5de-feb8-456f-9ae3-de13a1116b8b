<template>
  <el-dialog
    :model-value.sync="visible"
    width="540px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">导入短信通道配置</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
        label-width="80px"
        scroll-to-error
        ref="editRef"
      >
        <el-form-item label="适用行业：">
          <span class="info-title">{{ merchantStore.editingSmsTemplate?.secondIndustry || '-' }}</span>
        </el-form-item>
        <el-form-item label="短信模板：" prop="smsTemplateId">
          <el-select
            v-model="editData.smsTemplateId"
            clearable
            filterable
            class="tw-grow"
            placeholder="选择短信模板，从该模板中导入短信通道配置"
            style="width:100%"
          >
            <el-option
              v-for="item in smsTemplateList"
              :key="item.id"
              :label="item.templateName"
              :value="item.id"
            >
            <span class="tw-float-left tw-text-[13px]">{{ item.templateName }}</span>
            <span class="tw-float-right tw-text-[13px] tw-text-[#C8C9CC]">{{ item.messageSign }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button type="primary" @click="confirm" :icon="Select">导入</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive, onUnmounted, defineAsyncComponent, toRaw, nextTick } from 'vue'
import type { FormInstance, } from 'element-plus'
import { onBeforeRouteLeave } from 'vue-router'
import { SmsAccountGroupItem } from '@/type/merchant'
import { merchantSmsChannelModel } from '@/api/merchant'
import { merchantSmsTemplateModel, } from '@/api/merchant'
import { SmsTemplateItem, SmsChannelItem } from '@/type/merchant'
import { to } from 'await-to-js'
import { ElMessage } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { useMerchantStore } from '@/store/merchant'
import { pickAttrFromObj } from '@/utils/utils'
import { trace } from '@/utils/trace'

// props和emits
const emits = defineEmits(['confirm', 'update:visible',])
const props = defineProps<{
  visible: boolean,
  data: SmsChannelItem | null,
}>();

const loading = ref(false)
const merchantStore = useMerchantStore()

/** 变量 */
const visible = ref(false)
const editData = reactive<{
  smsTemplateId?: number
}>({
  smsTemplateId: undefined
}) // 表格数据
const editRef = ref<FormInstance  | null>(null) // 表格ref

// 弹窗-取消
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}
// 弹窗-确认操作
const confirm = async () => {
  if (!props.data || !props.data.tenantSmsTemplateId) return ElMessage.warning('获取短信模板信息失败')
  const currentId = props.data.tenantSmsTemplateId
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      // 获取需要导入的短信通道信息
      const [_ , loadData] = await to(merchantSmsChannelModel.getSmsChannelByTemplateId({
        templateId: editData.smsTemplateId!,
      }))
      if (!loadData || !loadData.smsAccountGroups?.length) return ElMessage.warning('导入的短信通道为空')
      const errMsg: string[] = []
      trace({
        page: `商户管理-短信通道-导入短信通道(${currentId})`,
        params: pickAttrFromObj(loadData, ['smsAccountGroups', 'tenantSmsTemplateId'])
      })
      // 删除当前短信模板下的短信通道
      await Promise.all(props.data?.smsAccountGroups?.map(async (item: SmsAccountGroupItem) => {
        const [err] = await to(merchantSmsChannelModel.deleteSmsChannelAccountGroup({
          templateId: currentId,
          smsAccountGroupId: item.id!,
        }))
        err && errMsg.push(`${item.id}删除失败:${err?.message || ''}`)
      }) || [])
      // 将新的短信通道信息加入进去
      await Promise.all(loadData.smsAccountGroups.map(async (item: SmsAccountGroupItem) => {
        const [err] = await to(merchantSmsChannelModel.saveSmsChannelAccountGroup({
          smsAccountGroup: pickAttrFromObj(item, [
            'smsAccountNumbers', 'cityCodes', 'smsServiceProvider', 'tenantSmsTemplateId'
          ]),
          templateId: currentId
        }))
        err && errMsg.push(`添加失败:${err?.message || ''}`)
      }))
      loading.value = false
      if (errMsg.length) ElMessage.error(errMsg.join('\n'))
      emits('confirm')
      cancel()
    }
  })
}

const rules = {
  smsTemplateId: [
    { required: true, message: '请选择需要导入的短信模板', trigger: 'change' },
  ],
}

/** watch */
const smsTemplateList = ref<null | SmsTemplateItem[]>(null)
// 监听visible等组件入参
watch(() => props.visible, async () => {
  visible.value = props.visible
  if (props.visible) {
    editData.smsTemplateId = undefined
    const [err, res] = await to(merchantSmsTemplateModel.getSmsTemplate({
      secondIndustry: merchantStore.editingSmsTemplate?.secondIndustry
    }))
    smsTemplateList.value = res || []
    editRef.value && editRef.value.clearValidate()
  }
})

const clearAllData = () => {
  editRef.value = null
  smsTemplateList.value = null
}
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    .el-form-item__content {
      align-items: flex-start;
      justify-content: flex-start;
    }
    &:first-child {
      margin-top: 14px;
    }
    &.muti-items {
      margin-top: 0;
      width: 320px;
      &:last-child {
        margin-left: 20px;
      }
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-table {
  font-size: var(--el-font-size-base);
}
</style>
