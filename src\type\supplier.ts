/**
 * 线路供应商
 */
import { MerchantLineInfo, } from '@/type/merchant'
import { GatewayItem } from '@/type/gateway'
import { BlackListGroupItem } from '@/type/dataFilter'
import { supplierLineStatusEnum } from '@/assets/js/map-supplier'

export enum SupplierBelongEnum {
  限时传送 = 'XSCS',
  仙人指路 = 'XRZL',
  得心应手 = 'DXYS',
  白泽自有 = 'BZZY',
  客户自有 = 'KHZY',
  其他 = 'QT',
}

// 供应商信息
export interface SupplierInfo {
  id?: number | null
  supplierNumber?: string
  supplierName?: string
  supplierBelong?: SupplierBelongEnum // 供应线路归属
  supplierProfile?: string
  supplierAddress?: string
  contactName?: string
  phoneNumber?: string
  email?: string
  duty?: string
  contactAddress?: string
  cooperationStatus?: string
  notes?: string
  createTime?: string
  updateTime?: string
  businessScope?: string
}

// 供应线路信息
export interface SupplierLineInfo {
  // V3接口，已弃用
  primaryIndustry?: string
  scope?: {
    [serviceProviderName: string]: {
      provinceCodes?: string[] | number[]
      cityCodes?: string[] | number[]
    }
  }

  // V4接口
  billingCycle?: number | string | null
  callLineSupplierId?: number
  callLineSupplierNumber?: string
  callLineSupplierName?: string
  callingRestrictions?: string[]
  callingRestrictionsGlobal?: string[]
  caps?: number | null
  cityCodeGroups?: {
    cityCodes?: string[]
    createTime?: string
    id?: number
    serviceProvider?: string
    supplyLineNumber?: string
    updateTime?: string
  }[]
  cityCodes?: string[]
  concurrentLimit?: number | string
  createTime?: string
  dialingRestrictions?: string[]
  dialingRestrictionsGlobal?: string[]
  disableTimeSlots?: number[]
  displayCallNumber?: string
  enableStatus?: supplierLineStatusEnum
  id?: number | null
  lineAccessType?: string
  lineGatewayIds?: number[]
  lineGateways?: GatewayItem[]
  lineName?: string
  lineNumber?: string
  masterCallNumber?: string
  notes?: string
  outboundTypes?: string[]
  // 挂起状态
  pending?: boolean
  prefix?: string
  registerIp?: string
  registerPort?: number | string
  secondIndustries?: string[]
  serviceProviders?: string[]
  tenantLines?: MerchantLineInfo[]
  unitPrice?: number | null
  updateTime?: string

  // V5接口
  // 线路类型，AI外呼和人工直呼
  lineType?: string

  // V6接口
  // 数据传输方式，true表示加密，false表示普通
  isForEncryptionPhones?: boolean
  // 临停状态
  pendingStatus?: boolean
  // 可支配并发
  supplyLimit?: number
  // 是否优先线路
  priorityStatus?: boolean
}

// 供应线路搜索条件的接口参数
export interface SearchLineParams {
  callLineSupplierId?: number
  callLineSupplierName?: string
  callLineSupplierNumber?: string
  cityCodes?: string[]
  displayCallNumber?: string
  masterCallNumber?: string
  secondIndustries?: string[]
  serviceProviders?: string[]
  status?: string
  enableStatus?: string
  supplyLineName?: string
  supplyLineNumber?: string
  supplyLineType?: string
  isForEncryptionPhones?: boolean // true表示加密， false表示普通
}

// 供应商模块映射表单项
export interface SupplierMapItemType {
  name: string
  val: any
  text: string
  tabText?: string
  province?: string
  city?: string
  tabName?: string
}

// 供应商模块映射表
export interface SupplierMapType {
  [propName: string]: SupplierMapItemType
}

// 供应线路 线路类型 映射表
export const supplierLineTypeList = {
  AI_OUTBOUND_CALL: { name: 'AI外呼', value: 'AI_OUTBOUND_CALL' },
  MANUAL_DIRECT_CALL: { name: '人工直呼', value: 'MANUAL_DIRECT_CALL' },
}

// 供应商 黑名单分组
export interface SupplierBlackGroupInfo extends BlackListGroupItem {
  supplyLines?: SupplierLineInfo[]
  isAllLinesActive?: boolean | null
}

// 供应商 黑名单分组 请求参数
export interface SupplierBlackGroupParam {
  callLineSupplierId?: number
  blackListGroupId?: number
  allLinesActive?: boolean
  supplyLineNumbers?: string[]
}

// 供应商 靓号限制
export interface SupplierGoodNumberInfo {
  lightPhoneId?: number | null
  phoneRegex?: string | null
  rank?: string | null
  isAllLinesActive?: boolean | null
  supplyLines?: SupplierLineInfo[] | null
  regexName?: string | null
  comment?: string | null
}

// 供应商 靓号限制 请求参数 基本信息
export interface SupplierGoodNumberBaseParam {
  allLinesActive?: boolean
  supplyLineNumbers?: string[]
}

// 供应商 靓号限制 请求参数 单个
export interface SupplierGoodNumberOneParam extends SupplierGoodNumberBaseParam {
  callLineSupplierId?: number
  lightPhoneId?: number
}

// 供应商 靓号限制 请求参数 多个
export interface SupplierGoodNumberMultipleParam {
  callLineSupplierId?: number
  lightPhoneBatchParamMap?: { [lightPhoneId: number]: SupplierGoodNumberBaseParam }
}
