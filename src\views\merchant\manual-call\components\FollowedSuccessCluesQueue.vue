<template>
  <!--筛选容器-->
  <div class="search-box">
    <div class="tw-grid tw-grid-cols-4 tw-gap-[12px]">
      <div class="item-col">
        <div class="label">号码/名单编号：</div>
        <el-input
          v-model="searchForm.phone"
          placeholder="输入号码/名单编号搜索"
          clearable
          @blur="formatPhone(searchForm.phone)"
          @keyup.enter="search()"
        >
          <template #suffix>
            <el-button :icon="FullScreen" type="primary" link @click="showPhone" />
          </template>
        </el-input>
      </div>

      <div class="item-col">
        <div class="label">星标：</div>
        <el-select v-model="searchForm.isStar" placeholder="全部" clearable @change="search()">
          <!--<el-option label="全部" value="" />-->
          <el-option label="&#9733; 标记" :value="true">
            <el-icon style="height: 100%;" :size="14" color="#FFAA3D">
              <SvgIcon name="star-active" />
            </el-icon>
            标记
          </el-option>
          <el-option label="&#9734; 未标记" :value="false">
            <el-icon style="height: 100%;" :size="14" color="#FFAA3D">
              <SvgIcon name="star-inactive" />
            </el-icon>
            未标记
          </el-option>
        </el-select>
      </div>

      <div class="item-col">
        <div class="label">分配时间：</div>
        <TimePickerBox
          v-model:start="searchForm.beAllocatedTimeStart"
          v-model:end="searchForm.beAllocatedTimeEnd"
        />
      </div>

      <div class="item-col">
        <span class="label">审核状态：</span>
        <el-select v-model="searchForm.examineStatus" placeholder="请选择审核状态" clearable>
          <el-option v-for="item in examineStatusOption" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
    </div>

    <!--展开更多-->
    <div v-show="isExpand" class="tw-grid tw-grid-cols-4 tw-gap-[12px] tw-mt-[12px]">
      <div class="item-col">
        <div class="label">姓名：</div>
        <el-input
          v-model="searchForm.name"
          placeholder="输入姓名"
          clearable
          @keyup.enter="search()"
        />
      </div>
      <div class="item-col">
        <div class="label">省：</div>
        <el-select v-model="searchForm.provinceCode" placeholder="省" filterable clearable>
          <el-option
            v-for="item in provinceList"
            :key="item.split(',')[0]"
            :label="item.split(',')[0]"
            :value="item.split(',')[1]"
          />
        </el-select>
      </div>

      <div class="item-col">
        <div class="label">市：</div>
        <el-select v-model="searchForm.cityCode" placeholder="市" filterable clearable>
          <el-option
            v-for="item in cityList"
            :key="item.split(',')[0]"
            :label="item.split(',')[0]"
            :value="item.split(',')[1]"
          />
        </el-select>
      </div>

      <div class="item-col">
        <span class="label">最近跟进时间：</span>
        <TimePickerBox
          v-model:start="searchForm.latestFollowUpTimeStart"
          v-model:end="searchForm.latestFollowUpTimeEnd"
        />
      </div>

      <div class="item-col">
        <span class="label">跟进次数：</span>
        <div class="item">
          <InputNumberBox
            v-model:value="searchForm.minFollowUpCount"
            placeholder="最低"
            :max="searchForm.maxFollowUpCount || Number.MAX_VALUE"
            append="次"
          />
          <span class="tw-w-[14px] tw-shrink-0">-</span>
          <InputNumberBox
            v-model:value="searchForm.maxFollowUpCount"
            placeholder="最高"
            :min="searchForm.minFollowUpCount || 0"
            append="次"
          />
        </div>
      </div>

      <div class="item-col ">
        <span class=" label">自动回收时间：</span>
        <TimePickerBox
          v-model:start="searchForm.autoRecoveryTimeStart"
          v-model:end="searchForm.autoRecoveryTimeEnd"
        />
      </div>
    </div>

    <!--功能按钮-->
    <div class="tw-flex tw-justify-end tw-items-center tw-h-[32px] tw-mt-[12px] tw-pt-[12px] tw-border-t-[1px]">
      <el-button type="primary" link @click="clearSearchForm">
        <el-icon size="--el-font-size-base">
          <SvgIcon name="reset" color="var(--el-color-primary)" />
        </el-icon>
        <span>重置</span>
      </el-button>

      <el-button type="primary" link @click="search()">
        <el-icon size="--el-font-size-base" color="var(--el-color-primary)">
          <SvgIcon name="filter" color="none" />
        </el-icon>
        <span>查询</span>
      </el-button>

      <el-button v-if="isExpand" type="primary" link @click="isExpand=false">
        收起
        <el-icon size="--el-font-size-base">
          <ArrowUp />
        </el-icon>
      </el-button>

      <el-button v-else type="primary" link @click="isExpand=true">
        展开
        <el-icon size="--el-font-size-base">
          <ArrowDown />
        </el-icon>
      </el-button>
    </div>
  </div>

  <!--表格-->
  <ClueFollowUpTable
    v-model:selectClues="selectClues"
    :loading="loading"
    :tableData="tableData"
    :followUpStatus="FollowUpStatusEnum['跟进成功']"
    @search="updateTable"
  >
    <!--操作列按钮-->
    <template v-slot:operate="{ row }">
      <el-button type="primary" link @click="goDetail(row)">详情</el-button>
    </template>
  </ClueFollowUpTable>

  <!--批量导入名单弹窗 -->
  <el-dialog v-model="phoneDialogVisible" width="480px" :close-on-click-modal="false" align-center>
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">号码/名单编号</div>
    </template>
    <div class="tw-grid tw-grid-cols-2 tw-gaps-1 tw-p-[12px]">
      <div>
        <el-input
          v-model="tempPhone"
          :rows="10"
          type="textarea"
          placeholder="请输入号码/名单编号"
          @blur="formatPhone(tempPhone)"
        />
      </div>
      <div class="tw-ml-[12px]">
        <div class="tw-text-left tw-mb-[6px] tw-text-[14px]">号码/名单编号：</div>
        <div v-for="p in tempPhone?.split(',') || []" v-show="!!p" class="tw-text-left tw-list-decimal tw-my-[2px]">
          {{ p }}
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmPhone">确认</el-button>
        <el-button @click="cancelPhone">关闭</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 线索详情 -->
  <ClueDetailsDrawer
    v-model:visible="clueDrawerVisible"
    :clueData="currentClue"
    :permission="permissions"
    :transferRecordVisible="false"
    :fromWorkbench="true"
    @update:data="updateTable"
  />
</template>

<script lang="ts" setup>
// 插件、依赖等
import { computed, defineAsyncComponent, onActivated, reactive, ref, watch } from 'vue'
import routeMap from '@/router/asyncRoute/route-map'
import { useGlobalStore } from '@/store/globalInfo'
import { useUserStore } from "@/store/user"
import { useSeatPhoneStore } from '@/store/seat-phone'
import { enum2Options } from '@/utils/utils'
import { ArrowDown, ArrowUp, FullScreen } from '@element-plus/icons-vue'
import { ClueItem, ClueStatusEnum, ExamineStatusEnum, FollowUpStatusEnum, SearchFormOrigin, WorkbenchClueSearchInfo } from '@/type/clue'
import dayjs from 'dayjs'
// api
import { seatWorkbenchClueModel } from '@/api/seat'
import to from "await-to-js"

// 动态引入组件
const ClueFollowUpTable = defineAsyncComponent(() => import('./ClueFollowUpTable.vue'))
const ClueDetailsDrawer = defineAsyncComponent(() => import('./ClueDetailsDrawer.vue'))
const TimePickerBox = defineAsyncComponent(() => import('@/components/TimePickerBox.vue'))
const InputNumberBox = defineAsyncComponent(() => import('@/components/InputNumberBox.vue'))

const props = defineProps<{
  followUpStatus: FollowUpStatusEnum;
  needRefresh?: boolean;
}>();
const emits = defineEmits(['update:needRefresh'])

const globalStore = useGlobalStore()
const loading = ref(false)
// 用户权限获取
const userStore = useUserStore();
const seatPhoneStore = useSeatPhoneStore()

const permissions = computed(() => {
  const curPermissions = userStore.permissions[routeMap['坐席工作台'].id]
  const res: Record<string, boolean> = {
    '修改跟进状态': true,
    '修改星标': true
  }
  if (curPermissions?.includes(routeMap['坐席工作台'].permissions['表单编辑'])) {
    res['编辑表单'] = true
  }
  if (curPermissions?.includes(routeMap['坐席工作台'].permissions['查看明文'])) {
    res['查看明文'] = true
  }
  return res
})

const isExpand = ref(false)

// 审核状态选项
const examineStatusOption = enum2Options(ExamineStatusEnum)

/** 搜索、列表等主体数据 */
const searchForm = reactive<WorkbenchClueSearchInfo>(new SearchFormOrigin(userStore.groupId,  ClueStatusEnum['已分配']))
const provinceList = ref<string[]>([])
const provinceAllMap = ref<{ [key: string]: string[] }>({})
const cityList = computed(() => {
  const provinces = Object.keys(provinceAllMap.value)
  const province = provinces.find(item => searchForm.provinceCode && item.includes(searchForm.provinceCode))
  return searchForm.provinceCode && province ? (provinceAllMap.value[province] || []) : (Object.values(provinceAllMap.value).flat() || [])
})
const tableData = ref<ClueItem[]>([])
const search = async () => {
  loading.value = true
  const params = JSON.parse(JSON.stringify(searchForm))
  Object.keys(params).forEach((key: string) => {
    if (params[<keyof typeof params>key] === '') {
      params[<keyof typeof params>key] = undefined
    }
  })
  params.latestCallStatusList = params.latestCallStatusList?.join(',')?.split(',') || undefined
  const [_, res] = <[any, ClueItem[]]>await to(seatWorkbenchClueModel.getFollowSuccessClueList(params))
  tableData.value = Array.isArray(res) ? res : []
  // 按分配时间倒序排序
  tableData.value = Array.isArray(res)
    ? res.sort((a, b) => dayjs(a.beAllocatedTime).isAfter(b.beAllocatedTime) ? -1 : 1)
    : []
  selectClues.value = []
  loading.value = false
}
const updateTable = () => {
  search()
  emits('update:needRefresh', true)
}
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchFormOrigin(userStore.groupId))
}
/** 线索操作 【开始】 */
const selectClues = ref<ClueItem[]>([]) // 选中数据

const clueDrawerVisible = ref(false)
const currentClue = ref<ClueItem>({ id: undefined, })
const goDetail = (row: ClueItem) => {
  clueDrawerVisible.value = true
  currentClue.value = row
}

/** 号码、名单编号批量输入显示弹窗 开始 */
const phoneDialogVisible = ref(false) // 名单编号弹窗Visible
const tempPhone = ref() // 名单编号弹窗 数据
// 点击icon打开名单编号弹窗
const showPhone = () => {
  phoneDialogVisible.value = true
  tempPhone.value = searchForm.phone
}
// 点击名单编号弹窗确认
const confirmPhone = () => {
  searchForm.phone = tempPhone.value
  phoneDialogVisible.value = false
  tempPhone.value = ''
}
// 点击名单编号弹窗取消
const cancelPhone = () => {
  phoneDialogVisible.value = false
  tempPhone.value = ''
}
// 弹窗-失焦后，格式化名单编号
const formatPhone = (val?: string) => {
  if (!val || !val?.trim()) {
    tempPhone.value = ''
  } else {
    const str = val?.trim()?.replace(/\n/g, ',') || ''
    tempPhone.value = [...new Set(str.split(',').map(item => item.trim()).filter(item => !!item))].join(',')
  }
}
/** 号码、名单编号批量输入显示弹窗 结束 */

const init = async () => {
  await globalStore.getProvinceInfo()
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
}
// 表格区
onActivated(() => {
  init()
  updateTable()
})

watch(() => props.needRefresh, n => {
  n && updateTable()
}, {
  deep: true
})
watch(() => props.followUpStatus, () => {
  clearSearchForm()
  updateTable()
}, {
  deep: true
})
</script>

<style scoped lang="postcss">
</style>
