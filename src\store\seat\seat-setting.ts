import { defineStore } from 'pinia'
import { ref } from 'vue'
import { SeatSetting } from '@/type/seat'
import { assignMatchingProperties } from "@/utils/utils";

export const useSeatSettingStore = defineStore('seatSetting', () => {
  // 坐席设置（由坐席自己配置）
  const seatSetting = ref<SeatSetting>({
    autoCheckInTask: true, // 人机协同 自动签入任务
    autoAccept: false, // 人机协同 自动监听/接听
    autoCallNext: false, // 人工直呼 自动拨打下一个
    autoResendSms: true, // 人工直呼 自动重发备注短信
  })

  /**
   * 更新坐席设置
   * @param data 新设置
   */
  const updateSeatSetting = (data: SeatSetting) => {
    assignMatchingProperties(seatSetting.value, data)
  }

  return {
    seatSetting,
    updateSeatSetting,
  }
}, {
  persist: [
    { paths: ['seatSetting'], storage: localStorage },
  ]
})
