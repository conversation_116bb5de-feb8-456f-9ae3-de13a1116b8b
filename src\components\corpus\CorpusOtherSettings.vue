<template>
  <div class="tw-font-[600] tw-text-left tw-my-[6px] tw-text-[14px]">其他设置</div>
  <div class="tw-bg-white tw-px-[12px] tw-py-[8px] tw-rounded-[4px]">
    <el-form-item v-if="!isInterrupt" label="触发事件：" prop="event" >
      <el-cascader
        v-model="addData.eventTriggerValueIds"
        :options="eventOptions"
        :props="eventProps"
        clearable
        style="width:100%"
        collapse-tags
        collapse-tags-tooltip
        :max-collapse-tags="1"
        placeholder="请选择事件触发类型"
        @change="updateDate"
      />
    </el-form-item>
    <el-form-item v-if="isNormal && !isInterrupt" label="触发转人工："  prop="listenInOrTakeOver" @change="updateDate">
      <el-radio-group v-model="addData.listenInOrTakeOver" class="tw-ml-[6px]" :disabled="props.corpusData.connectType == ConnectTypeEnum['挂机']">
        <el-radio :label="true">开启</el-radio>
        <el-radio :label="false">关闭</el-radio>
      </el-radio-group>
    </el-form-item>
    <!-- 普通语料/连接（挂机）语料 -->
    <el-form-item v-if="showSmsTrigger && !isInterrupt" label="触发短信："  @change="updateDate">
      <el-radio-group v-model="ifSendSms" class="tw-ml-[6px]">
        <el-radio :label="true">开启</el-radio>
        <el-radio :label="false">关闭</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="ifSendSms && showSmsTrigger" label="触发点名称：" prop="smsTriggerName" @change="updateDate">
      <el-input v-model="addData.smsTriggerName" placeholder="请输入触发点名称，不超过8个字符，同一个话术不可重复" show-word-limit clearable />
    </el-form-item>
    <el-form-item v-if="isNormal && !isInterrupt" label="最长等待：" prop="maxWaitingTime">
      <InputNumberBox v-model:value="addData.maxWaitingTime" placeholder="最长等待" append="毫秒" @update:value="updateDate"/>
    </el-form-item>
    <el-form-item label="意向标签：" prop="aiLabelIds">
      <SelectBox 
        v-model:selectVal="addData.aiLabelIds"
        @update:select-val="updateLabel"
        :options="scriptStore.intentionTagOptions"
        :canCopy="!isChecked"
        copyName="aiLabelIds"
        name="labelName"
        val="id"
        :limitNum="20"
        placeholder="请选择意向标签"
        :filterable="true" 
        class="tw-flex-grow"
        :multiple="true"
      >
      </SelectBox>
    </el-form-item>
    <el-form-item label="意向分类：" prop="aiIntentionTypeId">
      <el-select
        v-model="addData.aiIntentionTypeId"
        clearable
        placeholder="请选择意向分类"
        style="width:100%"
        @change="updateDate"
      >
        <el-option
          v-for="item in intentionLevelOptions"
          :key="item.id"
          :label="item.intentionType + ' - ' + item.intentionName"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, onActivated } from 'vue'
import { ScriptCorpusItem, CorpusTypeEnum, ScriptCorpusOtherItem, ConnectTypeEnum } from '@/type/speech-craft'
import { IntentionType, } from '@/type/IntentionType'
import { pickAttrFromObj } from '@/utils/utils'
import { ElMessage, } from 'element-plus'
import { useScriptStore } from '@/store/script'
import InputNumberBox from '@/components/InputNumberBox.vue'
import SelectBox from '@/components/SelectBox.vue'

// props & emits
const emits = defineEmits(['update:corpusData',])
const props = defineProps<{
  corpusData: ScriptCorpusItem;
}>();

/** 常量 */
const scriptStore = useScriptStore() // 话术信息
const isChecked = scriptStore.isChecked // true: 话术查看, false: 话术编辑
const eventProps = { emitPath: false, multiple: true} // 设置常量：用于事件触发级联
const ifSendSms = ref(false) // 默认不触发短信不显示触发点

/** 变量 */
// 语料其他设置，表格数据
const addData = reactive<ScriptCorpusOtherItem>({})
// 选项信息
const intentionLevelOptions = ref<IntentionType[]>([]) // 意向分类 选项
// 事件触发 选项
const eventOptions = ref<{
  label: string,
  value: number,
  children: {
    label: string,value: number
  }[]
}[]>([])
const isNormal = computed(() => addData.corpusType?.includes('_CONNECT') ? false : true) // 是否非连接语料
const isInterrupt = computed(() => addData.corpusType && [CorpusTypeEnum['打断垫句'], CorpusTypeEnum['续播垫句'],].includes(addData.corpusType)) // 是否是中断语料
const showSmsTrigger = computed(() => {
  return props.corpusData?.connectType === ConnectTypeEnum['挂机'] || isNormal.value
})

/** 处理函数开始 */
// 初始化选项
const initOptions = async() => {
  try {
    intentionLevelOptions.value = await scriptStore.getIntentionLevelOptions()
    const res = await scriptStore.getEventOptions()
    eventOptions.value = []
    res.map(item => {
      if (item.id && item.eventName && item.eventValuemap && Object.values(item.eventValuemap).length > 0) {
        eventOptions.value.push({
          value: item.id,
          label: item.eventName,
          children: Object.values(item.eventValuemap)!.map(v => {
            return {
              value: v.valueId!,
              label: v.name!,
            }
          })
        })
      }
    })
    if (addData.id) {
      addData.aiLabelIds = addData.aiLabels?.map(item => item.id as number) || []
      addData.aiIntentionTypeId = addData.aiIntentionType?.id || undefined
    }
  } catch (err) {
    ElMessage({
      message: '获取选项数据有误',
      type: 'error',
    })
  }
}
// 更新update:corpusData
const updateDate = () => {
  addData.aiIntentionType = intentionLevelOptions.value.find(item => addData.aiIntentionTypeId == item.id) || null
  const propList:( keyof ScriptCorpusOtherItem)[] = [
    'id','corpusType', 'aiLabelIds', 'aiLabels', 'aiIntentionType', 'aiIntentionTypeId',
    'maxWaitingTime', 'eventTriggerValueIds', 'listenInOrTakeOver', 'smsTriggerName'
  ]
  if (!showSmsTrigger.value || !ifSendSms.value) {
    addData.smsTriggerName = undefined
  }
  emits('update:corpusData', pickAttrFromObj(addData, propList))
}
/** 处理函数结束 */

/** watch开始 */ 
// 监听标签和分类，更新接口实际需要的aiLabels、aiIntentionType字段
const updateLabel = () => {
  addData.aiLabels = scriptStore.intentionTagOptions?.filter(item => addData.aiLabelIds?.includes(item.id as number)) || []
  updateDate()
}
// 监听语料id，用于语料变化初始化数据、重新赋值addData
watch(() => props.corpusData, () => {
  Object.assign(addData, pickAttrFromObj(props.corpusData, [
    'id','corpusType', 'aiLabelIds', 'aiLabels', 'aiIntentionType', 'aiIntentionTypeId', 'smsTriggerName',
    'maxWaitingTime', 'eventTriggerValueIds', 'listenInOrTakeOver', 'connectType'
  ]))
  addData.listenInOrTakeOver = props.corpusData.connectType === ConnectTypeEnum['挂机'] ? false : addData.listenInOrTakeOver ?? false

  addData.aiLabelIds = addData.aiLabels?.map(item => item.id as number) || []
  addData.aiIntentionTypeId = addData.aiIntentionType?.id || undefined
}, {deep: true, immediate: true})

// 仅在语料id切换后更新，防止更新数据后，未填写触发点名称，导致触发短信关闭
watch(() => props.corpusData.id, () => {
  if (!props.corpusData.id) return
  ifSendSms.value = !!props.corpusData.smsTriggerName
}, {immediate: true})
onActivated(() => {
  initOptions()
})
initOptions()
/** watch结束 */ 
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.text {
  font-size: var(--el-font-size-base);
  line-height: 24px;
  margin-right: 10px;
  text-align: left;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    font-size: var(--el-font-size-base);
    &:first-child {
      margin-top: 14px;
    }
  }
  .el-table {
    font-size: var(--el-font-size-base);
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
}
:deep(.el-cascader__tags .el-tag) {
  margin: 0 0 0 4px;
  .el-tag__content {
    height: 100%;
    display: flex;
    align-items: center;
  }
}
</style>