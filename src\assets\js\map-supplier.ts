import { SupplierMapItemType, SupplierMapType } from '@/type/supplier'

// ---------------------------------------- 供应商 信息 开始 ----------------------------------------
/**
 * 供应商 状态 枚举值 接口数据
 */
export enum supplierStatusEnum {
  DISABLED = 'DISABLE',
  ENABLED = 'ENABLE',
}

/**
 * 供应商 状态 转换表 页面展示
 */
export const supplierStatusList: SupplierMapType = {
  enabled: { name: 'enabled', text: '启用', tabText: '启用', val: supplierStatusEnum.ENABLED },
  disabled: { name: 'disabled', text: '停用', tabText: '停用', val: supplierStatusEnum.DISABLED },
}
// ---------------------------------------- 供应商 信息 结束 ----------------------------------------

// ---------------------------------------- 供应商 线路 开始 ----------------------------------------
/**
 * 供应线路 状态 枚举值 接口数据
 */
export enum supplierLineStatusEnum {
  DISABLED = 'DISABLE',
  ENABLED = 'ENABLE',
}

/**
 * 供应线路 状态 转换表 页面展示
 */
export const supplierLineStatusList: SupplierMapType = {
  enabled: { name: 'enabled', text: '启用', val: supplierLineStatusEnum.ENABLED },
  disabled: { name: 'disabled', text: '停用', val: supplierLineStatusEnum.DISABLED },
}

/**
 * 供应线路 状态 映射表 页面展示
 */
export const supplierLineStatusMap: Map<supplierLineStatusEnum, SupplierMapItemType> = new Map([
  [supplierLineStatusEnum.ENABLED, supplierLineStatusList.enabled],
  [supplierLineStatusEnum.DISABLED, supplierLineStatusList.disabled],
])

/**
 * 供应线路 外呼类型 枚举值 接口数据
 */
export enum supplierLineOutboundTypeEnum {
  MOBILE = 'MOBILE_PHONE',
  LANDLINE = 'FIXED_PHONE',
}

/**
 * 供应线路 外呼类型 转换表 页面展示
 */
export const supplierLineOutboundTypeList: SupplierMapType = {
  mobile: { name: 'mobile', text: '手机号', val: supplierLineOutboundTypeEnum.MOBILE },
  landline: { name: 'landline', text: '固定电话', val: supplierLineOutboundTypeEnum.LANDLINE },
}

/**
 * 供应线路 运营商 枚举值 接口数据
 */
export enum supplierOperatorEnum {
  ALL = 'ALL_OPERATOR',
  TELECOM = 'CHINA_TELECOM',
  UNICOM = 'CHINA_UNICOM',
  MOBILE = 'CHINA_MOBILE',
  // VIRTUAL = 'VIRTUAL_OPERATOR',
  UNKNOWN = 'UNKNOWN',
}

/**
 * 供应线路 运营商 转换表 页面展示
 */
export const supplierOperatorList: SupplierMapType = {
  'all': {
    name: 'all',
    text: '全部',
    val: supplierOperatorEnum.ALL,
    province: 'allRestrictProvince',
    city: 'allRestrictCity',
    tabName: '全部',
  },
  'telecom': {
    name: 'telecom',
    text: '中国电信',
    val: supplierOperatorEnum.TELECOM,
    province: 'dxRestrictProvince',
    city: 'dxRestrictCity',
    tabName: '电信',
  },
  'unicom': {
    name: 'unicom',
    text: '中国联通',
    val: supplierOperatorEnum.UNICOM,
    province: 'ltRestrictProvince',
    city: 'ltRestrictCity',
    tabName: '联通',
  },
  'mobile': {
    name: 'mobile',
    text: '中国移动',
    val: supplierOperatorEnum.MOBILE,
    province: 'ydRestrictProvince',
    city: 'ydRestrictCity',
    tabName: '移动',
  },
  // 'virtual': {
  //   name: 'virtual',
  //   text: '虚拟运营商',
  //   val: supplierOperatorEnum.VIRTUAL,
  //   province: 'virtualRestrictProvince',
  //   city: 'virtualRestrictCity',
  //   tabName: '虚拟',
  // },
  'unknown': {
    name: 'unknown',
    text: '未知',
    val: supplierOperatorEnum.UNKNOWN,
    province: 'unknownRestrictProvince',
    city: 'unknownRestrictCity',
    tabName: '未知',
  },
}

/**
 * 供应线路 运营商 映射表 页面展示
 */
export const supplierOperatorMap: Map<supplierOperatorEnum, SupplierMapItemType> = new Map([
  [supplierOperatorEnum.ALL, supplierOperatorList.all],
  [supplierOperatorEnum.TELECOM, supplierOperatorList.telecom],
  [supplierOperatorEnum.UNICOM, supplierOperatorList.unicom],
  [supplierOperatorEnum.MOBILE, supplierOperatorList.mobile],
  // [supplierOperatorEnum.VIRTUAL, supplierOperatorList.virtual],
  [supplierOperatorEnum.UNKNOWN, supplierOperatorList.unknown],
])

/**
 * 供应线路 运营商 省市组件（外呼支持范围）的标签卡名称 映射表 页面展示
 */
export const supplierOperatorTabNameMap: Map<string | undefined, SupplierMapItemType> = new Map([
  [supplierOperatorList.all.tabName, supplierOperatorList.all],
  [supplierOperatorList.telecom.tabName, supplierOperatorList.telecom],
  [supplierOperatorList.unicom.tabName, supplierOperatorList.unicom],
  [supplierOperatorList.mobile.tabName, supplierOperatorList.mobile],
  // [supplierOperatorList.virtual.tabName, supplierOperatorList.virtual],
  [supplierOperatorList.unknown.tabName, supplierOperatorList.unknown],
])

/**
 * 供应线路 接入类型 枚举值 接口数据
 */
export enum supplierLineAccessTypeEnum {
  DIRECT = 'DIRECT_CONNECT',
  INDIRECT = 'INDIRECT_CONNECT',
}

/**
 * 供应线路 接入类型 转换表 页面展示
 */
export const supplierLineAccessTypeList: SupplierMapType = {
  direct: { name: 'direct', text: '直连', val: supplierLineAccessTypeEnum.DIRECT },
  indirect: { name: 'indirect', text: '间联', val: supplierLineAccessTypeEnum.INDIRECT },
}

/**
 * 供应线路 接入类型 映射表 页面展示
 */
export const supplierLineAccessTypeMap: Map<supplierLineAccessTypeEnum, SupplierMapItemType> = new Map([
  [supplierLineAccessTypeEnum.DIRECT, supplierLineAccessTypeList.direct],
  [supplierLineAccessTypeEnum.INDIRECT, supplierLineAccessTypeList.indirect],
])


/**
 * 供应线路 数据传输方式
 */
export const dataEncryptionMethodOption = [
  { label: '普通', value: false },
  { label: '加密', value: true },
]

// ---------------------------------------- 供应商 线路 结束 ----------------------------------------
