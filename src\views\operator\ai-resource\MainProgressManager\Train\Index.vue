<template>
  <!--模块容器-->
  <div class="training-container">
    <!--左侧区块-->
    <div class="block left-block">
      <el-scrollbar>
        <!--启停按钮 容器-->
        <div class="switch-box">
          <!--文字训练 启停按钮-->
          <div
            v-show="scriptTrainStore.trainType!==ScriptTrainTypeEnum.AUDIO"
            v-loading="loadingSwitch"
            class="switch"
            :class="scriptTrainStore.status===ScriptTrainStatusEnum.IDLE?'disabled':'enabled'"
            @click="onClickTextTrainSwitch"
          >
            <!--按钮图标-->
            <div :class="scriptTrainStore.status===ScriptTrainStatusEnum.IDLE?'tw-hidden':'tw-visible'">
              <el-icon class="button-icon-normal" :size="20" color="#626366">
                <SvgIcon name="file" />
              </el-icon>
              <el-icon class="button-icon-hover" :size="20" color="#fff">
                <SvgIcon name="file" />
              </el-icon>
            </div>
            <!--开关文本-->
            <div class="switch-text">
              <div v-show="scriptTrainStore.status===ScriptTrainStatusEnum.IDLE">开始</div>
              <div v-show="scriptTrainStore.status!==ScriptTrainStatusEnum.IDLE">结束</div>
              <div>文字训练</div>
            </div>
            <!--计时文本-->
            <div class="stopwatch-text" :class="scriptTrainStore.status===ScriptTrainStatusEnum.IDLE?'tw-hidden':'tw-visible'">
              {{ trainSecondText }}
            </div>
          </div>

          <!--语音训练 启停按钮-->
          <div
            v-show="scriptTrainStore.trainType!==ScriptTrainTypeEnum.TEXT"
            v-loading="loadingSwitch"
            class="switch"
            :class="scriptTrainStore.status===ScriptTrainStatusEnum.IDLE?'disabled':'enabled'"
            @click="onClickAudioTrainSwitch"
          >
            <!--按钮图标-->
            <div :class="scriptTrainStore.status===ScriptTrainStatusEnum.IDLE?'tw-hidden':'tw-visible'">
              <el-icon class="button-icon-normal" :size="20" color="#626366">
                <SvgIcon name="microphone2" />
              </el-icon>
              <el-icon class="button-icon-hover" :size="20" color="#fff">
                <SvgIcon name="microphone2" />
              </el-icon>
            </div>
            <!--开关文本-->
            <div class="switch-text">
              <div v-show="scriptTrainStore.status===ScriptTrainStatusEnum.IDLE">开始</div>
              <div v-show="scriptTrainStore.status!==ScriptTrainStatusEnum.IDLE">结束</div>
              <div>语音训练</div>
            </div>
            <!--计时文本-->
            <div class="stopwatch-text" :class="scriptTrainStore.status===ScriptTrainStatusEnum.IDLE?'tw-hidden':'tw-visible'">
              {{ trainSecondText }}
            </div>
          </div>
        </div>

        <!--训练历史-->
        <div v-show="historyVisible" v-loading="loadingHistory" class="left-block-main">
          <!--标题文本-->
          <h4 class="left-block-title">
            <span>训练历史</span>
            <span class="list-account">（{{ historyAllList?.length ?? 0 }}）</span>
          </h4>
          <template v-if="historyAllList.length">
            <!--历史列表-->
            <ul class="history-list">
              <!--历史单项-->
              <li
                v-for="historyItem in historyCurrentList"
                :key="historyItem.id"
                class="history-item"
                :class="{'current': historyItem.id === currentHistoryItem.id}"
                @click="onClickHistoryItem(historyItem)"
              >
                <span class="history-item-datetime">
                  {{ dayjs(historyItem.trainTime).format('YYYY-MM-DD HH:mm:ss') }}
                </span>
                <span class="history-item-type">
                  {{ historyItem.isStringTrain ? '文字' : '语音' }}
                </span>
                <span class="history-item-account tw-truncate">
                  {{ historyItem?.account ?? '' }}
                </span>
              </li>
            </ul>
            <!--分页条-->
            <div class="history-pagination">
              <PaginationBox
                :currentPage="historyPageNum"
                :pageSize="historyPageSize"
                :pagerCount="5"
                :pageSizeList="historyPageSizeList"
                :total="historyTotalCount"
                :mini="true"
                :padding="false"
                @search="onHistoryPaginationSearch"
                @update="onHistoryPaginationUpdate"
              />
            </div>
          </template>
          <template v-else>
            <el-empty />
          </template>
        </div>

        <!--事件动作-->
        <div v-show="eventVisible" class="left-block-main">
          <!--标题文本-->
          <h4 class="left-block-title">
            <span>事件动作</span>
            <span class="list-account">（{{ scriptTrainStore.eventAllList?.length ?? 0 }}）</span>
          </h4>
          <!--事件列表-->
          <el-checkbox-group v-model="scriptTrainStore.eventSelectedList" class="event-list">
            <!--事件单项-->
            <div v-for="eventItem in scriptTrainStore.eventAllList" class="event-item">
              <!--UI组件的label和插槽替代的label不影响-->
              <!--插槽label当做页面展示，组件label当做接口数据-->
              <!--<el-checkbox class="event-item" :label="eventItem.value" @change="change(eventItem, index)">-->
              <el-checkbox class="event-item" :label="eventItem.value" size="small">
                <div class="event-item-content tw-truncate">
                  {{ `${eventItem.definition} (${eventItem.value})` }}
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>
          <!--提交按钮-->
          <el-button
            :disabled="!scriptTrainStore.eventAllList.length"
            type="primary"
            class="event-submit"
            @click="onClickSubmitEvent"
          >
            记录事件
          </el-button>
        </div>
      </el-scrollbar>
    </div>

    <!--中间区块-->
    <div class="block center-block">
      <div class="tw-w-full tw-min-h-[1px] tw-flex-auto">
        <!--加载窗口-->
        <!--<div v-show="scriptTrainStore.status === ScriptTrainStatusEnum.CHECK_SCRIPT" v-loading="true" class="incoming" element-loading-text="正在检查话术是否符合发布要求"></div>-->
        <!--<div v-show="scriptTrainStore.status === ScriptTrainStatusEnum.WAIT_CALL" v-loading="true" class="incoming" element-loading-text="话术检查通过，正在等待AI来电"></div>-->
        <div v-show="scriptTrainStore.status === ScriptTrainStatusEnum.CHECK_SCRIPT" class="incoming">
          <div v-loading="true" class="tw-w-[60px] tw-h-[60px]"></div>
          <div>
            正在全力处理话术检查与同步，已用时 {{ scriptTrainStore.checkingSecond }} 秒
          </div>
        </div>
        <div v-show="scriptTrainStore.status === ScriptTrainStatusEnum.WAIT_CALL" class="incoming">
          <div v-loading="true" class="tw-w-[60px] tw-h-[60px]"></div>
          <div>
            话术检查通过，正在等待AI来电
          </div>
        </div>
        <!--对话详情组件-->
        <div
          v-show="scriptTrainStore.status !== ScriptTrainStatusEnum.CHECK_SCRIPT && scriptTrainStore.status !== ScriptTrainStatusEnum.WAIT_CALL"
          v-loading="loadingDialogDataVisible"
          class="dialog center tw-text-[13px]"
        >
          <CallRecordDialogBox
            :clearAudio="scriptTrainStore.clearAudio"
            :dataList="scriptTrainStore.dialogData"
            :startEndInfo="[]"
            :keepLatest="scriptTrainStore.status===ScriptTrainStatusEnum.IN_CALL"
            :train="true"
            :autoPlayLastAiAudio="autoPlayLastAiAudio"
            @update:clearAudio="onUpdateClearAudio"
          >
            <template #trainAiInterrupt="{itemData, itemIndex}: {itemData: Partial<RecordDialogueData>, itemIndex: number}">
              <template v-if="aiInterruptVisible && itemData.type === 0 && itemIndex === lastAiIndex">
                <div class="tw-flex tw-flex-col tw-justify-end tw-items-start tw-h-full tw-pb-[24px] tw-px-[4px] tw-text-left">
                  <!--可打断和已说完-->
                  <template v-if="itemData.interruptType!==InterruptTypeEnum['不允许打断']">
                    <!--可打断-->
                    <template v-if="aiInterruptStatus===ScriptTextTrainAiInterruptEnum.ALLOW">
                      <div>
                        AI说话中，可打断（{{ currentAiInterruptSecond }}秒）
                      </div>
                      <el-button link type="primary" @click="onClickStopAiInterrupt">
                        结束（F7）
                      </el-button>
                    </template>
                    <!--已说完-->
                    <template v-else-if="aiInterruptStatus===ScriptTextTrainAiInterruptEnum.FINISH">
                      AI已说完
                    </template>
                  </template>

                  <!--不允许打断-->
                  <template v-else>
                  </template>
                </div>
              </template>
            </template>
          </CallRecordDialogBox>
        </div>
      </div>

      <!--消息框-->
      <div v-show="scriptTrainStore.trainType===ScriptTrainTypeEnum.TEXT&&scriptTrainStore.status===ScriptTrainStatusEnum.IN_CALL" class="msg-editor">
        <!--标题-->
        <div class="msg-editor-header">
          <div class="msg-editor-header-row">
            <div class="msg-editor-title">
              输入文字
            </div>
            <div class="msg-editor-subtitle">
              {{ msgVal.length }} / {{ msgValMaxLength }}
            </div>
            <div class="msg-editor-subtitle tw-flex tw-items-center tw-gap-[4px] tw-ml-auto">
              <span>
                AI可打断
              </span>
              <el-switch v-model="scriptTrainStore.aiInterruptEnabled" @change="onChangeAiInterruptEnabled" />
            </div>
          </div>

          <div class="msg-editor-header-row">
            <div class="msg-editor-subtitle">
              {{ msgEditorHint }}
            </div>
          </div>
        </div>
        <!--主体-->
        <div class="msg-editor-main">
          <!--文本框-->
          <div class="msg-editor-input">
            <el-input
              ref="msgEditorRef"
              v-model="msgVal"
              type="textarea"
              clearable
              :maxlength="msgValMaxLength"
              show-word-limit
              resize="none"
              placeholder="请输入客户说话内容"
              @keyup="onMsgInputKeyEvent"
              @keydown.enter.prevent
            />
          </div>
          <!--按钮容器-->
          <div class="msg-editor-button-box">
            <el-button type="danger" :disabled="loadingSending||loadingSilence" @click="onClickHangup">
              挂断 (F8)
            </el-button>
            <el-button :disabled="loadingSending||loadingSilence" @click="onClickReset">
              清空 (Esc)
            </el-button>
            <!--占位符-->
            <div class="tw-ml-auto"></div>
            <el-button :disabled="loadingSending||!multipleCorpusFinished" :loading="loadingSilence" @click="onClickSilence">
              沉默 (F9)
            </el-button>
            <el-button type="primary" :disabled="!msgVal||loadingSilence" :loading="loadingSending" @click="onClickSendMsg">
              发送 (Enter)
            </el-button>
          </div>
        </div>
      </div>

      <!--消息框 电话已挂断-->
      <div v-show="scriptTrainStore.status===ScriptTrainStatusEnum.HANG_UP" class="msg-editor">
        <div class="msg-editor-title">
          电话已挂断，请结束训练
        </div>
      </div>
    </div>

    <!--右侧区块-->
    <div v-loading="loadingCallRecord" class="block right-block">
      <el-empty v-show="!callRecordVisible" class="tw-h-full" />

      <el-scrollbar v-show="callRecordVisible">
        <el-collapse v-model="resultCollapse" class="result-list">
          <!--分类说明-->
          <el-collapse-item class="result-item intention" :name="resultItemNameEnum.INTENTION">
            <template #title>
              <div class="result-header">
                <span class="result-title">分类说明</span>
                <span class="result-subtitle">
                  <template v-if="categoryInfo.intentionClass">
                    （{{ categoryInfo.intentionClass }}）
                  </template>
                  <template v-else>
                    （无）
                  </template>
                </span>
              </div>
            </template>

            <div
              v-show="categoryInfo.intentionClass||categoryInfo.cycleCount||categoryInfo.sayCount"
              class="result-content"
            >
              <div class="intention-content">
                <template v-if="categoryInfo.intentionClass">
                  <span>{{ categoryInfo.intentionClass }}类</span>
                  <span v-show="categoryInfo.intentionName">（{{ categoryInfo.intentionName }}）</span>
                </template>
                <template v-else>
                  （暂无分类）
                </template>
              </div>
              <div>
                <span class="count-text count-title">交互轮次：</span>
                <span class="count-text count-content">
                  <template v-if="categoryInfo.cycleCount===null">
                    （无）
                  </template>
                  <template v-else>
                    {{ categoryInfo.cycleCount }}
                  </template>
                </span>
              </div>
              <div>
                <span class="count-text count-title">说话次数：</span>
                <span class="count-text count-content">
                  <template v-if="categoryInfo.sayCount===null">
                    （无）
                  </template>
                  <template v-else>
                    {{ categoryInfo.sayCount }}
                  </template>
                </span>
              </div>
            </div>
          </el-collapse-item>

          <!--命中规则-->
          <el-collapse-item class="result-item rule" :name="resultItemNameEnum.RULE">
            <template #title>
              <div class="result-header">
                <span class="result-title">命中规则</span>
                <span class="result-subtitle">
                  （{{ hitRuleList.length ?? 0 }}）
                </span>
              </div>
            </template>

            <div v-show="hitRuleList.length" class="result-content">
              <el-tag v-for="ruleItem in hitRuleList" :key="ruleItem.ruleName" class="result-tag-item">
                {{ ruleItem.ruleName }}
              </el-tag>
            </div>
          </el-collapse-item>

          <!--语义回顾-->
          <el-collapse-item class="result-item" :name="resultItemNameEnum.SEMANTIC">
            <template #title>
              <div class="result-header">
                <span class="result-title">语义回顾</span>
                <span class="result-subtitle">
                  （{{ semanticList.length ?? 0 }}）
                </span>
              </div>
            </template>

            <div v-show="semanticList.length" class="result-content">
              <el-tag v-for="(semanticItem, semanticIndex) in semanticList" :key="semanticIndex" class="result-tag-item">
                {{ semanticItem }}
              </el-tag>
            </div>

          </el-collapse-item>

          <!--标签-->
          <el-collapse-item class="result-item" :name="resultItemNameEnum.TAG">
            <template #title>
              <div class="result-header">
                <span class="result-title">标签</span>
                <span class="result-subtitle">
                  （{{ tagList.length ?? 0 }}）
                </span>
              </div>
            </template>

            <div v-show="tagList.length" class="result-content">
              <el-tag v-for="(tagItem, tagIndex) in tagList" :key="tagIndex" class="result-tag-item">
                {{ tagItem }}
              </el-tag>
            </div>
          </el-collapse-item>

          <!--命中事件-->
          <el-collapse-item class="result-item" :name="resultItemNameEnum.EVENT">
            <template #title>
              <div class="result-header">
                <span class="result-title">命中事件</span>
                <span class="result-subtitle">
                  （{{ hitEventList.length ?? 0 }}）
                </span>
              </div>
            </template>

            <div v-show="hitEventList.length" class="result-content">
              <el-tag v-for="(hitEventItem, hitEventIndex) in hitEventList" :key="hitEventIndex" class="result-tag-item">
                {{ hitEventItem }}
              </el-tag>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onActivated, onDeactivated, onMounted, onUnmounted, ref, watch } from 'vue'
import { formatDuration, Throttle, updateCurrentPageList } from '@/utils/utils'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import {
  InterruptTypeEnum,
  ScriptTextTrainAiInterruptEnum,
  ScriptTrainStatusEnum,
  ScriptTrainTypeEnum,
  TrainDialogItem,
  TrainHistoryItem
} from '@/type/speech-craft'
import { scriptTrainAudioModel, scriptTrainCommonModel, scriptTrainTextModel } from '@/api/speech-craft'
import { ElMessage } from 'element-plus'
import { useScriptStore } from '@/store/script'
import { RecordDialogueData, TaskCallRecordItem } from '@/type/task'
import { onBeforeRouteLeave } from 'vue-router'
import { useScriptTrainStore } from '@/store/script-train'
import { parseRule } from './RuleContent'
import { IntentionType } from '@/type/IntentionType'
import CallRecordDialogBox from '@/components/record/CallRecordDialogBox.vue'
import PaginationBox from '@/components/PaginationBox.vue'
import { endAudioTrain, startAudioTrain, submitAudioTrainEvent } from './AudioTrain'
import {
  aiInterruptStatus,
  aiInterruptVisible,
  currentAiInterruptSecond,
  endTextTrain,
  lastAiIndex,
  loadingSending,
  loadingSilence,
  msgEditorHint,
  msgVal,
  msgValMaxLength,
  multipleCorpusFinished,
  onChangeAiInterruptEnabled,
  onClickHangup,
  onClickReset,
  onClickSendMsg,
  onClickSilence,
  onClickStopAiInterrupt,
  onMsgInputKeyEvent,
  onUpdateClearAudio,
  startTextTrain,
  submitTextTrainEvent,
} from './TextTrain'
import { corpusConfigMap, corpusIdSet, updateCorpusConfig } from './common'

// ---------------------------------------- 通用 开始 ----------------------------------------

const scriptStore = useScriptStore()
const scriptTrainStore = useScriptTrainStore()

// dayjs计时插件
dayjs.extend(duration)

// 语音训练时，有专门的接口定时调用并更新对话详情
// 文字训练时，每次接口返回的结果就是AI侧的说话内容，将此结果追加到对话详情里

// 训练开关，正在切换
const loadingSwitch = ref(false)
// 训练开关，切换节流锁
const throttleSwitch = new Throttle(loadingSwitch, 500)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 训练开关 开始 ----------------------------------------

// 训练时长文本，单位秒
const trainSecondText = computed(() => {
  // 有一个空格占位，保证有无数据都有高度，不然HTML里空字符串就没有高度了
  const empty = '0秒'
  if (!scriptTrainStore.trainSecond) {
    return empty
  } else {
    return formatDuration(scriptTrainStore.trainSecond) ?? empty
  }
})

/**
 * 点击语音训练开关
 */
const onClickAudioTrainSwitch = async () => {
  // 节流锁上锁
  if (throttleSwitch.check()) {
    return
  }
  throttleSwitch.lock()

  if (scriptTrainStore.status !== ScriptTrainStatusEnum.IDLE) {
    // 结束训练
    endAudioTrain()
  } else {
    // 重置对话详情
    resetDialogData()
    // 重置通话记录
    resetCallRecord()
    // 开始训练
    await startAudioTrain()
  }

  // 节流锁解锁
  throttleSwitch.unlock()
}
/**
 * 点击文字训练开关
 */
const onClickTextTrainSwitch = () => {
  // 节流锁上锁
  if (throttleSwitch.check()) {
    return
  }
  throttleSwitch.lock()

  if (scriptTrainStore.status !== ScriptTrainStatusEnum.IDLE) {
    // 结束训练
    endTextTrain()
  } else {
    // 重置对话详情
    resetDialogData()
    // 重置通话记录
    resetCallRecord()
    // 开始训练
    startTextTrain()
  }

  // 节流锁解锁
  throttleSwitch.unlock()
}

// ---------------------------------------- 训练开关 结束 ----------------------------------------

// ---------------------------------------- 训练历史 开始 ----------------------------------------

// 训练历史，是否显示
const historyVisible = computed(() => {
  return scriptTrainStore.status === ScriptTrainStatusEnum.IDLE
})

// 训练历史列表，全部
const historyAllList = ref<TrainHistoryItem[]>([])
// 训练历史列表，当前页码
const historyCurrentList = ref<TrainHistoryItem[]>([])

// 训练历史，正在加载
const loadingHistory = ref(false)
// 训练历史，加载节流锁
const throttleLoadingHistory = new Throttle(loadingHistory)

// 当前页码
const historyPageNum = ref(1)
// 每页大小
const historyPageSize = ref(10)
// 可选的页码大小
const historyPageSizeList = [5, 10, 20, 50]
// 总数
const historyTotalCount = computed(() => {
  return historyAllList?.value?.length ?? 0
})

// 当前训练历史信息
const currentHistoryItem = ref<TrainHistoryItem>({})

/**
 * 训练历史，更新列表，全部
 */
const updateHistoryAllList = async () => {
  // 节流锁上锁
  if (throttleLoadingHistory.check()) {
    return
  }
  throttleLoadingHistory.lock()

  try {
    // 请求接口
    const res = <TrainHistoryItem[]>await scriptTrainCommonModel.getTrainHistory(scriptStore.scriptStringId)
    // 更新全部列表
    // 按时间倒序排序
    historyAllList.value = (res?.length ? res : []).sort((a: TrainHistoryItem, b: TrainHistoryItem) => {
      return dayjs(a.trainTime).isAfter(b.trainTime) ? -1 : 1
    })
    // 更新当前页码的历史列表
    updateHistoryCurrentList(historyPageNum.value, historyPageSize.value)
  } catch (e) {
    console.error('无法获取训练历史', e)
    ElMessage.error('无法获取训练历史')
  } finally {
    // 节流锁解锁
    throttleLoadingHistory.unlock()
  }
}
/**
 * 训练历史，更新列表，当前页码
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateHistoryCurrentList = (p?: number, s?: number) => {
  // 页码
  p && (historyPageNum.value = p)
  // 分页大小
  s && (historyPageSize.value = s)
  // 更新当前页
  historyCurrentList.value = updateCurrentPageList(historyAllList.value, historyPageNum.value, historyPageSize.value)
}
/**
 * 训练历史，分页条，组件事件，更新全部列表
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const onHistoryPaginationSearch = (p?: number, s?: number) => {
  // console.log('onHistoryPaginationSearch', p, s)
  // 更新训练历史
  updateHistoryAllList()
  // 更新对话详情
  updateDialogData()
  // 更新通话记录
  updateCallRecord()
}
/**
 * 训练历史，分页条，组件事件，更新指定页码（当前页码）
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const onHistoryPaginationUpdate = (p?: number, s?: number) => {
  // console.log('onHistoryPaginationUpdate', p, s)
  updateHistoryCurrentList(p, s)
}
/**
 * 点击历史单项
 * @param historyItem
 */
const onClickHistoryItem = (historyItem: TrainHistoryItem) => {
  // 有接口正在加载，不做响应
  if (loadingSwitch.value || loadingHistory.value || loadingDialogData.value || loadingCallRecord.value) {
    ElMessage.warning('正在加载，请稍候')
    return
  }
  // 更新索引
  currentHistoryItem.value = historyItem
  scriptTrainStore.recordId = historyItem.recordId ?? ''
  // 更新对话详情
  updateDialogData()
  // 更新通话记录
  updateCallRecord()
}
/**
 * 训练历史选中第一个并更新信息
 */
const selectFirstHistoryItem = () => {
  // 如果列表有数据，就选中第一条记录，并更新对话详情和通话记录
  if (historyAllList.value.length) {
    // 默认显示第一个
    currentHistoryItem.value = historyAllList.value.at(0) ?? {}
    // 重置页码
    historyPageNum.value = 1
    // 更新通话记录ID
    scriptTrainStore.recordId = currentHistoryItem.value.recordId ?? ''
    // 更新当前页
    updateHistoryCurrentList(historyPageNum.value, historyPageSize.value)
  }
}

// ---------------------------------------- 训练历史 结束 ----------------------------------------

// ---------------------------------------- 事件 开始 ----------------------------------------

// 事件动作 是否显示
const eventVisible = computed(() => {
  return scriptTrainStore.status === ScriptTrainStatusEnum.IN_CALL
})

/**
 * 点击提交事件按钮
 */
const onClickSubmitEvent = () => {
  // 按训练类型区分
  if (scriptTrainStore.trainType === ScriptTrainTypeEnum.AUDIO) {
    submitAudioTrainEvent()
  } else if (scriptTrainStore.trainType === ScriptTrainTypeEnum.TEXT) {
    submitTextTrainEvent()
  }
}

// ---------------------------------------- 事件 结束 ----------------------------------------

// ---------------------------------------- 对话详情 开始 ----------------------------------------

// 对话详情，正在加载
const loadingDialogData = ref(false)
// 对话详情，加载节流锁
const throttleDialogData = new Throttle(loadingDialogData)
// 对话详情，加载动画，是否显示
const loadingDialogDataVisible = computed(() => {
  return scriptTrainStore.status !== ScriptTrainStatusEnum.IN_CALL && (loadingHistory.value || loadingDialogData.value)
    || scriptTrainStore.status === ScriptTrainStatusEnum.IDLE && loadingHistory.value
})
// 查询对话详情的时间间隔
const dialogDataDuration: number = 1000
// 是否自动播放最后一条AI语音
const autoPlayLastAiAudio = computed(() => {
  // 文字训练通话中
  return scriptTrainStore.trainType === ScriptTrainTypeEnum.TEXT
    && scriptTrainStore.status === ScriptTrainStatusEnum.IN_CALL
})

/**
 * 获取对话详情
 */
const updateDialogData = async () => {
  if (!scriptTrainStore.recordId) {
    return
  }

  // 节流锁上锁
  if (throttleDialogData.check()) {
    return
  }
  throttleDialogData.lock()

  try {
    // 确定训练类型
    let type: ScriptTrainTypeEnum = ScriptTrainTypeEnum.NULL
    if (scriptTrainStore.status === ScriptTrainStatusEnum.IDLE) {
      // 空闲中，根据当前训练历史信息的训练类型判断
      type = currentHistoryItem.value.isStringTrain
        ? ScriptTrainTypeEnum.TEXT
        : ScriptTrainTypeEnum.AUDIO
    } else if (scriptTrainStore.status === ScriptTrainStatusEnum.IN_CALL) {
      // 通话中，根据当前正在进行的训练类型判断
      type = scriptTrainStore.trainType
    } else {
      // 其他状态下，不应该触发更新对话详情
      console.warn('更新对话详情时，训练状态不正确', scriptTrainStore.status)
    }

    // 请求接口
    let res: TrainDialogItem[]
    if (type === ScriptTrainTypeEnum.TEXT) {
      // 文字训练
      res = <TrainDialogItem[]>await scriptTrainTextModel.getDialogData(scriptTrainStore.recordId)
    } else if (type === ScriptTrainTypeEnum.AUDIO) {
      // 语音训练
      res = <TrainDialogItem[]>await scriptTrainAudioModel.getDialogData(scriptTrainStore.recordId)
    } else {
      console.warn('更新对话详情时，训练类型不正确', type)
      res = []
    }

    // 将数据结构转换成对话详情组件能识别的数据结构
    scriptTrainStore.dialogData = (res?.length ? res : []).map((item: TrainDialogItem) => {
      const newItem: RecordDialogueData = { ...item } as RecordDialogueData
      newItem.content = newItem.content.replace(/\s/g, '')
      newItem.urls = [item.audioFileUrl ?? '']
      newItem.dialogTime = dayjs(item?.dialogTime ?? '').format('YYYY-MM-DD HH:mm:ss') ?? ''
      return newItem
    })
    await updateCorpusConfig(res?.length ? res : [])

    // 如果是语音训练通话中，开启定时查询，其他情况都不会自动查询
    if (scriptTrainStore.trainType === ScriptTrainTypeEnum.AUDIO && scriptTrainStore.needUpdate.dialogRepeat) {
      setTimeout(updateDialogData, dialogDataDuration ?? 1000)
    }
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleDialogData.unlock()
  }
}
/**
 * 重置对话详情
 */
const resetDialogData = () => {
  scriptTrainStore.dialogData = []
  corpusIdSet.clear()
  corpusConfigMap.clear()
}

// ---------------------------------------- 对话详情 结束 ----------------------------------------

// ---------------------------------------- 消息框 开始 ----------------------------------------

// 消息框DOM
const msgEditorRef = ref()
// 处于文字训练通话中时，聚焦消息框
watch(() => scriptTrainStore.status, (val) => {
  if (scriptTrainStore.trainType === ScriptTrainTypeEnum.TEXT) {
    if (val === ScriptTrainStatusEnum.IN_CALL) {
      // 聚焦消息框
      msgEditorRef.value?.focus()
    }
  }
})

// ---------------------------------------- 消息框 结束 ----------------------------------------

// ---------------------------------------- 通话记录 开始 ----------------------------------------

// 通话记录，接口数据
const callRecord = ref<TaskCallRecordItem>({})
// 通话记录，正在加载
const loadingCallRecord = ref(false)
// 通话记录，加载节流锁
const throttleCallRecord = new Throttle(loadingCallRecord)
// 通话记录，是否显示
const callRecordVisible = computed(() => {
  return scriptTrainStore.status === ScriptTrainStatusEnum.IDLE && !loadingHistory.value
    || scriptTrainStore.status === ScriptTrainStatusEnum.IN_CALL
    || scriptTrainStore.status === ScriptTrainStatusEnum.HANG_UP
})

// 分类说明，默认值
const categoryInfoDefault = () => {
  return {
    intentionClass: null,
    intentionName: null,
    cycleCount: null,
    sayCount: null,
  }
}
// 分类说明
const categoryInfo = ref<{
  // 意向分类标签
  intentionClass: string | null,
  // 意向分类名称
  intentionName: string | null,
  // 交互轮次
  cycleCount: number | string | null,
  // 说话次数
  sayCount: number | string | null,
}>(categoryInfoDefault())
// 意向分类全部列表
const intentionOptions = ref<IntentionType[]>([])

// 命中规则，数据类型
interface HitRuleType {
  order?: number
  ruleName?: string
}

// 命中规则列表
const hitRuleList = ref<HitRuleType[]>([])

// 语义列表
const semanticList = computed(() => {
  // 汇总当前通话记录的所有对话切片的命中语义
  const list: string[] = scriptTrainStore.dialogData.map((item: RecordDialogueData) => {
    return item?.hitSemantic?.trim() ?? ''
  })
  // 去重
  const set = new Set<string>(list)
  // 移除空白
  set.delete('')
  // 换成列表
  return Array.from(set)
})

// 标签列表
const tagList = ref<string[]>([])

// 命中事件列表
const hitEventList = ref<string[]>([])

// 折叠面板，枚举值
enum resultItemNameEnum {
  INTENTION = 'INTENTION',
  RULE = 'RULE',
  SEMANTIC = 'SEMANTIC',
  TAG = 'TAG',
  EVENT = 'EVENT',
}

// 折叠面板，展开的项目
const resultCollapse = ref<resultItemNameEnum[]>([
  resultItemNameEnum.INTENTION,
  resultItemNameEnum.RULE,
  resultItemNameEnum.SEMANTIC,
  resultItemNameEnum.TAG,
  resultItemNameEnum.EVENT,
])

/**
 * 更新通话记录
 * @param {number} delayMs 延迟毫秒
 */
const updateCallRecord = async (delayMs: number = 0) => {
  if (!scriptTrainStore.recordId) {
    return
  }

  // 节流锁上锁
  if (throttleCallRecord.check()) {
    return
  }
  throttleCallRecord.lock()

  try {
    if (delayMs) {
      // 延迟更新，刚结束训练时，接口能正在生成通话记录，需要点时间
      await new Promise((resolve) => {
        setTimeout(resolve, delayMs)
      })
    }

    // 请求接口
    const res = <TaskCallRecordItem>await scriptTrainCommonModel.getCallRecord(scriptTrainStore.recordId)

    // 更新数据
    callRecord.value = res ?? {}

    // ---------- 分类说明 开始 ----------

    // 重置数据
    categoryInfo.value = {
      intentionClass: null,
      intentionName: null,
      cycleCount: null,
      sayCount: null,
    }
    categoryInfo.value.intentionClass = res?.intentionClass ?? null
    categoryInfo.value.cycleCount = res?.cycleCount ?? null
    categoryInfo.value.sayCount = res?.sayCount ?? null

    try {
      // 更新意向分类全部列表
      intentionOptions.value = <IntentionType[]>await scriptStore.getIntentionLevelOptions()
      const intentionInfo = intentionOptions.value.find((option: IntentionType) => {
        return option?.intentionType === res?.intentionClass
      })
      categoryInfo.value.intentionName = intentionInfo?.intentionName ?? null
    } catch {
    }

    // ---------- 分类说明 结束 ----------

    // ---------- 规则列表 开始 ----------

    hitRuleList.value = res?.hitAdvanceIds ? await parseRule(res?.hitAdvanceIds) : []

    // ---------- 规则列表 结束 ----------

    // ---------- 语义列表 开始 ----------

    // 使用computed自动根据通话记录里所有命中语义进行汇总
    // semanticList.value

    // ---------- 语义列表 结束 ----------

    // ---------- 标签列表 开始 ----------

    tagList.value = !!res?.intentionLabels ? res?.intentionLabels.split(',') : []

    // ---------- 标签列表 结束 ----------

    // ---------- 事件列表 开始 ----------

    // 汇总当前通话记录的所有对话切片的触发分支
    hitEventList.value = []
    scriptTrainStore.dialogData.forEach((item: RecordDialogueData) => {
      if (item?.hitBranch?.includes('查询分支:')) {
        hitEventList.value.push(item?.hitBranch.replace('查询分支:', ''))
      }
    })

    // ---------- 事件列表 结束 ----------

  } catch (e) {
    console.error('更新通话记录时出错', e)
    ElMessage.error('更新通话记录时出错')
  } finally {
    // 节流锁解锁
    throttleCallRecord.unlock()
  }
}
/**
 * 重置通话记录
 */
const resetCallRecord = () => {
  callRecord.value = {}
  categoryInfo.value = categoryInfoDefault()
  hitRuleList.value = []
  // 语义使用computed自动更新，当前通话记录没有则没有
  // semanticList.value = []
  tagList.value = []
  hitEventList.value = []
}

// ---------------------------------------- 通话记录 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

/**
 * 监听列表是否需要更新
 */
const cancelWatchNeedUpdate = watch(scriptTrainStore.needUpdate, async (val) => {
  // 训练历史
  if (val.history) {
    await updateHistoryAllList()
    // 选中第一条训练历史
    selectFirstHistoryItem()
    await nextTick()
  }

  // 对话详情
  if (val.dialog || val.dialogRepeat) {
    updateDialogData().then(() => {
    }).catch(() => {
    })
  }

  // 通话记录
  if (val.record) {
    updateCallRecord(1500).then(() => {
    }).catch(() => {
    })
  }

  scriptTrainStore.needUpdate.history = false
  scriptTrainStore.needUpdate.dialog = false
  scriptTrainStore.needUpdate.record = false
}, { deep: true })
/**
 * 监听列表是否需要清空
 */
const cancelWatchNeedClear = watch(scriptTrainStore.needClear, (val) => {
  // 训练历史
  if (val.history) {
    historyAllList.value = []
    historyCurrentList.value = []
  }

  // 对话详情
  if (val.dialog) {
    scriptTrainStore.dialogData = []
  }

  // 通话记录
  if (val.record) {
    callRecord.value = {}
  }

  scriptTrainStore.needClear.history = false
  scriptTrainStore.needClear.dialog = false
  scriptTrainStore.needClear.record = false
}, { deep: true })
/**
 * 进入组件后
 */
const afterEnterComponent = async () => {
  console.log('afterEnterComponent')

  // window.addEventListener('beforeunload', beforeLeaveComponent)

  ElMessage({
    type: 'warning',
    message: `初次制作或修改音频，请至少等待5分钟后，再进行话术训练（请在全部音频上传后再话术训练）`,
    duration: 3000
  })

  // 更新训练历史
  await updateHistoryAllList()
  // 选中第一条训练历史
  selectFirstHistoryItem()
  await nextTick()
  // 更新通话记录
  updateCallRecord().then(() => {
  }).catch(() => {
  })
  // 更新对话详情
  updateDialogData().then(() => {
  }).catch(() => {
  })
  // 更新可提交的事件列表
  await scriptTrainStore.updateEventAllList()

  // 重置状态
  scriptTrainStore.resetStatus()

  // 测试
  // scriptTrainStore.trainType = ScriptTrainTypeEnum.TEXT
  // scriptTrainStore.status = ScriptTrainStatusEnum.CHECK_SCRIPT
  // scriptTrainStore.status = ScriptTrainStatusEnum.IN_CALL
  console.log('**********', '话术训练', '进入组件', 'scriptStore.id', scriptStore.id, 'scriptStore.scriptStringId', scriptStore.scriptStringId)
}
/**
 * 离开组件前
 */
const beforeLeaveComponent = () => {
  console.log('beforeLeaveComponent')

  // window.removeEventListener('beforeunload', beforeLeaveComponent)
  // 移除监听
  cancelWatchNeedUpdate()
  cancelWatchNeedClear()

  // 结束训练
  if (scriptTrainStore.trainType === ScriptTrainTypeEnum.AUDIO) {
    endAudioTrain()
  } else if (scriptTrainStore.trainType === ScriptTrainTypeEnum.TEXT) {
    endTextTrain()
  }

  // 重置全部数据
  scriptTrainStore.resetStatus()
  scriptTrainStore.dialogData = []
  scriptTrainStore.recordId = ''
}
onMounted(() => {
  console.log('onMounted')
})
onActivated(() => {
  console.log('onActivated')
  afterEnterComponent()
})
onDeactivated(() => {
  console.log('onDeactivated')
  beforeLeaveComponent()
})
onUnmounted(() => {
  console.log('onUnmounted')
})
onBeforeRouteLeave(() => {
  console.log('onBeforeRouteLeave')
  // 只要发生路由变化，就停止训练，因为路由变化时，已经退出这个话术了
  beforeLeaveComponent()
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
/* 模块容器 */
.training-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: stretch;
  height: calc(100vh - 180px);
  background-color: #fff;
}
/* 区块 */
.block {
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  width: auto;
  height: 100%;
  /* 左侧区块 */
  &.left-block {
    flex: 1;
    min-width: 310px;
    background-color: #fff;
  }
  /* 中间区块 */
  &.center-block {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 3;
    min-width: 400px;
    border-right: 1px solid #EAF0FE;
    border-left: 1px solid #EAF0FE;
    background-color: #f5f5f5;
  }
  /* 右侧区块 */
  &.right-block {
    flex: 1;
    min-width: 250px;
    background-color: #fff;
  }
}
/* 启停按钮 容器 */
.switch-box {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
}
/* 启停按钮 */
.switch {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  flex: none;
  overflow: hidden;
  width: 100px;
  height: 100px;
  margin: 10px 6px 0;
  border-radius: 50%;
  cursor: pointer;
  user-select: none;
  /* 按钮图标 */
  .button-icon-normal {
    display: block;
  }
  .button-icon-hover {
    display: none;
  }
  &:hover {
    .button-icon-normal {
      display: none;
    }
    .button-icon-hover {
      display: block;
    }
  }
  /* 开关文本 */
  .switch-text {
    font-size: 13px;
    line-height: 20px;
  }
  /* 计时文本 */
  .stopwatch-text {
    font-size: 12px;
    line-height: 20px;
    opacity: 0.8;
  }
  /* 未开始训练时 */
  &.disabled {
    border: 1px solid transparent;
    background: linear-gradient(0deg, #0167FF 0%, #019FFF 100%);
    .switch-text {
      color: #fff;
    }
    .stopwatch-text {
      color: #fff;
    }
    &:hover {
      border: 1px solid #C8C9CC;
      background: #fff;
      .switch-text {
        color: #626366;
      }
      .stopwatch-text {
        color: #626366;
      }
    }
  }
  /* 进行训练时 */
  &.enabled {
    border: 1px solid #C8C9CC;
    background: #fff;
    .switch-text {
      color: #626366;
    }
    .stopwatch-text {
      color: #626366;
    }
    &:hover {
      border: 1px solid transparent;
      background: linear-gradient(0deg, #0167FF 0%, #019FFF 100%);
      .switch-text {
        color: #fff;
      }
      .stopwatch-text {
        color: #fff;
      }
    }
  }
}
/* 开关文本 */
.switch-text {
  font-size: 16px;
}
/* 左侧区块主体 */
.left-block-main {
  display: flex;
  flex-direction: column;
  padding: 0 8px;
}
/* 左侧区块主体标题 */
.left-block-title {
  margin-top: 12px;
  color: #313233;
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
  text-align: left;
  .list-account {
    font-weight: 400;
    color: #969799;
  }
}
/* 历史列表 */
.history-list {
  height: 280px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
}
/* 历史单项 */
.history-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  gap: 4px;
  padding: 4px;
  background-color: transparent;
  color: #969799;
  font-size: 13px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  user-select: none;
  &.current,
  &:hover,
  &:active {
    border-radius: 4px;
    background-color: #EAF0FE;
    color: #165DFF;
  }
  .history-item-datetime {
    flex: none;
    width: 130px;
    text-align: left;
  }
  .history-item-type {
    flex: none;
    text-align: left;
  }
  .history-item-account {
    flex: auto;
    max-width: 40%;
    text-align: right;
  }
}
/* 分页条 */
.history-pagination {
  margin-top: 14px;
}
/* 事件列表 */
.event-list {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
/* 事件单项 */
.event-item {
  width: 100%;
  padding: 2px 0;
}
/* 事件单项内容 */
.event-item-content {
  width: 100%;
  color: #626366;
  font-size: 13px;
  line-height: 20px;
}
/* 事件提交按钮 */
.event-submit {
  margin: 14px auto 14px 0;
}
/* 来电显示容器 */
/* 对话详情容器 */
.incoming,
.dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  &.center {
    justify-content: center;
  }
}
.incoming {
  justify-content: center;
  background-color: #fff;
  color: var(--primary-blue-color);
  font-size: 14px;
}
.dialog {
  justify-content: flex-start;
}
/* 消息框 */
.msg-editor {
  flex: none;
  width: 100%;
  padding: 8px;
  border-top: 1px solid #EAF0FE;
  background: #fff;
  /* 顶部 */
  .msg-editor-header {
    margin-bottom: 8px;
    text-align: left;
  }
  /* 顶部每行 */
  .msg-editor-header-row {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  /* 标题 */
  .msg-editor-title {
    color: #313233;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
  }
  /* 副标题 */
  .msg-editor-subtitle {
    color: #969799;
    font-size: 13px;
    font-weight: 400;
    line-height: 22px;
  }
  .msg-editor-main {
    position: relative;
    width: 100%;
  }
  .msg-editor-button-box {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
  }
}
/* 结果列表 */
.result-list {
  padding: 0;
}
/* 结果单项 */
.result-item {
  padding: 0;
}
/* 结果顶部 */
.result-header {
  padding: 8px 16px;
  text-align: left;
  line-height: 1;
}
/* 折叠面板 顶部 */
:deep(.el-collapse-item__header) {
  position: relative;
  flex-wrap: wrap;
  height: auto;
  padding: 0;
  text-align: left;
}
/* 折叠面板 箭头按钮 */
:deep(.el-collapse-item__arrow) {
  position: absolute;
  top: 12px;
  right: 0;
}
/* 结果标题 */
.result-title {
  color: #313233;
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
}
/* 结果副标题 */
.result-subtitle {
  color: #969799;
  font-size: 12px;
  line-height: 20px;
}
/* 结果内容 */
.result-content {
  padding: 0 16px 8px;
  text-align: left;
}
:deep(.el-collapse-item__content) {
  padding: 0;
  line-height: 1;
}
/* 通话记录 分类说明 */
.intention {
  .intention-content {
    color: #165DFF;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
  }
  .count-text {
    font-size: 13px;
    line-height: 20px;
    &.count-title {
      color: #626366;
    }
    &.count-content {
      color: #313233;
    }
  }
}
/* 通话记录 高级规则 */
.rule {
  :deep(.el-collapse-item__header) {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    flex-wrap: nowrap;
  }
}
/* 通话记录 高级规则 */
.result-rule-item {
  padding: 4px;
  border-radius: 4px;
  background-color: #F0F2F5;
  margin-top: 4px;
  &:first-child {
    margin-top: 0;
  }
  /* 规则标题 */
  .rule-title {
    color: #313233;
    font-size: 13px;
    font-weight: 600;
    line-height: 20px;
    text-align: center;
  }
  /* 规则内容 */
  .rule-content {
    color: #626366;
    font-size: 13px;
    font-weight: 400;
    line-height: 20px;
    margin-top: 4px;
    &:first-child {
      margin-top: 0;
    }
  }
}
/* 通话记录 标签 */
.result-tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
