<template>
  <div v-if="props.id && props.id > 0" v-loading="loadingSetting" class="submodule-detail tw-p-[16px]">

    <!-- AK管理（仅查看） -->
    <div class="tw-w-full tw-flex tw-items-center">
      <span class="form-block-title">AK管理</span>
    </div>
    <div class="tw-h-[32px] tw-flex tw-items-center">
      <span class="tw-shrink-0 tw-w-[90px] tw-text-right">商户ID：</span>
      <span class="info">{{ merchantStore.currentMerchant?.id || '-' }}</span>
    </div>
    <div class="tw-h-[32px] tw-flex tw-items-center">
      <span class="tw-shrink-0 tw-w-[90px] tw-text-right">SALT：</span>
      <span class="info">{{ form.salt || '-' }}</span>
    </div>
    <div class="tw-h-[32px] tw-flex tw-items-center">
      <span class="tw-shrink-0 tw-w-[90px] tw-text-right">AES：</span>
      <span class="info">{{ form.aes || '-' }}</span>
    </div>

    <!--分割线-->
    <hr class="tw-mb-[12px] tw-mt-[6px]">

    <!-- 数据管理、回调管理、IP配置，可编辑 -->
    <!-- 表单 -->
    <el-form
      ref="editRef"
      v-loading="loadingSetting"
      :disabled="!editStatus"
      class="tw-grow"
      label-width="120px"
      :rules="rules"
      :model="form"
    >
      <div class="form-block-title tw-mb-[16px]">接口管理</div>
      <div class="form-block-title">数据统计</div>
      <el-form-item label="查询范围：" prop="dataStatisticRange">
        <el-checkbox-group v-model="form.dataStatisticRange" class="tw-ml-1  tw-grid tw-grid-cols-4 tw-text-[13px] tw-w-[440px]">
          <el-checkbox v-for="item in scopeOption" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <div class="form-block-title">任务回调</div>
      <el-form-item label="回调地址：" prop="taskCallbackUrl">
        <el-input v-if="editStatus" v-model.trim="form.taskCallbackUrl" type="textarea" clearable placeholder="请填写回调地址" maxlength="200" show-word-limit></el-input>
        <span v-else class="info">{{ form.taskCallbackUrl || '-' }}</span>
      </el-form-item>
      <div class="form-block-title">通话回调</div>
      <el-form-item label="回调范围：" prop="callBackRange">
        <el-checkbox-group v-model="form.callBackRange" class="tw-ml-1 tw-grid tw-grid-cols-4 tw-text-[13px] tw-w-[440px]">
          <el-checkbox v-for="item in scopeOption" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="回调状态：" prop="callbackStatusConfig">
        <el-checkbox-group v-model="form.callbackStatusConfig" class="tw-ml-1 tw-grid tw-grid-cols-4 tw-text-[13px] tw-w-[440px]">
          <el-checkbox v-for="item in callStatusOption" :disabled="item.value===CallStatusEnum3['呼叫成功']" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="回调字段：" prop="callbackFieldConfig">
        <el-checkbox class="tw-ml-1 tw-w-[110px]" disabled :model-value="true">手机号</el-checkbox>
        <el-checkbox-group v-model="form.callbackFieldConfig" class="tw-grid tw-grid-cols-3 tw-text-[13px] tw-w-[330px]">
          <el-checkbox v-for="item in fieldOption" :disabled="item.name==='手机号'" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="快速回调接口：" prop="callSmsCallbackUrl">
        <el-input v-if="editStatus" v-model.trim="form.callSmsCallbackUrl" type="textarea" clearable placeholder="请填写快速回调接口" maxlength="200" show-word-limit></el-input>
        <span v-else class="info">{{ form.callSmsCallbackUrl || '-' }}</span>
      </el-form-item>
     
      <el-form-item label="话后回调接口(新)：" prop="callDataCallbackUrl">
        <el-input v-if="editStatus" v-model.trim="form.callDataCallbackUrl" type="textarea" clearable placeholder="请填写通话回调接口（新）" maxlength="200" show-word-limit></el-input>
        <span v-else class="info">{{ form.callDataCallbackUrl || '-' }}</span>
      </el-form-item>
      <el-form-item label="文本补推接口：" prop="callUpdateCallbackUrl">
        <el-input v-if="editStatus" v-model.trim="form.callUpdateCallbackUrl" type="textarea" clearable placeholder="请填写文本补推接口" maxlength="200" show-word-limit></el-input>
        <span v-else class="info">{{ form.callUpdateCallbackUrl || '-' }}</span>
      </el-form-item>
      <div class="form-block-title">其他回调</div>
      <el-form-item label="M短信接口：" prop="callMCallbackUrl">
        <el-input v-if="editStatus" v-model.trim="form.callMCallbackUrl" type="textarea" clearable placeholder="请填写M短信接口" maxlength="200" show-word-limit></el-input>
        <span v-else class="info">{{ form.callMCallbackUrl || '-' }}</span>
      </el-form-item>
      <el-form-item label="通话回调接口(旧)：" prop="callBackUrl">
        <el-input v-if="editStatus" v-model.trim="form.callBackUrl" type="textarea" clearable placeholder="请填写通话回调接口（旧）" maxlength="200" show-word-limit></el-input>
        <span v-else class="info">{{ form.callBackUrl || '-' }}</span>
      </el-form-item>

      <div class="form-block-title">短信回调</div>
      <el-form-item label="回调字段：" prop="callbackFieldConfig">
        <el-checkbox class="tw-ml-1 tw-w-[110px]" disabled :model-value="true">手机号</el-checkbox>
        <el-checkbox-group v-model="form.callbackFieldConfig" class="tw-grid tw-grid-cols-3 tw-text-[13px] tw-w-[330px]">
          <el-checkbox v-for="item in fieldOption" :disabled="item.name==='手机号'" :key="item.smsValue" :label="item.smsValue">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="回调地址：" prop="smsCallbackUrl">
        <el-input v-if="editStatus" v-model.trim="form.smsCallbackUrl" type="textarea" clearable placeholder="请填写回调地址" maxlength="200" show-word-limit></el-input>
        <span v-else class="info">{{ form.smsCallbackUrl || '-' }}</span>
      </el-form-item>
      <el-form-item label="上行短信回调地址：" prop="smsMoCallbackUrl">
        <el-input v-if="editStatus" v-model.trim="form.smsMoCallbackUrl" type="textarea" clearable placeholder="请填写上行短信回调地址" maxlength="200" show-word-limit></el-input>
        <span v-else class="info">{{ form.smsMoCallbackUrl || '-' }}</span>
      </el-form-item>
      <div class="form-block-title">IP配置</div>
      <el-form-item label="IP地址：" prop="whiteIps">
        <InputListBox
          v-model:value="form.whiteIps"
          placeholder="IP地址"
          canCopy
          clearable
          copyName="ips"
          :disabled="!editStatus"
          @update:value="editRef?.validate()"
        />
      </el-form-item>
    </el-form>
    <!-- 按钮 -->
    <div v-if="!!merchantStore.currentAccount?.accountEnable" class="bottom-btn">
      <el-button v-if="!editStatus" :loading="loadingSetting" type="primary" class="tw-ml-[16px]" @click="goEdit">编辑</el-button>
      <div v-else>
        <el-button :loading="loadingSetting" type="primary" class="tw-ml-[16px]" @click="save">保存</el-button>
        <el-button class="tw-ml-[16px]" @click="cancel">取消</el-button>
      </div>
    </div>
  </div>
  <el-empty v-else></el-empty>
</template>

<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, watch, h } from 'vue';
import { enum2Options, pickAttrFromObj, Throttle, } from '@/utils/utils'
import Confirm from '@/components/message-box'
import { MerchantAccountInfo, MerchantSetting, MerchantSettingOrigin, RecordTypeEnum } from '@/type/merchant'
import { merchantModel } from '@/api/merchant'
import type { FormInstance, } from 'element-plus'
import { ElMessage } from 'element-plus'
import to from 'await-to-js'
import { ipReg, urlReg } from '@/utils/constant'
import InputListBox from '@/components/InputListBox.vue'
import { useMerchantStore } from '@/store/merchant'
import { trace } from '@/utils/trace';
import { CallStatusEnum3, CallStatusEnum } from '@/type/task'

const props = defineProps<{
  // 账号ID
  id: number,
}>()

const merchantStore = useMerchantStore()

const loadingSetting = ref(false)

/** 表单规则开始 */
const validateCallBackRange = (rule: any, value: string[], callback: any) => {
  const hasUrl = [
    form.callBackUrl,
    form.callSmsCallbackUrl,
    form.callDataCallbackUrl
  ].some(item => !!item)

  if (!form.callBackRange?.length && hasUrl) {
    return callback(new Error('回调范围和回调接口必须同时填写'))
  } else if(!!form.callBackRange?.length && !hasUrl) {
    return callback(new Error('回调范围必须同时填写'))
  }
  callback()
}
const validateCallBackUrl = (callback: any, type: number) => {
  const urlArr = [
    form.callBackUrl,
    form.callSmsCallbackUrl,
    form.callMCallbackUrl,
    form.callDataCallbackUrl,
    form.callUpdateCallbackUrl,
  ]
  const urlStr = urlArr[type] || ''
  if (urlStr && urlStr?.length > 200) {
    return callback(new Error('回调接口不得超过200个字符'))
  } else if (urlStr && !urlReg.test(urlStr)) {
    return callback(new Error('回调接口不符合要求'))
  } else {
    return callback()
  }
}
const validateOtherCallBackUrl = (rule: any, value: any, callback: any) => {
  if (value && value?.length > 200) {
    return callback(new Error('回调地址不得超过200个字符'))
  } else if (value && !urlReg.test(value)) {
    return callback(new Error('回调地址不符合要求'))
  } else {
    return callback()
  }
}
const validateIps = (rule: any, value: string[], callback: any) => {
  if (form.whiteIps && form.whiteIps?.length > 0) {
    let msg = ''
    form.whiteIps?.forEach(item => {
      if (item && !ipReg.test(item)) {
        msg = !msg ? ('输入IP有误:' + item) : (msg + ',' + item)
      }
    })
    if (msg) {
      return callback(new Error(msg))
    }
    callback()
  }
  callback()
}
const rules = {
  dataStatisticRange: [
    { required: true, message: '请选择查询范围', trigger: 'blur' },
  ],
  taskCallbackUrl: [
    { validator: validateOtherCallBackUrl, trigger: 'blur' },
  ],
  callBackRange: [
    { validator: validateCallBackRange, trigger: 'blur' },
  ],
  callBackUrl: [
    { validator: (rule: any, value: any, callback: any) => validateCallBackUrl(callback, 0), trigger: 'blur' },
  ],
  callSmsCallbackUrl: [
    { validator: (rule: any, value: any, callback: any) => validateCallBackUrl(callback, 1), trigger: 'blur' },
  ],
  callMCallbackUrl: [
    { validator: (rule: any, value: any, callback: any) => validateCallBackUrl(callback, 2), trigger: 'blur' },
  ],
  callDataCallbackUrl: [
    { validator: (rule: any, value: any, callback: any) => validateCallBackUrl(callback, 3), trigger: 'blur' },
  ],
  callUpdateCallbackUrl: [
    { validator: (rule: any, value: any, callback: any) => validateCallBackUrl(callback, 4), trigger: 'blur' },
  ],
  smsCallbackUrl: [
    { validator: validateOtherCallBackUrl, trigger: 'blur' },
  ],
  smsMoCallbackUrl: [
    { validator: validateOtherCallBackUrl, trigger: 'blur' },
  ],
  whiteIps: [
    { validator: validateIps, trigger: ['blur', 'change'] },
  ],
  callbackStatusConfig: [
    { required: true, message: '请选择回调状态', trigger: 'change' },
  ],
  // callbackFieldConfig: [
  //   { required: true, message: '请选择回调字段', trigger: 'change' },
  // ],
}
/** 表单规则结束 */

/** 表单提交 */
const form = reactive<MerchantSetting>(new MerchantSettingOrigin(props.id))
const editRef = ref<FormInstance | null>(null)

const editStatus = ref(false) // 是否处于编辑状态
/** 保存，需二次确认 */
const save = async () => {
  if (!!loadingSetting.value) return
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      // 节流锁上锁
      loadingSetting.value = true
      const [err] = await to(Confirm({
        text: `修改配置将立即生效，请确认配置内容符合业务要求`,
        type: 'warning',
        title: `请注意`,
        confirmText: '确认并保存',
      }))
      if (err) {
        loadingSetting.value = false
        return
      }
      const params = pickAttrFromObj(form, [
        'accountId',
        'dataStatisticRange',
        'taskCallbackUrl',
        'callBackRange',
        'callBackUrl',
        'callSmsCallbackUrl',
        'callMCallbackUrl',
        'callDataCallbackUrl',
        'callUpdateCallbackUrl',
        'smsCallbackUrl',
        'smsMoCallbackUrl',
        'callbackFieldConfig',
        'callbackStatusConfig',
        'whiteIps'
      ])
      params.callbackStatusConfig = params.callbackStatusConfig?.join(',').split(',') || []
      trace({
        page: `商户管理-修改账号设置`,
        params,
      })
      const [err2] = await to(merchantModel.saveAccountSetting(params))
      loadingSetting.value = false
      if (!err2) {
        ElMessage.success('操作成功')
        cancel()
      }
    }
  })
}
/** 取消，刷新数据并调整为非编辑状态 */
const cancel = async () => {
  editStatus.value = false
  findSettingInfo()
}
const goEdit = () => {
  editStatus.value = true
  editRef.value?.clearValidate()
  form.callbackFieldConfig = form.callbackFieldConfig?.length ? form.callbackFieldConfig : ['phone', 'smsPhone']
  // 回调状态：默认值，如果初始为空值，需要修改为全选；
  form.callbackStatusConfig = form.callbackStatusConfig?.length ? form.callbackStatusConfig : callStatusOption.map(item => item.value)
  // 回调状态：呼叫成功必选，如查询数据未选择，需要加上；
  if (!form.callbackStatusConfig.includes(CallStatusEnum3['呼叫成功'])) {
    form.callbackStatusConfig.push(CallStatusEnum3['呼叫成功'])
  }
}

/** 表单内部选项及增删改查 开始 */

// 通话记录范围
const scopeOption = enum2Options(RecordTypeEnum)
const callStatusOption = enum2Options(CallStatusEnum3)
const fieldOption = [
  { name: '姓名', value: 'name', smsValue: 'smsFullName' },
  { name: '公司', value: 'company', smsValue: 'smsCompany' },
  { name: '备注', value: 'remarks', smsValue: 'smsRemarks' },
]
/** 表单内部 结束 */

/** 查询账号设置信息 */
const findSettingInfo = async () => {
  // 节流锁上锁
  if (!!loadingSetting.value) return
  loadingSetting.value = true
  if (props.id && props.id > 0) {
    const [err, data] = await to(merchantModel.getAccountSetting({
      accountId: props.id!
    }))
    !err && Object.assign(form, new MerchantSettingOrigin(props.id), data || new MerchantSettingOrigin(props.id))
    // 特殊逻辑：针对呼叫状态未接通
    // 在读取数据时，只要有未接统一个状态，就勾选上未接通
    // 在传参时，未接统需要传未接通、频率限制、平台黑名单等
    form.callbackStatusConfig = form.callbackStatusConfig && form.callbackStatusConfig.flatMap(item => {
      if (callStatusOption.find(v => v.value === item)) return item
      return item === CallStatusEnum['未接通'] ? CallStatusEnum3['未接通'] : []
    }) || []

    form.callBackRange = form.callBackRange || []
    form.dataStatisticRange = form.dataStatisticRange || []
    form.whiteIps = form.whiteIps || []
  }
  setTimeout(() => {
    editRef.value?.clearValidate()
  }, 100)
  // 节流锁解锁
  loadingSetting.value = false
}

/** 生命周期函数 */
onMounted(() => {
  findSettingInfo()
})
/** 监听器 */
watch(() => props.id, () => {
  editStatus.value = false
  findSettingInfo()
})
watch(() => merchantStore.currentAccount, async (val: MerchantAccountInfo | null | undefined) => {
  if (typeof val?.id === 'number') {
    findSettingInfo()
  }
}, { immediate: true, deep: true })
</script>

<style scoped lang="postcss">
.info {
  color: var(--primary-black-color-400);
  font-size: 13px
}
.el-button + .el-button {
  margin-left: 4px;
}
:deep(.el-checkbox__label) {
  font-size: 13px;
}
.submodule-detail {
  position: relative;
  padding-bottom: 80px;
}
.bottom-btn {
  position: fixed;
  right: 8px;
  bottom: 0;
  width: calc(100% - 450px);
  display: flex;
  justify-content: flex-end;
  z-index: 9;
  padding: 12px;
  background-color: #fff;
  border-top: 1px solid var(--primary-black-color-200);
}
</style>
