<template>
  <!--模块标题-->
  <HeaderBox :title="title" />

  <!--模块主体-->
  <div v-loading="loadingMerchantAllList" class="module-main module-main-scroll tw-min-w-[1080px]">

    <!--左半部分-->
    <div class="aside-list-box">
      <!--创建商户按钮-->
      <el-button
        v-if="route.name !== 'VolcanoManager'"
        size="default"
        type="primary"
        class="tw-w-[226px] tw-mx-[16px] tw-box-border"
        @click="clickCreateMerchantButton"
      >
        创建商户
      </el-button>
      <!--搜索框-->
      <InputSelectBox
        class="tw-mt-[16px] tw-mx-[16px] tw-mb-[12px]"
        style="width:226px"
        v-model:value="merchantSearchForm[merchantSearchType]"
        v-model:selectVal="merchantSearchType"
        :selectList="merchantSearchTypeOption"
        placeholder="Enter触发搜索"
        @search="handleMerchantSearchChange"
      />

      <!--商户状态切换菜单-->
      <TabsBox
        class="merchant-tab"
        :active="activeTabIndex"
        :tabList="merchantStatusList"
        @update:active="handleSelectTab"
      />

      <!--符合搜索条件的列表-->
      <el-scrollbar ref="merchantListRef" class="tw-px-[16px]">
        <!--列表里的单项-->
        <div
          v-for="merchantItem in merchantCurrentList"
          :key="merchantItem.id??-1"
          ref="merchantItemRef"
          class="aside-normal-item"
          :class="[{'aside-active-item':merchantItem.id===currentMerchant.id}]"
        >
          <!--内层容器-->
          <div class="aside-normal-item-inner" @click="clickMerchantItem(merchantItem)">
            <!--行容器-->
            <div class="tw-flex tw-items-center">
              <!--编号-->
              <div class="tw-flex-1 tw-text-left tw-break-words tw-truncate tw-text-[var(--primary-black-color-400)]">
                {{ merchantItem.tenantNo }}
              </div>
              <!--联系人姓名-->
              <div class="tw-grow-0 tw-shrink-0 tw-w-[60px] tw-text-right tw-break-words tw-truncate tw-text-[var(--primary-black-color-500)]">
                {{ merchantItem.contactName }}
              </div>
            </div>

            <!--商户名称-->
            <div class="tw-text-left tw-text-[14px] tw-font-bold tw-break-words">
              {{ merchantItem.tenantName }}
            </div>

            <!--日期时间-->
            <div class="tw-text-left tw-text-[13px] tw-break-words tw-text-[var(--primary-black-color-400)]">
              创建时间：{{ dayjs(merchantItem.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>

          <!--按钮-->
          <div class="aside-item-btn-box">
            <el-tooltip content="编辑商户" placement="right" :show-after="500">
              <span class="tw-cursor-pointer" @click="clickEditMerchant(merchantItem)">
                <el-icon :size="16" color="#fff">
                  <SvgIcon name="edit" color="#fff"></SvgIcon>
                </el-icon>
              </span>
            </el-tooltip>
          </div>
        </div>
        <!--空数据提示-->
        <div v-show="!merchantCurrentList.length" class="tw-bg-white">
          <el-empty />
        </div>
      </el-scrollbar>

      <!--分页条-->
      <PaginationBox
        class="tw-border-t-[1px] tw-border-t-[#ebeef5] tw-mt-1"
        :pageSize="merchantPageSize"
        :currentPage="merchantPageNum"
        :total="merchantPageTotal"
        :mini="true"
        @search="updateMerchantAllList(currentMerchant)"
        @update="updateMerchantCurrentList"
      />
    </div>

    <!--右半部分-->
    <el-scrollbar class="tw-grow" wrap-class="tw-p-[16px]">

      <!--空数据提示-->
      <el-empty v-show="!merchantFilterList.length" class="tw-bg-white"/>

      <!--有商户信息-->
      <div v-show="merchantFilterList.length" class="tw-flex tw-flex-col tw-w-full tw-h-full">

        <!--商户信息模块-->
        <div>
          <!--空数据-->
          <template v-if="currentMerchant.id===undefined||currentMerchant.id===null">
            <div class="card-box tw-flex-col">
              <el-empty />
            </div>
          </template>

          <!--有数据-->
          <template v-else>
            <!--标题和内容-->
            <div class="card-box tw-flex-col">
              <!--标题-->
              <div class="tw-flex tw-flex-row tw-items-center tw-w-full">
                <p class="tw-text-[14px] tw-font-bold tw-text-[#313233]">
                  {{ currentMerchant.tenantName }}
                </p>
                <p class="tw-ml-[8px] tw-text-[13px] tw-font-normal tw-text-[#626366]">
                  简称：{{ currentMerchant.tenantShortName }}
                </p>
                <p class="tw-ml-auto tw-text-[13px] tw-font-normal tw-text-[#626366]">
                  <span class="info-title">编号：</span>
                  <span class="info-content">{{ currentMerchant.tenantNo }}</span>
                </p>
              </div>

              <!--更多信息-->
              <div
                v-show="isInfoExpand"
                class="tw-flex tw-flex-row tw-justify-start tw-flex-wrap tw-w-full tw-mt-[8px]"
              >
                <div class="tw-flex tw-flex-row tw-justify-start tw-flex-wrap tw-w-full tw-mt-[8px]">
                  <p class="info-item">
                    <span class="info-title">联系人：</span>
                    <span class="info-content">{{ currentMerchant.contactName }}</span>
                  </p>
                  <p class="info-item">
                    <span class="info-title">联系电话：</span>
                    <span class="info-content">{{ currentMerchant.contactPhone }}</span>
                  </p>
                  <p class="info-item">
                    <span class="info-title">邮箱：</span>
                    <span class="info-content">{{ currentMerchant.contactMail }}</span>
                  </p>
                  <p class="info-item">
                    <span class="info-title">联系地址：</span>
                    <span class="info-content">{{ currentMerchant.contactAddress }}</span>
                  </p>
                </div>

                <p class="info-item tw-flex tw-mt-[8px]">
                  <span class="info-title">运营备注：</span>
                  <span v-if="(currentMerchant.remark ?? '') === ''" class="info-content">暂未填写</span>
                  <span v-else class="info-content-dark">{{ currentMerchant.remark }}</span>
                </p>
              </div>
            </div>

            <!--展开收起箭头-->
            <div class="tw-flex tw-justify-center">
              <div class="trapezoid" @click="isInfoExpand=!isInfoExpand">
                <el-icon v-if="isInfoExpand" :size="13">
                  <ArrowUpBold />
                </el-icon>
                <el-icon v-else :size="13">
                  <ArrowDownBold />
                </el-icon>
              </div>
            </div>
          </template>
        </div>

        <!--账号模块-->
        <div v-loading="loadingAccountList" element-loading-background="transparent" class="tw-mt-[8px]">
          <!--模块顶部-->
          <div class="account-header">
            <!--模块标题-->
            <div class="account-title">账号</div>
            <!--搜索框-->
            <div class="tw-flex tw-justify-end tw-items-center tw-gap-x-[8px] tw-grow">
              <el-input
                v-model.trim="accountSearchForm.name"
                placeholder="搜索账号/联系人/业务名称"
                style="width:250px"
                clearable
                :icon="Search"
                @clear="handleAccountSearchChange"
                @input="handleAccountSearchChange"
              >
                <template #suffix>
                  <el-icon :size="16">
                    <SvgIcon name="search" />
                  </el-icon>
                </template>
              </el-input>
              <el-radio-group v-model="accountSearchForm.accountEnable" @change="handleAccountSearchChange">
                <el-radio-button :label="true">启用中</el-radio-button>
                <el-radio-button :label="false">停用中</el-radio-button>
              </el-radio-group>
              <el-divider direction="vertical" class="tw-text-[22px] tw-m-0"/>
              <!--添加账号按钮-->
              <el-button type="primary" plain class="tw-w-[80px]" @click="clickCreateAccount">
                <el-icon :size="13"><SvgIcon name="add1" color="inherit"/></el-icon>
                <span>新增账号</span>
              </el-button>
            </div>
          </div>

          <div class="tw-flex tw-flex-row tw-justify-center tw-items-center tw-min-h-[101px]">
            <!--账号列表为空-->
            <template v-if="!accountList?.length">
              <div class="tw-h-full tw-p-[16px] tw-text-center">
                暂无数据
              </div>
            </template>

            <!--账号列表不为空-->
            <template v-if="accountList?.length">
              <!--往前滚动按钮-->
              <el-icon :size="32" color="#fff" class="account-list-button" @click="onClickAccountListButton('left')">
                <SvgIcon name="arrow-prev" />
              </el-icon>

              <!--账号列表-->
              <el-scrollbar
                ref="accountListRef"
                class="account-list"
                view-class="account-list-inner"
                always
                @wheel.prevent="onWheelAccountList"
              >
                <!--账号单项-->
                <div
                  v-for="(accountItem) in accountList"
                  :key="accountItem.id??-1"
                  class="account-item"
                  :class="[{'active':accountItem.id===currentAccount.id}]"
                  @click="clickAccountItem(accountItem)"
                >
                  <!--左侧信息-->
                  <div class="account-item-left">
                    <!--联系人姓名-->
                    <div class="account-item-name">
                      {{ (accountItem.isForEncryptionPhones ? '【加密】' : accountItem.isForEncryptionAgain ? '【解密】' : '') + accountItem.name }}
                    </div>
                    <!--账号名称-->
                    <div class="account-item-account">
                      {{ accountItem.account }}
                    </div>
                  </div>
                  <!--右侧按钮-->
                  <div class="account-item-right">
                    <!--启用状态-->
                    <div v-if="accountItem.accountEnable" class="account-item-status enabled">
                      启用中
                    </div>
                    <!--停用状态-->
                    <div v-else class="account-item-status disabled">
                      停用中
                    </div>

                    <!--下拉按钮-->
                    <el-dropdown
                      placement="bottom-end"
                      class="account-item-more"
                      trigger="click"
                      :show-timeout="0"
                      :hide-timeout="0"
                    >
                      <el-icon :size="20" color="#F0F2F5" class="account-item-more-button">
                        <SvgIcon name="more" />
                      </el-icon>

                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="clickResetPassword(accountItem)">
                            重置密码
                          </el-dropdown-item>
                          <el-dropdown-item @click="clickEditCurrentAccount(accountItem)">
                            编辑
                          </el-dropdown-item>
                          <!--删除账号按钮，仅开发时调试使用-->
                          <el-dropdown-item v-if="false" @click="clickDeleteCurrentAccount(accountItem)">
                            删除
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </el-scrollbar>

              <!--往后滚动按钮-->
              <el-icon :size="32" color="#fff" class="account-list-button" @click="onClickAccountListButton('right')">
                <SvgIcon name="arrow-next" />
              </el-icon>
            </template>
          </div>
        </div>

        <!--列表模块-->
        <div v-loading="loadingTab" class="tw-overflow-hidden tw-flex-col tw-mt-[16px] tw-bg-white tw-rounded-[4px]">
          <!--标签卡切换-->
          <TabsBox class="list-tab" :active="listTabName" :tabList="tabList" @update:active="handleListTabChange" />

          <!--未选中商户账号-->
          <el-empty v-if="!accountList.length || currentAccount.id === -1">
            <template #description>
              <span class="info-title">请选中商户账号后再操作</span>
            </template>
          </el-empty>
          
          <!--已选中商户账号-->
          <template v-else>
            <!--线路列表标签卡-->
            <Line v-if="listTabName===LIST_TAB_NAME.LINE"></Line>

            <!--话术列表标签卡-->
            <SpeechScript v-else-if="listTabName===LIST_TAB_NAME.SCRIPT"></SpeechScript>

            <!--项目列表标签卡-->
            <Project v-else-if="listTabName===LIST_TAB_NAME.PROJECT" />

            <!--短信列表标签卡-->
            <SmsTemplate v-else-if="listTabName===LIST_TAB_NAME.SMS" />

            <!--变量列表标签卡-->
            <SmsVariable v-else-if="listTabName===LIST_TAB_NAME.VARIABLE" />

            <!--任务模板标签卡-->
            <TaskTemplate v-else-if="listTabName===LIST_TAB_NAME.TASK" v-model:autoShowDialog="autoShowDialogInTaskTemplate" :readonly="!currentAccount?.accountEnable" :groupId="currentAccount.groupId!" />

            <!--账号设置标签卡-->
            <AccountSetting v-else-if="listTabName===LIST_TAB_NAME.SETTING" :id="currentAccount.id!" />
          </template>
        </div>

      </div>

    </el-scrollbar>

  </div>

  <!--编辑商户弹窗-->
  <DialogMerchant
    :id="editingMerchantId"
    :visible="dialogMerchantVisible"
    :content="editingMerchantItem"
    @close="closeDialogMerchant"
    @update="handleDialogMerchantUpdate"
  />

  <!--编辑账户弹窗-->
  <DialogAccount
    :id="editingAccountId"
    :visible="dialogAccountVisible"
    :content="editingAccountItem"
    :merchantId="currentMerchant.id??-1"
    :accountList="accountAllList"
    @close="closeDialogAccount"
    @update="onDialogAccountUpdate"
  />
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { computed, defineAsyncComponent, nextTick, onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ArrowDownBold, ArrowUpBold, Plus, Search, } from '@element-plus/icons-vue'
import {
  MerchantAccountInfo,
  MerchantInfo,
  MerchantStatusEnum,
} from '@/type/merchant'
import { merchantModel } from '@/api/merchant'
import { getListFirstItem, Throttle, updateCurrentPageList } from '@/utils/utils'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { merchantUserModel } from '@/api/user'
import { onBeforeRouteLeave, useRoute } from 'vue-router'
import { useMerchantStore } from '@/store/merchant'
import Confirm from '@/components/message-box'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from '@/store/user'
import InputSelectBox from '@/components/InputSelectBox.vue'
import to from 'await-to-js'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const TabsBox = defineAsyncComponent(() => import('@/components/TabsBox.vue'))
const PaginationBox = defineAsyncComponent(() => import('@/components/PaginationBox.vue'))
const SvgIcon = defineAsyncComponent(() => import('@/components/SvgIcon.vue'))
const DialogMerchant = defineAsyncComponent(() => import('./DialogMerchant.vue'))
const DialogAccount = defineAsyncComponent(() => import('./DialogAccount.vue'))
const Line = defineAsyncComponent(() => import('./Line/Index.vue'))
const SpeechScript = defineAsyncComponent(() => import('./SpeechScript/Index.vue'))
const Project = defineAsyncComponent(() => import('./Project/Index.vue'))
const SmsTemplate = defineAsyncComponent(() => import('./SmsTemplate/Index.vue'))
const SmsVariable = defineAsyncComponent(() => import('./SmsVariable/Index.vue'))
const TaskTemplate = defineAsyncComponent(() => import('@/views/merchant/ai-report/TemplateManagement/Index.vue'))
const AccountSetting = defineAsyncComponent(() => import('./AccountSetting/Index.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const route = useRoute()

const userStore = useUserStore()
const merchantStore = useMerchantStore()
const { currentMerchant, currentAccount } = storeToRefs(merchantStore)

const title = computed(() => route.name === 'VolcanoManager' ? '火山商户管理' : '商户管理')
/**
 * 点击商户/切换商户/初始化商户时，更新商户主体信息（右侧部分）
 * 1. 重置右侧主账号列表搜索信息、当前选中的主账号信息
 * 2. 更新主账号列表
 */
const updateInfoInMerchant = () => {
  // 重置信息
  accountAllList.value = []
  accountList.value = []
  accountSearchForm.name = ''
  accountSearchForm.accountEnable = true
  editingAccountItem.value = { id: -1 }
  editingAccountId.value = -1

  // 如果有账号被选中
  if (typeof currentMerchant.value.id === 'number') {
    // 更新账户列表
    updateAccountAllList(true)
    // 更新列表模块
    updateListTab()
  }
}

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 商户列表 开始 ----------------------------------------

// 当前激活的标签卡索引
const merchantStatusList: string[] = ['启用', '禁用']
const activeTabIndex = ref<string>(merchantStatusList[0])

// 商户列表DOM
const merchantListRef = ref()

// 商户列表，全部，正在加载
const loadingMerchantAllList = ref<boolean>(false)
// 商户列表，全部，加载节流锁
const throttleMerchantAllList = new Throttle(loadingMerchantAllList)

// 商户列表，搜索结果，两种状态列表之一的子集
const merchantFilterList = ref<MerchantInfo[]>([])
// 商户列表，总数
const merchantPageTotal = computed(() => {
  return merchantFilterList.value.length ?? 0
})
// 商户列表，搜索框文本
const merchantSearchForm = reactive({
  tenantName: '',
  account: '',
  accountName: '',
})
const merchantSearchTypeOption: { name: string, value: 'tenantName' | 'account' | 'accountName' }[] = [
  { name: '账号', value: 'account' },
  { name: '联系人', value: 'accountName' },
  { name: '商户名称', value: 'tenantName' },
]
const merchantSearchType = ref<'tenantName' | 'account' | 'accountName'>(merchantSearchTypeOption[0].value)

// 商户列表，当前页，搜索结果的子集
const merchantCurrentList = ref<MerchantInfo[]>([])
// 商户列表，当前页，页码
const merchantPageNum = ref(1)
// 商户列表，当前页，每页大小
const merchantPageSize = ref(100)

// 正在编辑的商户信息
const editingMerchantItem = ref<MerchantInfo>({ id: -1 })
// 正在编辑的商户ID，新建默认为-1
const editingMerchantId = ref<number>(-1)

// 显示编辑商户弹窗（新建和编辑）
const dialogMerchantVisible = ref<boolean>(false)

// 任务模板tab--自动弹窗创建弹窗
const autoShowDialogInTaskTemplate = ref<boolean>(false)

/**
 * 更新全部商户列表
 * @param {MerchantInfo} current 当前选中信息
 */
const updateMerchantAllList = async (current?: MerchantInfo) => {
  // 节流锁上锁
  if (throttleMerchantAllList.check()) {
    return
  }
  throttleMerchantAllList.lock()
  

  // 请求接口
  const [err, data] = await to(
    route.name === 'VolcanoManager' ?
    merchantModel.getVolcanoMerchantList() : merchantModel.getMerchantListByCondition({
      [merchantSearchType.value]: merchantSearchForm[merchantSearchType.value],
    })
  ) as [Error | null, MerchantInfo[] | null];

  // 按当前标签卡选择的状态筛选列表(过滤状态)
  // 先清空列表
  merchantFilterList.value = [];
  // 根据状态过滤
  (data || []).forEach(item => {
    if (item.status === MerchantStatusEnum[activeTabIndex.value as keyof typeof MerchantStatusEnum]) {
      merchantFilterList.value.push(item)
    }
  })

  // 按创建时间倒序排序
  merchantFilterList.value.sort((a, b) => dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1) || []

  // 更新商户列表(分页)
  updateMerchantCurrentList(merchantPageNum.value, merchantPageSize.value)

  // 更新当前商户信息，如未找到，定位至第一个
  const index = merchantCurrentList.value?.findIndex((item: MerchantInfo) => item.id === current?.id)
  currentMerchant.value = JSON.parse(JSON.stringify(merchantCurrentList.value[index > -1 ? index : 0] || {id: undefined}))
  
  // 滚动至目标元素
  scrollMerchantList()

  // 更新右侧模块
  updateInfoInMerchant()

  throttleMerchantAllList.unlock()
}

/**
 * 商户列表搜索框文本变化时
 */
const handleMerchantSearchChange = () => {
  // 搜索
  updateMerchantAllList()

  // 页码从1开始
  merchantPageNum.value = 1
  // 更新当前页码列表
  updateMerchantCurrentList()
}

/**
 * 切换标签卡
 * @param {string} val 选中菜单项的索引名
 */
const handleSelectTab = (val: string) => {
  // 如果标签卡索引相同则不执行后续动作
  if (val === activeTabIndex.value) {
    return
  }
  // 如果节流锁生效，不响应
  if (throttleMerchantAllList.check()) {
    return
  }
  // 切换标签卡
  activeTabIndex.value = val
  // 更新商户全部列表
  updateMerchantAllList()
}

/**
 * 商户列表滚动到当前商户的位置
 */
const scrollMerchantList = async () => {
  // 等DOM更新后再继续操作，防止滚动列表的高度不正确
  await nextTick()
  // 滚动到当前选中的位置
  if (merchantListRef.value) {
    const currentMerchantDom = merchantListRef.value?.$el.querySelector('.aside-active-item') as HTMLElement | null
    const currentMerchantOffsetTop = Math.max((currentMerchantDom?.offsetTop ?? 0) - 8, 0)
    merchantListRef.value.setScrollTop(currentMerchantOffsetTop)
  }
}
/**
 * 更新商户列表，当前页
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateMerchantCurrentList = (p?: number, s?: number) => {
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined, null, 0等逻辑假值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    merchantPageNum.value = p!
    merchantPageSize.value = s!
  }
  // 更新当前页码
  merchantCurrentList.value = updateCurrentPageList(merchantFilterList.value, merchantPageNum.value, merchantPageSize.value)
}
/**
 * 点击商户单项
 * @param {MerchantInfo} current 当前选中商户信息
 */
const clickMerchantItem = (current: MerchantInfo) => {
  // 点击当前商户可以刷新

  // 更新当前商户信息
  currentMerchant.value = current
  // 更新主体信息
  updateInfoInMerchant()
}
/**
 * 点击创建商户按钮
 */
const clickCreateMerchantButton = () => {
  // 重置正在编辑的商户信息
  editingMerchantItem.value = { id: -1 }
  editingMerchantId.value = -1
  // 显示弹窗
  dialogMerchantVisible.value = true
}

// ---------------------------------------- 商户列表 结束 ----------------------------------------

// ---------------------------------------- 商户信息 开始 ----------------------------------------

// 折叠面板，商户更多信息
const isInfoExpand = ref(false)

/**
 * 点击编辑商户按钮
 */
const clickEditMerchant = (current: MerchantInfo) => {
  // 将当前商户信息传给正在编辑的商户信息
  editingMerchantItem.value = { ...current }
  editingMerchantId.value = current.id ?? -1

  // 显示弹窗
  dialogMerchantVisible.value = true
}
/**
 * 关闭编辑商户弹窗
 */
const closeDialogMerchant = () => {
  // 关闭弹窗
  dialogMerchantVisible.value = false
}
/**
 * 商户弹窗通知父组件更新
 * @param {MerchantInfo} merchant 当前编辑商户信息
 */
const handleDialogMerchantUpdate = (merchant: MerchantInfo) => {
  // 更新后，当前编辑的商户会被选中（不管原来编辑的是否是选中的）
  updateMerchantAllList(merchant)
}

// ---------------------------------------- 商户信息 结束 ----------------------------------------

// ---------------------------------------- 账号列表 开始 ----------------------------------------

// 正在加载账号列表
const loadingAccountList = ref<boolean>(false)
// 节流锁
const throttleAccountList = new Throttle(loadingAccountList)

// 账户列表，全部
const accountAllList = ref<MerchantAccountInfo[]>([])
// 账户列表，搜索结果，是全部的子集，用于页面展示
const accountList = ref<MerchantAccountInfo[]>([])
// 账号搜索条件
const accountSearchForm = reactive<{
  name: string,
  accountEnable: boolean
}>({
  name: '',
  accountEnable: true
})

// 正在编辑的账号信息
const editingAccountItem = ref<MerchantAccountInfo>({ id: -1 })
// 正在编辑的账号ID，创建时默认为-1
const editingAccountId = ref<number>(-1)

// 显示编辑账号弹窗（新建和编辑）
const dialogAccountVisible = ref<boolean>(false)

// 账号列表DOM
const accountListRef = ref()


/**
 * @description: 更新账号列表
 * @param {string} isFirstLoad - 如果是首次加载/商户列表搜索，则需要根据'account'按照账号搜索，'name'按照名称搜索
 */
const updateAccountAllList = async (isFirstLoad: boolean = false) => {
  // 节流锁上锁
  if (throttleAccountList.check()) {
    return
  }
  throttleAccountList.lock()

  try {
    // 处理参数
    const params = {
      tenantId: currentMerchant.value.id ?? -1
    }

    // 请求接口
    const res = <MerchantAccountInfo[]>await merchantUserModel.getAccountList(params)

    // 更新列表
    accountAllList.value = Array.isArray(res) ? res : []
    // 按创建时间倒序排序
    accountAllList.value = accountAllList.value.sort((a, b) => {
      return dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1
    })
    // 如果是商户列表搜索条件刷新了，则需要判断是否根据主账号名称和联系人搜索，从而更新currentAccount
    if (isFirstLoad && merchantSearchType.value === 'account' && merchantSearchForm.account) {
      currentAccount.value = accountAllList.value?.find((item: MerchantAccountInfo) => {
        return merchantSearchType.value === 'account' && item.account?.includes(merchantSearchForm.account)
      }) || {id: -1}
    } else if (isFirstLoad && merchantSearchType.value === 'accountName' && merchantSearchForm.accountName) {
      currentAccount.value = accountAllList.value?.find((item: MerchantAccountInfo) => {
        return merchantSearchType.value === 'accountName' && item.name?.includes(merchantSearchForm.accountName)
      }) || {id: -1}
    }
    // 更新账号列表
    updateAccountList()
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleAccountList.unlock()
  }
}
/**
 * 搜索账号
 */
const searchAccount = () => {
  // 从列表里搜索含有搜索框文本的话术，筛选出来
  // 文字模糊匹配账号、联系人、联系电话；状态匹配状态
  accountList.value = accountAllList.value?.filter((item: MerchantAccountInfo) => {
    const str = accountSearchForm.name.trim() || ''
    const matchAccount = item?.account?.includes(str)
    const matchName = item?.name?.includes(str)
    const matchPhone = item?.phone?.includes(str)
    return (!str ||matchAccount || matchName || matchPhone) && item.accountEnable === accountSearchForm.accountEnable
  }) || []
}
/**
 * 账号搜索框文本变化时
 */
const handleAccountSearchChange = () => {
  // 搜索账号
  updateAccountList()
}

/**
 * 更新账号列表，部分数据，搜索结果
 * 寻找上一个选中的账号，找不到则选中第一个
 */
const updateAccountList = () => {
  // 搜索账号
  searchAccount()

  // 尝试在当前列表找到选中
  const itemIndex = currentAccount.value?.id && accountList.value.findIndex((item: MerchantAccountInfo) => {
    return item.id === currentAccount.value?.id
  }) || -1

  if (!currentAccount.value || itemIndex === -1) {
    // 如果无历史选中或者未找到历史选中，将列表第一个信息展示出来
    const resultItem = <MerchantAccountInfo>getListFirstItem(accountList.value, { id: -1 })
    currentAccount.value = JSON.parse(JSON.stringify(resultItem))
  } else {
    // 更新当前历史选中信息
    currentAccount.value = accountList.value[itemIndex] || accountList.value[0] || { id: -1 }
  }

  // 将选中账号滚动到列表可见范围内
  scrollToCurrentAccount()

  // 更新账号相关数据
  updateAccountInfo()
}
/**
 * 点击账号单项
 * @param {MerchantAccountInfo} current 点击的账号信息
 * @param {number} index 点击的账号索引
 */
const clickAccountItem = (current: MerchantAccountInfo) => {
  // // 如果点击的是当前已选中账号，不响应
  // if (current?.id === currentAccount.value?.id) {
  //   return
  // }
  // 节流锁
  if (loadingTab.value) {
    ElMessage.warning('正在加载，请稍候')
    return
  }

  // 更新当前账号信息
  currentAccount.value = current
  // 更新账号相关数据
  updateAccountInfo()
}
/**
 * 更新账号相关数据
 */
const updateAccountInfo = () => {
  // 更新当前标签卡
  updateListTab()
}
/**
 * 点击新增账号按钮
 */
const clickCreateAccount = () => {
  // 将默认值传入弹窗表单
  editingAccountItem.value = { id: -1 }
  editingAccountId.value = -1
  // 显示编辑账号弹窗
  dialogAccountVisible.value = true
}
/**
 * 点击编辑当前账号按钮
 */
const clickEditCurrentAccount = (current: MerchantAccountInfo) => {
  // 将信息传入弹窗表单
  editingAccountItem.value = { ...current }
  editingAccountId.value = current.id ?? -1
  // 显示编辑账号弹窗
  dialogAccountVisible.value = true
}
/**
 * 点击删除当前账号按钮
 */
const clickDeleteCurrentAccount = (current: MerchantAccountInfo) => {
  // 显示确认弹窗
  Confirm({
    text: `您确定要删除当前账号【${current?.account || ''}】？`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(async () => {
    try {
      // 请求接口
      await merchantUserModel.deleteAccount({
        id: current.id ?? -1
      })

      ElMessage({
        message: '删除账号成功',
        duration: 3000,
        type: 'success',
      })
    } catch (e) {
      ElMessage({
        message: '删除账号失败',
        duration: 3000,
        type: 'error',
      })
    } finally {
      // 更新列表
      await updateAccountAllList()
    }
  }).catch(() => {
  })
}
/**
 * 点击重置密码按钮
 */
const clickResetPassword = (current: MerchantAccountInfo) => {
  // 显示确认弹窗
  Confirm({
    text: `您确定要重置【${current?.account || ''}】密码?`,
    type: 'warning',
    title: `重置密码确认`,
    confirmText: '重置'
  }).then(async () => {
    try {
      // 请求接口
      await merchantUserModel.resetAccountPassword({
        userId: current.id ?? -1,
      })

      ElMessage({
        message: '该账号密码重置成功',
        duration: 3000,
        type: 'success',
      })
    } catch (e) {
      ElMessage({
        message: '该账号密码重置失败',
        duration: 3000,
        type: 'error',
      })
    } finally {
      // 更新列表
      await updateAccountAllList()
    }
  }).catch(() => {
  })
}
/**
 * 关闭编辑账号弹窗
 */
const closeDialogAccount = () => {
  dialogAccountVisible.value = false
}
/**
 * 滚动账号列表，偏移值
 * @param {number} deltaLeft 左右滚动距离
 */
const scrollAccountListDelta = (deltaLeft: number = 0) => {
  if (accountListRef.value) {
    const newLeft = (accountListRef.value.wrapRef?.scrollLeft ?? 0) + (deltaLeft ?? 0)
    accountListRef.value.setScrollLeft(newLeft)
  }
}
/**
 * 将选中账号滚动到列表可见范围内
 * @param {number} index 选中账号的索引
 */
const scrollToCurrentAccount = async () => {
  // 等DOM更新后再继续操作，防止滚动列表的高度不正确
  await nextTick()

  if (accountListRef.value) {
    const currentMerchantDom = accountListRef.value?.$el.querySelector('.active') as HTMLElement | null
    const currentMerchantOffsetLeft = Math.max((currentMerchantDom?.offsetLeft ?? 0), 0)
    accountListRef.value.setScrollLeft(currentMerchantOffsetLeft)
  }
}
/**
 * 账号列表 鼠标滚轮事件处理
 * @param {WheelEvent} event 事件数据
 */
const onWheelAccountList = (event: WheelEvent) => {
  // console.log(event?.deltaX, event?.deltaY)
  if (!accountListRef.value) {
    return
  }

  // 鼠标滚轮是deltaY一维变化，deltaX不变化
  // 触摸板双指滑动是deltaX和deltaY二维变化
  const deltaX = event?.deltaX ?? 0
  const deltaY = event?.deltaY ?? 0
  const diff = 50
  // 优先判断触摸板滚动
  if (deltaX > 0) {
    // 触摸板手指往左滑动，列表往左滚动
    scrollAccountListDelta(-diff)
  } else if (deltaX < 0) {
    // 触摸板手指往右滑动，列表往右滚动
    scrollAccountListDelta(diff)
  } else {
    // 不是触摸板，判断鼠标滚轮
    if (deltaY > 0) {
      // 滚轮向下，列表往右滚动
      scrollAccountListDelta(diff)
    } else if (deltaY < 0) {
      // 滚轮向上，列表往左滚动
      scrollAccountListDelta(-diff)
    }
  }
}
/**
 * 账号列表 点击列表滚动按钮
 */
const onClickAccountListButton = (direction: string = 'right') => {
  // 节流锁
  if (loadingAccountList.value || loadingTab.value) {
    ElMessage.warning('正在加载，请稍候')
    return
  }

  // 账号列表DOM不存在
  // 页面展示的账号列表为空（搜索筛选为空）
  if (!accountListRef.value || !accountList.value?.length) {
    return
  }

  // 找到当前选中账户在账户列表中的索引位置
  // 如果找到，则按方向切换；如果没找到，则后续操作不执行，保持不动
  const currentIndex = accountList.value.findIndex((accountItem: MerchantAccountInfo) => {
    return accountItem.id === currentAccount.value.id
  })

  if (currentIndex > -1) {
    // 计算新索引
    let newIndex = currentIndex + (direction === 'right' ? 1 : -1)
    // 防止数组越界
    if (0 <= newIndex && newIndex <= accountList.value.length - 1) {
      // 选中新的账户
      clickAccountItem(accountList.value[newIndex])
    }
  }
}
/**
 * 账号弹窗 更新数据
 * @param {MerchantAccountInfo} account 当前编辑账号信息
 */
const onDialogAccountUpdate = (account: MerchantAccountInfo) => {
  // 更新后，当前编辑的会被选中（不管原来编辑的是否是选中的）
  currentAccount.value = account
  updateAccountAllList()
}

// ---------------------------------------- 账号信息 结束 ----------------------------------------

// ---------------------------------------- 列表标签卡 通用 开始 ----------------------------------------

// 正在切换标签卡
const loadingTab = ref<boolean>(false)
// 标签卡切换节流锁
const throttleTab = new Throttle(loadingTab)

// 标签卡名称枚举
enum LIST_TAB_NAME {
  LINE = '线路列表',
  SCRIPT = '话术列表',
  PROJECT = '项目列表',
  SMS = '短信列表',
  VARIABLE = '变量列表',
  TASK = '任务模板',
  SETTING = '账号设置',
}

// 所有标签卡名称列表
const tabList = computed(() => {
  const permissions = userStore.permissions[routeMap[title.value].id]
  return permissions?.includes(routeMap[title.value].permissions['账号设置'])
    ? Object.values(LIST_TAB_NAME)
    : Object.values(LIST_TAB_NAME).filter(item => item !== '账号设置')
})
// 当前激活的列表标签卡名称
const listTabName = ref<string>(LIST_TAB_NAME.LINE)
// const listTabName = ref<string>(LIST_TAB_NAME.SMS)

/**
 * 切换列表标签卡
 */
const handleListTabChange = (val: string) => {
  listTabName.value = val
  updateListTab()
}
/**
 * 更新列表标签卡
 */
const updateListTab = () => {
  // 如果没有选中账号，不应该更新列表
  if (!accountList.value.length || currentAccount.value.id === -1) {
    return
  }
}



// ---------------------------------------- 立即执行 开始 ----------------------------------------

const stopWatchQueryTenantName = watch([
  () => route.query.tenantName,
  () => route.query.account,
], () => {
  // URL参数中，商户名称发生变化，更新整个页面
  // console.log('route.query.tenantName', val)
  init()
})
const init = () => {
  // 路由跳转存在两种模式，模式1：商户名称（线路运营跳转），模式2：搜索账号（话术制作绑定商户后跳转）
  // 读取URL参数，筛选商户，切换为搜索商户名称，否则搜索账号
  merchantSearchForm.tenantName = route.query.tenantName as string ?? merchantSearchForm.tenantName ??''
  merchantSearchForm.account = route.query.account as string ?? merchantSearchForm.account ?? ''
  listTabName.value = route.query.tabName as string ?? LIST_TAB_NAME.LINE
  if (listTabName.value === LIST_TAB_NAME.TASK) {
    autoShowDialogInTaskTemplate.value = true
  }
  if (!!merchantSearchForm.tenantName) {
    merchantSearchType.value = 'tenantName'
  } else {
    merchantSearchType.value = 'account'
  }
  // 清除缓存，但是保留当前商户和当前账号，以便更新后立马跳转过去
  const current = JSON.parse(JSON.stringify(merchantStore.currentMerchant))
  const account = JSON.parse(JSON.stringify(merchantStore.currentAccount))
  merchantStore.clear()
  currentAccount.value = account
  // 更新全部商户列表
  updateMerchantAllList(current)
}
onMounted(() => {

})
onActivated(() => {
  // console.log('onActivated')
  init()
})
onBeforeRouteLeave(() => {
  // console.log('onBeforeRouteLeave')
  stopWatchQueryTenantName()
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
:deep(.el-table) {
  font-size: var(--el-font-size-base);
}
:deep(.tab-box) {
  height: auto;
  padding: 0;
  /* 商户列表标签卡 */
  &.merchant-tab {
    padding: 8px 12px 0;
    .normal-tab {
      width: 100%;
      height: 100%;
      padding: 4px 12px;
      border-bottom: none;
      font-size: 14px;
      line-height: 22px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  /* 列表模块标签卡 */
  &.list-tab {
    padding: 12px 8px 0;
    .normal-tab {
      width: fit-content;
      height: 100%;
      padding: 8px 12px;
      border-bottom: none;
      font-size: 16px;
      line-height: 17px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
/*文本框内的标签*/
:deep(.el-cascader__tags .el-tag) {
  margin: 0 0 0 4px;
}
:deep(.el-form-item__content) {
  width: 460px;
}
.account-header {
  display: flex;
  align-items: center;
}
.account-title {
  flex: none;
  color: #313233;
  font-size: 16px;
  font-weight: 600;
}
.account-list-button {
  flex: none;
  cursor: pointer;
  user-select: none;
}
.account-list {
  flex: auto;
  margin: 0 12px;
}
:deep(.account-list-inner) {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: stretch;
  padding: 16px 0;
}
.account-item {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  flex: none;
  width: 220px;
  margin-right: 8px;
  border-radius: 4px;
  border: 2px solid transparent;
  background-color: #fff;
  text-align: left;
}
.account-item-left {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  flex: auto;
  padding: 12px 0 12px 12px;
}
.account-item-right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  flex: none;
  width: 64px;
  padding: 12px 8px 12px 0;
}
.account-item-name {
  width: 100%;
  color: #313233;
  font-size: 14px;
  font-weight: 600;
  overflow: hidden;
  word-break: break-all;
  overflow-wrap: break-word;
}
.account-item-account {
  width: 100%;
  color: #626366;
  font-size: 16px;
  overflow: hidden;
  word-break: break-all;
  overflow-wrap: break-word;
}
.account-item-status {
  border-radius: 2px;
  padding: 2px 8px;
  border: 2px solid #fff;
  font-size: 12px;
  font-weight: 600;
  &.enabled {
    background-color: rgba(19, 191, 119, 0.20);
    color: #13BF77;
  }
  &.disabled {
    background-color: rgba(229, 144, 0, 0.20);
    color: #E59000;
  }
}
.account-item-more {
  display: none;
}
.account-item-more-button {
  display: none;
}
.account-item:hover {
  .account-item-status {
    display: none;
  }
  .account-item-more,
  .account-item-more-button {
    display: block;
  }
}
.account-item.active {
  border: 2px solid #0167FF;
  /* 底部三角 */
  &::after {
    content: "";
    display: block;
    position: absolute;
    bottom: -16px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 16px;
    border-top: 6px solid #0167FF;
    border-right: 8px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 8px solid transparent;
    background-color: transparent;
  }
  .account-item-name {
    color: #0167FF;
  }
}
</style>
