<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    align-center
    width="720px"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">检测结果</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px] tw-my-[16px]"
      view-class="tw-flex tw-flex-col tw-items-center tw-leading-[24px]"
    >
      <div class="info-title tw-text-left tw-w-full">
        <p>商户线路：{{ props.lineInfo?.tenantLineName || '-' }}</p>
        <p>供应线路：{{ props.lineInfo?.supplyLineName || '-' }}</p>
      </div>
      <el-table
        v-if="tableData && tableData?.length>0"
        class="tw-mt-[12px]"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="tableHeaderStyle"
      >
        <el-table-column align="left" label="序号" width="64">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column align="left" property="msg" label="结果" min-width="240"></el-table-column>
        <el-table-column align="center" property="percent" label="正常比例" width="120"></el-table-column>
        <el-table-column align="center" property="createTime" label="检测时间" width="160"></el-table-column>
      </el-table>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="loading" type="primary" class="tw-mr-[8px] tw-underline tw-underline-offset-[4px]" link @click="sampleTenantSupplyLine">
          再次检测
        </el-button>
        <el-button @click="cancel" :icon="CloseBold">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, onUnmounted, } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js'
import { merchantSupplyLineModel } from '@/api/merchant'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { formatNumberPercent } from '@/utils/utils'


const emits = defineEmits(['update:visible'])
const props = defineProps<{
  lineInfo: null | {
    tenantLineNumber: string,
    supplyLineNumber: string,
    tenantLineName?: string,
    supplyLineName?: string,
  },
  visible: boolean;
}>();
const visible = ref(false)
watch(() => props.visible, () => {
  visible.value = props.visible
  if(props.visible) {
    sampleTenantSupplyLine()
  } else {
    tableData.value = null
  }
})

const cancel = () => {
  emits('update:visible', false)
}

const tableData = ref<{
  createTime: string,
  percent: string,
  msg: string,
}[] | null>([])
const loading = ref(false)
const sampleTenantSupplyLine = async () => {
  if (!props.lineInfo || !props.lineInfo?.tenantLineNumber || !props.lineInfo?.supplyLineNumber) {
    return ElMessage.warning('线路信息有误')
  }
  loading.value = true
  const [err, res] =await to(merchantSupplyLineModel.sampleTenantSupplyLine({
    tenantLineNumber: props.lineInfo?.tenantLineNumber  || '',
    supplyLineNumber : props.lineInfo?.supplyLineNumber || ''
  }))
  loading.value = false
  if (!!err) return
  if (!tableData.value) tableData.value = []
  const result: string[] = []
  let total = 0
  if (res) {
    Object.keys(res).forEach((key: string) => {
      total = total + (res[key] || 0)
      result.push(`${key}：${res[key] ?? ''}`)
    })
  } else {
    result.push('检测失败')
  }
  tableData.value.push({
    msg: result.join('，'),
    percent: formatNumberPercent((res['线路正常'] || 0) / total * 100, 2),
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  })
}

onUnmounted(() => {
  visible.value = false
  tableData.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-scrollbar__bar.is-vertical) {
  top: 18px;
}
.el-table {
  font-size: 13px;
}
</style>