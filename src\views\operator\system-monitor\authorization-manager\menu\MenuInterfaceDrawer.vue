<template>
  <el-drawer
    v-model="dialogVisible"
    :before-close="cancel"
    size="900px"
    :with-header="false"
    :close-on-click-modal="isChecked"
  >
    <div class="tw-bg-[#f2f3f5] tw-overflow-hidden tw-h-full tw-flex tw-flex-col">
      <div class="tw-px-[16px] tw-py-[12px] tw-text-left tw-bg-white tw-justify-between tw-flex tw-items-center tw-w-full">
        <span class="form-dialog-header ">{{props.data?.menuName + '的接口'}}</span>
      </div>
      <div class="tw-bg-white tw-px-[12px] tw-py-[16px] tw-flex tw-items-center">
        <span>选择接口：</span>
        <SelectBox 
          v-model:selectVal="editData.permissionIdList"
          :options="interfaceList"
          name="name"
          suffix="identifier"
          val="id"
          placeholder="请选择接口"
          filterable
          class="tw-w-[300px]"
          multiple
          canCopy
          copy-name="permissionIdList"
          @update:select-val="isChecked = false"
        >
        </SelectBox>
      </div>
      <div class="tw-p-[12px] tw-flex tw-justify-end">
        <el-button type="primary" @click="batchVisible = true">批量导入接口</el-button>
      </div>
      <el-table
        class="tw-grow"
        :data="tableData"
        v-loading="loading"
        :header-cell-style="tableHeaderStyle"
        stripe
        border
        row-key="id"
      >
        <el-table-column type="index" align="center" width="40" />
        <el-table-column property="name" label="名称" align="left" show-overflow-tooltip min-width="280" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="serviceId" label="服务名" align="left" width="100"></el-table-column>
        <el-table-column property="identifier" label="接口地址" align="left" show-overflow-tooltip min-width="400" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column label="操作" width="90" align="right" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="editInterface(row)">编辑</el-button>
            <el-button type="danger" link @click="deleteInterface(row)">移除</el-button>
          </template>
        </el-table-column>
        <template #empty>
          <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
        </template>
      </el-table>
    </div>
    <template #footer>
      <span class="dialog-footer tw-flex tw-justify-between tw-items-center">
        <div>
          <el-button type="primary" link @click="goNext(-1)">
            上一个
          </el-button>
          <el-button type="primary" link @click="goNext(1)">
            下一个
          </el-button>
        </div>
        <div>
          <el-button @click="cancel" :icon="CloseBold">取消</el-button>
          <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
        </div>
      </span>
    </template>
  </el-drawer>
  <EditDialog
    v-model:visible="editVisible"
    :data="currentData"
    @confirm="initInterfaceList()"
  ></EditDialog>
  <BatchImportDialog
    v-model:visible="batchVisible"
    :data="props.data"
    @confirm="updateInterfaceList"
  />
</template>

<script setup lang="ts">
import { ref,watch, reactive, computed, defineAsyncComponent, } from 'vue'
import { Select, CloseBold } from '@element-plus/icons-vue'
import { MenuItem, MenuInterfaceItem, InterfaceItem, InterfaceTypeEnum, ServiceEnum } from '@/type/authorization'
import { formatterEmptyData, findValueInEnum  } from '@/utils/utils'
import { onBeforeRouteLeave } from 'vue-router'
import to from 'await-to-js'
import { authorizationModel } from '@/api/authorization'
import { ElMessage } from 'element-plus'
import { tableHeaderStyle } from '@/assets/js/constant'
import SelectBox from '@/components/SelectBox.vue'
import EditDialog from '../interface/EditDialog.vue'
import BatchImportDialog from './BatchImportDialog.vue'

const emits = defineEmits(['update:visible', 'go-next'])
const props = defineProps<{
  visible: boolean,
  data: MenuItem | null
}>();
const dialogVisible = ref(props.visible)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const loading = ref(false)
const interfaceList = ref<InterfaceItem[] | null>([])
const isChecked = ref(true) // 打开未作任何修改则是为查看模式
const tableData = computed(() => {
  return !!editData.permissionIdList?.length ? interfaceList.value?.filter(item => editData.permissionIdList?.includes(item.id)) || [] : []
})

const editData = reactive<MenuInterfaceItem>({
  menuId: props.data?.id,
  permissionIdList: []
})

// 切换菜单：上一步：-1 | 下一步：1
const goNext = (flag: -1 | 1) => {
  emits('go-next', flag)
}

// 批量导入
const batchVisible = ref(false)
/**
 * 更新批量导入信息更新当前绑定的话术
 * @param data 类型：0 新增 | 1 覆盖；apiStr 接口字符串，逗号隔开
 */
const updateInterfaceList = async (data: {
  apiStr?: string,
  type: number,
}) => {
  if (!data.apiStr) return ElMessage.warning('请输入导入接口')
  let errNum = 0
  const ids: number[] = [];
  await Promise.all((data.apiStr.split(',') || []).map(async item => {
    const res = item.split('/')
    const serviceId = res[1]
    const identifier = '/' + res.slice(2).join('/')
    const interfaceItem = interfaceList.value?.find(v => v.serviceId ===serviceId && v.identifier === identifier)
    
    if (!interfaceItem) {
      const [err, res] = await to(authorizationModel.saveInterface({
        type: InterfaceTypeEnum['TOKEN类'],
        name: identifier,
        identifier: identifier,
        remark: '',
        serviceId: serviceId as ServiceEnum,
      }))
      if (res?.id) {
        ids.push(res.id)
      } else {
        errNum++
      }
    } else {
      interfaceItem?.id && ids.push(interfaceItem.id)
    }
  }))
  // 匹配失败
  if (errNum) return ElMessage.warning(`共${errNum}条匹配失败`)
  // 增加
  if (data.type === 0) {
    editData.permissionIdList = [...new Set([...(editData.permissionIdList || []), ...ids])]
  }
  // 覆盖
  if (data.type === 1) {
    editData.permissionIdList = [...new Set(ids)]
  }
  ElMessage.warning('导入成功, 点击确认后生效')
}

const confirm = async () => {
  loading.value = true
  const [err] = await to(authorizationModel.saveMenuPermission(editData))
  !err && ElMessage.success('操作成功')
  loading.value = false
  cancel()
}

// 编辑接口信息
const currentData = ref<InterfaceItem | null>(null)
const editVisible = ref(false)
// 编辑接口
const editInterface = (data?: InterfaceItem) => {
  currentData.value = data || null
  editVisible.value = true
}

// 删除接口
const deleteInterface = async (row: InterfaceItem) => {
  if (!row.id) return
  isChecked.value = false
  editData.permissionIdList = editData.permissionIdList?.filter(item => item !== row.id) || []
}

const initInterfaceList = async () => {
  const res1 = await to(authorizationModel.findInterfaceList())
  interfaceList.value = (res1[1] || []).filter(item => item.type === InterfaceTypeEnum['TOKEN类'])
}

const init = async () => {
  loading.value = true
  isChecked.value = true
  // 获取全部接口
  if (!interfaceList.value?.length) {
    initInterfaceList()
  }

  // 更新当前菜单的信息
  if (!props.data?.id) return ElMessage.warning('无法获取当前菜单ID')
  editData.menuId = props.data?.id

  // 获取当前菜单绑定的接口
  const [err, res2] = await to(authorizationModel.findInterfacePermissionByMenuId({
    menuId: props.data?.id
  }))
  editData.permissionIdList = res2?.map(item => item.id) || []
  loading.value = false
}

watch([() => props.visible, () => props.data?.id], async () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
  }
}, {deep: true})

onBeforeRouteLeave(() => {
  interfaceList.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-table {
  font-size: var(--el-font-size-base);
  :deep(.cell) {
    padding: 0 6px;
  }
  :deep(.caret-wrapper) {
    display: none;
  }
  .el-empty {
    padding: 10px 0;
  }
}
</style>
