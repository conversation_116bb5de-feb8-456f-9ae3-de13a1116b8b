import { MerchantMapItemType, MerchantMapType } from '@/type/merchant'


// ---------------------------------------- 商户 账号 开始 ----------------------------------------
/**
 * 商户账号 状态 转换表 页面展示
 */
export const merchantAccountStatusList = {
  enabled: { name: 'enabled', text: '启用中', val: true },
  disabled: { name: 'disabled', text: '停用中', val: false },
}
// ---------------------------------------- 商户 账号 结束 ----------------------------------------

// ---------------------------------------- 商户 线路 开始 ----------------------------------------
/**
 * 商户线路 状态 枚举值 接口数据
 */
export enum merchantLineStatusEnum {
  DISABLED = 'DISABLE',
  ENABLED = 'ENABLE',
}

/**
 * 商户线路 状态 转换表 页面展示
 */
export const merchantLineStatusList: MerchantMapType = {
  all: { name: 'all', text: '全部', val: 'all' },
  enabled: { name: 'enabled', text: '启用', val: merchantLineStatusEnum.ENABLED },
  disabled: { name: 'disabled', text: '停用', val: merchantLineStatusEnum.DISABLED },
}

/**
 * 商户线路 状态 映射表 页面展示
 */
export const merchantLineStatusMap: Map<merchantLineStatusEnum, MerchantMapItemType> = new Map([
  [merchantLineStatusEnum.ENABLED, merchantLineStatusList.enabled],
  [merchantLineStatusEnum.DISABLED, merchantLineStatusList.disabled],
])

/**
 * 商户线路 状态 映射表 启停切换开关 页面展示
 */
export const merchantLineStatusSwitchMap: Map<merchantLineStatusEnum, MerchantMapItemType> = new Map([
  [merchantLineStatusEnum.ENABLED, merchantLineStatusList.disabled],
  [merchantLineStatusEnum.DISABLED, merchantLineStatusList.enabled],
])

// ---------------------------------------- 商户 线路 结束 ----------------------------------------

// ---------------------------------------- 商户 话术 开始 ----------------------------------------
/**
 * 商户话术 状态 枚举值 接口数据
 */
export enum merchantScriptStatusEnum {
  DISABLED = 'INACTIVE',
  ENABLED = 'ACTIVE',
}

/**
 * 商户话术 状态 转换表 页面展示
 */
export const merchantScriptStatusList: MerchantMapType = {
  all: { name: 'all', text: '全部', searchText: '全部', val: 'all' },
  enabled: { name: 'enabled', text: '启用', searchText: '生效中', val: merchantScriptStatusEnum.ENABLED },
  disabled: { name: 'disabled', text: '停用', searchText: '已禁用', val: merchantScriptStatusEnum.DISABLED },
}

/**
 * 商户话术 状态 映射表 页面展示
 */
export const merchantScriptStatusMap: Map<merchantScriptStatusEnum, MerchantMapItemType> = new Map([
  [merchantScriptStatusEnum.ENABLED, merchantScriptStatusList.enabled],
  [merchantScriptStatusEnum.DISABLED, merchantScriptStatusList.disabled],
])
// ---------------------------------------- 商户 话术 结束 ----------------------------------------
