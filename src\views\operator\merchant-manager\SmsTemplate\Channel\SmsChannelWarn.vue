<template>
  <div class="card-box" v-loading="loading">
    <el-form
      ref="formRef"
      :disabled="isCheck"
      label-width="90px"
      :model="form"
      :rules="rules"
    >
      <el-form-item label="短信预警：" prop="channelWarnOpen">
        <el-switch
          v-model="form.channelWarnOpen"
          class="tw-mx-[8px]"
          inline-prompt
          active-text="开启"
          inactive-text="关闭"
        />
      </el-form-item>
      <div class="tw-pl-[36px]">
        <el-form-item label="账号预警：">
          所有短信对接账号挂起、临停时，自动挂起短信通道
          <el-switch
            v-model="form.channelAccountWarnOpen"
            class="tw-mx-[8px]"
            inline-prompt
            active-text="开启"
            inactive-text="关闭"
            :disabled="!form.channelWarnOpen"
          />
        </el-form-item>
        <el-form-item label="发送预警：" prop="sendCount">
          连续发送
          <InputNumberBox v-model:value="form.sendCount" :disabled="!form.channelWarnOpen" :precision="0" class="tw-mx-[8px]" style="width: 160px;" :min="1" append="次"/>
          ，发送失败率高于
          <InputNumberBox v-model:value="sendFailRate" :disabled="!form.channelWarnOpen" class="tw-mx-[8px]" :precision="2" style="width: 160px;" :min="1" :max="100" append="%"/>
          时，自动挂起短信通道
        </el-form-item>
        <el-form-item label="回执预警：" prop="receiptCount">
          连续发送
          <InputNumberBox v-model:value="form.receiptCount" :disabled="!form.channelWarnOpen" class="tw-mx-[8px]" :precision="0" style="width: 160px;" :min="1" append="次"/>
          ，回执超时率高于
          <InputNumberBox v-model:value="receiptFailRate" :disabled="!form.channelWarnOpen" class="tw-mx-[8px]" :precision="2" style="width: 160px;" :min="1" :max="100" append="%"/>
          时，自动挂起短信通道
        </el-form-item>
      </div>

      <el-form-item label="关联任务：">
        <el-switch
          v-model="form.channelRelateTaskOpen"
          class="tw-mx-[8px]"
          inline-prompt
          active-text="开启"
          inactive-text="关闭"
        />
        通道挂起时，使用该短信模板且进行中的任务，将自动暂停
      </el-form-item>
    </el-form>
    <div class="tw-w-full tw-flex tw-justify-end">
      <el-button v-if="isCheck" type="primary" @click="goEdit">
        <span>编辑</span>
      </el-button>
      <el-button v-if="!isCheck" :loading="loading" type="primary" @click="confirm">确定</el-button>
      <el-button v-if="!isCheck" @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed } from 'vue'
import { ElMessage, FormRules } from 'element-plus'
import { ChannelWarnConfig, SmsAccountGroupItem } from '@/type/merchant'
import { useMerchantStore } from '@/store/merchant'
import { merchantSmsChannelModel } from '@/api/merchant'
import to from 'await-to-js'
import { pickAttrFromObj } from '@/utils/utils'
import InputNumberBox from '@/components/InputNumberBox.vue'

const merchantStore = useMerchantStore()
const loading = ref(false)
const isCheck = ref(true)

// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): ChannelWarnConfig => {
  return {
    channelAccountWarnOpen: false,
    channelRelateTaskOpen: false,
    channelWarnOpen: false,
    sendFailCount:  null,
    sendCount: null,
    receiptCount: null,
    receiptFailCount: null,
  }
}
// 表单数据
const form: ChannelWarnConfig = reactive(formDefault())
const sendFailRate = ref<null | number>(null)
const receiptFailRate = ref<null | number>(null)
// 表单校验规则
const rules: FormRules = reactive({
  sendCount: [
    { validator: (rule: any, value: any, callback: any) => {
      if (isCheck.value || !form.sendCount) {
        return callback()
      }
      if (!sendFailRate.value) {
        return callback(new Error('发送失败率不能为空'))
      }
      const pre =  sendFailRate.value
      form.sendFailCount = Math.round(form.sendCount * pre / 100)
      sendFailRate.value = form.sendFailCount / form.sendCount * 100
      if (pre !== sendFailRate.value) {
        ElMessage.warning('以为您自动调整发送失败率，以保证失败次数为整数')
      }
      callback()
    } , trigger: ['change' ]}
  ],
  receiptCount: [
    { validator: (rule: any, value: any, callback: any) => {
      if (isCheck.value || !form.receiptCount) {
        return callback()
      }
      if (!receiptFailRate.value) {
        return callback(new Error('回执失败率不能为空'))
      }
      const pre =  receiptFailRate.value
      form.receiptFailCount = Math.round(form.receiptCount * pre/ 100)
      receiptFailRate.value = form.receiptFailCount / form.receiptCount * 100
      if (pre !== receiptFailRate.value) {
        ElMessage.warning('以为您自动调整回执失败率，以保证失败次数为整数')
      }
      callback()
    } , trigger: ['change' ]}
  ],
})


const goEdit = () => {
  isCheck.value = false
}
const cancel = () => {
  formRef.value.resetFields()
  isCheck.value = true
  updateWarnData()
}

const confirm = () => {
  if (form.channelWarnOpen && !form.channelAccountWarnOpen && !form.sendCount && !form.receiptCount) {
    return ElMessage.warning('开启短信通道预警时，至少选择一项预警内容')
  }
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      const [err] = await to(merchantSmsChannelModel.saveChannelWarnConfig(form))
      if (!err) {
        ElMessage.success('操作成功')
      }
      updateWarnData()
      loading.value = false
    }
  })
}

const updateWarnData = async () => {
  isCheck.value = true
  const [_ , res] = await to(merchantSmsChannelModel.getSmsChannelByTemplateId({
    templateId: merchantStore.editingSmsTemplate?.id!
  }))
  if (!res) return
  Object.assign(form, pickAttrFromObj(res, [
    'channelAccountWarnOpen',
    'channelRelateTaskOpen',
    'channelWarnOpen',
    'sendFailCount',
    'sendCount',
    'receiptCount',
    'receiptFailCount',
    'tenantSmsTemplateId'
  ]))
  sendFailRate.value = (form.sendFailCount && form.sendCount) ? form.sendFailCount / form.sendCount * 100 : null
  receiptFailRate.value = (form.receiptFailCount && form.receiptCount) ? form.receiptFailCount / form.receiptCount * 100 : null
  
}
updateWarnData()

</script>

<style scoped lang="postcss">
.card-box {
  flex-direction: column;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
    .el-input__wrapper {
      width: 100%
    }
  }
  .el-form-item__label {
    padding-right: 0;
    font-size: 13px;
  }
  :deep(.el-form-item__content) {
    font-size: 13px;
    color: var(--primary-black-color-400);
  }
}
</style>
