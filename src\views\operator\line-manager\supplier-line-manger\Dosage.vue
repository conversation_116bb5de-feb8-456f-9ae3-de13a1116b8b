<template>
  <div class="dosage-manger-container">
    <div class="tw-w-full tw-flex tw-items-center tw-p-[16px] tw-grow-0 tw-shrink-0">
      <div class="item tw-w-[350px]">
        <TimePickerBox
          v-model:start="searchForm.startTime"
          v-model:end="searchForm.endTime"
          placeholder="最后登录时间"
          separator="-"
          type="daterange"
          :clearable="false"
          format="YYYY-MM-DD"
          @change="search()"
        />
        <div class="tw-ml-[12px]">
          <el-button type="primary" @click="search" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="tw-mb-[12px] tw-mr-[12px]">
      <button @click="export2Excel" class="common-btn tw-float-right"><el-icon size="--el-font-size-base" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>导出数据</button>
    </div>
    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      ref="tableRef"
      row-key="statisticDate"
      :header-cell-style="tableHeaderStyle"
      :row-class-name="getTotalRowClass"
      @sort-change="handleSortChange"
      stripe
      border
    >
      <el-table-column property="statisticDate" label="日期" align="left" sortable="custom" min-width="160" :formatter="formatterEmptyData" fixed="left">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="calculateNumOfSixty" label="计量数（60s)" sortable="custom" align="left" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.calculateNumOfSixty) }}
        </template>
      </el-table-column>
      <el-table-column property="calculateNumOfSix" label="计量数（6s)" sortable="custom" align="left" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.calculateNumOfSix) }}
        </template>
      </el-table-column>
      <el-table-column property="totalCallNum" label="呼叫总量" align="left" sortable="custom" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.totalCallNum) }}
        </template>
      </el-table-column>
      <el-table-column property="totalConnectNum" label="接通总量" align="left" sortable="custom" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.totalConnectNum) }}
        </template>
      </el-table-column>
      <el-table-column property="connectRate" label="接通率" sortable="custom" align="left" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="totalConnectedDuration" label="接通时长" sortable="custom" align="left" min-width="160" :formatter="(row:any, column:any, data:number)=>formatMsDuration(data/1000)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="averageConnectedDuration" label="平均接通时长" align="left" sortable="custom" min-width="120" :formatter="(row:any, column:any, data:number)=>formatMsDuration(data/1000)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="silenceCallProportion" label="无声通话占比" sortable="custom" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="silenceHangup" sortable="custom" label="沉默挂机" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="assistant" sortable="custom" label="小助理" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="promptSound" sortable="custom" label="运营商提示音" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="oneSecondConnectedProportion" label="秒挂（1s）占比" sortable="custom" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="twoSecondConnectedProportion" label="秒挂（2s）占比" sortable="custom" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="callFailedProportion" label="送呼失败" sortable="custom" align="left" min-width="200" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="transCallSeatNum" sortable="custom" label="转人工占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classANum" sortable="custom" label="A类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classBNum" sortable="custom" label="B类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classCNum" sortable="custom" label="C类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classDNum" sortable="custom" label="D类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="60" align="right" fixed="right">
        <template #default="{ row }">
          <el-button v-if="row.statisticDate && !['合计','总计'].includes(row.statisticDate)" type="primary" link @click="goDetail(row)">详情</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onActivated, nextTick, onDeactivated, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { exportExcel, } from '@/utils/export'
import { useGlobalStore } from '@/store/globalInfo'
import { formatterEmptyData, formatNumber, formatMsDuration, handleTableSort } from '@/utils/utils'
import { LineDosageItem, SupplierDosageDailyItem } from '@/type/line'
import { getSupplierLineTypeText } from '@/utils/line'
import { lineSupplierModel } from '@/api/line'
import router from '@/router'
import { tableHeaderStyle } from '@/assets/js/constant'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { trace } from '@/utils/trace'

const globalStore = useGlobalStore()
const { loading, } = storeToRefs(globalStore)
const currentPage = ref(1)
const total = ref(0)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<LineDosageItem[] | null>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const orderCol = ref('')
const orderType = ref('')
const handleSortChange = async (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
  await nextTick()
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const getTotalRowClass = ({ row }: {row: LineDosageItem}) => {
  if ([row.statisticDate].includes('总计') || [row.statisticDate].includes('合计')) {
    return 'tw-font-[700] tw-bg-[#f7f8fa]'
  } else {
    return undefined
  }
}
const searchForm = reactive({
  startTime: dayjs().add(-1, 'day').format('YYYY-MM-DD'),
  endTime: dayjs().add(-1, 'day').format('YYYY-MM-DD'),
})
const tableRef = ref(null)
const search = async () => {
  loading.value = true
  tableData.value = await lineSupplierModel.getDosageList(searchForm) as LineDosageItem[] || []
  total.value = tableData.value?.length || 0
  loading.value = false
}

const goDetail = (row: any) => {
  router.push({
    name: 'SupplierLineDosageDaily',
    query: {
      date: row.statisticDate
    }
  })
}
const export2Excel = async () => {
  loading.value = true
  try {
    const params: string[] = []
    let curTime = dayjs(searchForm.startTime)
    const endTime = dayjs(searchForm.endTime)
    while (!dayjs(curTime).isAfter(endTime)) {
      params.push(dayjs(curTime).format('YYYY-MM-DD'))
      curTime = dayjs(curTime).add(1, 'day')
    }
    const dataOrigin = await lineSupplierModel.getDosageXlsList(params)
    if (!dataOrigin || Object.keys(dataOrigin).length < 1) {
      loading.value = false
      return ElMessage({
        type: 'warning', message: '暂无数据可导出'
      })
    }
    const data: unknown[] = []
    const keyList = Object.keys(dataOrigin).sort((a, b) => dayjs(a).isAfter(b) ? -1 : 1)
    for (let i = 0; i < keyList.length; i ++ ) {
      const t = keyList[i]
      const res = []
      for (let j = 0; j < dataOrigin[t].length; j++) {
        const item = dataOrigin[t][j]
        const { calculateNumOfSixty, calculateNumOfSix, totalCallNum, totalConnectNum,
          connectRate, totalConnectedDuration, averageConnectedDuration, silenceCallProportion,
          oneSecondConnectedProportion, twoSecondConnectedProportion, callFailedProportion, masterCallNumber, supplierName,
          supplierNumber, supplyLineName, supplyLineNumber, supplyLineType,
          silenceHangup, assistant,  promptSound, classANum, classBNum, classCNum, classDNum, transCallSeatNum, unitPrice,
        } = item
        const obj = {
          '日期': t,
          '供应线路名称': supplyLineName || '',
          '供应线路编号': supplyLineNumber || '',
          '线路类型': supplyLineType ? getSupplierLineTypeText(supplyLineType) : '',
          '计量数（60s）': calculateNumOfSixty || 0,
          '计量数（6s）': calculateNumOfSix || 0,
          '线路单价': unitPrice ?? '',
          '呼叫总量': totalCallNum || 0,
          '接通总量': totalConnectNum || 0,
          '接通率': connectRate || 0,
          '接通时长': formatMsDuration(totalConnectedDuration/1000) || 0,
          '平均接通时长': formatMsDuration(averageConnectedDuration/1000) || 0,
          '无声通话占比': silenceCallProportion || 0,
          '沉默挂机': silenceHangup || 0,
          '小助理': assistant || 0,
          '运营商提示音': promptSound || 0,
          '秒挂（1s）占比': oneSecondConnectedProportion || 0,
          '秒挂（2s）占比': twoSecondConnectedProportion || 0,
          '送呼失败': callFailedProportion || 0,
          '转人工占比': transCallSeatNum || 0,
          'A类占比': classANum || 0,
          'B类占比': classBNum || 0,
          'C类占比': classCNum || 0,
          'D类占比': classDNum || 0,
          '供应商名称': supplierName || '',
          '供应商编号': supplierNumber || '',
          '主叫号码': masterCallNumber || '',
        }
        if ([supplyLineName, supplyLineNumber].includes('总计')) {
          obj['日期'] = t
          res.unshift(obj)
        } else {
          res.push(obj)
        }
      }
      data.push(...res)
    }
    trace({
      page: `线路运营-供应线路-用量统计-导出数据`,
      params: data,
    })
    exportExcel(data, `供应线路用量统计【${dayjs(searchForm.startTime||undefined).format('YYYY-MM-DD')}至${dayjs(searchForm.endTime||undefined).format('YYYY-MM-DD')}）】.xlsx`);
    loading.value = false
  }catch(err) {
    loading.value = false
    return ElMessage({
      type: 'error', message: '导出数据出错'
    })
  }
  
  
}

onActivated(() => {
  search()
})
onDeactivated(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.dosage-manger-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  background-color: #fff;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
}
</style>