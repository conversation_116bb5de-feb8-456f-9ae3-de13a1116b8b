<template>
  <div class="main-container">
    <div v-if="menuVisible" class="aside" >
      <Aside @menu-change="handleMenuChange"/>
    </div>
    <div v-else class="menu-btn" @click="menuVisible=true">
      菜单
    </div>
    <div class="main">
      <div class="header">
        <Header>
          <template #seat v-if="router.currentRoute.value.name === 'Workbench'">
            <SeatWorkbenchHeader />
          </template>
        </Header>
      </div>
      <div v-loading="loadingModule" :data-loading="loadingModule" class="content">
        <!-- <router-view /> -->
        <router-view v-slot="{ Component }">
          <keep-alive :max="2">
            <component v-if="$route.meta.keepAlive" :key="$route.path" :is="Component" />
          </keep-alive>
          <component v-if="!$route.meta.keepAlive" :key="$route.path" :is="Component" />
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Aside from './Aside.vue'
import Header from './Header.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import { versionCheck } from '@/utils/version'
import { ref } from 'vue'
import SeatWorkbenchHeader from '@/components/seat/SeatWorkbenchHeader.vue'
import router from '@/router'

const globalStore = useGlobalStore()
const { loadingModule } = storeToRefs(globalStore)
globalStore.isMobile = window.innerWidth <= 600

globalStore.getProvinceInfo(true)
/**
 * head请求，检测版本更新
 */
const versionMonitor = ref<null | ReturnType<typeof setInterval>>(null)
const isDev = import.meta.env.MODE.includes('development')
const checkAction = async () => {
  versionMonitor.value && clearInterval(versionMonitor.value)
  versionMonitor.value = setInterval(() => {
    versionCheck()
  }, 90 * 1000)
}
!isDev && checkAction()
const menuVisible = ref(window.innerWidth > 600)
const handleMenuChange = () => {
  if (window.innerWidth <= 600) {
    menuVisible.value = false
  }
}

// 在浏览器切换标签时，直接检测版本更新并刷新,担心在弹窗、画布制作时刷新，取消该效果了。
// onMounted(() => {
//   document.addEventListener('visibilitychange', () => versionCheck(true))
// })
// onUnmounted(() => {
//   document.removeEventListener('visibilitychange', () => versionCheck(true))
// })
</script>

<style lang="postcss">
.main-container {
  display: flex;
  flex-direction: row;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
.aside {
  flex: none;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.menu-btn {
  height: 52px;
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
  opacity: 0.8;
  background-color: var(--primary-black-color-200);
  color: var(--primary-black-color-400);
  border-radius: 0 2px 2px 0;
  border: 1px solid var(--primary-black-color-300);
  border-left: 0;
  width: 26px;
  padding: 2px 4px;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
}
.main {
  position: relative;
  flex: auto;
  overflow-x: auto;
  overflow-y: hidden;
  max-height: 100vh;
}
.header {
  position: absolute;
  top: 0;
  right: 20px;
  z-index: 11;
  /*width: 100px;*/
  height: 56px;
}
.content {
  position: relative;
  display: flex;
  background-color: #f2f3f5;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;
  height: 100%;
}
.el-select-dropdown__item {
  text-align: left;
}
/** 弹窗-样式 */
.el-dialog__header {
  padding: 12px 16px;
  box-sizing: border-box;
  .el-dialog__headerbtn {
    top: 0;
  }
}
.el-dialog__footer {
  padding: 16px;
}
.el-dialog__body {
  border-top: 1px solid #EBEDF0;
  border-bottom: 1px solid #EBEDF0;
  padding: 0 12px;
}
.el-table thead th {
  font-weight: 600;
}
.el-form-item {
  font-size: var(--el-font-size-base);
}
.el-radio-button__original-radio:checked + .el-radio-button__inner {
  background: linear-gradient(90deg, #0167FF 0%, #019FFF 100%);
}
.el-date-editor .el-range-input {
  padding-left: 8px;
  text-align: left;
}
.el-popper.is-dark {
  padding: 8px;
  background: rgba(0, 0, 0, 0.75);
  .el-popper__arrow::before {
    border-color: #404040;
    background: #404040;
  }
}
</style>
