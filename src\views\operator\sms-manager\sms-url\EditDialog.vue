<template>
  <el-dialog
    :model-value="visible"
    width="640px"
    class="dialog-form"
    :close-on-click-modal="false"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="90px"
        ref="editRef"
      >
        <el-form-item v-if="editData.id" label="短链ID：" prop="linkNumber" class="info-title">
          {{ editData.linkNumber }}
        </el-form-item>
        <el-form-item label="短链名称：" prop="linkName">
          <el-input v-model.trim="editData.linkName" show-word-limit maxlength="30" clearable placeholder="请输入短链名称，30个字以内"></el-input>
        </el-form-item>
        <el-form-item label="原始链接：" prop="originalUrl">
          <el-input
            v-model.trim="editData.originalUrl"
            type="textarea"
            show-word-limit
            maxlength="255"
            clearable
            placeholder="请输入原始链接，255个字以内"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="editData.id && props.type===0" label="短链：" class="info-title">
          <span v-if="!editData.needRegister">{{ editData.shortUrl}}</span>
          <!-- 仅普通短链支持重新生成 -->
          <el-switch
            v-if="!!editData.id && props.type===0"
            v-model="editData.needRegister"
            class="tw-ml-1"
            inline-prompt
            active-text="重新生成"
            inactive-text="保持当前"
          />
        </el-form-item>
        <el-form-item v-if="props.type==1 || editData.needRegister" label="所属主账号：" prop="groupId">
          <el-select
            v-model="editData.groupId"
            :disabled="editData.tenantSmsTemplates && editData.tenantSmsTemplates.length > 0"
            clearable
            filterable
            placeholder="请选择所属商户主账号"
            style="width: 100%;"
            @change="handleGroupIdChange"
          >
            <el-option
              v-for="item in masterAccountList"
              :key="item.groupId"
              :label="item.account"
              :value="item.groupId"
            />
          </el-select>
        </el-form-item>
        <!-- 选择账号后才能配置域名 -->
        <el-form-item v-if="props.type==0 && editData.needRegister && editData.groupId" label="短链域名：" prop="domain">
          <el-select v-model="editData.domain" class="tw-w-full" placeholder="请选择域名">
            <el-option v-for="item in domainAvailableList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="props.type==1 && editData.groupId" label="短链域名：" prop="domainList">
          <div class="tw-h-[32px]">
            <el-button link type="primary" @click="addDomain">+新增域名</el-button>
          </div>
          <div v-if="domainList && domainList.length>0" class="tw-w-full tw-text-left">
            <el-row >
              <el-col :span="13">域名</el-col>
              <el-col :span="8">权重</el-col>
              <el-col :span="3">操作</el-col>
            </el-row>
            <el-row v-for="(_, index) in domainList" key="index" class="tw-mb-[4px] last-of-type:tw-mb-0">
              <el-col :span="13" class="tw-flex tw-items-center tw-h-[34px]">
                <el-select v-model="domainList[index][0]" class="tw-w-[96%]" placeholder="域名">
                  <el-option v-for="item in domainAvailableList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-col>
              <el-col :span="8" class="tw-flex tw-items-center tw-h-[34px]">
                <el-input-number v-model="domainList[index][1]" style="width: 96%" :controls="false" :precision="0" :min="1" :max="100" placeholder="权重，1-100"/>
              </el-col>
              <el-col :span="3" class="tw-flex tw-items-center tw-h-[34px]">
                <el-button link type="danger" @click="delDomain(index)">删除</el-button>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
        <el-form-item v-if="props.type===0" label="过期时间：" prop="expireTime">
          <el-date-picker
            v-model="editData.expireTime"
            style="width: 100%"
            format="YYYY-MM-DD"
            :shortcuts="shortcuts"
            :disabled-date="disabledFn"
            placeholder="请选择过期时间"
            type="date"
            :clearable="false"
          />
        </el-form-item>
        <el-form-item v-if="props.type===1" label="有效时长：" prop="expireDays">
          <InputNumberBox v-model:value="editData.expireDays" placeholder="请输入有效时长，输入范围【1-31】天" append="天" :max="31"/>
        </el-form-item>
        <el-form-item v-if="showMore" label="拼接信息：" prop="paramTypes">
          <el-checkbox-group v-model="editData.paramTypes" class="tw-ml-[6px] tw-grid tw-grid-cols-4 tw-gap-x-[16px]">
            <el-checkbox v-for="item in extraParamsOption" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button v-if="props.type===1 && !showMore" class="tw-float-left tw-mt-[11px] tw-ml-1" link type="primary" @click="showMore=true">更多</el-button>
        <div>
          <el-button @click="cancel" :icon="CloseBold">取消</el-button>
          <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { smsUrlModel } from '@/api/sms'
import type { FormInstance, } from 'element-plus'
import to from 'await-to-js';
import { useSmsStore } from '@/store/sms'
import { UrlOrigin, disabledFn, shortcuts } from './constant'
import { SmsUrlItem, ShortLinkParamEnum } from '@/type/sms'
import { enum2Options, pickAttrFromObj } from '@/utils/utils'
import dayjs from 'dayjs'
import { trace } from '@/utils/trace'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  isVolcano?: boolean,
  masterAccountList: {
    account: string,
    groupId: string,
  }[],
  type: number, // 0: 普通短链，1：千人千链
  data: SmsUrlItem | null
}>();
const loading = ref(false)
const visible = ref(props.visible)
const editData = reactive<SmsUrlItem>(props.data || new UrlOrigin(props.type))
const editRef = ref<FormInstance  | null>(null)
const title = computed(() => (`${editData.id ? '编辑' : '新增'}${props.type === 0 ? '普通短链' : '千人千链'}`))
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}
const validateUrl = (rule: any, value: any, callback: any) => {
  editData.originalUrl = editData.originalUrl?.replace(/\s/g, '') || ''
  if (!editData.originalUrl) {
    return callback(new Error('请输入原始链接'))
  }
  // if (!urlReg.test(editData.originalUrl)) {
  //   return callback(new Error('原始链接格式有误'))
  // }
  callback()
}
const validateDomain = (rule: any, value: any, callback: any) => {
  if (!domainList.value || domainList.value.length < 1) {
    return callback(new Error('请添加域名'))
  }
  const domainSet: Set<string> = new Set([])
  domainList.value?.forEach((item: any) => {
    if (!item[0] || !item[1]) {
      return callback(new Error('域名信息缺失'))
    }
    if (!domainSet.has(item[0])) {
      domainSet.add(item[0])
    } else {
      return callback(new Error('域名重复'))
    }
  })
  callback()
}
const rules = {
  linkName: [
    { required: true, message: '请输入短链名称', trigger: 'blur' },
    { min: 2, max: 30, message: '短链名称必须在2-30字', trigger: 'blur' },
  ],
  originalUrl: [
    { required: true, message: '请输入原始链接', trigger: 'blur' },
    { validator: validateUrl, trigger: 'blur' },
  ],
  domain: [
    { required: true, message: '请选择域名', trigger: 'change' },
  ],
  domainList: [
    { validator: validateDomain, trigger: 'change' },
  ],
  groupId: [
    { required: true, message: '请选择商户主账号', trigger: 'change' },
  ],
  expireTime: [
    { required: true, message: '请选择过期时间', trigger: 'change' },
  ],
  expireDays: [
    { required: true, message: '请选择有效时长', trigger: 'change' },
  ]
}

const extraParamsOption = enum2Options(ShortLinkParamEnum)
const showMore = ref(false)

const domainList = ref<[string, number | undefined][]>([])
const addDomain = () => {
  if (!domainList.value) {
    domainList.value = []
  }
  if (!domainAvailableList.value || domainList.value.length >= domainAvailableList.value?.length) {
    return ElMessage.warning('域名数超过系统可选择最大值')
  }
  domainList.value.push(['', 1])
}
const delDomain = (index: number) => {
  if (!domainList.value || domainList.value.length < 1) {
    domainList.value = []
    return 
  }
  domainList.value.splice(index, 1)
}

const smsInfo = useSmsStore()
const domainAvailableList = ref<null | string[]>(null)

// 主账号变化后清空域名选项
const handleGroupIdChange = () => {
  domainList.value = []
  editData.domain = undefined
  const arr = editData.groupId?.split('_') || []
  const tenantId = arr[1] || undefined // 商户id,火山商户14
  domainAvailableList.value = tenantId === '14' ? smsInfo.volcanoDomainList : smsInfo.domainList
}

const masterAccountList = ref<{
  account: string,
  groupId: string,
}[] | null>([]) // 主账号列表，全量
const confirm = () => {
  let params = pickAttrFromObj(editData, props.type === 0
    ? ['id', 'originalUrl', 'groupId','domain', 'linkName', 'expireTime', 'needRegister']
    : ['id', 'originalUrl', 'groupId', 'linkName', 'expireDays', 'paramTypes']
  )
  if (props.type === 1) {
    params.domainMap = Object.fromEntries(domainList.value?.map(v => [v[0], v[1]??0]) || [])
  }
  if (props.type === 0) {
    params.expireTime = editData.expireTime ? dayjs(editData.expireTime).format('YYYY-MM-DD') : undefined
  }
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      trace({
        page: `${props.isVolcano ? '火山' : ''}短链管理-${props.type === 1 ? '千人千链' : '普通短链'}-${editData.id ? '编辑' : '新增'}`,
        params
      })
      const [err] = await to(props.type === 0 ? smsUrlModel.saveNormal(params) : smsUrlModel.saveThousand(params))
      
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        cancel()
        emits('confirm')
      }
    }
  })
}

watch(() => props.visible, n => {
  visible.value = n
  if (n) {
    loading.value = false
    masterAccountList.value = props.masterAccountList || []
    Object.assign(editData, new UrlOrigin(props.type), props.data)
    if (props.type === 0) {
      editData.domain = props.data?.domain || undefined
      editData.needRegister = props.data?.id ? false : true
    } else {
      editData.domain = undefined
      domainList.value = Object.entries(props.data?.domainMap || [])
      !props.data?.id && (editData.paramTypes = [
        ShortLinkParamEnum['号码'],
        ShortLinkParamEnum['短信对接账号ID'],
        ShortLinkParamEnum['省'],
        ShortLinkParamEnum['市'],
        ShortLinkParamEnum['尾号'],
      ])
    }
    if (editData.groupId) {
      const arr = editData.groupId?.split('_') || []
      const tenantId = arr[1] || undefined // 商户id,火山商户14
      domainAvailableList.value = tenantId === '14' ? smsInfo.volcanoDomainList : smsInfo.domainList
    }
    editRef.value?.clearValidate()
  } else {
    showMore.value = false
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>