/**
 * 供应端
 */
import { CallStatusEnum } from '@/type/task'

// 供应平台-线路数据
// 供应平台-线路数据-数据详情 请求参数
export interface SupplierPlatformLineRecordSearchParam {
  supplyLineNumber?: string,
  callOutTimeStart: string,
  callOutTimeEnd: string,
  exceptTypes?: string[],
  pageNum?: number,
  pageSize?: number,
  totalCount?: number
}

export enum ErrorTypeEnum {
  '小助理' = 'ROBOT_ASSISTANT',
  '无声通话' = 'NO_SOUND',
  '沉默挂机' = 'SILENCE_HANGUP',
}

// 供应平台-线路数据-数据详情 返回
export interface SupplierPlatformLineRecordItem {
  recordId: string,
  plainPhone?: string, // 明文号码
  province: string
  city: string
  operator: string
  callStatusStr: string
  callStatus: CallStatusEnum
  callDurationSec: number // 通话时长s
  callOutTime: string // 外呼时间
  talkTimeStart: string // 通话开始
  talkTimeEnd: string // 通话结束
  types: ErrorTypeEnum[]
}

