<template>
  <el-scrollbar class="submodule-detail" wrap-class="detail-main">
    <el-form
      ref="formRef"
      label-width="90px"
      label-position="right"
      inline
      :model="form"
      :rules="rules"
      class="tw-w-[756px] tw-mx-auto"
    >
      <div class="form-block">
        <div class="form-block-title">
          基本信息
        </div>
        <el-form-item label="模板编号：">
          <el-input
            v-model.number="form.id"
            placeholder="创建成功后自动生成"
            clearable
            disabled
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item label="业务类型：" prop="businessType">
          <el-select
            v-model="form.businessType"
            placeholder="请选择业务类型"
            style="width: 240px;"
          >
            <el-option
              v-for="BusinessTypeEnumItem in Object.entries(BusinessTypeEnum)"
              :key="BusinessTypeEnumItem[1]"
              :value="BusinessTypeEnumItem[1]"
              :label="BusinessTypeEnumItem[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模板名称：" prop="templateName">
          <el-input
            v-model.trim="form.templateName"
            placeholder="请输入模板名称"
            maxlength="20"
            show-word-limit
            clearable
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item label="适用行业：" prop="secondIndustry">
          <el-cascader
            v-model="form.secondIndustry"
            :options="industryOptions"
            :props="industryProps"
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            :show-all-levels="false"
            placeholder="请选择适用行业"
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item label="短信类型：" prop="smsTemplateType">
          <el-select
            v-model="form.smsTemplateType"
            placeholder="请选择短信类型"
            style="width: 240px;"
            @change="handleSmsTemplateTypeChange"
          >
            <el-option
              v-for="item in Object.entries(SmsTemplateTypeEnum)"
              :key="item[1]"
              :value="item[1]"
              :label="item[0]"
            />
          </el-select>
        </el-form-item>
      </div>

      <div class="form-block">
        <div class="form-block-title">
          运营备注
        </div>
        <el-form-item label="备注：">
          <el-input
            v-model.trim="form.comments"
            type="textarea"
            placeholder="请输入备注，不超过250字"
            clearable
            maxlength="200"
            show-word-limit
            :autosize="true"
            resize="none"
            style="width: 600px;"
          />
        </el-form-item>
      </div>

      <div v-if="form.smsTemplateType && [SmsTemplateTypeEnum['白泽短信'], SmsTemplateTypeEnum['火山短信']].includes(form.smsTemplateType)" class="form-block">
        <div class="form-block-title tw-flex tw-flex-row tw-justify-between">
          {{ findValueInEnum(form.smsTemplateType, SmsTemplateTypeEnum) || '短信' }}内容
          <el-button v-if="form.smsTemplateType === SmsTemplateTypeEnum['白泽短信']" type="primary" size="small" class="tw-mr-[32px]" @click="onClickSmsPreview">
            短信预览
          </el-button>
        </div>
        <el-form-item label="短信签名：" prop="messageSign">
          <el-input
            v-model.trim="form.messageSign"
            placeholder="请输入短信签名"
            maxlength="20"
            show-word-limit
            clearable
            style="width: 240px;"
          />
        </el-form-item>
        <br>
        <el-form-item v-if="form.smsTemplateType === SmsTemplateTypeEnum['白泽短信']" label="短信内容：" prop="messageContent">
          <!--短信内容富文本编辑框-->
          <div
            ref="smsContentRef"
            contenteditable="true"
            class="sms-content"
            @input="onSmsContentInput"
          />
          <div class="tw-flex tw-flex-col tw-text-[13px]">
            <!--短信全文总长度-->
            <div style="color: var(--primary-red-color); font-size: 13px;">
              <span>
                字数预估（含短信签名）：{{ smsLength }}字
              </span>
              <span v-show="exceedSmsLengthLimit">
                ；字数超过69，请进行修改
              </span>
            </div>
            <!--变量工具条-->
            <div class="tw-flex tw-flex-row tw-justify-start tw-items-center">
              <span class="tw-mr-[20px]">
                插入变量：
              </span>
              <el-button
                type="primary"
                size="small"
                link
                @click="onClickSystemVariable('手机尾号')"
              >
                手机尾号
              </el-button>
              <el-button
                type="primary"
                size="small"
                link
                @click="onClickSystemVariable('归属城市')"
              >
                归属城市
              </el-button>
              <el-button
                type="primary"
                size="small"
                link
                @click="onClickSystemVariable('下发日期')"
              >
                下发日期
              </el-button>
              <el-dropdown placement="top" trigger="click" class="tw-ml-[20px]">
                <el-button type="primary" size="small">
                  自定义变量
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="variableItem in variableList"
                      :key="variableItem.id"
                      @click="onClickCustomVariable(variableItem)"
                    >
                      {{ variableItem.variableName }}
                    </el-dropdown-item>
                    <el-dropdown-item @click="onClickAddCustomVariable">
                      <el-icon :size="20" color="#165DFF">
                        <SvgIcon color="#165DFF" name="add1" />
                      </el-icon>
                      <span style="font-size: 13px; color: #165DFF;">
                        新增变量
                      </span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <div class="tw-flex tw-flex-row tw-justify-start tw-items-center">
              <span class="tw-mr-[20px]">
                插入短链：
              </span>
              <el-select
                v-model="selectedUrl"
                size="small"
                value-key="id"
                :disabled="loadingShortUrl"
                @visible-change="onUrlVisibleChange"
              >
                <el-option
                  v-for="urlItem in shortUrlAllList"
                  :key="urlItem.id"
                  :label="urlItem.linkName"
                  :value="urlItem"
                />
              </el-select>
              <el-icon v-show="loadingShortUrl" :size="18" class="loading-icon tw-mx-[8px]">
                <Loading />
              </el-icon>
            </div>
            <div style="color: var(--primary-red-color); font-size: 13px;">
              注意：
              <br>
              1. 请确保短信签名和短信内容与账号侧审核通过的内容一致！
              <br>
              2. 请确保修改变量后，不影响任务中已导入号码的变量匹配！
              <br>
              3. 单条短信模版限制69字符，如含有变量和短链，请确保变量值、短链值计入后短信不超长
              <br>
              4. 插入链接或短链时，为保证客户端可正常点击，请在链接前后各插入一个空格
            </div>
          </div>
        </el-form-item>
        <template v-if="form.smsTemplateType === SmsTemplateTypeEnum['火山短信']">
          <el-form-item label="火山账号ID：" prop="volcanoAccountId">
            <el-input
              v-model.trim="form.volcanoAccountId"
              placeholder="请输入火山账号ID"
              maxlength="50"
              show-word-limit
              clearable
              style="width: 240px;"
            />
          </el-form-item>
          <el-form-item label="火山短信模板ID：" :label-width="120" prop="volcanoSmsTemplateId">
            <el-input
              v-model.trim="form.volcanoSmsTemplateId"
              placeholder="请输入火山短信模板ID"
              maxlength="50"
              show-word-limit
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="关联短链：" prop="shortLinkThousandNumbers">
            <SelectBox
              v-model:selectVal="form.shortLinkThousandNumbers"
              :options="shortThousandUrlList.filter(item => item.enableStatus === 'ENABLE')"
              name="linkName"
              val="linkNumber"
              :limitNum="1"
              placeholder="请选择关联短链"
              filterable
              style="width: 600px;"
              multiple
            >
            </SelectBox>
          </el-form-item>
        </template>
      </div>

      <div class="form-block">
        <div class="form-block-title">
          短信补偿
        </div>
        <el-form-item label="短信补偿：" prop="compensateMessage">
          <el-switch
            v-model="form.compensateMessage"
            style="--el-switch-on-color: #165DFF; --el-switch-off-color: #C8C9CC;"
            inline-prompt
            :active-value="true"
            :inactive-value="false"
            active-text="开启"
            inactive-text="关闭"
            @change="onCompensateMessageChange"
          />
        </el-form-item>
        <br>
        <el-form-item label="补偿范围：" prop="compensateRange" :required="form.compensateMessage">
          <el-checkbox-group v-model="form.compensateRange">
            <el-checkbox :label="SmsConfigCompensateRangeEnum.SUBMIT_FAIL" disabled checked>
              提交失败
            </el-checkbox>
            <el-checkbox :label="SmsConfigCompensateRangeEnum.SEND_FAIL" disabled checked>
              发送失败
            </el-checkbox>
            <el-checkbox :label="SmsConfigCompensateRangeEnum.RECEIPT_TIMEOUT" :disabled="!form.compensateMessage">
              回执超时
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="补偿时效：" prop="compensateEffectivePeriod" :required="form.compensateMessage">
          距初次触发时间
          <el-input
            v-model.trim.number="form.compensateEffectivePeriod"
            type="number"
            :min="compensateEffectivePeriodMin"
            :max="compensateEffectivePeriodMax"
            placeholder="请输入"
            :disabled="!form.compensateMessage"
            class="tw-mx-[8px]"
            style="width: 100px;"
          />
          秒内，执行短信补偿
        </el-form-item>
        <br>
        <el-form-item label="补偿时段：" prop="compensateStartTime" :required="form.compensateMessage">
          <div class="tw-flex tw-justify-start tw-items-center" style="min-width: 240px;">
            <TagsBox :tagsArr="timeSlotStrList" tagsName="选中时间段" :total="true" :wrap="true" />
            <el-button link type="primary" :disabled="!form.compensateMessage" @click="onClickTimeSlot">
              选择时间段
            </el-button>
          </div>
        </el-form-item>
        <br>
        <el-form-item label="补偿次数：" prop="compensateCount">
          <el-input
            v-model.trim.number="form.compensateCount"
            type="number"
            min="1"
            placeholder="请输入"
            clearable
            :disabled="!form.compensateMessage"
            class="tw-mr-[8px]"
            style="width: 100px;"
          />
          次
        </el-form-item>
      </div>
    </el-form>
  </el-scrollbar>

  <!--变量填写弹窗-->
  <VariableFillDialog
    v-model:visible="variableFillDialogVisible"
    :list="customVariableList"
    @confirm="onVariableFillDialogConfirm"
  />

  <!--短信预览弹窗-->
  <SmsPreviewDialog
    v-model:visible="smsPreviewDialogVisible"
    :content="smsContentForPreview"
    @close="onSmsPreviewDialogClose"
  />

  <!--自定义变量弹窗-->
  <CustomVariableEditDialog
    v-model:visible="customVariableDialogVisible"
    :data="{}"
    :isEdit="false"
    @confirm="onCustomVariableEditDialogConfirm"
  />

  <!--时间段弹窗-->
  <TimeRangePickerDialog
    v-model:visible="timeRangePickerDialogVisible"
    format="HH:mm"
    defaultStart="00:00"
    defaultEnd="24:00"
    :edit-data="{startWorkTimeList, endWorkTimeList}"
    @confirm="onUpdateTimeRangerPicker"
  />
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { CascaderProps, ElMessage, FormRules } from 'element-plus'
import {
  SmsConfigCompensateRangeEnum,
  SmsConfigCompensateRangeValList,
  SmsConfigItem,
  SmsVariableItem,
  SmsVariableParams,
  SystemVariableEnum
} from '@/type/merchant'
import { useGlobalStore } from '@/store/globalInfo'
import {
  BusinessTypeEnum,
  DeltaAttributeItem,
  SmsContentTagEnum,
  SmsTemplateTypeEnum,
  SmsUrlItem,
  VariableSmsPojoItem
} from '@/type/sms'
import { convertTimeSlotComponentToDisplay, deduplicateBaseArray, findValueInEnum, Throttle } from '@/utils/utils'
import TagsBox from '@/components/TagsBox.vue'
import TimeRangePickerDialog from '@/components/TimeRangePickerDialog.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { useMerchantStore } from '@/store/merchant'
import to from 'await-to-js'
import { merchantSmsVariableModel } from '@/api/merchant'
import CustomVariableEditDialog from '@/views/operator/merchant-manager/SmsVariable/EditDialog.vue'
import { smsUrlModel } from '@/api/sms'
import VariableFillDialog from '@/views/operator/merchant-manager/SmsTemplate/Sms/VariableFillDialog.vue'
import SmsPreviewDialog from '@/components/sms/SmsPreviewDialog.vue'
import { Loading } from '@element-plus/icons-vue'
import { formatSmsContent } from '@/views/operator/merchant-manager/SmsTemplate/Sms/common'
import { storeToRefs } from 'pinia'
import { useSmsStore } from '@/store/sms'
import Delta, { Op } from 'quill-delta'
import SelectBox from '@/components/SelectBox.vue';

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  data: SmsConfigItem,
}>(), {
  data: () => ({}),
})
const emits = defineEmits([
  'update',
])

const globalStore = useGlobalStore()
const merchantStore = useMerchantStore()
const { currentAccount } = storeToRefs(merchantStore)
const smsStore = useSmsStore()
const { smsLength } = storeToRefs(smsStore)

// 默认开始时间
const defaultStartTime = '08:00'
// 默认结束时间
const defaultEndTime = '18:00'

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 短信补偿时效，最小值，单位秒
const compensateEffectivePeriodMin = ref(1)
// 短信补偿时效，最大值，单位秒
const compensateEffectivePeriodMax = ref(24 * 3600)

// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): SmsConfigItem => ({
  id: undefined,
  businessType: BusinessTypeEnum['营销短信'],
  smsTemplateType: SmsTemplateTypeEnum['白泽短信'],
  templateName: '',
  secondIndustry: '',

  comments: '',

  messageSign: '',
  messageContent: '',
  volcanoAccountId: '',
  volcanoSmsTemplateId: '',
  shortLinkOrdinaryNumbers: [],
  shortLinkThousandNumbers: [],

  compensateMessage: false,
  compensateRange: SmsConfigCompensateRangeValList.ENABLE_RECEIPT,
  compensateEffectivePeriod: undefined,
  compensateStartTime: defaultStartTime,
  compensateEndTime: defaultEndTime,
  compensateCount: 3,
})
// 表单数据
const form: SmsConfigItem = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  businessType: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('业务类型' + '不能为空'))
      } else {
        callback()
      }
    }
  },
  templateName: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('模板名称' + '不能为空'))
      } else {
        callback()
      }
    }
  },
  secondIndustry: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('适用行业' + '不能为空'))
      } else {
        callback()
      }
    }
  },
  messageSign: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('短信签名' + '不能为空'))
      } else {
        callback()
      }
    }
  },
  messageContent: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('短信内容' + '不能为空'))
      } else {
        callback()
      }
    }
  },
  volcanoAccountId: { required: true, message: '请输入火山账号ID', trigger: 'blur', },
  volcanoSmsTemplateId: { required: true, message: '请输入火山短信模板ID', trigger: 'blur', },
  compensateRange: {
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!form.compensateMessage) {
        // 关闭短信补偿，不校验
        callback()
      } else {
        if (!value?.length) {
          callback(new Error('补偿范围' + '不正确'))
        } else {
          callback()
        }
      }
    }
  },
  compensateEffectivePeriod: {
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!form.compensateMessage) {
        // 关闭短信补偿，不校验
        callback()
      } else {
        if (typeof value === 'number'
          && compensateEffectivePeriodMin.value <= value
          && value <= compensateEffectivePeriodMax.value) {
          callback()
        } else {
          callback(new Error('补偿时效范围为'
            + `${compensateEffectivePeriodMin.value}s-${compensateEffectivePeriodMax.value / 3600}h`))
        }
      }
    }
  },
  compensateStartTime: {
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!form.compensateMessage) {
        // 关闭短信补偿，不校验
        callback()
      } else {
        if (!value) {
          callback(new Error('补偿时段' + '不能为空'))
        } else {
          callback()
        }
      }
    }
  },
  compensateCount: {
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      // 补偿次数可以为空，表示无限制
      callback()
    }
  },
})
/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(form, formDefault())
}
/**
 * 更新表单
 */
const updateForm = () => {
  // 提取出当前表单需要的属性，而不是直接照搬整个对象
  form.id = (typeof props.data.id === 'number') ? props.data.id : undefined
  form.businessType = props.data.businessType || BusinessTypeEnum['营销短信']
  form.templateName = props.data.templateName || ''
  form.secondIndustry = props.data.secondIndustry || ''

  form.comments = props.data.comments || ''
  form.messageSign = props.data.messageSign || ''
  form.smsTemplateType = props.data.smsTemplateType || SmsTemplateTypeEnum['白泽短信']

  if (form.smsTemplateType === SmsTemplateTypeEnum['白泽短信']) {
    form.volcanoAccountId = ''
    form.volcanoSmsTemplateId = ''
    form.messageContent = props.data.messageContent || ''
    formToData(form.messageContent)
    dataToHtml(smsContentDelta)
    updateFormShortUrlNumberList()
  } else if (form.smsTemplateType === SmsTemplateTypeEnum['火山短信']) {
    form.volcanoAccountId = props.data.volcanoAccountId || ''
    form.volcanoSmsTemplateId = props.data.volcanoSmsTemplateId || ''
    form.messageContent = ''
    form.shortLinkThousandNumbers = props.data.shortLinkThousandNumbers || []
  } else if (form.smsTemplateType === SmsTemplateTypeEnum['M短信']) {
    form.messageContent = ''
    form.messageSign = ''
    form.shortLinkThousandNumbers = []
    form.shortLinkOrdinaryNumbers = []
  }

  form.compensateMessage = !!props.data.compensateMessage

  form.compensateRange = props.data.compensateRange?.length
    ? props.data.compensateRange
    : SmsConfigCompensateRangeValList.ENABLE_RECEIPT
  form.compensateEffectivePeriod = props.data.compensateEffectivePeriod ?? undefined
  form.compensateStartTime = props.data.compensateStartTime ?? formDefault().compensateStartTime ?? undefined
  form.compensateEndTime = props.data.compensateEndTime ?? formDefault().compensateEndTime ?? undefined
  form.compensateCount = props.data.compensateCount ?? null
}
// 子组件的表单发生更新，通知父组件更新
watch(form, (val) => {
  emits('update', val)
}, {
  deep: true,
})

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 行业 开始 ----------------------------------------

// 搜索条件，行业，级联选择器，数据内容
const industryOptions = ref<any[]>([])
// 搜索条件，行业，级联选择器，配置信息
const industryProps: CascaderProps = {
  multiple: false,
  emitPath: false,
  value: 'name',
  label: 'name',
  children: 'secondaryIndustries',
}

/**
 * 获取行业列表
 */
const getIndustryList = async () => {
  // console.log('获取行业列表')
  try {
    await globalStore.getAllIndustryList()
    industryOptions.value = globalStore.getIndustryOption
  } catch (e) {
    ElMessage.error('无法正确获取行业列表')
  } finally {
  }
}

// ---------------------------------------- 行业 结束 ----------------------------------------

// ---------------------------------------- 短信类型 开始 ----------------------------------------

const handleSmsTemplateTypeChange = () => {
  // 切换类型清空短链
  form.shortLinkOrdinaryNumbers = []
  form.shortLinkThousandNumbers = []
  form.messageContent = ''
  // 切换类型清空短信内容
  if (form.smsTemplateType === SmsTemplateTypeEnum['白泽短信']) {
    formToData(form.messageContent)
    dataToHtml(smsContentDelta)
    updateFormShortUrlNumberList()
  }
  // 切换类型清空火山属性
  if (form.smsTemplateType !== SmsTemplateTypeEnum['火山短信']) {
    form.volcanoAccountId = ''
    form.volcanoSmsTemplateId = ''
  }
  // 切换M短信清空短信签名
  if (form.smsTemplateType === SmsTemplateTypeEnum['M短信']) {
    form.messageSign = ''
  }
}

// ---------------------------------------- 短信类型 结束 ----------------------------------------

// ---------------------------------------- 短信内容 开始 ----------------------------------------

/**
 * 短信内容，在前端页面上处理展示时，分为三层：
 * 顶层 html 用户展示层：也是HTML模板层，是用户所见即所得的页面内容，纯文本用原生HTML展示，变量和短链用element-plus的tag组件实现；
 * 中层 data 数据驱动层：将用户展示层和表单数据层，抽象整理成一种通用的文档结构，方便互相转换，本代码采用的是和Quill这款富文本编辑器实现的Delta的文档结构；
 * 底层 form 表单数据层：需要传给接口的最终参数，纯文本保持不动，变量和短链都加上双花括号来和纯文本区分。
 */

// 短信内容DOM
const smsContentRef = ref<HTMLElement | null>(null)
// 短信全文长度是否超过限制
const exceedSmsLengthLimit = computed(() => {
  return 69 < smsLength.value
})
// 短信内容，当前光标选中范围
let smsContentRange: Range | null = null
// 短信内容，Delta文档结构，作为数据驱动层
let smsContentDelta: Delta = new Delta()

/**
 * 短信内容，表单数据层转换到数据驱动层
 * @param {string} str 表单数据层的字符串
 */
const formToData = (str: string = '') => {
  // console.log('formToData', 'form', str)
  // 按花括号进行分割，得到子字符串列表
  const list = str.split(/{{|}}/)
  // console.log('formToData', 'list', list)
  // 无论什么情况，变量或短链都是在数组的奇数索引里
  // 你看，如果开头是普通文本，索引0，那第一个出现的变量或短链，索引是1；
  // 如果开头是变量或短链，没有普通文本，但是split方法会在索引0放一个空字符串，所以此时的变量或短链，索引依然是1；
  // 后续就是 普通文本 和 (变量或短链) 交替出现
  list.forEach((str: string, index: number) => {
    if (index % 2 === 1) {
      // 奇数索引，变量或短链
      if (str.includes('shortOrdinaryId:')) {
        // 普通短链
        // 根据短链编号找到对应短链
        const linkNumber = str.split(':').at(1) ?? ''
        const urlItem = shortOrdinaryUrlList.value.find((item: SmsUrlItem) => {
          return item.linkNumber === linkNumber
        })
        // 得到短链名称，加入到文档结构中
        const linkName = urlItem ? (urlItem?.linkName ?? '') : linkNumber
        smsContentDelta.insert(linkName, {
          tag: SmsContentTagEnum['普通短链'],
          display: linkName,
          value: linkNumber,
          timestamp: window.performance.now(),
        })
      } else if (str.includes('shortThousandId:')) {
        // 千人千链
        // 根据短链编号找到对应短链
        const linkNumber = str.split(':').at(1) ?? ''
        const urlItem = shortThousandUrlList.value.find((item: SmsUrlItem) => {
          return item.linkNumber === linkNumber
        })
        // 得到短链名称，加入到文档结构中
        const linkName = urlItem ? (urlItem?.linkName ?? '') : linkNumber
        smsContentDelta.insert(linkName, {
          tag: SmsContentTagEnum['千人千链'],
          display: linkName,
          value: linkNumber,
          timestamp: window.performance.now(),
        })
      } else {
        // 变量
        // 区分系统内置变量和自定义变量
        // 系统变量 文本展示用变量说明 variableComment 数据驱动用变量名称 variableName
        // 自定义变量 文本展示用变量名称 variableName 数据驱动用变量名称 variableName
        const variableName = str
        // [variableComment, variableName]
        const systemVariableItem: [string, SystemVariableEnum] | undefined = Object.entries(SystemVariableEnum).find(([_, value]) => {
          return value === variableName
        })
        const variableComment = systemVariableItem ? (systemVariableItem.at(0) ?? '') : variableName
        // 得到变量信息，加入到文档结构中
        smsContentDelta.insert(variableName, {
          tag: SmsContentTagEnum['变量'],
          display: variableComment,
          value: variableName,
          timestamp: window.performance.now(),
        })
      }
    } else {
      // 偶数索引，普通文本
      smsContentDelta.insert(str)
    }
  })
  // console.log('formToData', 'delta.ops.insert', smsContentDelta?.ops.map((op: Op) => (op.insert)))
  // console.log('formToData', 'delta', smsContentDelta)
}
/**
 * 短信内容，数据驱动层转换到用户展示层
 * @param {Delta} delta 表单数据层的文档结构
 */
const dataToHtml = (delta: Delta) => {
  // 短信内容DOM
  if (!smsContentRef.value) {
    return
  }

  let html = ''
  delta.ops?.forEach((op: Op) => {
    if (op?.attributes?.tag === SmsContentTagEnum['变量']) {
      // 变量
      html += `<span class="el-tag el-tag--primary el-tag-variable" contenteditable="false">${op.attributes.display}</span>`
    } else if (op?.attributes?.tag === SmsContentTagEnum['普通短链']) {
      // 普通短链
      html += `<span class="el-tag el-tag--success el-tag-ordinary" contenteditable="false">${op.attributes.display}</span>`
    } else if (op?.attributes?.tag === SmsContentTagEnum['千人千链']) {
      // 千人千链
      html += `<span class="el-tag el-tag--success el-tag-thousand" contenteditable="false">${op.attributes.display}</span>`
    } else {
      // 普通文本
      // 将换行符替换为换行标签
      html += op.insert ? (<string>op.insert).replaceAll('\n', '<div></div>') : ''
    }
  })

  smsContentRef.value.innerHTML = html
  // console.log('dataToHtml', 'html', smsContentRef.value.innerHTML)
}
/**
 * 短信内容，用户展示层转换到数据驱动层
 */
const htmlToData = () => {
  // 短信内容DOM
  if (!smsContentRef.value) {
    return
  }

  smsContentDelta = new Delta()

  /**
   * 处理标签节点并插入到 Delta 文档结构中
   * @param span 标签元素
   * @param tagType 标签类型
   * @param findItem 查找对应项的函数
   */
  const handleTagNode = (span: HTMLElement, tagType: SmsContentTagEnum, findItem: (text: string) => any) => {
    const text = span.innerText
    const item = findItem(text)
    const value = item ? (item?.linkNumber ?? item?.variableName) : text
    smsContentDelta.insert(text, {
      tag: tagType,
      display: text,
      value,
      timestamp: window.performance.now(),
    })
  }

  // 缓存短链列表
  const ordinaryUrlMap = new Map(shortOrdinaryUrlList.value.map(item => [item.linkName, item]))
  const thousandUrlMap = new Map(shortThousandUrlList.value.map(item => [item.linkName, item]))

  // 查找系统变量
  const findSystemVariable = (text: string) => {
    const systemVariableItem = Object.entries(SystemVariableEnum).find(([key, _]) => key === text)
    return systemVariableItem ? { variableName: systemVariableItem.at(1) } : undefined
  }

  /**
   * 处理子节点
   * @param parentNode 父节点
   */
  const handleChildNodes = (parentNode: Node) => {
    if (!parentNode.childNodes) return

    for (const node of parentNode.childNodes) {
      if (node.nodeType !== Node.ELEMENT_NODE) {
        // 普通文本
        smsContentDelta.insert(<string>node.textContent ?? '')
        continue
      }

      const element = <HTMLElement>node
      if (element.nodeName === 'DIV') {
        // div标签
        if (element.childNodes.length) {
          handleChildNodes(element)
          smsContentDelta.insert('\n')
        }
        continue
      }

      if (element.nodeName === 'SPAN' && element.classList.contains('el-tag')) {
        if (element.classList.contains('el-tag-variable')) {
          // 变量
          handleTagNode(element, SmsContentTagEnum['变量'], findSystemVariable)
        } else if (element.classList.contains('el-tag-ordinary')) {
          // 普通短链
          handleTagNode(element, SmsContentTagEnum['普通短链'], (text) => ordinaryUrlMap.get(text))
        } else if (element.classList.contains('el-tag-thousand')) {
          // 千人千链
          handleTagNode(element, SmsContentTagEnum['千人千链'], (text) => thousandUrlMap.get(text))
        } else {
          // 其他
          handleTagNode(element, SmsContentTagEnum['变量'], () => undefined)
        }
        continue
      }

      // 其他
      smsContentDelta.insert(<string>element.textContent ?? '')
    }
  }

  // 遍历短信内容DOM的所有子节点
  handleChildNodes(smsContentRef.value)

  // 移除最后一个多余的换行符
  const lastOp = smsContentDelta.ops.at(-1)
  if (lastOp && typeof lastOp.insert === 'string' && lastOp.insert.endsWith('\n')) {
    lastOp.insert = lastOp.insert.slice(0, -1)
  }

  // console.log('htmlToData', 'delta.ops.insert', smsContentDelta?.ops.map((op: Op) => (op.insert)))
  // console.log('htmlToData', 'delta', smsContentDelta)
}
/**
 * 短信内容，数据驱动层转换到表单数据层
 * @param {Delta} delta 数据驱动层的文档结构
 */
const dataToForm = (delta: Delta) => {
  let str = ''
  delta?.ops?.forEach((op: Op) => {
    // console.log('op', op)
    const attr = <DeltaAttributeItem><unknown>op?.attributes
    if (!attr) {
      // 普通文本
      str += op.insert
    } else if (attr.tag === SmsContentTagEnum['变量']) {
      // 变量
      str += `{{${attr.value}}}`
    } else if (attr.tag === SmsContentTagEnum['普通短链']) {
      // 普通短链
      str += `{{shortOrdinaryId:${attr.value}}}`
    } else if (attr.tag === SmsContentTagEnum['千人千链']) {
      // 千人千链
      str += `{{shortThousandId:${attr.value}}}`
    } else {
      // 其他
      str += op.insert
    }
  })
  form.messageContent = str
  // console.log('dataToForm', 'form.messageContent', form.messageContent)
}
/**
 * 插入短信标签
 * @param type 短信标签类型
 * @param val 短信标签内容
 */
const insertSmsTag = async (type: SmsContentTagEnum = SmsContentTagEnum['变量'], val: string = '') => {
  // 标签元素
  const element = document.createElement(`span`)
  if (type === SmsContentTagEnum['变量']) {
    element.setAttribute('class', 'el-tag el-tag--primary el-tag-variable')
  } else if (type === SmsContentTagEnum['普通短链']) {
    element.setAttribute('class', 'el-tag el-tag--success el-tag-ordinary')
  } else if (type === SmsContentTagEnum['千人千链']) {
    element.setAttribute('class', 'el-tag el-tag--success el-tag-thousand')
  } else {
    element.setAttribute('class', 'el-tag el-tag--info')
  }
  element.setAttribute('contenteditable', 'false')
  element.innerText = val

  // 有光标或者选中范围，就在光标处插入或者替换选中内容
  // 没有就直接追加到最后面
  if (smsContentRange) {
    smsContentRange.deleteContents()
    smsContentRange.insertNode(element)
  } else {
    smsContentRef.value?.appendChild(element)
  }

  await nextTick()
  smsContentRange = null
  // 更新数据驱动层的文档结构
  htmlToData()
  // 更新表单数据层的字符串
  dataToForm(smsContentDelta)

  // 更新表单里的短链编号列表
  updateFormShortUrlNumberList()
}
/**
 * 点击删除标签
 * @param event
 * @deprecated
 */
const onClickDeleteTag = (event: MouseEvent) => {
  const self = (event.target as HTMLElement)?.parentElement?.parentElement
  const parent = self?.parentElement
  if (parent) {
    parent.removeChild(self)
  }
}
/**
 * 短信内容输入变化时
 */
const onSmsContentInput = async () => {
  await nextTick()
  // 富文本是从HTML结构也就是用户展示层变化的
  // 所以需要转换为数据驱动层的文档结构，再进一步转换为表单数据层的字符串
  htmlToData()
  dataToForm(smsContentDelta)
  // 更新表单里的短链编号列表
  updateFormShortUrlNumberList()
}
// 短信签名或短信内容发生变化，重新计算短信全文长度
watch([() => form.messageSign, () => form.messageContent], () => {
  // 计算短信全文长度
  smsStore.computeSmsTotalLength(form.messageSign, form.messageContent)
})

// ---------------------------------------- 短信内容 结束 ----------------------------------------

// ---------------------------------------- 短信预览 开始 ----------------------------------------

// 变量填写弹窗，是否显示
const variableFillDialogVisible = ref(false)
// 短信预览弹窗，是否显示
const smsPreviewDialogVisible = ref(false)

// 自定义变量列表
const customVariableList = ref<VariableSmsPojoItem[]>([])
// 用于预览的短信内容
const smsContentForPreview = ref<string>('')

/**
 * 点击短信预览按钮
 *
 * 判断短信内容是否存在自定义变量
 * 如果有，则显示变量填写弹窗
 * 如果没有，则显示短信预览弹窗
 */
const onClickSmsPreview = () => {
  // TODO 更新普通短链和千人千链的展示
  // 重置自定义变量列表
  customVariableList.value = []
  // 遍历当前商户账号的所有变量列表
  variableList.value?.forEach((item: SmsVariableItem) => {
    const name = '{{' + item.variableName + '}}'
    if (form.messageContent?.includes(name) && item.variableName) {
      customVariableList.value.push({
        variableName: item.variableName,
        variableType: item.columnType,
        variableValue: null,
      })
    }
  })
  if (customVariableList.value.length) {
    // 如果确实用到自定义变量，则显示变量填写弹窗
    variableFillDialogVisible.value = true
  } else {
    // 否则拼接短信签名和短信内容后，直接显示短信预览弹窗
    smsContentForPreview.value = formatSmsContent(form.messageSign, form.messageContent, [])
    smsPreviewDialogVisible.value = true
  }
}
/**
 * 变量填写弹窗，表单提交
 * @param list 填写好的自定义变量列表
 */
const onVariableFillDialogConfirm = (list: VariableSmsPojoItem[]) => {
  // 短信预览，格式化短信内容
  smsContentForPreview.value = formatSmsContent(form.messageSign, form.messageContent, list)
  // 显示短信预览弹窗
  smsPreviewDialogVisible.value = true
}
/**
 * 短信预览弹窗，弹窗关闭
 */
const onSmsPreviewDialogClose = () => {
  // 重置中间变量
  customVariableList.value = []
  smsContentForPreview.value = ''
}

// ---------------------------------------- 短信预览 结束 ----------------------------------------

// ---------------------------------------- 系统内置变量 开始 ----------------------------------------

/**
 * 点击系统内置变量
 * @param val 变量名
 */
const onClickSystemVariable = (val: keyof typeof SystemVariableEnum) => {
  // 系统内置变量，手机尾号和归属城市，主账号加密时，不支持使用
  if (currentAccount.value.isForEncryptionPhones) {
    if (val === '手机尾号' || val === '归属城市') {
      ElMessage.warning('加密账号不支持使用该变量')
      return
    }
  }

  // 插入变量
  insertSmsTag(SmsContentTagEnum['变量'], val)
}

// ---------------------------------------- 系统内置变量 结束 ----------------------------------------

// ---------------------------------------- 自定义变量 开始 ----------------------------------------

// 列表，全部，接口数据
const variableList = ref<SmsVariableItem[]>([])

/**
 * 更新全部列表
 */
const updateVariableList = async () => {
  // 处理参数
  const params: SmsVariableParams = {
    groupId: currentAccount.value.groupId ?? '',
  }

  // 请求接口
  const [err, res] = <[any, SmsVariableItem[]]>await to(merchantSmsVariableModel.getSmsVariable(params))

  // 返回失败结果
  if (err) {
    ElMessage.error('无法获取短信变量列表')
    variableList.value = []
    return
  }

  // 返回成功结果
  // 更新全部列表
  variableList.value = res?.length ? res : []
}
/**
 * 点击自定义变量
 * @param item 短信变量信息
 */
const onClickCustomVariable = (item: SmsVariableItem) => {
  insertSmsTag(SmsContentTagEnum['变量'], item.variableName)
}
/**
 * 点击添加自定义变量
 */
const onClickAddCustomVariable = () => {
  // 显示自定义变量编辑弹窗
  customVariableDialogVisible.value = true
}

// ---------------------------------------- 自定义变量 结束 ----------------------------------------

// ---------------------------------------- 自定义变量编辑弹窗 开始 ----------------------------------------

// 编辑弹窗，是否显示
const customVariableDialogVisible = ref<boolean>(false)

/**
 * 编辑弹窗，提交表单
 */
const onCustomVariableEditDialogConfirm = () => {
  // 刷新自定义变量列表
  updateVariableList()
}

// ---------------------------------------- 自定义变量编辑弹窗 结束 ----------------------------------------

// ---------------------------------------- 短链 开始 ----------------------------------------

// 短链列表，正在加载
const loadingShortUrl = ref<boolean>(false)
// 短链列表，提交节流锁
const throttleShortUrl = new Throttle(loadingShortUrl)
// 短链列表，普通短链，接口数据
const shortOrdinaryUrlList = ref<SmsUrlItem[]>([])
// 短链列表，千人千链，接口数据
const shortThousandUrlList = ref<SmsUrlItem[]>([])
// 短链列表，全部，接口数据
const shortUrlAllList = ref<SmsUrlItem[]>([])
// 选中的短链
const selectedUrl = ref<SmsUrlItem>({})

/**
 * 更新全部短链URL列表
 */
const updateUrlList = async () => {
  // 节流锁上锁
  if (throttleShortUrl.check()) {
    return
  }
  throttleShortUrl.lock()

  // 初始化
  shortUrlAllList.value = []
  shortOrdinaryUrlList.value = []
  shortThousandUrlList.value = []

  // 处理参数
  const params = {
    groupId: currentAccount.value.groupId ?? '',
  }

  // 请求接口
  // 普通短链
  const [err1, res1] = <[any, SmsUrlItem[]]>await to(smsUrlModel.findNormalListByGroupId(params))
  // 千人千链
  const [err2, res2] = <[any, SmsUrlItem[]]>await to(smsUrlModel.findThousandListByGroupId(params))

  // 返回失败结果
  if (err1 && err2) {
    ElMessage({
      message: '无法获取短链列表',
      type: 'error',
    })
    shortOrdinaryUrlList.value = []
    shortThousandUrlList.value = []
    shortUrlAllList.value = []
    // 节流锁解锁
    throttleShortUrl.unlock()
    return
  }

  // 返回成功或部分成功结果
  // 更新全部列表
  // 分开的列表是方便查询映射，所以不需要按条件剔除，应该全部保留
  // 合并的列表是展示在下拉选择框里的，需要剔除冻结状态的
  let tempAllList = []
  if (res1) {
    shortOrdinaryUrlList.value = res1?.length ? res1 : []
    tempAllList.push(...shortOrdinaryUrlList.value)
  }
  if (res2) {
    shortThousandUrlList.value = res2?.length ? res2 : []
    tempAllList.push(...shortThousandUrlList.value)
  }
  shortUrlAllList.value = tempAllList.filter((item: SmsUrlItem) => {
    // 保留启用状态的，剔除冻结状态的
    return item?.enableStatus === 'ENABLE'
  })
  // 节流锁解锁
  throttleShortUrl.unlock()
}
/**
 * 短链列表，下拉选择框显示隐藏时
 * @param visible 显示隐藏状态
 */
const onUrlVisibleChange = async (visible: boolean) => {
  // 下拉框从隐藏到显示时
  if (visible) {
    return
  }

  // 下拉框从显示到隐藏时
  await nextTick()

  // console.log('selectedUrl.value', JSON.parse(JSON.stringify(selectedUrl.value)))
  // 如果没选
  if (selectedUrl.value.id === undefined) {
    return
  }
  // 如果选中
  // 插入到短信内容中
  // let str: string
  if (selectedUrl.value.shortUrl) {
    // 普通短链
    // str = `{{shortOrdinaryId:${selectedUrl.value.linkNumber}}}`
    await insertSmsTag(SmsContentTagEnum['普通短链'], selectedUrl.value.linkName)
  } else {
    // 千人千链
    // str = `{{shortThousandId:${selectedUrl.value.linkNumber}}}`
    await insertSmsTag(SmsContentTagEnum['千人千链'], selectedUrl.value.linkName)
  }
  // 清空选中
  selectedUrl.value = {}
  // 更新表单里的短链编号列表
  updateFormShortUrlNumberList()
}
/**
 * 更新表单里的短链编号列表
 */
const updateFormShortUrlNumberList = () => {
  // 从短信内容里找到短链，收集到数组里，然后去重
  const list: string[] = (form.messageContent ?? '').split(/{{|}}/)

  // 普通短链
  const ordinaryList: string[] = list.filter((str: string) => {
    return str.includes('shortOrdinaryId:')
  }).map((str: string) => {
    return str.split(':').at(1) ?? ''
  })
  form.shortLinkOrdinaryNumbers = deduplicateBaseArray(ordinaryList)

  // 千人千链
  const thousandList: string[] = list.filter((str: string) => {
    return str.includes('shortThousandId:')
  }).map((str: string) => {
    return str.split(':').at(1) ?? ''
  })
  form.shortLinkThousandNumbers = deduplicateBaseArray(thousandList)
}

// ---------------------------------------- 短链 结束 ----------------------------------------

// ---------------------------------------- 短信补偿 开始 ----------------------------------------

/**
 * 短信补偿，开关切换
 * @param {boolean} val 开关状态
 */
const onCompensateMessageChange = (val: boolean) => {
  if (val) {
    // 短信补偿从关闭变成开启
  } else {
    // 短信补偿从开启变成关闭
    // 清空短信补偿相关的表单项校验结果
    formRef.value && formRef.value.clearValidate([
      'compensateRange',
      'compensateEffectivePeriod',
      'compensateStartTime',
      'compensateCount',
    ])
  }
}

// ---------------------------------------- 短信补偿 结束 ----------------------------------------

// ---------------------------------------- 时间段 开始 ----------------------------------------

// 显示时间段弹窗
const timeRangePickerDialogVisible = ref(false)
// 时间段开始位置，组件数据
const startWorkTimeList = ref<string[]>([defaultStartTime])
// 时间段开始位置，组件数据
const endWorkTimeList = ref<string[]>([defaultEndTime])
// 时间段展示文本
const timeSlotStrList = ref<string[]>([])

/**
 * 点击选择时间段按钮
 */
const onClickTimeSlot = () => {
  timeRangePickerDialogVisible.value = true
}
/**
 * 时间段，接口数据转换成组件数据
 */
const convertApiToComponentTimeList = () => {
  startWorkTimeList.value = form.compensateStartTime?.split(',') ?? []
  endWorkTimeList.value = form.compensateEndTime?.split(',') ?? []
}
/**
 * 时间段，组件数据转换成接口数据
 */
const convertComponentToApiTimeList = () => {
  form.compensateStartTime = startWorkTimeList.value?.join(',') ?? ''
  form.compensateEndTime = endWorkTimeList.value?.join(',') ?? ''
}
/**
 * 时间段，更新展示文本
 */
const updateTimeSlotStr = () => {
  timeSlotStrList.value = convertTimeSlotComponentToDisplay(startWorkTimeList.value, endWorkTimeList.value)
}
/**
 * 时间段弹窗通知父组件
 * @param data 时间段，组件数据
 */
const onUpdateTimeRangerPicker = (data: {
  startWorkTimeList: string[],
  endWorkTimeList: string[],
}) => {
  startWorkTimeList.value = data.startWorkTimeList ?? []
  endWorkTimeList.value = data.endWorkTimeList ?? []
  convertComponentToApiTimeList()
  updateTimeSlotStr()
}

// ---------------------------------------- 时间段 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

const onSelectionChange = (event: Event) => {
  if ((<Document>event?.target)?.activeElement === smsContentRef.value) {
    // 在短信内容富文本编辑器内
    const selection = document.getSelection()
    if (selection) {
      smsContentRange = selection.getRangeAt(0)
      // console.log('smsContentRange', smsContentRange)
    }
  }
}
onMounted(async () => {
  await getIndustryList()
  await updateVariableList()
  await updateUrlList()

  resetForm()
  updateForm()
  convertApiToComponentTimeList()
  updateTimeSlotStr()

  document.addEventListener('selectionchange', onSelectionChange)
})
onUnmounted(() => {
  document.removeEventListener('selectionchange', onSelectionChange)
})
defineExpose({
  formRef,
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.sms-content {
  width: 100%;
  height: auto;
  min-height: 60px;
  margin: 8px 0;
  padding: 4px 8px;
  border: 1px solid #E5E7EB;
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  font-size: 13px;
  line-height: 30px;
  outline: none;
  word-break: break-all;
  &:focus {
    border: 1px solid var(--primary-blue-color);
  }
  /* 空内容时的占位符 */
  &:empty::before {
    content: '请输入短信内容';
    font-size: 13px;
    color: var(--primary-gray-color);
    user-select: none;
    cursor: text;
  }
  :deep(.el-tag) {
    margin: 0 4px;
    user-select: none;
  }
}
</style>
