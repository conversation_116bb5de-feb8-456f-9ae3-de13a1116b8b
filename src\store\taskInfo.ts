import { defineStore } from "pinia";
import dayjs from 'dayjs'
import { SpeechCraftInfoItem, SpeechCraftStatusEnum, } from '@/type/speech-craft'
import { clueManagerModel, formSettingModel } from '@/api/clue'
import { useUserStore } from '@/store/user'
import { scriptTableModel, } from '@/api/speech-craft'
import { aiOutboundTaskTemplateModel, aiOutboundTaskModel } from'@/api/ai-report'
import { SeatTeam, SeatMember } from '@/type/seat'
import { inspectionModel } from '@/api/Inspection'
import { CallSetting } from '@/type/seat'
import { callSettingModel } from '@/api/seat'
import { CollectionFormItem } from '@/type/clue'
import { RecordTypeEnum, TaskTypeEnum } from '@/type/task'
import to from 'await-to-js';

const scriptAllList: SpeechCraftInfoItem[] = []
// const taskTemplateList: TemplateBaseItem[] = []
const batchTaskIds:number[] = []
const callTeamList:SeatTeam[] = []
const callTeamSeatList:SeatMember[] = []
const callGroupSeatList:SeatMember[] = []
const enableFormSetting: CollectionFormItem[]  = []
const batchTaskInfo: {
  expire: string,
  lastOperator: string
} = {
  expire: '',
  lastOperator: ''
}
const intentionClassScope: string = 'A,B,C,D'
const autoRuleMap: Record<number, number|undefined> = {}
export const useTaskStore = defineStore({
  id: "task-info",
  state() {
    return {
      scriptAllList,
      enableFormSetting,
      autoRuleMap,
      taskCacheList: [] as {taskName: string, taskType?: TaskTypeEnum, id: number}[],
      // taskTemplateList,
      callTeamList,
      callTeamSeatList,
      callGroupSeatList,
      batchTaskIds,
      batchTaskInfo,
      intentionClassScope,
      lockInspecte: false,
      lastInspecteRecordIds: ['', ''], // 上一次的[通话详情，播放音频]的通话记录recordId
    };
  },
  getters: {
  },
  actions: {
    async getCallTeamListOptions(groupId?: string, needUpdate: boolean = false) {
      const userStore = useUserStore()
      // 运营端， 如传groupId强制重新获取；商户端，通过needUpdate强制重新获取
      if (!this.callTeamList || this.callTeamList?.length === 0 || needUpdate || (groupId  && userStore.accountType === 0)) {
        const [_, res] = await to(aiOutboundTaskTemplateModel.findAllCallTeams({
            groupId: groupId || userStore.groupId,
          })) // 针对运营端调用，需手动传入groupId
        this.callTeamList = (res || []).map(item => {
          const { callTeamName, id, leaderAccountId } = item
          return {
            callTeamName, id, leaderAccountId
          }
        })
      }
      return this.callTeamList
    },
    async getCallTeamSeatListOptions(needUpdate: boolean = false) {
      if (!this.callTeamSeatList || this.callTeamSeatList?.length === 0 || needUpdate) {
        const [_, res] = await to(clueManagerModel.getTeamSeatsList())
        this.callTeamSeatList = (res || []).map(item => {
          const { id, account, name } = item
          return {
            id, account, name
          }
        })
        return this.callTeamSeatList
      } else {
        return this.callTeamSeatList
      }
    },
    async getCallGroupSeatListOptions(needUpdate: boolean = false) {
      if (!this.callGroupSeatList || this.callGroupSeatList?.length === 0 || needUpdate) {
        const [_, res] = await to(clueManagerModel.getGroupSeatsList())
        this.callGroupSeatList = (res || []).map(item => {
          const { id, account, accountId, name } = item
          return {
            id, account, accountId, name
          }
        })
        return this.callGroupSeatList
      } else {
        return this.callGroupSeatList
      }
    },
    async getEnableFormSetting(needUpdate: boolean = false) {
      if (!this.enableFormSetting || this.enableFormSetting?.length === 0 || needUpdate) {
        const [_, res] = await to(formSettingModel.getEnableFormSettingInfo())
        this.enableFormSetting = res || []
        return this.enableFormSetting
      } else {
        return this.enableFormSetting
      }
    },
    async getAllScriptListOptions (needUpdate: boolean = false):Promise<SpeechCraftInfoItem[]> {
      if (needUpdate || !this.scriptAllList || this.scriptAllList.length < 1) {
        const userStore = useUserStore()
        let data: SpeechCraftInfoItem[] = []
        if (userStore.accountType === 0) {
            const [err1, data1] = await to(scriptTableModel.getScriptTables({
              status: SpeechCraftStatusEnum['生效中'],
            })) as [any, SpeechCraftInfoItem[]]
            const [err2, data2] = await to(scriptTableModel.getScriptTables({
              status: SpeechCraftStatusEnum['已停用'],
            })) as [any, SpeechCraftInfoItem[]]
            data = [...(data1 || []), ...(data2 || [])]
        } else {
          const [_, data1] = await to(scriptTableModel.findScriptByGroupId({
            groupId: userStore.groupId,
          })) as [any, SpeechCraftInfoItem[]]
          data = data1 || []
        }
        this.scriptAllList  = (data||[]).sort((a, b) => dayjs(a.createTime).isAfter(dayjs(b.createTime)) ? -1 : 1).map(item => {
          const  { id, scriptName, scriptStringId, secondaryIndustryId } = item
          return { id, scriptName, scriptStringId, secondaryIndustryId }
        })
        return this.scriptAllList
      } else {
       return this.scriptAllList || []
      }
    },
    async getIntentionClassScope (groupId?: string, accountType?: number) {
      let res: CallSetting
      if (groupId && accountType === 0) {
        res = await callSettingModel.findCallSettingByGroupIdForOperation({ groupId })
      } else {
        res = await callSettingModel.findCallSetting()
      }
      this.intentionClassScope = res?.intentionClass || 'A,B,C,D'
    },
    async inspecteRecordDetail (recordId: string, recordType: RecordTypeEnum) {
      const userStore = useUserStore() 
      const { accountType, account, roleName } = userStore
      // 仅运营端才进行巡检通话音频
      if (!recordId || accountType === 1) return
      // 防止同一音频连续多次暂停播放
      if (this.lastInspecteRecordIds[0] === recordId) return
      // 先更新最新的巡检记录
      this.lastInspecteRecordIds[0] = recordId
      // 巡检通话详情
      await to(inspectionModel.inspecteRecordDetail({
        recordId: recordId || '',
        callType: recordType,
        account: account,
        roleName,
      }))
    },
    async inspecteRecordAudio (audioStatus: string, recordId: string, recordType: RecordTypeEnum) {
      const userStore = useUserStore() 
      const { accountType, account, roleName, } = userStore
      // 仅运营端才进行巡检通话音频
      if (!recordId || accountType === 1 || audioStatus !== 'play') return
      // 防止同一音频连续多次暂停播放
      if (this.lastInspecteRecordIds[1] === recordId) return
      if (this.lockInspecte) return
      this.lockInspecte = true
      // 巡检通话详情
      if (this.lastInspecteRecordIds[0] !== recordId) {
        this.lastInspecteRecordIds[0] = recordId
        await to(inspectionModel.inspecteRecordDetail({
          recordId: recordId || '',
          callType: recordType,
          account: account,
          roleName,
        }))
      }
      // 巡检通话音频
      await to(inspectionModel.inspecteRecordAudio({
        recordId: recordId || '',
        callType: recordType,
        account: account,
        roleName,
      }))
      this.lockInspecte = false
      // 更新上一次的播放音频的通话记录recordId
      this.lastInspecteRecordIds[1] = recordId
    }
  },
  persist: [
    {
      storage: sessionStorage,
    },
  ]
});
