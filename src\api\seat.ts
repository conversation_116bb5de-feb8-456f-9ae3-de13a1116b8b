import { http } from '@/axios'
import {
  CallSetting,
  EventInfoParams,
  SeatAccountParam,
  SeatCallParam,
  SeatFsCallParam,
  SeatLogParam,
  SeatTeam,
  SeatTeamApiParam
} from '@/type/seat'
import { ClueFollowItem, ClueSearchInfo, CollectionFormItem, FormRecordItem } from '@/type/clue'
import { SmsSendParam, VariableSmsPojoItem } from '@/type/sms'
import { RecordDialogueData } from '@/type/task'
import { workbenchApiUrlNewPrefix } from './common'

// 监控平台接口公共前缀
let baseMonitorUrl: string = location.protocol === 'https:'
  ? import.meta.env.VITE_MONITOR_HTTPS
  : import.meta.env.VITE_MONITOR_HTTP

// 坐席管理
export const seatManagerModel = {
  // 运营端查询所有坐席组列表
  getGlobalSeatTeam: async () => {
    return http({
      url: '/AiSpeech/callSeatManager/findAllCallTeams',
      method: 'GET',
    }).then(res => res as unknown as SeatTeam[])
  },
  // 根据groupId查询所有坐席组列表
  getAllSeatTeam: async (params: SeatTeamApiParam) => {
    return http({
      url: '/AiSpeech/callSeatManager/findAllCallTeamsByGroupId',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },
  // 根据坐席组Id查找坐席组
  getSeatTeam: async (params: SeatTeamApiParam) => {
    return http({
      url: '/AiSpeech/callSeatManager/findCallTeamById',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },
  // 根据groupId获取可选的坐席组长
  getAvailableLeader: async (params: SeatTeamApiParam) => {
    return http({
      url: '/AiSpeech/callSeatManager/getAvailableAccountOfLeader',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },
  // 根据groupId获取可选的组员
  getAvailableMember: async (params: SeatTeamApiParam) => {
    return http({
      url: '/AiSpeech/callSeatManager/getAvailableAccountsOfCallSeat',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },
  // 保存一个坐席组
  saveSeatTeam: async (data: SeatTeamApiParam) => {
    return http({
      url: '/AiSpeech/callSeatManager/saveOneCallTeam',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}

// 通话设置
export const callSettingModel = {
  // 获取通话设置
  findCallSetting: async () => {
    return http({
      url: '/AiSpeech/callSetting/findCallSetting',
      method: 'POST',
    }).then(res => res as unknown as CallSetting)
  },
  // 运营端通过groupId获取通话设置
  findCallSettingByGroupIdForOperation: async (params: { groupId: string }) => {
    return http({
      url: '/AiSpeech/callSetting/findCallSettingForOperation',
      method: 'POST',
      params,
    }).then(res => res as unknown as CallSetting)
  },
  // 保存通话设置
  saveCallSetting: async (data: CallSetting) => {
    return http({
      url: '/AiSpeech/callSetting/saveCallSetting',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}

// 线索
export const seatWorkbenchClueModel = {
  // 获取关联任务列表
  getRelatedTaskList: async () => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatClue/findCallSeatRelatedTasks'
        : '/AiSpeech/callSeatClue/findCallSeatRelatedTasks',
      method: 'GET',
    }).then(res => res as unknown)
  },
  // 获取今日线索统计
  getTodayClueStatistics: async () => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatClue/findSeatBenchStatisticToday'
        : '/AiSpeech/callSeatClue/findSeatBenchStatisticToday',
      method: 'GET',
    }).then(res => res as unknown)
  },
  // 获取全部线索统计
  getAllClueStatistics: async () => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatClue/findSeatBenchStatisticTotal'
        : '/AiSpeech/callSeatClue/findSeatBenchStatisticTotal',
      method: 'GET',
    }).then(res => res as unknown)
  },
  // 获取跟进中线索列表
  getFollowingClueList: async (data: ClueSearchInfo) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatClue/findSeatBenchCluesFollowing'
        : '/AiSpeech/callSeatClue/findSeatBenchCluesFollowing',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 获取跟进成功线索列表
  getFollowSuccessClueList: async (data: ClueSearchInfo) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatClue/findSeatBenchCluesSuccess'
        : '/AiSpeech/callSeatClue/findSeatBenchCluesSuccess',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 获取跟进失败线索列表
  getFollowFailClueList: async (data: ClueSearchInfo) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatClue/findSeatBenchCluesFail'
        : '/AiSpeech/callSeatClue/findSeatBenchCluesFail',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 切换线索星标状态
  switchClueStar: async (params: { clueId: number, isStar: boolean }) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatStatus/setClueStarStatus'
        : '/AiSpeech/clue/setClueStarStatus',
      method: 'POST',
      params
    }).then(res => res as unknown)
  },
  // 修改下次跟进时间
  changeNextFollowUpTime: async (params: {
    clueId: number,
    followUpLogId?: number | null,
    nextFollowUpTime: string
  }) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/clueFollowUpLog/changeNextFollowUpTime'
        : '/AiSpeech/callSeatOperation/changeNextFollowUpTime',
      method: 'GET',
      params
    }).then(res => res as unknown)
  },
  // 修改跟进状态
  changeFollowUpStatus: async (params: {
    callSeatId: number,
    clueId: number,
    followUpLogId: number,
    status: string
  }) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/clueFollowUpLog/changeFollowUpStatus'
        : '/AiSpeech/callSeatOperation/changeFollowUpStatus',
      method: 'GET',
      params
    }).then(res => res as unknown)
  },
  // 根据线索Id查询跟进记录
  getFollowById: async (params: { clueId: number }) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/clueFollowUpLog/getClueFollowUpInfoByClueId'
        : '/AiSpeech/clue/getClueFollowUpInfoByClueId',
      method: "POST",
      params,
    }).then(res => res as unknown as ClueFollowItem[])
  },
}

// 坐席账号
export const seatWorkbenchAccountModel = {
  // 获取当前坐席信息（状态、签入任务、所属坐席组等信息）
  getSeatInfo: async () => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatStatus/findCurrentAccountCallSeat'
        : '/AiSpeech/callSeatOperation/findCurrentAccountCallSeat',
      method: 'GET',
    }).then(res => res as unknown)
  },
  // 上线
  online: async (params: SeatAccountParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatStatus/upLine'
        : '/AiSpeech/callSeatOperation/upLine',
      method: 'GET',
      timeout: 5000,
      params,
    }).then(res => res as unknown)
  },
  // 下线
  offline: async () => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatStatus/offLine'
        : '/AiSpeech/callSeatOperation/offLine',
      method: 'GET',
      timeout: 5000,
    }).then(res => res as unknown)
  },
  // 获取可签入任务列表
  getAvailableTaskList: async () => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatStatus/findCurrentCallSeatCanCheckinTasks'
        : '/AiSpeech/callSeatOperation/findCurrentCallSeatCanCheckinTasks',
      method: 'POST',
    }).then(res => res as unknown)
  },
  // 签入任务
  checkIn: async (data: SeatAccountParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatStatus/checkinTask'
        : '/AiSpeech/callSeatOperation/checkinTask',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 签出任务
  checkOut: async (data: SeatAccountParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatStatus/checkoutTask'
        : '/AiSpeech/callSeatOperation/checkoutTask',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 开始休息
  startRest: async () => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatStatus/startRest'
        : '/AiSpeech/callSeatOperation/startRest',
      method: 'POST',
    }).then(res => res as unknown)
  },
  // 结束休息
  endRest: async (params: SeatAccountParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatStatus/endRest'
        : '/AiSpeech/callSeatOperation/endRest',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
}

// 坐席账号FS服务
export const seatServerModel = {
  // 坐席账号注册时绑定FS账号
  bind: async (data: SeatAccountParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/fsCallSeat/bind'
        : '/AiSpeech/seat-fs/bind',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 坐席账号获取绑定信息
  getBindInfo: async (data: SeatAccountParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/fsCallSeat/getBindInfo'
        : '/AiSpeech/seat-fs/getBindInfo',
      method: 'POST',
      data,
      timeout: 5000,
    }).then(res => res as unknown)
  },
  // 心跳检测
  heart: async (data: SeatAccountParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/fsCallSeat/heart'
        : '/AiSpeech/seat-fs/heart',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 来电等待接听超时
  acceptTimeout: async (data: SeatFsCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/fsCallSeat/seat-miss-call'
        : '/AiSpeech/seat-fs/seat-miss-call',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 接听
  accept: async (data: SeatFsCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/fsCallSeat/seat-deal'
        : '/AiSpeech/seat-fs/seat-deal',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}

// 通话
export const seatWorkbenchCallModel = {

  // -------------------- 通用 开始 --------------------

  // 获取通话携带信息（通话时，展示在顶部的客户电话号码和姓名）
  getCallCarryInfo: async (params: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callRecord/getPhoneCarryInfo'
        : '/AiSpeech/callRecord/getPhoneCarryInfo',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },
  // 查询待发送短信里已经填好的自定义变量
  findVariableByCallRecordId: async (params: SmsSendParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callRecord/findVariableByCallRecordId'
        : '/AiSpeech/message/findVariableByCallRecordId',
      method: 'POST',
      params,
    }).then(res => res as unknown as VariableSmsPojoItem[])
  },

  // -------------------- 通用 结束 --------------------

  // -------------------- 人工直呼 开始 --------------------

  // 人工直呼 呼叫
  launchManualDirectCall: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatManual/initManualDirectCall'
        : '/AiSpeech/callSeatOperation/initManualDirectCall',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人工直呼 呼叫失败
  launchFailManualDirectCall: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatManual/manualDirectCallFail'
        : '/AiSpeech/callSeatOperation/manualDirectCallFail',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人工直呼 接通
  startManualDirectCall: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatManual/startManualDirectDialing'
        : '/AiSpeech/callSeatOperation/startManualDirectDialing',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人工直呼 挂断
  stopManualDirectCall: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatManual/endManualDirectDialing'
        : '/AiSpeech/callSeatOperation/endManualDirectDialing',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人工直呼 话后处理 提交
  submitProcessManualDirectCall: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatManual/manualDirectPostingSubmit'
        : '/AiSpeech/callSeatOperation/manualDirectPostingSubmit',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },

  // -------------------- 人工直呼 结束 --------------------

  // -------------------- 人机协同 开始 --------------------

  // 人机协同 来电 弹窗
  incomingHumanMachine: async () => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatAIMan/startPopWindow'
        : '/AiSpeech/callSeatOperation/startPopWindow',
      method: 'POST',
    }).then(res => res as unknown)
  },
  // 人机协同 监听 开始
  startMonitorHumanMachine: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatAIMan/startHumanMachineListening'
        : '/AiSpeech/callSeatOperation/startHumanMachineListening',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人机协同 介入 开始
  startSpeakHumanMachine: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatAIMan/startHumanMachineDialing'
        : '/AiSpeech/callSeatOperation/startHumanMachineDialing',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人机协同 介入 结束
  stopSpeakHumanMachine: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatAIMan/endHumanMachineDialing'
        : '/AiSpeech/callSeatOperation/endHumanMachineDialing',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人机协同 监听或介入 失败
  failHumanMachine: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatAIMan/humanMachineListenOrDialingFail'
        : '/AiSpeech/callSeatOperation/humanMachineListenOrDialingFail',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
  // 人机协同 话后处理 获取当前话术的意向等级和标签
  getHumanRecordAvailableData: async (params: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/scriptSearch/getCurrentScriptIntention'
        : '/AiSpeech/callSeatOperation/getCurrentScriptIntention',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },
  // 人机协同 话后处理 提交
  submitProcessHumanMachine: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatAIMan/submitHumanMachinePosting'
        : '/AiSpeech/callSeatOperation/submitHumanMachinePosting',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人机协同 客户状态记录
  getEventList: async (data: EventInfoParams) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/event/list'
        : '/AiSpeech/event/list',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  }

  // -------------------- 人机协同 结束 --------------------

}

// 坐席监控
export const seatMonitorModel = {
  // 获取实时状态
  getRealStatistics: async (params: { groupId: string }) => {
    return http({
      url: '/AiSpeech/callSeatMonitor/getCallSeatMonitorsForStatusByGroupId',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },
  // 获取全部空闲坐席和其空闲时长
  getSeatIDELDuration: async (params: { groupId: string }) => {
    return http({
      url: '/AiSpeech/callSeatMonitor/getCallSeatIDELStatusByGroupId',
      method: 'GET',
      params,
    }).then(res => res as unknown as Record<string, string>)
  },
  // 获取坐席数据统计
  getSeatStatistics: async (params: { groupId: string, date?: string }) => {
    return http({
      url: '/AiSpeech/callSeatMonitor/getCallSeatMonitorForStatisticData',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },
  // 获取坐席组数据统计
  getSeatTeamStatistics: async (params: { groupId: string, date?: string }) => {
    return http({
      url: '/AiSpeech/callSeatMonitor/getCallTeamMonitorForStatisticData',
      method: 'GET',
      params,
    }).then(res => res as unknown)
  },
}

// 坐席日志
export const seatLogModel = {
  // 发送日志
  log: async (data: SeatLogParam) => {
    return http({
      url: baseMonitorUrl + '/remote/logs/seat_workbench_web_log',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  }
}

// 通话记录
export const seatCallRecordModel = {
  // 获取人工直呼对话详情
  getManualDialogueDataList: async (data: {
    callId: string,
    recordId: string,
  }) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callRecordDialog/getManualDialogueDataList'
        : '/AiSpeech/dialog/getManualDialogueDataList',
      method: 'GET',
      data,
    }).then(res => res as unknown as RecordDialogueData[])
  },
  // 获取人机协同对话详情
  getMixDialogueDataList: async (data: {
    callId: string,
    recordId: string,
  }) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callRecordDialog/getAiManualDialogueDataList'
        : '/AiSpeech/dialog/getAiManualDialogueDataList',
      method: 'GET',
      data,
    }).then(res => res as unknown as RecordDialogueData[])
  },
  // 获取人机协同通话记录
  getCallRecordMix: async (data: SeatCallParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/callSeatAIMan/callRecordAiManual'
        : '/AiSpeech/callRecordAiManual/callRecordAiManual',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}

// 线索表单记录
export const seatFormRecordModel = {
  // 获取当前商户坐席的表单设置（表单记录的结构）
  getEnableFormSettingInfo: async () => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/formRecord/findEnableFormCollectionList'
        : '/AiSpeech/formCollection/findEnableFormCollectionList',
      method: "GET",
    }).then(res => res as unknown as CollectionFormItem[])
  },
  // 获取当前线索的表单记录（表单记录的数据）
  getFormRecordByClueId: async (data: { clueId: number }) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/formRecord/findFormRecordListByClueId'
        : '/AiSpeech/formRecord/findFormRecordListByClueId',
      method: 'GET',
      data
    }).then(res => res as unknown as FormRecordItem)
  },
  // 保存当前线索的表单记录（表单记录的数据）
  saveFormRecord: async (data: FormRecordItem) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/formRecord/saveFormRecord'
        : '/AiSpeech/formRecord/saveFormRecord',
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
}
