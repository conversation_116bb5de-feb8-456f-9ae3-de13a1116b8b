<template>
  <el-dialog
    :model-value="visible"
    width="640px"
    class="dialog-form"
    :close-on-click-modal="false"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="90px"
        ref="editRef"
      >
        <el-form-item label="菜单名称：" prop="menuName">
          <el-input v-model.trim="editData.menuName" maxlength="30" clearable placeholder="请输入菜单名称，30个字以内"></el-input>
        </el-form-item>
        <el-form-item label="菜单编码：" prop="menuCode">
          <el-input v-model.trim="editData.menuCode" maxlength="10" clearable placeholder="请输入菜单编码"></el-input>
        </el-form-item>
        <el-form-item label="菜单类型：" prop="">
          <el-radio-group v-model="editData.menuType" class="tw-ml-[6px]">
            <el-radio v-for="item in enum2Options(MenuTypeEnum)" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注：" prop="description">
          <el-input v-model.trim="editData.description" type="textarea" show-word-limit maxlength="200" clearable placeholder="请输入备注，200个字以内"></el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <div>
          <el-button @click="cancel" :icon="CloseBold">取消</el-button>
          <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { authorizationModel } from '@/api/authorization'
import type { FormInstance, } from 'element-plus'
import to from 'await-to-js';
import { MenuItemOrigin } from './constant'
import { MenuItem, MenuTypeEnum } from '@/type/authorization'
import { enum2Options } from '@/utils/utils'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  data: MenuItem | null
}>();
const loading = ref(false)
const visible = ref(props.visible)
const editData = reactive<Partial<MenuItem>>(props.data || new MenuItemOrigin())
const editRef = ref<FormInstance  | null>(null)
const title = computed(() => (`${editData.id ? '编辑' : '新增'}菜单`))
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}

const rules = {
  menuCode: [
    { required: true, message: '请输入菜单编码', trigger: 'blur' },
  ],
  menuName: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
  ],
  // description: [
  //   { required: true, message: '请输入菜单描述', trigger: 'blur' },
  // ],
}

const confirm = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const [err] = await to(authorizationModel.saveMenu(editData))
      
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        cancel()
        emits('confirm')
      }
    }
  })
}

watch(() => props.visible, n => {
  visible.value = n
  if (n) {
    Object.assign(editData, new MenuItemOrigin(), props.data)
    editRef.value?.clearValidate()
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>