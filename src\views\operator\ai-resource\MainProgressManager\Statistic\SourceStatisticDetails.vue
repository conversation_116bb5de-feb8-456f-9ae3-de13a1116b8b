<template>
<el-dialog
    v-model="dialogVisible"
    width="720px"
    :close-on-click-modal="true"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">
        {{  `【${props.currentItem?.corpusName || ''}】命中来源` }}
      </div>
    </template>
    <div v-loading="loading" class="tw-flex tw-flex-col">
      <div class="tw-leading-[20px] tw-pr-[12px] tw-bg-white tw-pl-[16px]">
        <el-row class="tw-py-[8px]">
            <el-col :span="8" class="tw-text-[13px] tw-text-left">
              <span class="tw-text-[--primary-black-color-400]">语料命中数：</span>
              <span>{{ props.currentItem?.numerator || '-' }}</span>
            </el-col>
            <el-col :span="8" class="tw-text-[13px] tw-text-left">
              <span class="tw-text-[--primary-black-color-400]">总接通数：</span>
              <span>{{ props.currentItem?.denominator || '-' }}</span>
            </el-col>
            <el-col :span="8" class="tw-text-[13px] tw-text-left">
              <span class="tw-text-[--primary-black-color-400]">命中分布：</span>
              <span>{{ props.currentItem?.denominator ? formatNumber(props.currentItem?.numerator/props.currentItem?.denominator*100, 2)+'%' || '-' : '0' }}</span>
            </el-col>
        </el-row>
      </div>
      <el-table
        :data="sourceList"
        class="tw-w-full tw-pb-[8px]"
        max-height="300px"
        row-key="id"
        :header-cell-style="{background:'#f7f8fa', 'border-top': '1px solid #EBEEF5', color: 'var(--primary-black-color-500)'}"
      >
        <el-table-column property="corpusName" label="来源" align="left"  min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        <el-table-column property="hitNum" label="命中数" align="left" sortable min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
          <template #header="{ column }">
            <div class="tw-flex tw-items-center tw-justify-start">
              <span>{{column.label}}</span>
              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="命中分布" align="left" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            {{ props.currentItem?.numerator ? formatNumber(row.hitNum/props.currentItem?.numerator*100, 2)+'%' || '-' : '0' }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel()" :icon="CloseBold">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, reactive, onMounted, watch} from 'vue'
import { CaretTop, CaretBottom, ArrowUpBold, ArrowDownBold, CloseBold } from '@element-plus/icons-vue'
import { useScriptStore } from '@/store/script'
import to from 'await-to-js';
import { scriptStatisticsModel } from '@/api/speech-craft'
import { formatterEmptyData, formatNumber } from '@/utils/utils'
import { CorpusStatisticDetails, ScriptStatisticItem, ScriptStatisticParams, } from '@/type/speech-craft'


const props = defineProps<{
  currentItem: ScriptStatisticItem,
  visible: boolean,
  scriptId: number
}>();

const loading = ref(false)

const emits = defineEmits(['update:visible'])
const dialogVisible = ref(false)
const sourceList = ref<CorpusStatisticDetails[]>([])

const search = async () => {
  const params: ScriptStatisticParams = {
    corpusId: props.currentItem.corpusId,
    scriptId: props.scriptId,
  }
  const [_, res] =await to(scriptStatisticsModel.getCorpusHitDetail(params))
  sourceList.value = res || []
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
// 执行区
watch(() => props.visible, async n => {
  dialogVisible.value = n
  n && search()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.el-table {
  font-size: 13px;
  color: var(--primary-black-color-500);
  :deep(.el-table__cell) {
    padding: 4px 0;
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
</style>