<template>
  <div class="graph-static-container" :class="{'graphClass': isFullScream}" ref="graphRef">
    <el-scrollbar class="tw-w-full tw-h-full">
      <div id="graph-static" class="tw-box-border tw-relative tw-grow tw-overflow-hidden">
      </div>
    </el-scrollbar>
    <svg viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <marker id="triangle" viewBox="0 0 10 10"
          refX="8" refY="5"
          markerUnits="strokeWidth"
          markerWidth="7" markerHeight="7"
          orient="auto"
        >
          <path d="M 0 0 L 10 5 L 0 10 L 3 5 z" fill="#409eff"/>
        </marker>
      </defs>
    </svg>
    <div class="tw-absolute tw-left-1 tw-top-1 tw-z-10">
      <slot name="list"></slot>
    </div>
    <div class="tw-absolute tw-right-1 tw-top-[13px] tw-text-[#999] tw-flex tw-items-center tw-z-[11]">
      <slot name="detail"></slot>
      <div v-if="!!currentAudioName" class="info-title tw-bg-white tw-border-[#409eff] tw-border-[1px] tw-w-[250px] tw-p-[12px] tw-z-[11]">
        <div class="tw-flex tw-mb-[8px]">
          <span class="tw-shrink-0">正在播放：</span>
          <span class="tw-grow tw-text-justify">{{ currentAudioName || '-' }}</span>
        </div>
        <div class="tw-flex">
          <span class="tw-shrink-0">播放速度：</span>
          <el-select v-model="playbackRate" class="tw-grow" size="small" placeholder="请选择播放速度" @change="handlePlayRateChange">
            <el-option v-for="item in audioRateOption" :key="item.value" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </div>
        <el-button size="small" class="tw-w-full tw-mt-1" type="primary" @click="clearAudio">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, reactive, onUnmounted, } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { Graph, Node, Edge, Cell, NodeView, Shape  } from '@antv/x6'
import { useScriptStore } from '@/store/script'
import { EventValueItem } from '@/type/speech-craft'
import { EdgeItem } from '@/type/common'
import { Selection } from '@antv/x6-plugin-selection'
import { x6ColorList, filterNextStr, filterNodeTitle, filterNodeContent } from './config'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import { scriptCorpusModel } from '@/api/speech-craft'
import { audioRateOption } from '@/assets/js/constant'

const props = defineProps<{
  edgeList: EdgeItem[];
  nodeList: Node.Metadata[];
  canvasId?: number | null
}>();
const emits = defineEmits(['select'])
const nodeList = ref<Node.Metadata[] | null>([])
const edgeList = ref<EdgeItem[] | null>([])
const isFullScream = ref(false)
const graphRef = ref()
const graph = ref()
watch(() => isFullScream.value, (n) => {
  if (graph.value && n) {
    graph.value.width = window.innerWidth - 10
  }
  graph.value && (isFullScream.value ? graph.value.resize(5000, 10000) : graph.value.resize(3000, 6000))
})
const portObj = reactive<{
  [key: string]: number
}>({})
Shape.HTML.register({
  shape: 'cu-data-node',
  effect: ['data'],
  width: 200,
  height: 120,
  html(cell) {
    // 获取节点传递过来的数据
    const data = cell.getData();
    return `
      <div class="dom-body tw-bg-white tw-rounded-[4px] tw-border-transparent tw-h-[100%] tw-w-[100%] tw-flex tw-flex-col tw-justify-between tw-text-[12px] tw-pt-[16px] tw-overflow-y-hidden tw-box-border">
        <div class="tw-flex tw-w-full tw-text-[14px] tw-px-[12px] tw-font-semibold tw-text-[#313233]">${filterNodeTitle(data, true)}</div>
        ${filterNodeContent(data, eventValuesOptions)}
        ${filterNextStr(data)}
      </div>
    `
  }
})

const scriptStore = useScriptStore()
const eventValuesOptions = reactive<{[key: string]: EventValueItem}>({})
const timer = ref<ReturnType<typeof setTimeout> | null>(null)

// 当前正在听音的语料
const currentAudioName = ref<string | null>(null)
const playbackRate = ref(1)
// 音频播放模块
const currentAudio = ref<null | HTMLAudioElement>(null)

const init = async () => {
  const res = await scriptStore.getEventOptions()
  if (res && res.length > 0) {
    res.map(item => {
      item.eventValuemap && Object.assign(eventValuesOptions, item.eventValuemap)
    })
  }
  if (graph.value) {
    graph.value?.dispose()
  }
  graph.value = new Graph({
    container: document.getElementById('graph-static') as HTMLElement,
    // width: document.querySelector('.graph-static-container .el-scrollbar__view')?.getBoundingClientRect().width,
    height: document.body.clientHeight - 40,
    width: 1000,
    autoResize: true,
    async: true,
    panning: true,
    mousewheel: true,
    scaling: {
      min: 0.3,
      max: 1.5,
    }, // 最大最小缩放尺寸
    interacting: false,
    background: {
      color: '#f2f2f2',
    },
    translating: {
      restrict: false,
    },
    highlighting: {
      embedding: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#fff',
            stroke: '#47C769',
          },
        },
      },
      // 连线过程中，链接桩可以被链接时被使用
      magnetAvailable: { // 高亮
        name: 'stroke',
        args: {
          attrs: {
            fill: '#fff',
            stroke: '#47C769',
          },
        },
      },
      // 连线过程中，自动吸附到链接桩时被使用
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#fff',
            stroke: '#31d0c6',
          },
        },
      },
    },
    connecting: {
      snap: {
        radius: 30,
      },
      // allowMulti: false,
      allowNode: false,
      anchor: 'center',
      allowLoop: false,
      connectionPoint: 'anchor',
      // anchor: 'top',
      router: {
        name: 'normal',
        args: {
          offset: 12,
          direction: 'T',
        },
      },
      allowBlank: false,
      connector: {
        name: 'smooth',
        args: {
          offset: 24,
          direction: 'V',
        },
      },
      validateConnection({ targetPort }) {
        targetPort?.split('--')[1]
        return targetPort?.split('--')[1] === 'in'
      },
    }
  })
  graph.value.use(
    new Selection({
      enabled: true,
      multiple: true,
      rubberband: false,
      rubberEdge : true,
      movable: false,
    })
  )
  nodeList.value = props.nodeList
  edgeList.value = props.edgeList
  graph.value?.fromJSON({
    nodes: nodeList.value,
    edges: edgeList.value,
  })
  graph.value.centerContent()
  // 右击查看/听音频
  graph.value.on('node:contextmenu', async (params: { node: Node }) => {
    const node = params.node

    // 如语料不存在文字内容(如非挂机的连接语料），不进行操作
    if (!node.data?.content) return
    // 查询语料信息
    const [err, data] = await to(scriptCorpusModel.findMasterCorpus({corpusId: node.data.corpusId}))
    if (err || !data?.scriptMultiContents?.length) return ElMessage.error('获取语料音频失败')
    // 校验全部语句是否有未上传的音频
    let hasEmptyAudio = false
    const currentAudioList = (data?.scriptMultiContents[0]?.scriptUnitContents || []).map(item => {
      if (!item.audioPath) {
        hasEmptyAudio = true
      }
      return {
        content: item.content || '',
        contentName: item.contentName || '',
        id: item.id || undefined,
        audioPath: item.audioPath || '',
      }
    })
    if (hasEmptyAudio) {
      return ElMessage.warning('部分语句未上传音频！')
    }
    // 赋值右上侧当前播放的语料内容
    currentAudioName.value = node.data?.name || ''

    // 播放音频
    playList(currentAudioList?.map(item => item.audioPath || '') || [], 0)
  })

  graph.value.on('node:selected', async ({ node, }: {
    node: Node,
    view: NodeView,
  }) => {
    const selectedNode = nodeList.value?.find(item => item.id === node.id)
    // @ts-ignore
    const edges = edgeList.value.filter(item => item.target.cell === selectedNode.id)
    graph.value.select(edges.map(item => item.id))
    emits('select', selectedNode)
  })

  graph.value.on('edge:selected', ({ edge }: {edge: Edge, }) => {
    const {cell: targetCell, port: targetport} = edge.target as { cell:string, port: string }
    const {cell: sourceCell, port: sourceport} = edge.source as { cell:string, port: string }
    const node1 = edge.getSourceNode()
    if (node1) {
      // 获取Source链接桩stroke颜色
      const fillColor: string = node1.getPortProp(
        sourceport, ['attrs', 'circle', 'stroke'],
      ) || '#1f8cec'
      // 修改Source链接桩fill颜色
      node1.setPortProp(
        sourceport, ['attrs', 'circle'], { fill: fillColor }
      )
      // 对普通分支，修改edge为蓝色
      edge.setAttrs({
        line: {
          stroke: fillColor || '#1f8cec'
        }
      })
    }
    // 修改target链接桩fill颜色
    const node2 = edge.getTargetNode()
    node2 && node2.setPortProp(
      targetport, '', { group: 'in-selected' }
    )
    if (portObj[targetport]) {
      portObj[targetport] += 1
    } else {
      portObj[targetport] = 1
    }
  })

  graph.value.on('edge:unselected', ({ edge }: {edge: Edge,}) => {
    const {cell: targetCell, port: targetport} = edge.target as { cell:string, port: string }
    const {cell: sourceCell, port: sourceport} = edge.source as { cell:string, port: string }
    const node1 = edge.getSourceNode()
    node1 && node1.setPortProp(
      sourceport, ['attrs', 'circle'], { fill: '#fff' }
    )
    // 对普通分支，重置edge颜色
    if (!x6ColorList.find(item => item.name === edge.getAttrs().line.stroke)) {
      edge.setAttrs({
        line: {
          stroke: '#8f8f8f'
        }
      })
    }
    const node2 = edge.getTargetNode()
    portObj[targetport] = portObj[targetport] >= 1 ? (portObj[targetport] - 1) : 0
    portObj[targetport] === 0 && node2 && node2.setPortProp(
      targetport, '', { group: 'in' }
    )
  })
}
onMounted(() => {
  init()
})

const resizeGraph = () => {
  !!timer.value && clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    graph.value?.resize(undefined, document.body.clientHeight - 40)
    timer.value = null
  }, 600)
}
window.addEventListener('resize', resizeGraph)

watch(() => props.canvasId, () => {
  if (!props.canvasId) return clearAll()
  nodeList.value = props.nodeList
  edgeList.value = props.edgeList
  clearAudio()
  const cells: Cell[] = []
  let portsAll: string[] = []
  nodeList.value && nodeList.value.map(item => {
    // @ts-ignore
    portsAll = [...portsAll, ...(item.ports?.items ? item.ports?.items.map(v => v.id) : [])]
    cells.push(graph.value.createNode(item))
  })
  edgeList.value = edgeList.value && edgeList.value.filter(item => portsAll.includes(item.source.port) && portsAll.includes(item.target.port))
  edgeList.value.map(item => cells.push(graph.value.createEdge(item)))
  graph.value.resetCells(cells, {silence: true})
  graph.value?.centerContent()
})

const playList = (urls: string[], i: number = 0) => {
  if (!urls || i >= urls.length) {
    if (currentAudio.value) {
      currentAudio.value.src = ''
      currentAudio.value.pause()
    }
    return
  }
  if (!currentAudio.value) {
    currentAudio.value = new Audio()
  }
  if (currentAudio.value) {
    currentAudio.value.src = urls[i]
    currentAudio.value.playbackRate = playbackRate.value || 1
    currentAudio.value.loop = false
    currentAudio.value.addEventListener('loadedmetadata', () => {
      currentAudio.value && currentAudio.value.play();
    })
    currentAudio.value.addEventListener('ended', function () {
      playList(urls, i + 1);
    }, false)
  }
}

const handlePlayRateChange = () => {
  if (currentAudio.value) {
    currentAudio.value.playbackRate = playbackRate.value || 1
  }
}

const clearAudio = () => {
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value.src = ''
  }
  currentAudio.value = null
  currentAudioName.value = null
}

const clearAll = () => {
  graph.value?.clearKeys()
  graph.value?.dispose()
  nodeList.value = null
  edgeList.value = null
  graph.value = null
  graphRef.value = null
  clearAudio()
  window.removeEventListener('resize', resizeGraph)
}
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style lang="postcss" type="text/postcss">

.graph-static-container {
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  .el-scrollbar__view {
    width: 100%;
    display: flex;
  }
  .x6-node-selected .dom-body {
    opacity: 0.9;
    box-sizing: border-box;
    &::before {
      content: "";
      position: absolute;
      top: -3px;
      left: -3px;
      right:-3px;
      bottom: -3px;
      border-radius: 3px;
      background:
        linear-gradient(90deg, rgba(26, 115, 232) 50%, transparent 0) repeat-x,
        linear-gradient(90deg, rgba(26, 115, 232) 50%, transparent 0) repeat-x,
        linear-gradient(0deg, rgba(26, 115, 232) 50%, transparent 0) repeat-y,
        linear-gradient(0deg, rgba(26, 115, 232) 50%, transparent 0) repeat-y;
      background-size: 6px 2px, 6px 2px, 2px 6px, 2px 6px;
      background-position: 0 0, 0 100%, 0 0, 100% 0;
    }
  }
  .x6-edge-selected path{
    /* marker-end: url(#triangle); */
    stroke-width: 3px;
    stroke-dasharray: 5px, 2px;
  }
  .btn-box {
    position: absolute;
    left: 20px;
    top: 10px;
    display: flex;
    flex-direction: column;
    border: 0;
    .el-button:nth-child(n+2) {
      margin-left: 0;
      margin-top: 10px;
    }
  }
}
.graphClass {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2001;
}
</style>
