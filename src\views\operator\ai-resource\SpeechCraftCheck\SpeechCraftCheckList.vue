<template>
  <!--模块主体-->
  <div v-loading="loading" class="module-main">

    <!--左半部分，话术列表-->
    <div class="module-aside">
      <!--搜索框-->
      <el-input
        v-model.trim="scriptSearchVal"
        placeholder="请输入话术名称"
        :prefix-icon="Search"
        clearable
        @keyup.enter="updateCheckList"
        @clear="handleSearchClear"
      />

      <!--符合搜索条件的话术列表-->
      <el-scrollbar class="tw-mt-[8px]">
        <!--话术单项-->
        <div
          v-for="scriptItem in scriptList"
          :key="scriptItem.id"
          class="aside-item"
          :class="[{'aside-item--active':scriptItem.id===currentScriptItem?.id}]"
          @click="clickScriptItem(scriptItem)"
        >
          <!--话术名称-->
          <div class="tw-flex tw-flex-row tw-justify-between tw-items-start">
            <!--话术图标-->
            <!-- <div class="script-check-item-icon tw-flex-shrink-0"></div> -->
            <!--话术名称文本-->
            <!--文本溢出省略号-->
            <div class="tw-flex-1 tw-mr-[6px] tw-text-left tw-line-clamp-2 tw-break-all title">
              {{ scriptItem.scriptName }}
            </div>
            <div
              class="status-box-mini"
              :class="scriptItem.commitType === '修改' ? 'blue-status' : 'green-status'"
            >
              {{ scriptItem.commitType }}
            </div>
          </div>
          <!--审核状态-->
          
          <!--更新时间-->
          <div class="info-title">
            更新时间：{{ dayjs(scriptItem.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
          </div>
        </div>

        <!--空数据提示-->
        <div class="tw-bg-white">
          <el-empty v-if="!scriptList || scriptList.length < 1" />
        </div>
      </el-scrollbar>
    </div>

    <!--右半部分，主体-->
    <div class="module-content tw-min-w-[700px] tw-flex tw-flex-col ">
      <!--顶部区块-->
      <div class="tw-bg-white tw-rounded-[4px] tw-mb-[12px] tw-flex tw-flex-col tw-shrink-0 tw-py-[8px] tw-px-[12px]">
        <!--子模块标题-->
        <div class="submodule-title">
          <!--子模块标题文本-->
          <h5 class="submodule-title-text">
            {{ currentScriptItem?.scriptName || '' }}
          </h5>
          <!--审核按钮容器-->
          <!--只有当前话术存在且ID合法时，才显示审核的两个按钮-->
          <div
            class="script-check-button-box-check tw-flex-shrink-0 tw-ml-[1em]"
            :style="currentScriptItem && currentScriptItem.id >= 0 ? 'visibility: visible' : 'visibility: hidden'"
          >
            <el-button type="danger" :plain="true" :loading="loading" @click="reject">
              驳回
            </el-button>
            <el-button type="primary" :loading="loading" @click="approve">
              通过
            </el-button>
          </div>
        </div>

        <!--提交信息-->
        <div class="tw-text-left">
          <p>
            <span class="info-title">提交人：</span>
            <!--接口暂时没有提交人字段，前端留空-->
            <span class="info-content">-</span>
          </p>
          <p>
            <span class="info-title">提交类型：</span>
            <!--接口暂时没有提交人字段，前端留空-->
            <span class="info-content">{{ currentScriptItem?.commitType }}</span>
          </p>
        </div>
      </div>

      <TabsBox v-model:active="activeTab" :tabList="tabList" @update:active="handleTabChange"></TabsBox>
      <!--音频列表区块-->
      <template v-if="activeTab === '音频列表'">
        <!--只看更新切换开关，无论修改还是新增的话术都能看到-->
        <div class="tw-self-end tw-bg-white tw-px-[12px] tw-py-[8px] tw-w-full">
          <el-radio-group v-model="onlyNew" class="tw-float-right">
            <el-radio-button :label="false">全部音频</el-radio-button>
            <el-radio-button :label="true">只看更新</el-radio-button>
          </el-radio-group>
        </div>

        <!--表格和分页条容器-->
        <!--音频列表表格-->
        <el-table
          ref="audioListRef"
          class="tw-grow"
          v-loading="loadingAudioList"
          stripe
          :data="audioList"
          :header-cell-style="tableHeaderStyle"
        >
          <!--更新图标-->
          <el-table-column align="center" prop="updateStatus" label="" width="66">
            <template #default="scope">
              <SvgIcon
                v-show="scope.row.updateStatus==='已修改'"
                name="new"
                color="#409EFF"
                width="40"
                height="40"
              />
            </template>
          </el-table-column>

          <el-table-column align="left" fixed="left" prop="contentName" label="语料名称" width="160" :formatter="formatterEmptyData"/>
          <el-table-column align="left" prop="name" label="语料类型" width="160">
            <template #default="{row}">
              {{ row.corpusType ? corpusTypeOption[row.corpusType as CorpusTypeEnum]?.name || '-' : '-' }}
            </template>
          </el-table-column>
          <el-table-column align="left" prop="content" label="文字内容" min-width="300" :formatter="formatterEmptyData"/>
          <el-table-column property="contentType" label="文本类型" align="left" width="80">
            <template #default="{ row }">
              {{ findValueInEnum(row.contentType, ContentTypeEnum) || '普通文本' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updateTime" label="最后更新时间" width="160">
            <template #default="{row}">
              {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') || '-' : '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" fixed="right" prop="audioPath" label="操作" width="54">
            <template #default="{ row }">
              <!--音频播放悬浮窗-->
              <el-button
                v-if="row?.id && tempAudio.id == row.id && audioStatus == 'play'"
                type="danger"
                link
                :disabled="!row.audioPath"
                @click="handleAudioPlay(row)"
              >
                <el-icon :size="20">
                  <VideoPause />
                </el-icon>
              </el-button>
              <el-button v-else :type="!row.audioPath ? 'default' : 'primary'" link :disabled="!row.audioPath" @click="handleAudioPlay(row)">
                <el-icon :size="20">
                  <VideoPlay />
                </el-icon>
              </el-button>
            </template>
          </el-table-column>
          <!--空数据提示-->
          <template #empty>
            <el-empty v-if="!audioList || audioList.length < 1" description="暂无数据" />
          </template>
        </el-table>

        <!--音频列表分页条-->
        <PaginationBox
          :pageSize="audioPageSize"
          :currentPage="audioPageNum"
          :total="audioTableSize || 0"
          @search="updateAudioAllList()"
          @update="updateAudioPage"
        />
      </template>

      <!--操作记录区块-->
      <template v-if="activeTab === '操作记录'">
        <!--表格和分页条容器-->
        <!--操作记录表格-->
        <el-table v-loading="loadingLogList" class="tw-grow" stripe :data="logList" :header-cell-style="tableHeaderStyle">
          <el-table-column align="left" prop="userAccount" label="操作账号" min-width="160" :formatter="formatterEmptyData"/>
          <el-table-column align="center" prop="createTime" label="操作时间" width="160">
            <template #default="{row}">
              {{ row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="operateType" label="操作类型" width="120" :formatter="formatterEmptyData"/>
          <el-table-column align="left" prop="opinion" label="备注" min-width="240" :formatter="formatterEmptyData"/>
          <!--空数据提示-->
          <template #empty>
            <el-empty v-if="!logList || logList.length < 1" description="暂无数据" />
          </template>
        </el-table>

        <!--操作记录分页条-->
        <PaginationBox
          :pageSize="logPageSize"
          :currentPage="logPageNum"
          :total="logTableSize || 0"
          @search="updateLogAllList()"
          @update="updateLogPage"
        />
      </template>
    </div>

  </div>

  <!--音频播放弹窗-->
  <MiniAudio
    v-if="tempAudio.id && tempAudio.id > 0"
    v-model:audioStatus="audioStatus"
    v-model:playRate="playRate"
    :tempAudio="tempAudio"
    @close="handleAudioPlay()"
    @update:audio="handleAudioChange"
  />

  <!--审核话术确认弹窗-->
  <CheckScriptDialog
    v-model:visible="checkScriptDialogVisible"
    :status="checkScriptDialogApproveStatus"
    :scriptId="currentScriptItem?.scriptId || -1"
    @submit="submitScriptCheck"
  />
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  CorpusTypeEnum,
  corpusTypeOption,
  ScriptCheckAudioItem,
  ScriptCheckItem,
  ScriptOperationLog,
  ContentTypeEnum,
} from '@/type/speech-craft'
import { Search, VideoPause, VideoPlay, } from '@element-plus/icons-vue'
import CheckScriptDialog from './CheckScriptDialog.vue'
import { scriptCheckModel } from '@/api/speech-craft'
import MiniAudio from '@/components/MiniAudio.vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { formatterEmptyData, findValueInEnum } from '@/utils/utils'
import dayjs from 'dayjs'
import { tableHeaderStyle } from '@/assets/js/constant'
import PaginationBox from '@/components/PaginationBox.vue'
import TabsBox from '@/components/TabsBox.vue'
import { traceApi } from '@/utils/trace';
// ---------- 生命周期 开始 ----------

/**
 * 挂载后
 */
onMounted(async () => {
  // 更新整个模块
  await updateWholeModule()
})

// ---------- 生命周期 结束 ----------

// ---------- 通用 开始 ----------

// 全局变量
const globalStore = useGlobalStore()
// 正在加载
const { loading } = storeToRefs(globalStore)

/**
 * 更新整个模块
 */
const updateWholeModule = async () => {
  // 等话术列表加载完后
  await updateCheckList()

  // 获取列表第一个话术
  if (scriptAllList.value.length) {
    currentScriptItem.value = scriptAllList.value[0] || null
    onlyNew.value = true
  }

  handleTabChange()
}

// ---------- 通用 结束 ----------

/**
 * 按模块分类变量和函数
 * - 话术列表模块
 * - 话术信息模块
 * - 音频列表模块
 * - 日志列表模块
 *
 * - 话术审核弹窗
 */

// ---------- 话术列表模块 开始 ----------

// 话术列表，全部，接口数据
const scriptAllList = ref<ScriptCheckItem[]>([])
// 话术列表，筛选，页面展示
const scriptList = ref<ScriptCheckItem[]>([])

// 话术搜索框文本
const scriptSearchVal = ref<string>('')

// 当前话术信息
const currentScriptItem = ref<ScriptCheckItem | null>(null)

const tabList = ['音频列表', '操作记录']
const activeTab = ref(tabList[0]!)

/**
 * 更新待审核话术列表
 */
const updateCheckList = async () => {
  loading.value = true
  try {
    // 请求接口
    const res = <ScriptCheckItem[]>await scriptCheckModel.getScriptCheckForProcess()

    // 更新列表，全部，接口数据
    scriptAllList.value = Array.isArray(res) ? res : []
    // 将列表按更新时间倒序排序
    scriptAllList.value = scriptAllList.value?.filter(item => item.scriptName?.includes(scriptSearchVal.value)).sort((a, b) => {
      return dayjs(a.updateTime).isAfter(b.updateTime) ? -1 : 1
    })

    // 更新列表，页面展示
    scriptList.value = scriptAllList.value

    // 如果列表为空，清空右上方的当前话术信息
    if (!scriptList.value || scriptList.value.length < 1) {
      currentScriptItem.value = null
    }
  } catch (e) {
    ElMessage({
      message: '获取话术列表失败',
      type: 'error',
    })
  }
  loading.value = false
}

/**
 * 搜索框文本清空时
 */
const handleSearchClear = () => {
  // 显示全部列表
  scriptList.value = scriptAllList.value
}

/**
 * 点击话术单项
 * @param scriptItem 当前话术单项的信息
 */
const clickScriptItem = (scriptItem: ScriptCheckItem) => {
  // 如果点击的当前话术，不用响应点击
  if (scriptItem.id === currentScriptItem.value?.id) {
    return
  }
  // 更新成点击的那个话术
  currentScriptItem.value = scriptItem

  // 切换话术，默认改为只看更新
  onlyNew.value = true

  handleTabChange()
}

// ---------- 话术列表模块 结束 ----------

// ---------- 话术信息模块 开始 ----------

/**
 * 点击顶部驳回按钮
 */
const reject = () => {
  // 显示审核话术确认弹窗
  checkScriptDialogApproveStatus.value = false
  checkScriptDialogVisible.value = true
}

/**
 * 点击顶部通过按钮
 */
const approve = () => {
  // 显示审核话术确认弹窗
  checkScriptDialogApproveStatus.value = true
  checkScriptDialogVisible.value = true
}

// ---------- 话术信息模块 结束 ----------

// ---------- 音频列表模块 开始 ----------

// 正在加载音频列表
const loadingAudioList = ref<boolean>(false)

// 只看更新
const onlyNew = ref<boolean>(true)

// 音频列表，全部，接口返回
const audioAllList = ref<ScriptCheckAudioItem[]>([])

// 当前页码，从1开始
const audioPageNum = ref<number>(1)
// 每页大小
const audioPageSize = ref<number>(20)
// 列表总长度
const audioTableSize = ref<number>(0)

/**
 * 更新整个音频列表
 */
const updateAudioAllList = async () => {
  // 上锁，正在加载音频列表
  loadingAudioList.value = true

  try {
    // 处理参数
    const params = {
      onlyUpdate: onlyNew.value ? 1 : 0,
      id: currentScriptItem.value?.scriptId ?? -1
    }

    // 请求接口
    let res: ScriptCheckAudioItem[] = []
    if (params.id > -1) {
      res = <ScriptCheckAudioItem[]>await scriptCheckModel.getAudioList(params)
    }

    // 更新列表
    audioAllList.value = Array.isArray(res) ? res : []
    // 更新列表总长度
    audioTableSize.value = audioAllList.value.length
    // 页码重置到第一页
    audioPageNum.value = 1

  } catch (e) {
    ElMessage({
      message: '音频列表获取失败',
      type: 'error',
    })
  } finally {
    // 节流
    setTimeout(() => {
      // 解锁
      loadingAudioList.value = false
    }, 200)
  }
}

const audioList = computed(() => {
  return audioAllList.value?.slice((audioPageNum.value - 1) * audioPageSize.value, audioPageNum.value * audioPageSize.value) || []
})

/**
 * 修改音频列表当前页码
 */
const updateAudioPage = (p: number, s: number) => {
  audioPageNum.value = p
  audioPageSize.value = s
}


// 只看更新开关切换时
watch(
  onlyNew,
  async () => {
    if (loadingAudioList.value) {
      return
    }
    // 上锁
    loadingAudioList.value = true

    // 更新页面显示的列表，只显示新记录
    await updateAudioAllList()
  }
)

/**
 * 播放音频，显示浮窗
 */
class TempAudioOrigin {
  id = -1
  name = ''
  content = ''
  audioPath = ''
}

const audioStatus = ref<'pause' | 'play' | 'none'>('none')
const playRate = ref(1)
const tempAudio = reactive<ScriptCheckAudioItem>(new TempAudioOrigin())
const handleAudioChange = (flag: -1 | 1, aId: number) => {
  const index = audioList.value.findIndex(item => item.id == aId)
  const index2 = index + flag
  if (!!audioList.value[index2]) {
    if (audioList.value[index2].audioPath) {
      const row = audioList.value[index2]
      return Object.assign(tempAudio, row ? {
        id: row.id,
        name: row.contentName,
        content: row.content,
        audioPath: row.audioPath,
      } : new TempAudioOrigin())
    } else {
      handleAudioChange(flag, audioList.value[index2].id || -1)
    }
  } else {
    return Object.assign(tempAudio, new TempAudioOrigin())
  }
}
const handleAudioPlay = (row?: ScriptCheckAudioItem) => {
  if (tempAudio.id === row?.id && tempAudio.audioPath === row?.audioPath) {
    Object.assign(tempAudio, row ? {
      id: row.id,
      name: row.contentName,
      content: row.content,
      audioPath: row.audioPath,
    } : new TempAudioOrigin())
    audioStatus.value = audioStatus.value == 'play' ? 'pause' : 'play'
  } else {
    Object.assign(tempAudio,  row ? {
      id: row.id,
      name: row.contentName,
      content: row.content,
      audioPath: row.audioPath,
    } : new TempAudioOrigin())
    audioStatus.value = 'play'
  }
}

// ---------- 音频列表模块 结束 ----------

// ---------- 日志列表模块 开始 ----------

// 正在加载日志列表
const loadingLogList = ref<boolean>(false)

// 操作记录列表，全部，接口返回
const logAllList = ref<ScriptOperationLog[]>([])
// 当前页码，从1开始
const logPageNum = ref<number>(1)
// 每页大小
const logPageSize = ref<number>(20)
// 列表总长度
const logTableSize = ref<number>(0)

/**
 * 更新整个操作日志列表
 */
const updateLogAllList = async () => {
  // 上锁，正在加载日志列表
  loadingLogList.value = true

  try {
    // 处理参数
    const params = {
      id: currentScriptItem.value?.scriptStringId ?? -1
    }

    // 请求接口
    let res: ScriptOperationLog[] = []
    if (params.id !== -1) {
      res = <ScriptOperationLog[]>await scriptCheckModel.getOperationLogList(params)
    }

    // 更新列表
    logAllList.value = Array.isArray(res) ? res : []
    // 按时间倒序排序
    logAllList.value = logAllList.value.sort((a, b) => {
      return dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1
    })

    // 更新列表总长度
    logTableSize.value = logAllList.value.length
    // 页码重置到第一页
    logPageNum.value = 1

    // 节流
    setTimeout(() => {
      // 解锁
      loadingLogList.value = false
    }, 200)
  } catch (e) {
    ElMessage({
      message: '操作记录获取失败',
      type: 'error',
    })
    // 解锁
    loadingLogList.value = false
  }
}

const logList = computed(() => {
  return logAllList.value?.slice((logPageNum.value - 1) * logPageSize.value, logPageNum.value * logPageSize.value) || []
})

/**
 * 修改音频列表当前页码
 */
const updateLogPage = (p: number, s: number) => {
  logPageNum.value = p
  logPageSize.value = s
}

// ---------- 日志列表模块 结束 ----------

const handleTabChange = () => {
  if (activeTab.value === tabList[1]) {
    updateLogAllList()
  }
  if (activeTab.value === tabList[0]) {
    updateAudioAllList()
  }
}

// ---------- 话术审核弹窗 开始 ----------

// 显示审核话术确认弹窗
const checkScriptDialogVisible = ref<boolean>(false)
// 审核话术弹窗通过状态
const checkScriptDialogApproveStatus = ref<boolean>(false)

/**
 * 提交话术审核意见
 */
const submitScriptCheck = (data: { approved: boolean, opinion: string }) => {
  checkScript({ ...data })
}

/**
 * 审批话术
 */
const checkScript = async (content: { approved: boolean, opinion: string }) => {
  // 添加loading状态
  loading.value = true

  // 处理参数
  const params = {
    id: currentScriptItem.value?.id || -1,
    checkRes: content.approved ? '通过' : '驳回',
    opinion: content.opinion
  }

  const err = await traceApi(
    `话术审核-${content.approved ? '通过' : '驳回'}`,
    params,
    scriptCheckModel.approveScript // 请求接口
  )

  // 审核成功
  if (!err) {
    ElMessage.success(`审核成功，${params.checkRes}当前话术`)
    // 更新话术列表
    await updateWholeModule()
  }

  // 关闭loading状态
  loading.value = false
}

// ---------- 话术审核弹窗 结束 ----------
</script>

<style scoped lang="postcss">
/* 话术图标 */
.script-check-item-icon {
  width: 18px;
  height: 18px;
  background-image: url("@/assets/img/rebot.png");
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.module-content {
  padding: 12px 0 0 12px;
  margin: 0;
  .el-table {
    font-size: var(--el-font-size-base);
    :deep(.cell) {
      padding: 0 8px;
    }
    :deep(.caret-wrapper) {
      display: none;
    }
  }
}
.module-main {
  padding: 0;
  height: calc(100vh - 130px);
  min-width: 1000px;
  width: 100%;
  overflow-x: hidden;
  .module-aside {
    padding: 12px;
    background-color: #fff;
    width: 250px;
    .aside-item {
      border: 1px solid var(--primary-black-color-300);
      padding: 8px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      min-height: 70px;
      .status-box-mini {
        width: 36px;
        height: 18px;
      }
      .title {
        color: var(--primary-black-color-600);
        font-size: 14px;
        line-height: 18px;
        font-weight: 600;
      }
      &.aside-item--active {
        border-color: var(--primary-blue-color);
        .title {
          color: var(--primary-blue-color);
        }
      }
    }
  }
  .submodule-title {
    padding: 0;
    display: flex;
    justify-content: space-between;
  }
    /* 标题文本 */
  .submodule-title-text {
    font-size: 15px;
    font-weight: 600;
    text-align: left;
    word-break: break-all;
  }
}
</style>
