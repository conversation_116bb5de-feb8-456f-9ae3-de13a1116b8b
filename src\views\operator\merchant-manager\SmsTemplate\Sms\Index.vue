<template>
  <HeaderBox :title="[{title: '商户管理'}, {title: '编辑短信模板'}]" />

  <div class="tw-flex-auto tw-min-h-[1px]">
    <SmsConfig ref="smsConfigRef" :data="form" @update="onUpdateFormFromComponent" />
  </div>

  <div class="submodule-detail">
    <div class="detail-footer">
      <div class="detail-footer-inner">
        <el-button
          :icon="CloseBold"
          :disabled="loadingConfirm"
          @click="onClickCancel"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          :icon="Select"
          :loading="loadingConfirm"
          :disabled="loadingConfirm || exceedSmsLengthLimit"
          @click="onClickConfirm"
        >
          确定
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import HeaderBox from '@/components/HeaderBox.vue'
import { computed, reactive, ref, toRaw } from 'vue'
import SmsConfig from './SmsConfig.vue'
import { useMerchantStore } from '@/store/merchant'
import { Throttle } from '@/utils/utils'
import { SmsTemplateItem, SmsTemplateParams, SmsTemplateStatusEnum } from '@/type/merchant'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import { CloseBold, Select } from '@element-plus/icons-vue'
import router from '@/router'
import to from 'await-to-js'
import { merchantSmsTemplateModel } from '@/api/merchant'
import { useSmsStore } from '@/store/sms'
import { storeToRefs } from 'pinia'
import { trace } from '@/utils/trace'

// ---------------------------------------- 通用 开始 ----------------------------------------

const merchantStore = useMerchantStore()
const smsStore = useSmsStore()
const { smsLength } = storeToRefs(smsStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const smsConfigRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): SmsTemplateItem => ({
  tenantId: merchantStore.currentMerchant.id ?? undefined,
  groupId: merchantStore.currentAccount.groupId ?? undefined,
})
// 表单数据
const form: SmsTemplateItem = reactive(formDefault())

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  smsConfigRef.value?.formRef && smsConfigRef.value.formRef.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: SmsTemplateParams = JSON.parse(JSON.stringify(toRaw(form)))
  // console.log('params', JSON.parse(JSON.stringify(params)))

  // 埋点
  trace({
    page: `商户管理-${form.id !== undefined ? '编辑' : '新建'}短信模板`,
    params,
  })
  // 请求接口
  let error: any
  let res: SmsTemplateItem
  // 是否存在ID
  if (form.id !== undefined) {
    // 编辑
    const [err, data] = <[any, SmsTemplateItem]>await to(merchantSmsTemplateModel.editSmsTemplate(params))
    error = err
    res = data
  } else {
    // 新建
    const [err, data] = <[any, SmsTemplateItem]>await to(merchantSmsTemplateModel.addSmsTemplate(params))
    error = err
    res = data
  }

  // 返回失败结果
  if (error) {
    ElMessage({
      type: 'error',
      message: '保存失败'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }

  // 返回成功结果
  ElMessage({
    message: '保存成功',
    type: 'success',
  })

  // 节流锁解锁
  throttleConfirm.unlock()

  // 针对新建成功、后端成功返回短息模板数据的情况，询问是否需要直接前往短信通道配置
  if (!form.id && !!res && !!res.id) {
    closeNewSmsTemplate(res)
  } else {
    // 关闭弹窗
    closeDialog()
  }

}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  smsConfigRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 更新表单
 * @param data 手动更新的数据
 */
const updateForm = (data: SmsTemplateItem = {}) => {
  Object.assign(form, data)
  form.templateStatus = data.templateStatus ?? SmsTemplateStatusEnum['启用']
  form.variableUsed = data.variableUsed ?? []
}
/**
 * 新增完成跳转
 */
const closeNewSmsTemplate = (data: SmsTemplateItem) => {
  Confirm({
    text: `为确保新短信模板正常使用，您还需配置短信通道，是否前往配置？`,
    type: 'warning',
    title: '前往确认',
    confirmText: '立即前往'
  }).then(() => {
    // 缓存数据
    merchantStore.editingSmsTemplate = data
    router.push({ name: 'SmsChannelDetail' })
  }).catch(() => {
    // 关闭弹窗
    closeDialog()
  })
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  router.back()
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}
/**
 * 子组件更新表单
 * @param data 新的表单数据
 */
const onUpdateFormFromComponent = (data: SmsTemplateItem) => {
  Object.assign(form, data)
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 短信内容 开始 ----------------------------------------

// 短信全文长度是否超过限制
const exceedSmsLengthLimit = computed(() => {
  return smsLength.value > 69
})

// ---------------------------------------- 短信内容 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// console.log('merchantStore.editingSmsTemplate', merchantStore.editingSmsTemplate)
updateForm(merchantStore.editingSmsTemplate)

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.detail-footer-inner {
  width: 756px;
  margin: 0 auto;
}
</style>
