import { InfoQueryItem, InterruptTypeEnum, scriptUnitContent } from '@/type/speech-craft'
import { RecordDialogueData } from '@/type/task'

/**
 * hitBranch组成
 * 1：沉默
 * 2：重复
 * 3：语料名->普通分支:分支名--命中语义
 * 4：语料名->查询分支:分支名{信息字段=信息字段值}
 * 5：语料名->统一回复
 */
/** 翻译分支命中事件类型，主要翻译沉默和信息查询 */
export const translateBranchType = (branch: string, infoQueryMap?: Map<string, InfoQueryItem>) => {
  if (branch.includes('沉默')) {
    return ['沉默']
  }

  if (branch.includes('查询分支')) {
    const obj = branch?.split('{')[1]?.slice(0, -1)
    let res: string[] = []
    obj?.split(',').map(item => {
      const [infoFieldName = '', val = ''] = item?.trim()?.split('=')
      const definition = infoQueryMap?.get(infoFieldName)?.infoQueryValues?.find(info => info.value === val)?.definition || ''
      res.push(definition ? `${item}(${definition})` : item)
    })
    return res
  }
}
/** 翻译分支命中信息 */
export const translateBranchMsg = (branch: string) => {
  const arrowIndex = branch.indexOf('->');
  const dashIndex = branch.indexOf('--');
  if (arrowIndex === -1) {
    return branch; // 如果没有 '->'，返回字符串
  }
  const preCorpusName = branch.slice(0, arrowIndex).trim();
  let info = ''
  if (dashIndex === -1 || dashIndex < arrowIndex) {
    info =  branch.slice(arrowIndex + 2).trim(); // 获取 '->' 后面的所有内容
  } else {
    info = branch.slice(arrowIndex + 2, dashIndex).trim(); // 获取 '->' 和 '--' 之间的内容
  }
  if (info?.includes('查询分支') || info?.includes('普通分支')) {
    const arr = info.split(':')
    return `${preCorpusName}-${arr[1]??''}(${arr[0]})`
  } else {
    return branch.replace('->', '-')
  }
}

/** 翻译分支命中信息 */
export const translateHitSemanticFromBranch = (branch: string) => {
  const dashIndex = branch.indexOf('--');
  if (dashIndex === -1) {
    return ''; // 如果没有 '->'，返回字符串
  }
  return branch.slice(dashIndex + 2).trim(); // 获取 '--' 后面的所有内容
}

export const getTimeClass = (str?: string) => {
  if (str?.includes('转人工')) {
    return 'pop-win-time-box'
  } else {
    return ''
  }
}

export /**
* 格式化对话内容
* 已说完的用黑色展示，未说出的内容用灰色展示
* 打断/返回相关字段用来区分已说完和未说完
* @param item 单条对话内容
* @param bubbleColor 气泡颜色
*/
const formatContent = (item: Partial<RecordDialogueData>, bubbleColor: string = 'white') => {
 let str = item?.content ?? ''
 // 转义字符
 str = str.replace('\\\\', '\\')
 // 打断/返回
 if (item?.unitContent) {
   str = str.replace(
     item.unitContent,
     bubbleColor === 'white'
       ? `<span style="color: #323233;">${item.unitContent}</span>`
       : `<span style="color: #fff;">${item.unitContent}</span>`
   )
   str = bubbleColor === 'white'
     ? `<span style="color: var(--primary-black-color-300);">${str || '（空）'}</span>`
     : `<span style="color: var(--primary-black-color-300);">${str || '（空）'}</span>`
 }
 return str || '（空）'
}
