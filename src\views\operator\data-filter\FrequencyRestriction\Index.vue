<template>
  <!--模块标题-->
  <HeaderBox title="频率限制" />

  <!--模块主体-->
  <div class="module-container tw-min-w-[1080px]">
    <el-scrollbar class="tw-px-[16px]" view-class="module-container-inner">

      <!--标签卡-->
      <TabsBox
        :active="listTabName"
        :tabList="Object.values(FrequencyRestrictionTypeEnum)"
        @update:active="handleListTabChange"
      />

      <!--全局限制-->
      <div v-if="listTabName===FrequencyRestrictionTypeEnum.GLOBAL" v-loading="loadingGlobal" class="tab-box">
        <Table
          :type="FrequencyRestrictionTypeEnum.GLOBAL"
          :data="globalTableData"
          @update="onUpdateTableGlobal"
        />
      </div>

      <!--行业限制-->
      <div v-else-if="listTabName===FrequencyRestrictionTypeEnum.INDUSTRY" v-loading="loadingIndustry" class="tab-box">
        <Table
          :type="FrequencyRestrictionTypeEnum.INDUSTRY"
          :data="industryTableData"
          @update="onUpdateTableIndustry"
        />
      </div>

      <!--产品限制-->
      <div v-else-if="listTabName===FrequencyRestrictionTypeEnum.PRODUCT" v-loading="loadingProduct" class="tab-box">
        <Table
          :type="FrequencyRestrictionTypeEnum.PRODUCT"
          :data="productTableData"
          @update="onUpdateTableProduct"
        />
      </div>

    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, onMounted, ref } from 'vue'
import TabsBox from '@/components/TabsBox.vue'
import { FrequencyRestrictionInfo, FrequencyRestrictionParam, FrequencyRestrictionTypeEnum } from '@/type/dataFilter'
import { Throttle } from '@/utils/utils'
import { frequencyRestrictionModel } from '@/api/data-filter'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import Table from './Table.vue'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 标签卡 开始 ----------------------------------------

// 当前激活的列表标签卡名称
const listTabName = ref<string>(FrequencyRestrictionTypeEnum.GLOBAL)

/**
 * 切换标签卡
 */
const handleListTabChange = (val: string) => {
  // console.log('handleListTabChange', val)
  listTabName.value = val
  updateListTab()
}
/**
 * 更新标签卡
 */
const updateListTab = () => {
  // console.log('updateListTab')
  // 根据标签卡名称更新相关列表
  switch (listTabName.value) {
    case FrequencyRestrictionTypeEnum.GLOBAL:
      updateRestrictionGlobal()
      break
    case FrequencyRestrictionTypeEnum.INDUSTRY:
      updateRestrictionIndustry(undefined)
      break
    case FrequencyRestrictionTypeEnum.PRODUCT:
      updateRestrictionProduct(undefined)
      break
    default:
      break
  }
}

// ---------------------------------------- 标签卡 结束 ----------------------------------------

// ---------------------------------------- 全局限制 开始 ----------------------------------------

// Global

// 全局限制 正在加载
const loadingGlobal = ref<boolean>(false)
// 全局限制 加载节流锁
const throttleGlobal = new Throttle(loadingGlobal)

// 全局限制 接口数据 组件数据
const globalTableData = ref<FrequencyRestrictionInfo[]>([])

/**
 * 全局限制 更新
 */
const updateRestrictionGlobal = async () => {
  // console.log('updateRestrictionGlobal')
  // 节流锁上锁
  if (throttleGlobal.check()) {
    return
  }
  throttleGlobal.lock()

  try {
    // 请求接口
    const res = <FrequencyRestrictionInfo[]>await frequencyRestrictionModel.getGlobalList()
    // 更新数据
    globalTableData.value = res?.length ? res : []
    // console.log('globalTableData.value', globalTableData.value.length)
  } catch (e) {
    // 更新数据
    globalTableData.value = []
  } finally {
    // 节流锁解锁
    throttleGlobal.unlock()
  }
}
/**
 * 全局限制 组件更新
 */
const onUpdateTableGlobal = () => {
  // console.log('onUpdateTableGlobal')
  updateRestrictionGlobal()
}

// ---------------------------------------- 全局限制 结束 ----------------------------------------

// ---------------------------------------- 行业限制 开始 ----------------------------------------

// Industry

// 行业限制 正在加载
const loadingIndustry = ref<boolean>(false)
// 行业限制 加载节流锁
const throttleIndustry = new Throttle(loadingIndustry)

// 行业限制 接口数据 组件数据
const industryTableData = ref<FrequencyRestrictionInfo[]>([])

/**
 * 行业限制 更新
 * @param {string | number} industryId 行业ID
 */
const updateRestrictionIndustry = async (industryId?: string | number) => {
  // console.log('updateRestrictionIndustry')
  // 节流锁上锁
  if (throttleIndustry.check()) {
    return
  }
  throttleIndustry.lock()

  const params: FrequencyRestrictionParam = {
    industryId: (industryId ?? '') + '' || undefined,
  }
  const [err, res] = <[any, FrequencyRestrictionInfo[]]>await to(frequencyRestrictionModel.getIndustryList(params))
  if (err) {
    ElMessage({
      type: 'error',
      message: '无法获取行业限制列表'
    })
    industryTableData.value = []
    // 节流锁解锁
    throttleIndustry.unlock()
    return
  }
  industryTableData.value = res?.length ? res : []
  // console.log('industryTableData.value', industryTableData.value.length)

  // 节流锁解锁
  throttleIndustry.unlock()
}
/**
 * 行业限制 组件更新
 * @param {string} val 行业ID
 */
const onUpdateTableIndustry = (val: string) => {
  // console.log('onUpdateTableIndustry')
  updateRestrictionIndustry(val)
}

// ---------------------------------------- 行业限制 结束 ----------------------------------------

// ---------------------------------------- 产品限制 开始 ----------------------------------------

// Product

// 产品限制 正在加载
const loadingProduct = ref<boolean>(false)
// 产品限制 加载节流锁
const throttleProduct = new Throttle(loadingProduct)

// 产品限制 接口数据 组件数据
const productTableData = ref<FrequencyRestrictionInfo[]>([])

/**
 * 产品限制 更新
 * @param {string | number} productId 产品ID
 */
const updateRestrictionProduct = async (productId?: string | number) => {
  // console.log('updateRestrictionProduct')
  // 节流锁上锁
  if (throttleProduct.check()) {
    return
  }
  throttleProduct.lock()

  const params: FrequencyRestrictionParam = {
    productId: (productId ?? '') + '' || undefined,
  }
  const [err, res] = <[any, FrequencyRestrictionInfo[]]>await to(frequencyRestrictionModel.getProductList(params))
  if (err) {
    ElMessage({
      type: 'error',
      message: '无法获取产品限制列表'
    })
    productTableData.value = []
    // 节流锁解锁
    throttleProduct.unlock()
    return
  }
  productTableData.value = res?.length ? res : []
  // console.log('productTableData.value', productTableData.value.length)

  // 节流锁解锁
  throttleProduct.unlock()
}
/**
 * 产品限制 组件更新
 * @param {string} val 产品ID
 */
const onUpdateTableProduct = (val: string) => {
  // console.log('onUpdateTableProduct')
  updateRestrictionProduct(val)
}

// ---------------------------------------- 产品限制 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// console.log('index setup')
onMounted(updateListTab)

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.module-container {
  padding: 16px 0;
  text-align: left;
}
</style>
