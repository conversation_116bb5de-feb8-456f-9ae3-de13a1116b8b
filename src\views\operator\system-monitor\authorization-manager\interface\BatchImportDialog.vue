<template>
  <el-dialog
    :model-value="visible"
    width="640px"
    class="dialog-form"
    :close-on-click-modal="false"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">批量导入接口</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-steps
        class="tw-mb-1"
        style="max-width: 600px"
        :space="200"
        :active="step"
        simple
        finish-status="success"
      >
        <el-step title="创建接口" />
        <el-step title="绑定接口菜单" />
        <el-step title="完成" />
      </el-steps>
      <div class="tw-text-left">
        <!-- 接口创建情况 -->
        <template v-if="newInterfaceList?.length">
          <div>
            <span class="info-title">创建接口：</span>
            <span class="tw-text-[var(--primary-blue-color)] tw-text-[13px]">{{ newInterfaceList?.length || 0 }}</span>
          </div>
          <div>
            <div v-for="(item, index) in newInterfaceList" :key="index" class="name-box">
              <span class="rectangle-left"></span>
              <span class="tw-truncate">{{ item||'' }}</span>
            </div>
          </div>
        </template>
        <!-- 接口页面关系创建情况 -->
        <div v-if="step === 2">
          <div>
            <span class="info-title">新绑定接口：</span>
          </div>
          <div>
            <div v-for="item in Object.keys(menuCodeObj)" :key="item" class="name-box">
              <span class="rectangle-left"></span>
              <span>
                {{ `${menuCodeObj[item].menuName || ''}(${item})成功导入` }}
                <span class="tw-text-[var(--primary-blue-color)]">【{{menuCodeObj[item].permissionIdList?.length || 0}}】</span>
                个接口
              </span>
            </div>
          </div>
        </div>
        <!-- 失败信息汇总 -->
        <template v-if="errorMsg?.length">
          <div class="tw-text-[var(--primary-red-color)]">
            <span class="info-title">失败：</span>
            <span class="tw-text-[var(--primary-red-color)] tw-text-[13px]">{{ errorMsg?.length || 0 }}</span>
          </div>
          <div>
            <div v-for="(item, index) in errorMsg" :key="index" class="name-box">
              <span class="rectangle-left-error"></span>
              <span class="tw-truncate">{{ item || '' }}</span>
            </div>
          </div>
        </template>
      </div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <div>
          <el-button :loading="loading" @click="cancel" :icon="CloseBold">关闭</el-button>
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, } from '@element-plus/icons-vue'
import { authorizationModel } from '@/api/authorization'
import to from 'await-to-js';
import { InterfaceTypeEnum, } from '@/type/authorization'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  data: Record<string, any>[] | null
}>();
const loading = ref(false)
const visible = ref(props.visible)

const step = ref(0)
// 获取接口列表
const findInterfaceAndMenuList = async () => {
  const res1 = await to(authorizationModel.findInterfaceList())
  const res2 = await to(authorizationModel.findMenuList())
  return {
    interfaceList: res1[1] || [],
    menuList: res2[1] || [],
  }
}

const newInterfaceList = ref<string[] | null>(null)
const errorMsg = ref<string[] | null>(null)
const menuCodeObj = reactive<Record<string, {menuId: number, permissionIdList: number[], menuName: string}>>({})
const initAction = async () => {
  if (!props.data?.length) {
    cancel()
    return ElMessage.warning('文档内容为空')
  }
  errorMsg.value = []
  // 第一步：检查接口并创建（已有接口则获取，非已有接口则创建，data内数据去重）
  step.value = 0
  loading.value = true
  const { interfaceList, menuList } = await findInterfaceAndMenuList()
  
  const newInterfaceMap = new Map<string, number>([]) // 为页面绑定接口（根据页面code查找），如未找到菜单code则报错；
  for (let index = 0; index < props.data?.length; index++) {
    const item = props.data[index]
    // 缺少必填项
    if (!(item['接口名称'] && item['服务名'] && item['接口地址'])) {
      return errorMsg.value?.push(`第${index + 1}行数据：${item['接口名称']}缺少必填项`)
    }
    // 获取接口的id，先在本次创建中找，再去原有的列表找，最后再创建并获取
    let interfaceId: number | null = null
    if (newInterfaceMap.has(`/${item['服务名']}${item['接口地址']}`)) {
      interfaceId = newInterfaceMap.get(`/${item['服务名']}${item['接口地址']}`) as number
    } else if (interfaceList?.find(i => i.identifier === item['接口地址'] && i.serviceId === item['服务名'])) {
      interfaceId = interfaceList?.find(i => i.identifier === item['接口地址'] && i.serviceId === item['服务名'])?.id as number
    } else {
      const [err, res] = await to(authorizationModel.saveInterface({
        type: item['菜单编码'] ? InterfaceTypeEnum['TOKEN类'] : InterfaceTypeEnum['登录类'],
        name: item['接口名称'].trim(),
        identifier: item['接口地址'].trim(),
        remark: '',
        serviceId: item['服务名'].trim(),
      }))
      if (err) {
        errorMsg.value?.push(`第${index + 1}行数据：${item['服务名']}${item['接口地址']}创建失败`)
      } else {
        newInterfaceMap.set(`/${item['服务名']}${item['接口地址']}`, res?.id as number)
        interfaceId = res?.id
      }
    }
    // 如果获取到接口id，并且无菜单编码（即token类接口），则继续绑定菜单接口关系
    if (interfaceId && item['菜单编码']) {
      const menuCode = item['菜单编码'].trim()?.split(',')
      menuCode.forEach((code: string) => {
        const currentMenu = menuList?.find(m => m.menuCode === code)
        if (!currentMenu) {
          errorMsg.value?.push(`第${index + 1}行数据：菜单${code}未找到`)
        } else {
          if (menuCodeObj[code]) {
            menuCodeObj[code].permissionIdList.push(interfaceId)
          } else {
            menuCodeObj[code] = {
              menuName: currentMenu.menuName,
              menuId: currentMenu.id,
              permissionIdList: [interfaceId],
            }
          }
        }
      })
    }
  }
  newInterfaceList.value =  Array.from(newInterfaceMap.keys())
  // 如存在报错，则停留再这一步
  if (errorMsg.value?.length) {
    loading.value = false
    return
  }
  // 第二步：绑定菜单和接口关系
  step.value = 1
  await Promise.allSettled(Object.keys(menuCodeObj).map(async (code: string) => {
    const { menuId, permissionIdList:newPermissionIdList } = menuCodeObj[code]
    const [err1, res1] = await to(authorizationModel.findInterfacePermissionByMenuId({ menuId }))
    if (err1) {
      return errorMsg.value?.push(`菜单${code}获取绑定接口失败`)
    }
    const currentPermissionIdList = res1?.map(item => item.id) || []
    const [err2] = await to(authorizationModel.saveMenuPermission({
      menuId,
      permissionIdList: [...new Set([...currentPermissionIdList, ...newPermissionIdList])]
    }))
    if (err2) {
      errorMsg.value?.push(`菜单${code}绑定失败：${err2?.message}`)
    }
  }) || [])
  emits('confirm')
  step.value = 2
  loading.value = false
}
const cancel = () => {
  emits('update:visible', false)
}

watch(() => props.visible, n => {
  visible.value = n
  if (n) {
    initAction()
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.name-box {
  display: flex;
  height: 36px;
  padding: 8px 12px;
  margin: 6px auto;
  align-items: center;
  gap: 10px;
  font-size: 13px;
  border-radius: 4px;
  background: #F0F2F5;
  position: relative;
  line-height: 20px;
  color: var(--primary-black-color-600);
}
.rectangle-left {
  border-radius: 0px 2px 2px 0px;
  width: 2px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 6px;
  background-color: var(--primary-blue-color)
}
.rectangle-left-error {
  border-radius: 0px 2px 2px 0px;
  width: 2px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 6px;
  background-color: var(--primary-red-color)
}
</style>