<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">单个导入</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="130px"
        ref="editRef"
      >
       <el-form-item label="手机号：" prop="手机号">
          <el-input v-model="editData['手机号']" placeholder="请输入手机号"/>
        </el-form-item>
        <el-form-item label="姓名：" prop="姓名">
          <el-input v-model="editData['姓名']" placeholder="请输入姓名，最多50个字符"/>
        </el-form-item>
        <el-form-item label="公司：" prop="公司">
          <el-input v-model="editData['公司']" placeholder="请输入公司，最多100个字符"/>
        </el-form-item>
        <el-form-item label="备注：" prop="备注">
          <el-input v-model="editData['备注']" placeholder="请输入备注，最多200个字符"/>
        </el-form-item>
        <el-form-item v-for="variableSmsItem in variableSms" :label="variableSmsItem.variableName + '：'" :key="variableSmsItem.variableName" :prop="variableSmsItem.variableName">
          <InputNumberBox
            v-if="SmsVariableColumnTypeEnum['金额'] === variableSmsItem.variableType"
            v-model:value="editData[variableSmsItem.variableName]"
            :placeholder="`请输入${variableSmsItem.variableName}(${findValueInEnum(variableSmsItem.variableType, SmsVariableColumnTypeEnum)})`"
            class="tw-grow"
            style="width: 209px;"
            :min="0"
            :precision="2"
            append="元"
          />
          <el-cascader
            v-else-if="SmsVariableColumnTypeEnum['城市'] === variableSmsItem.variableType"
            v-model.trim="editData[variableSmsItem.variableName]"
            :placeholder="`请输入${variableSmsItem.variableName}(${findValueInEnum(variableSmsItem.variableType, SmsVariableColumnTypeEnum)})`"
            class="tw-grow"
            clearable
            filterable
            :options="cityCodeOptions"
            :props="cityCodeProps"
            show-all-levels
          />
          <el-date-picker
            v-else-if="SmsVariableColumnTypeEnum['日期'] === variableSmsItem.variableType"
            v-model="editData[variableSmsItem.variableName]"
            class="tw-grow"
            type="date"
            :placeholder="`请输入${variableSmsItem.variableName}(${findValueInEnum(variableSmsItem.variableType, SmsVariableColumnTypeEnum)})`"
            clearable
            value-format="YYYY-MM-DD"
          />
          <el-time-picker
            v-else-if="SmsVariableColumnTypeEnum['时间'] === variableSmsItem.variableType"
            v-model="editData[variableSmsItem.variableName]"
            clearable
            class="tw-grow"
            :placeholder="`请输入${variableSmsItem.variableName}(${findValueInEnum(variableSmsItem.variableType, SmsVariableColumnTypeEnum)})`"
            value-format="HH:mm:ss"
          />
          <el-input-number
            v-else-if="[SmsVariableColumnTypeEnum['数字'], ].includes(variableSmsItem.variableType)"
           v-model="editData[variableSmsItem.variableName]"
           class="tw-grow"
           :controls="false"
           :placeholder="`请输入${variableSmsItem.variableName}(${findValueInEnum(variableSmsItem.variableType, SmsVariableColumnTypeEnum)})`"
            clearable
          />
          <el-input v-else v-model="editData[variableSmsItem.variableName]" :placeholder="`请输入${variableSmsItem.variableName}`"/>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, nextTick } from 'vue'
import { TaskManageItem, TaskStatusEnum, TaskTypeEnum } from '@/type/task'
import { ElMessage, } from 'element-plus'
import { generateExcelByAoa } from '@/utils/export'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { useUserStore } from '@/store/user'
import { phoneReg, } from '@/utils/constant'
import { SmsVariableColumnTypeEnum, SystemVariableEnum } from '@/type/merchant'
import type { FormInstance, FormRules } from 'element-plus'
import InputNumberBox from '@/components/InputNumberBox.vue';
import { useGlobalStore } from '@/store/globalInfo'
import { findValueInEnum } from '@/utils/utils'
import { trace } from '@/utils/trace';
import to from 'await-to-js';

const emits = defineEmits(['update:visible', 'confirm'])
const userInfo  = useUserStore()
const loading = ref(false)
const props = defineProps<{
  visible: boolean,
  data: TaskManageItem
}>();

const globalStore = useGlobalStore()
// 地区 级联选择器 配置项
const cityCodeProps = { multiple: false, emitPath: false }
// 地区 级联选择器 数据
const cityCodeOptions = (globalStore.getProvinceCascaderOption)


const editData = reactive<Record<string, string | number | undefined>>({
  '手机号': '', '姓名': '', '公司': '', '备注': '',
})
const dialogVisible = ref(props.visible)
const editRef = ref<FormInstance  | null>(null)
const variableSms = ref<{variableName: string, variableType: SmsVariableColumnTypeEnum}[]>([])
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const validatePhone = (rule: any, value: any, callback: any) => {
  // 兼容11位常规手机号和+68开头的特殊16位手机号
  if (!value.match(phoneReg) && !value.startsWith('+')) {
    callback(new Error('手机号不合规'))
  } else {
    callback()
  }
}
const rules = computed(() =>{
  const res: FormRules = {
    '手机号': [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { validator: validatePhone, trigger: 'blur' },
    ],
    '姓名': [
      { max: 50, message: '姓名不能超过50个字符', trigger: 'blur' },
    ],
    '公司': [
      { max: 100, message: '公司名称不能超过100个字符', trigger: 'blur' },
    ],
    '备注': [
      { max: 200, message: '备注不能超过200个字符', trigger: 'blur' },
    ],
  }
  variableSms.value?.forEach(item => {
    res[item.variableName] = [{ required: true, message: `请输入${item.variableName}`, trigger: 'blur' }]
  })
  return res
})
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const fm = new FormData();
      const titleArr = ['手机号', '姓名', '公司', '备注', ]
      const paramsArr = [editData['手机号'], editData['姓名'], editData['公司'], editData['备注']]
      variableSms.value?.forEach(item => {
        titleArr.push(item.variableName)
        paramsArr.push(editData[item.variableName])
      })

      const data = generateExcelByAoa([
        titleArr, paramsArr as string[]
      ])
      data && fm.append('file', data);
      if (!props.data?.id) return ElMessage.warning('获取任务信息失败')
      await trace({
        page: `${findValueInEnum(props.data.taskType, TaskTypeEnum)}任务-导入名单-单个导入: 开始`,
        params: {...editData, taskId: props.data.id},
      })
      const [err] = await to(aiOutboundTaskModel.importPhonesFromExcel(fm, {id: props.data.id}))
      await trace({
        page: `${findValueInEnum(props.data.taskType, TaskTypeEnum)}任务-导入名单-单个导入: 完成`,
        params: err || null,
      })
      if (!err) {
        ElMessage.success('导入成功')
        emits('confirm')
        cancel()
      }
    }
  })
}
watch(() => props.visible, async () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    loading.value = false
    editData['手机号'] = ''
    editData['姓名'] = ''
    editData['公司'] = ''
    editData['备注'] = ''
    // @ts-ignore
    variableSms.value = props.data.variableSms?.flatMap(item => {
      if (item.variableName && !findValueInEnum(item.variableName, SystemVariableEnum)) {
        editData[item.variableName] = undefined
        return [{variableName: item.variableName, variableType: item.variableType!}]
      } else {
        return []
      }
    }) || []
    setTimeout(() => {
      editRef.value && editRef.value.clearValidate()
    }, 10);
    
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
</style>
