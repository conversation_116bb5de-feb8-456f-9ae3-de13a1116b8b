<template>
  <el-dialog
    v-model="dialogVisible"
    class="requirement-dialog tw-max-h-full tw-overflow-y-auto"
    width="800px"
    align-center
    :before-close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">重复语触发条件</div>
    </template>
    <el-form
      :model="requirementData"
      :rules="rules"
      :disabled="isChecked"
      label-width="80px"
      ref="branchEditRef"
    >
      <el-form-item label-width="0" prop="satisfySemConditions">
        <MultCorpusRuleBox
            v-if="dialogVisible && requirementData.semCombineEntity?.satisfySemConditions"
          v-model:data="requirementData.semCombineEntity.satisfySemConditions"
          :readonly="isChecked"
          required
          title="满足条件"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, defineAsyncComponent,} from 'vue'
import { ElMessage, } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { scriptCorpusModel, } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import to from 'await-to-js';
import { trace } from '@/utils/trace'
import { RequirementData, RequirementOriginData } from './constant'
import { checkCorpusRules }  from '@/components/corpus/constant'

const MultCorpusRuleBox = defineAsyncComponent({ loader:() => { return import('@/components/corpus/MultCorpusConditionBox.vue')}})

const scriptStore = useScriptStore()
const isChecked = scriptStore.isChecked
const loading = ref(false)
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  requirementData: RequirementData | null,
  phraseOptions: {
    id: number,
    name: string,
  }[]
}>();
const dialogVisible = ref(props.visible)
const requirementData = reactive<RequirementData>(props.requirementData || new RequirementOriginData(scriptStore.id))

const rules = {
  satisfySemConditions: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkCorpusRules(requirementData.semCombineEntity?.satisfySemConditions, true)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
}

const branchEditRef = ref<FormInstance  | null>(null)
const confirm = async () => {
  if (!requirementData.scriptId) {
    requirementData.scriptId = scriptStore.id
  }
  branchEditRef.value && branchEditRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      await trace({ page: `话术编辑-重复语料-修改触发条件(${scriptStore.id})`, params: requirementData })
      const [err] = await to(scriptCorpusModel.saveRepeatRequirement(requirementData))
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        cancel()
        emits('confirm')
      }
    }
  })
}
const cancel = () => {
  branchEditRef.value && branchEditRef.value.clearValidate()
  emits('update:visible', false)
}

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    Object.assign(requirementData, props.requirementData?.semCombineEntity?.satisfySemConditions ? JSON.parse(JSON.stringify(props.requirementData)) : new RequirementOriginData(scriptStore.id))
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.requirement-dialog {
  .el-input-number .el-input__inner {
    text-align: left;
  }
  .text {
    font-size: 14px;
    line-height: 24px;
    margin-right: 10px;
    text-align: left;
  }
  .el-form {
    color: var(--primary-black-color-600);
    width: 100%;
    padding: 0 12px;
    .el-form-item {
      margin-bottom: 14px;
      &:first-child {
        margin-top: 14px;
      }
    }
    :deep(.el-form-item__label) {
      padding-right: 0;
    }
    :deep(.el-form-item__content) {
      font-size: var(--el-font-size-base);
    }
  }
}
</style>
