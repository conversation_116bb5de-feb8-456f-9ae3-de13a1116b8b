<template>
  <div class="search-box">
    <div class="tw-grid tw-grid-cols-5 sm:tw-grid-cols-2 tw-gap-[8px] tw-w-full">
      <div class="item">
        <el-input
          v-model.trim="searchForm.name"
          placeholder="名称"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div>
      <div class="item">
        <el-input
          v-model.trim="searchForm.identifier"
          placeholder="接口地址"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div>
      <div class="item">
        <el-select v-model="searchForm.type" placeholder="接口类型" class="tw-w-full" @change="search()" clearable>
          <el-option v-for="item in enum2Options(InterfaceTypeEnum)" :key="item.value" :label="item.name" :value="item.value"/>
        </el-select>
      </div>
      <div class="item">
        <el-select v-model="searchForm.serviceId" placeholder="服务名" class="tw-w-full" @change="search()" clearable>
          <el-option v-for="item in enum2Options(ServiceEnum)" :key="item.value" :label="item.name" :value="item.value"/>
        </el-select>
      </div>
      <div class="item-btn">
        <el-button type="primary" @click="search()" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
  </div>
  <div class="tw-bg-white tw-px-[12px] tw-pb-[12px]  tw-flex tw-justify-end">
    <el-button @click="editInterface()" type="primary">
      <el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="add1"></SvgIcon></el-icon>
      <span>新增接口</span>
    </el-button>
    <input ref="fileRef" type="file" @change="handleFileChange" accept=".xls,.xlsx" class="batch-upload tw-hidden"/>
    <el-button @click="handleUpload()" class="tw-ml-1">
      <el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="upload"></SvgIcon></el-icon>
      <span>批量导入接口</span>
    </el-button>
    <!-- <el-button @click="deleteAll()" type="danger">
      <el-icon :size="16" color="inherit" class="tw-mr-0.5"><SvgIcon name="clear"></SvgIcon></el-icon>
      <span>清空接口</span>
    </el-button> -->
  </div>

  <el-table
    :data="tableTempData"
    v-loading="loading"
    class="tw-grow"
    row-key="id"
    :header-cell-style="tableHeaderStyle"
    @sort-change="handleSortChange"
    stripe
  >
    <el-table-column property="name" fixed="left" label="名称" align="left" show-overflow-tooltip width="280" :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="type" label="类型" align="center" width="120">
      <template #default="{row} : { row: InterfaceItem }">
        <span
          v-if="row?.type"
          class="status-box-mini tw-mx-auto"
          :class="typeClassObj[row.type] || 'blue-status'"
        >
        {{ findValueInEnum(row.type, InterfaceTypeEnum) || row.type }}
        </span>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column property="serviceId" label="服务名" align="left" width="120"></el-table-column>
    <el-table-column property="identifier" label="接口地址" align="left" width="400" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="remark" label="备注" align="left" min-width="240" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="createTime" label="创建时间" align="center" width="160" sortable>
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
      <template #default="{ row }">
        {{ row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
      </template>
    </el-table-column>
    <el-table-column property="updateTime" label="更新时间" align="center" width="160" sortable :formatter="formatterEmptyData">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
      <template #default="{ row }">
        {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="100" align="right" fixed="right">
      <template #default="{ row }">
        <el-button type="primary" link @click="editInterface(row)">编辑</el-button>
        <el-button type="danger" link @click="deleteInterface(row)">删除</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
    </template>
  </el-table>
  <PaginationBox
    :pageSize="pageSize"
    :currentPage="currentPage"
    :total="total"
    @search="search"
    @update="updatePage"
  >
  </PaginationBox>
  <EditDialog
    v-model:visible="editVisible"
    :data="currentData"
    @confirm="search"
  >
  </EditDialog>
  <BatchImportDialog
    v-model:visible="batchImportVisible"
    :data="batchImportData"
    @confirm="search"
  >
  </BatchImportDialog>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { findValueInEnum, formatterEmptyData, handleTableSort, enum2Options } from '@/utils/utils'
import { InterfaceItem, InterfaceTypeEnum, ServiceEnum } from '@/type/authorization'
import { authorizationModel } from '@/api/authorization'
import { tableHeaderStyle } from '@/assets/js/constant'
import { readXlsx, } from "@/utils/export"
import to from 'await-to-js'
import Confirm from '@/components/message-box'
import EditDialog from './EditDialog.vue'
import BatchImportDialog from './BatchImportDialog.vue'
import { typeClassObj } from './constant'
import { useUserStore } from '@/store/user'

const loading = ref(false)


const currentPage = ref(1)
const total = ref(0)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<InterfaceItem[] | null>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const orderCol = ref('createTime')
const orderType = ref('desc')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}
const tableTempData = computed(() => {
  const data1 = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data1.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const searchForm = reactive<Partial<InterfaceItem>>({
  name: '',
  type: undefined,
  serviceId: undefined,
  identifier: '',
})

const search = async () => {
  loading.value = true
  const res = await to(authorizationModel.findInterfaceList())
  tableData.value = (res[1] || []).filter(item => (
    (!searchForm.name || item.name?.includes(searchForm.name))
    && (!searchForm.identifier || item.identifier?.includes(searchForm.identifier))
    && (!searchForm.serviceId || item.serviceId === searchForm.serviceId)
    && (!searchForm.type || item.type === searchForm.type)
  )) || []
  total.value = tableData.value?.length || 0
  loading.value = false
}


const currentData = ref<InterfaceItem | null>(null)
const editVisible = ref(false)

// 编辑接口
const editInterface = (data?: InterfaceItem) => {
  currentData.value = data || null
  editVisible.value = true
}

// 上传文件
const fileRef = ref()
const handleUpload = () => {
  // @ts-ignore
  document.querySelector('.batch-upload')!.click()
}
// 上传文件转化为表格数据
const batchImportVisible = ref(false)
const batchImportData = ref<Record<string, any>[] | null>(null)
const handleFileChange = async (e: Event) => {
  const {data, aoa: aoaData} = await readXlsx(e) as { data: Record<string, any>[], aoa: any[][] }
  if (!data?.length) return ElMessage.warning('文档内容为空')

  fileRef.value.value = null
  const errorMsg: string[] = []
  const titleList = ['接口名称', '服务名', '接口地址', '菜单编码']
  titleList.forEach(item => {
    !aoaData[0].includes(item) && errorMsg.push(`表头缺少${item}`)
  })
  if (errorMsg.length > 0) return ElMessage.warning(errorMsg.join(','))
  batchImportData.value = data
  batchImportVisible.value = true
}

const deleteInterface = (row: InterfaceItem) => {
  if (!row.id) {
    return ElMessage.warning('接口ID获取失败')
  }
  Confirm({
    text: `您确定要删除【${row?.name || ''}】?`,
    type: 'danger',
    title: `删除确认`,
  }).then(async () => {
    const [err] = await to(authorizationModel.deleteInterface({
      id: row.id!,
    }))
    !err && ElMessage.success('删除成功')
    search()
  }).catch(() => {
  })
}


const deleteAll = async () => {
  loading.value = true
  await Promise.allSettled(tableData.value?.map(async item => {
    await to(authorizationModel.deleteInterface({
      id: item.id!
    }))
  }) || [])
  setTimeout(() => {
    search()
  }, 1000)
  loading.value = false
}

onMounted(() => {
  search()
})
onUnmounted(() => {
  tableData.value = null
})

</script>

<style scoped lang="postcss" type="text/postcss">
:deep(.el-table .caret-wrapper) {
  display: none;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
.status-box-mini {
  width: 80px;
}
</style>
