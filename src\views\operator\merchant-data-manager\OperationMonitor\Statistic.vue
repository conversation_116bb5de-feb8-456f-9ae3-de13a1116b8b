<template>
  <div v-if="isMobile" v-loading="loading1" class="tw-p-[4px] tw-bg-white tw-w-full">
    <StatisticMobile :monitorData="monitorData" :concurrentData="concurrentData" @update:total="init"/>
  </div>
  <div v-else class="tw-grid tw-grid-cols-11 tw-gap-[8px] tw-w-full">
    <div v-loading="loading1"  class="card-box tw-flex-col tw-col-span-2">
      <div class="title-normal">
        平台任务数
        <el-tooltip content="刷新数据" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="init"><SvgIcon name="reset" color="inherit"/></el-icon>
        </el-tooltip>
      </div>
      <div class="tw-flex tw-w-full tw-justify-around">
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(monitorData.totalTaskNum || 0) }}</span>
          <span class="title-small tw-mt-1">今日创建</span>
        </div>
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(monitorData.runningTaskNum || 0) }}</span>
          <span class="title-small tw-mt-1">执行中</span>
        </div>
      </div>
    </div>
    <div v-loading="loading1"  class="card-box tw-flex-col  tw-col-span-3">
      <div class="title-normal">
        平台名单数
        <el-tooltip content="刷新数据" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="init"><SvgIcon name="reset" color="inherit"/></el-icon>
        </el-tooltip>
      </div>
      <div class="tw-flex tw-w-full tw-justify-around">
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(monitorData.phoneNum || 0) }}</span>
          <span class="title-small tw-mt-1">今日导入</span>
        </div>
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(monitorData.putThroughPhoneNum || 0) }}</span>
          <span class="title-small tw-mt-1">今日接通</span>
        </div>
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(monitorData.calledPhoneNum || 0) }}</span>
          <span class="title-small tw-mt-1">今日外呼</span>
        </div>
      </div>
    </div>
    <div v-loading="loading1"  class="card-box tw-flex-col  tw-col-span-2">
      <div class="title-normal">
        平台外呼数
        <el-tooltip content="刷新数据" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="init"><SvgIcon name="reset" color="inherit"/></el-icon>
        </el-tooltip>
      </div>
      <div class="tw-flex tw-w-full tw-justify-around">
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(monitorData.callRecordPutThroughNum || 0) }}</span>
          <span class="title-small tw-mt-1">今日接通次数</span>
        </div>
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(monitorData.callRecordNum || 0) }}</span>
          <span class="title-small tw-mt-1">今日外呼次数</span>
        </div>
      </div>
    </div>
    <div v-loading="loading1"  class="card-box tw-flex-col  tw-col-span-2">
      <div class="title-normal">
        平台短信数
        <el-tooltip content="刷新数据" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="init"><SvgIcon name="reset" color="inherit"/></el-icon>
        </el-tooltip>
      </div>
      <div class="tw-flex tw-w-full tw-justify-around">
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(monitorData.sendSmsNum || 0) }}</span>
          <span class="title-small tw-mt-1">今日发送成功</span>
        </div>
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(monitorData.triggerSmsNum || 0) }}</span>
          <span class="title-small tw-mt-1">今日触发次数</span>
        </div>
      </div>
    </div>
    <div v-loading="loading2" class="card-box tw-flex-col is-grid  tw-col-span-2">
      <div class="title-normal">
        线路并发
        <el-tooltip content="刷新数据" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="init"><SvgIcon name="reset" color="inherit"/></el-icon>
        </el-tooltip>
        <!-- <el-tooltip :show-after="500">
          <template #content>
            <div class="tw-text-left">
              <h5>线路并发</h5>
              <div>①供应并发：平台所有任务启动时设置的所需并发总数。</div>
              <div>②锁定并发：呼叫名单时但还未送到FS之前占用的并发总数。</div>
              <div>③实际并发：FS上实际使用的并发总数。</div>
              <div>④暂停中：处于非拨打时段但即将送呼前所锁定的并发总数。</div>
            </div>
          </template>
          <el-icon :size="16" class="tw-ml-[3px] tw-cursor-pointer"><QuestionFilled /></el-icon>
        </el-tooltip> -->
      </div>
      <div class="tw-grid tw-grid-cols-2 tw-w-full tw-justify-around">
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(concurrentData.supplyConcurrent||0) }}</span>
          <span class="title-small">线路供应并发</span>
        </div>
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(concurrentData.tenantConcurrent||0) }}</span>
          <span class="title-small">任务锁定并发</span>
        </div>
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(concurrentData.realConcurrent||0) }}</span>
          <span class="title-small">实际并发</span>
        </div>
        <div class="tw-flex tw-items-center tw-flex-col">
          <span class="number-normal">{{ formatNumber1(concurrentData.pauseConcurrent||0) }}</span>
          <span class="title-small">暂停并发</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, computed, ref, reactive, defineAsyncComponent, } from 'vue'
import { formatNumber1 } from '@/utils/utils'
import to from 'await-to-js'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { MonitorTotalInfo, MonitorConcurrentInfo } from '@/type/monitor-statistic'
import { useGlobalStore } from '@/store/globalInfo'

const StatisticMobile = defineAsyncComponent(() => import('./StatisticMobile.vue'))
const props = defineProps<{
  needUpdate: boolean
}>();
const emits = defineEmits(['update:needUpdate',])

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

/** 头部数据 开始 */ 
const monitorData = reactive<MonitorTotalInfo>({})
const concurrentData = reactive<MonitorConcurrentInfo>({})
// 头部数据loading
const loading1 = ref(false)
const loading2 = ref(false)
const updateMonitorData = async () => {
  loading1.value = true
  const [_, res] = await to(monitorStatisticModel.findTotalData()) as [any, MonitorTotalInfo]
  Object.assign(monitorData, res)
  loading1.value = false
}
const updateConcurrentData = async () => {
  loading2.value = true
  const [_, res] = await to(monitorStatisticModel.findConcurrentData())
  Object.assign(concurrentData, res)
  loading2.value = false
}
/** 头部数据 结束 */ 

const init = async () => {
  await Promise.allSettled([
    updateConcurrentData(),
    updateMonitorData(),
  ])
  emits('update:needUpdate', false)
}
watch(() => props.needUpdate, n => {
  n && init()
})
init()

</script>

<style scoped lang="postcss" type="text/postcss">
.title-normal {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-black-color-600);
  line-height: 20px;
  display: flex;
  align-items: center;
}
.number-normal {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-blue-color);
  line-height: 28px;
  margin-top: 8px;
}
.title-small {
  font-size: 13px;
  color: var(--primary-black-color-400);
  line-height: 20px;
}
.number-small {
  font-size: 13px;
  color: var(--primary-black-color-400);
}
.content-normal {
  color: var(--primary-black-color-600);
  font-size: 13px;
  line-height: 18px;
  text-align: justify;
}
.card-box {
  padding: 16px 12px;
  &.is-grid {
    padding: 12px;
    .number-normal {
      font-size: 18px;
      font-weight: 700;
      color: var(--primary-blue-color);
      line-height: 20px;
      margin-top: 2px;
    }
    .title-small {
      font-size: 12px;
      color: var(--primary-black-color-400);
      line-height: 16px;
    }
  }
}
</style>