import type { RouteRecordRaw } from "vue-router";

interface RouteMeta {
  id: string,
  title: string;
  type?: number;
  icon?: string;
  keepAlive?: boolean;
  preRoute?: string;
  permissions?: {
    id: string | number,
    name: string
  }[];
}
//@ts-ignore
export interface AppRouteRecordRawT extends Omit<RouteRecordRaw, "meta"> {
  name: string;
  meta: RouteMeta;
  children?: AppRouteRecordRawT[];
  fullPath?: string;
}
