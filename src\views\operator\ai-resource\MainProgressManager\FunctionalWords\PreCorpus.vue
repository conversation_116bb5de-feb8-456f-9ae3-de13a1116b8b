<template>
  <div class="repeat-container">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <span class="info-title">该配置仅交互版本2.0可用，1.0版本设置无效</span>
      <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="edit">新增语料</el-button>
    </div>
    <el-table
      :data="tableData || []"
      style="width: 100%"
      id="repeat-draggable-boxes"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column fixed="left" type="index" width="60" />
      <el-table-column fixed="left" property="name" label="语料名称" align="left" min-width="120">
        <template #default="{ row }">
          <div class="tw-w-full tw-flex tw-items-center">
            <span class="tw-line-clamp-2">{{ row.name ?? '-' }}</span>
            <el-icon v-if="row.smsTriggerName && row.corpusType === CorpusTypeEnum['承接语料']" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-sms"></SvgIcon></el-icon>
            <el-icon v-if="row.listenInOrTakeOver && row.corpusType === CorpusTypeEnum['承接语料']" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-human"></SvgIcon></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="content" align="left" label="文字内容" min-width="240">
        <template #default="{ row }">
            {{ row.content || '-' }}
        </template>
      </el-table-column>
      <template v-if="props.corpusType === CorpusTypeEnum['承接语料']">
        <el-table-column property="eventTriggerValueIds" align="left" label="触发事件" min-width="120">
          <template #default="{ row }">
            {{ filterEventValueIds(row.eventTriggerValueIds) || '-' }}
          </template>
        </el-table-column>
        <el-table-column  property="aiIntentionType" align="center" label="分类" width="120">
          <template #default="{ row }">
            {{ row.aiIntentionType ? row.aiIntentionType.intentionType + '-' + row.aiIntentionType.intentionName : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="标签" align="left" min-width="160">
          <template #default="{ row }">
            {{ row.aiLabels?.map((item: LabelItem) =>item.labelName).join(',') || '-' }}
          </template>
        </el-table-column>
      </template>
      <el-table-column property="updateTime" align="center" label="最后更新时间" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">{{!isChecked ? '编辑' : '查看'}}</el-button>
          <el-button v-if="!isChecked" type="danger" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
  </div>
  <BaseCorpusDrawer v-model:visible="corpusDialogVisible" :corpusData="corpusData" @update:data="search"/>
</template>

<script lang="ts" setup>
import { ScriptCorpusItem, corpusTypeOption, CorpusDataOrigin, CorpusTypeEnum, EventValueItem, } from '@/type/speech-craft'
import { scriptCorpusModel, } from '@/api/speech-craft'
import { CaretTop, CaretBottom, Plus, } from '@element-plus/icons-vue'
import { reactive, ref, watch, onActivated, onDeactivated } from 'vue'
import { ElMessage } from 'element-plus'
import { findValueInEnum, pickAttrFromObj } from "@/utils/utils";
import Confirm from '@/components/message-box'
import BaseCorpusDrawer from '@/components/corpus/BaseCorpusDrawer.vue'
import { useScriptStore } from '@/store/script'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { tableHeaderStyle } from '@/assets/js/constant'
import { LabelItem, } from "@/type/IntentionType";
import to from 'await-to-js'
import { trace } from '@/utils/trace'

// props & emits
const emits = defineEmits(['update:visible', 'play'])
const props = defineProps<{
  corpusType: CorpusTypeEnum
}>();

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const scriptId = scriptStore.id
const isChecked = scriptStore.isChecked

// 表格区
const tableData = ref<ScriptCorpusItem[] | null>([])
// 搜索区
const search = async () => {
  loading.value = true
  if (!props.corpusType) return
  const res = await to(scriptCorpusModel.findPreCorpusList({
    scriptId,
    type: props.corpusType,
  }))
  tableData.value = (res[1] || []).map(item => {
    const scriptMultiContents = item.scriptMultiContents || []
    const content = (scriptMultiContents && scriptMultiContents.length) ? scriptMultiContents[0].scriptUnitContents?.reduce((a, b) => a + b.content, '') || '' : ''
    return {
      ...item,
      content: content
    }
  })
  loading.value = false
}

// 操作区
const corpusDialogVisible = ref(false)

const corpusData = reactive<ScriptCorpusItem>(new CorpusDataOrigin(scriptId, props.corpusType || CorpusTypeEnum['打断垫句']))
const edit = (row?: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row,
      [...corpusTypeOption[corpusData.corpusType!].keys, 'corpusType']
    ))
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId,  props.corpusType || CorpusTypeEnum['打断垫句']))
  }
  corpusDialogVisible.value = true
}

const delAction = async (id: number) => {
  loading.value = true
  trace({
    page: `话术编辑-功能语料-${findValueInEnum(props.corpusType, CorpusTypeEnum) || '未知语料'}：删除(${scriptId})`,
    params: { id },
  })
  await scriptCorpusModel.deleteFuncCorpus({
    corpusId: id
  }) as boolean
  ElMessage({
    message: '删除成功',
    type: 'success',
  })
  search()
  loading.value = false
}
const del = (row: ScriptCorpusItem) => {
  Confirm({ 
    text: `您确定要删除语料【${row.name}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}

/** 翻译事件触发 */
const filterEventValueIds = (ids?: number[]) => {
  if (ids && ids.length > 0) {
    const eventTriggerValueArr: string[] = []
    ids && ids?.length > 0 && ids?.map(item => {
      eventValuesOptions[item] && eventTriggerValueArr.push(`${(eventValuesOptions[item].name || '')}(${(eventValuesOptions[item].explanation || '')})`)
    })
    return eventTriggerValueArr.join(',') || ''
  } else {
    return ''
  }
}

// 执行区
const eventValuesOptions = reactive<{[key: string]: EventValueItem}>({})
const init = async () => {
  const res = await scriptStore.getEventOptions()
  if (res && res.length > 0) {
    res.map(item => {
      item.eventValuemap && Object.assign(eventValuesOptions, item.eventValuemap)
    })
  }
}

watch(() => props.corpusType, () => {
  init()
  search()
})

onActivated(async () => {
  init()
  search()
})
onDeactivated(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.repeat-container {
  width: 100%;
  padding: 16px 12px 0;
  height: calc(100vh - 240px);
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
    line-height: 20px;
  }
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
</style>