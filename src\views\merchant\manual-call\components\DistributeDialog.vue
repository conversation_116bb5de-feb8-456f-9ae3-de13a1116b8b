<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="480px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">下发线索</div>
    </template>
    <el-form
      :model="editData"
      class="tw-px-[12px]"
      :rules="rules"
      label-width="100px"
      ref="editRef"
    >
      <!-- <div class="info-title tw-text-left tw-my-[12px]">已成功选择{{editData.clueIds?.length || 0}}条数据。</div> -->
      <el-form-item label="选择坐席组：" prop="callTeamId">
        <div class="tw-flex tw-w-full tw-flex-col">
          <el-select
            v-model="editData.callTeamIds"
            multiple
            collapse-tags
            :max-collapse-tags="2"
            class="tw-flex-grow"
            clearable
            placeholder="请选择坐席组"
          >
            <el-option v-for="item in callTeamList" :key="item.id" :label="item.callTeamName" :value="item.id" />
          </el-select>
          <div v-if="!!globalStore.getCopyCallTeamNames" class="tw-w-full tw-flex tw-justify-between tw-items-center">
            <div>
              <span>上次选择：</span>
              <span>{{ globalStore.getCopyCallTeamNames }}</span>
            </div>
            <el-button link type="primary" @click="pasteCallTeam">复用</el-button>
          </div>
        </div>
        
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onDeactivated, } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { clueManagerModel } from '@/api/clue'
import { ElMessage, } from 'element-plus'
import { useTaskStore } from '@/store/taskInfo'
import { SeatTeam } from '@/type/seat'
import type { FormInstance, } from 'element-plus'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'
import { trace } from '@/utils/trace'

const emits = defineEmits(['update:visible', 'confirm'])
const userInfo  = useUserStore()
const globalStore = useGlobalStore()
const  {loading} = storeToRefs(globalStore)
const props = defineProps<{
  visible: boolean,
  list: number[],
}>();
const editData = reactive<{
  clueIds: number[],
  callTeamIds?: number[],
}>({
  clueIds: props.list || [],
  callTeamIds: undefined,
})

const taskStore = useTaskStore()

const dialogVisible = ref(props.visible)
const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const rules = {
  callTeamIds: [
    { required: true, message: '请选择坐席组', trigger: 'change' },
  ],
}

const pasteCallTeam = () => {
  editData.callTeamIds = globalStore.copyCallTeamIds || undefined
}

const confirm = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      globalStore.copyCallTeamIds = editData.callTeamIds || null
      let errNum = 0
      const clueNumPerTeam = Math.ceil(editData.clueIds?.length / (editData.callTeamIds?.length || 1))
      await Promise.all((editData.callTeamIds || [])?.map(async (item, index) => {
        const clueIds = editData.clueIds?.slice(index * clueNumPerTeam, index * clueNumPerTeam + clueNumPerTeam)
        if (clueIds.length > 0) {
          const [err, _] = await to(clueManagerModel.distributeClues({
            callTeamId: item,
            clueIds,
          }))
          err && errNum++
        }
      }))
      trace({
        page: '线索管理-下发线索',
        params: {
          clueIds: editData.clueIds,
          callTeamIds: editData.callTeamIds,
        }
      })
      loading.value = false
      !errNum && ElMessage({
        type: 'success',
        message: '线索下发成功！'
      })
      emits('confirm')
    }
  })
}

const callTeamList = ref<SeatTeam[] | null>([])
const init = async () => {
  callTeamList.value = await taskStore.getCallTeamListOptions()
}

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
    editData.clueIds = props.list || []
    editData.callTeamIds = undefined
    editRef.value?.clearValidate()
  }
})

onDeactivated(() => {
  editRef.value = null
  callTeamList.value = null
})
onBeforeRouteLeave(() => {
  editRef.value = null
  callTeamList.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
</style>
