<template>
  <div class="sms-container">
    <div class="search-box">
      <div class="tw-grid tw-grid-cols-6 tw-gap-[8px]">
        <div class="item-col">
          <span class="label">执行状态：</span>
          <el-select
            v-model="searchForm.status"
            clearable
            placeholder="请选择执行状态"
            style="width: 100%;"
            @change="search()"
          >
            <el-option
              v-for="item in enum2Options(RecordStatusEnum)"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="item-col tw-col-span-2">
          <span class="label">执行时间：</span>
          <TimePickerBox
            v-model:start="searchForm.startTime"
            v-model:end="searchForm.endTime"
            placeholder="执行时间"
            separator="-"
            :maxRange="60*60*1000*24*3"
            :clearable="false"
            format="YYYY-MM-DD HH:mm:ss"
            @change="search()"
          />
        </div>
        <div class="item-btn">
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
      <div class="tw-flex tw-items-center">
        <div class="tw-flex tw-self-end tw-grow">
          <span class="tw-text-[var(--primary-black-color-400)]">已选：</span>
          <span class="tw-text-[var(--primary-black-color-400)]">{{ selectIds?.length || 0 }}</span>
          <span class="tw-text-[var(--primary-black-color-400)]">/</span>
          <span class="tw-text-[var(--primary-black-color-400)]">{{ total || 0 }}</span>
        </div>
        <el-button v-if="canReEdit" type="primary" class="tw-mr-[12px]" @click="handleBatchEdit">
          <el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="edit" color="none"></SvgIcon></el-icon>
          <span>批量编辑</span>
        </el-button>
        <el-dropdown v-if="canReExecution" @command="handleReExecutionCommand" class="tw-cursor-pointer tw-text-[#323233]">
          <el-button type="primary">
            <el-icon :size="16">
            <SvgIcon name="reset3" color="none"></SvgIcon>
            </el-icon>
            <span>重新执行</span>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="1" :key="1">自动匹配</el-dropdown-item>
              <el-dropdown-item :command="2" :key="2">手动匹配</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      row-key="packageId"
      ref="tableRef"
      :header-cell-style="tableHeaderStyle"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      border
      stripe
    >
      <el-table-column width="36" align="left" fixed="left" type="selection"></el-table-column>
      <el-table-column property="packageId" fixed="left" label="人群包ID" align="left" width="160"></el-table-column>
      <el-table-column property="modelName" label="人群包名" show-overflow-tooltip align="left" min-width="280" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="totalNumber" label="号码数量" show-overflow-tooltip  align="left" min-width="120">
        <template #default="{ row }">
          {{ row.totalNumber ? formatNumber(row.totalNumber) : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="status" label="执行状态" align="center" width="120">
        <template #default="{ row }">
          <span
            v-if="row?.status"
            class="status-box-mini tw-mx-auto"
            :class="recordStatusClass[row.status as RecordStatusEnum] || 'blue-status'"
          >
            {{ findValueInEnum(row.status, RecordStatusEnum) || row.status }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="updateTime" label="执行时间" align="center" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="createTime" label="创建时间" align="center" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="80" align="right" fixed="right">
        <template #default="{ row }">
          <div class="table-btn-box">
            <el-button type="primary" link @click="editPackageName(row)">修改包名</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  <ManualDialog
    v-model:visible="manualVisible"
    :selectIds="selectIds"
    @confirm="search"
  />
  <EditDialog
    v-model:visible="editVisible"
    :data="editItem!"
    @confirm="search"
  />
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { enum2Options, findValueInEnum, formatterEmptyData, handleTableSort, formatNumber } from '@/utils/utils'
import ManualDialog from './ManualDialog.vue'
import EditDialog from './EditDialog.vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js'
import { volcanoModel } from '@/api/volcano'
import { VolcanoTaskLogItem, RecordStatusEnum } from '@/type/volcano'
import SvgIcon from '@/components/SvgIcon.vue'
import { trace } from '@/utils/trace';
import Confirm from '@/components/message-box'
import TimePickerBox from '@/components/TimePickerBox.vue'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import { recordStatusClass } from './constant'
import PaginationBox from '@/components/PaginationBox.vue'

const userStore = useUserStore();
const permissions = userStore.permissions[routeMap['火山运营'].id]
// 重新执行按钮权限
const canReExecution = computed(() => {
  return permissions?.includes(routeMap['火山运营'].permissions['重新执行'])
})

const canReEdit = computed(() => {
  return permissions?.includes(routeMap['火山运营'].permissions['批量编辑'])
})

const loading = ref(false)
const tableRef = ref()
const tableData = ref<VolcanoTaskLogItem[] | null>([])


// 批量选中数据
const selectIds = ref<number[]>([])
const handleSelectionChange = (val: VolcanoTaskLogItem[]) => {
  selectIds.value = val.map(item => item.id) || []
}

// 排序
const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}

// 分页
const currentPage = ref(1)
const total = ref(0)
const pageSizeList = [50, 100, 200]
const pageSize = ref(pageSizeList[0])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const searchForm = reactive<{
  status?: RecordStatusEnum,
  startTime?: string,
  endTime?: string
}>({
  status: undefined,
  startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  endTime:  dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
})
const search = async () => {
  if (loading.value) return ElMessage.warning('请勿频繁操作')
  loading.value = true
  const [_, res] = await to(volcanoModel.findPackageLogList(searchForm))
  tableData.value = (res || []).map(item => {
    // 提取modelName开头的中括号内容
    const match = item.modelName?.match(/^\【(.*?)\】/); 
    return  {
      ...item,
      taskTemplateName: match ? match[1] : ''
    }
  })
  orderCol.value = 'createTime'
  orderType.value = 'descending'
  total.value = tableData.value?.length || 0
  loading.value = false
}

// 单个修改人群包名
const editVisible = ref(false)
const editItem = ref<null | VolcanoTaskLogItem>(null)
const editPackageName = (row: VolcanoTaskLogItem) => {
  if (!row.id) return ElMessage.warning('获取人群包信息失败')
  editItem.value = row
  editVisible.value = true
}

// 重新执行: 1:自动匹配 2:手动选择模板匹配
const handleReExecutionCommand = (command: 1 | 2) => {
  if (command === 1) {
    autoExecution()
  }
  if (command === 2) {
    manualExecution()
  }
}

// 重新执行函数
const autoExecution = async () => {
  if (!selectIds.value?.length) return ElMessage.warning('请选择需要重新执行的人群包')
  loading.value = true
  const [err1] = await to(Confirm({
    text: `<div>您确定要重新执行这<span class="tw-text-[#3A7CFF]">【${selectIds.value.length}】</span>个任务吗?</div>
    <div class="tw-mt-[12px] tw-text-[#E54B17] tw-font-[600]">1.该操作会根据渠道号重新创建任务，请确保渠道号与模版准确无误</div>
    <div class="tw-text-[#E54B17] tw-font-[600]">2.已创建的历史任务仍然存在，如需删除请在商户端操作</div>`,
    type: 'warning',
    title: '重新执行-自动匹配确认',
    confirmText: '确认'
  }))
  if (err1) return
  const [err] = await to (volcanoModel.autoBatch({ids: selectIds.value}))
  trace({
    page: '火山运营-人群包列表-重新执行-自动匹配',
    params: {
      ids: selectIds.value
    }
  })
  if (!err) {
    ElMessage.success('重新执行成功')
  }
  search()
  loading.value = false
}

// 批量修改模板
const manualVisible = ref(false)
const manualExecution = async () => {
  if (!selectIds.value?.length) return ElMessage.warning('请选择需要重新执行的人群包')
  manualVisible.value = true
}


// 批量编辑
const handleBatchEdit = async () => {
  if (!selectIds.value?.length) return ElMessage.warning('请选择需要重新执行的人群包')
  const [err1] = await to(Confirm({
    text: `<div>您确定要批量编辑这<span class="tw-text-[#3A7CFF]">【${selectIds.value.length}】</span>个的短信模板吗?</div>
    <div class="tw-mt-[12px] tw-text-[#E54B17] tw-font-[600]">该操作会根据渠道号重新匹配任务中的短信模版，操作前请确短信模板已经创建</div>`,
    type: 'warning',
    title: '批量编辑短信模板',
    confirmText: '确认'
  }))
  if (err1) return
  loading.value = true
  const [err] = await to (volcanoModel.editTaskSmsTemplate({ids: selectIds.value}))
  trace({
    page: '火山运营-人群包列表-批量编辑',
    params: {
      ids: selectIds.value
    }
  })
  if (!err) {
    ElMessage.success('操作成功')
  }
  search()
  loading.value = false
}

onMounted(() => {
  search()
})
onUnmounted(() => {
  tableData.value = null
})

</script>

<style scoped lang="postcss" type="text/postcss">
.sms-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
.search-box {
  padding: 12px 8px 8px;
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
