<template>
  <el-scrollbar v-if="rowDialogData && rowDialogData?.length>0" ref="infiniteScrollbarRef" class="tw-w-full tw-bg-[#f5f5f5]" wrap-class="tw-pt-[16px] tw-pl-[32px] tw-pr-[24px] tw-pb-[8px]">
    <div class="tw-absolute tw-right-1 tw-top-1 tw-z-[9]">
      <el-button v-if="!!props.isTransfer" v-loading="loading" type="primary" @click="goPopWinTime">
        <el-icon :size="15"><SvgIcon name="chevrons-down"/></el-icon>
        <span>转人工</span>
      </el-button>
    </div>
    <div ref="contentRef" v-infinite-scroll="() => count=count+2" :infinite-scroll-immediate="false" :infinite-scroll-distance="1" class="tw-flex tw-flex-col" :class="{'tw-pb-[100px]': props.paddingBottom}">
      <template v-for="(itemData, itemIndex) in rowDialogData.slice(0, count)">
        <!-- 时间-操作 显示模块，注：沉默也不会有id，通过是否有dialogTime进行过滤 -->
        <div v-if="typeof itemData.id !== 'number' && itemData.dialogTime" :class="getTimeClass(itemData.content)" class="time-box"> {{ dayjs(itemData.dialogTime).format('YYYY-MM-DD HH:mm:ss') + ' ' + itemData.content }}</div>

        <!-- 用户(type: 1,2)对话模块 -->
        <div v-else-if="itemData.content?.includes('用户沉默中') || (itemData.type && [1, 2].includes(itemData.type))" class="tw-mb-3 tw-flex" :class="props.train ? 'tw-flex-row-reverse' : ''">
          <!-- 通过content包含【用户沉默中】，判断是否为沉默（沉默语料、沉默分支、查询分支沉默） -->
          <!-- 用户对话头像 -->
          <span v-if="itemData.content?.includes('用户沉默中')" class="dialog-o grey-o">默</span>
          <span v-else-if="!itemData.content" class="dialog-o grey-o">空</span>
          <span v-else class="dialog-o">{{  props.clientName ? props.clientName[0] : '客' }}</span>
          <span :class="props.train ? 'tw-mr-1' : 'tw-ml-1'">
            <div class="tw-m-0.5 tw-text-left">
              <!-- 用户对话气泡标题：沉默则用content，否则为用户回复 -->
              <span
                class="tw-rounded-[4px] tw-mr-1 tw-p-0.5"
                :class="itemData.content?.includes('用户沉默中') ? 'tw-bg-[#eaf0fe] tw-text-[var(--el-color-primary)]' : 'tw-bg-[#E6E6E6] tw-text-[#323233]'"
              >
                {{ itemData.content?.includes('用户沉默中') ? itemData.content?.replace('用户', '客户') : '客户回复' }}
              </span>
              <span>{{ itemData.dialogTime || '' }}</span>
            </div>
            <!-- 用户对话气泡内容：沉默不显示 -->
            <template v-if="!props.train">
              <div
                v-if="itemData.urls?.length"
                class="box-normal box-left box-dark"
                @click="handleAudioPlay(itemData)"
              >
                <template v-if="!itemData.content?.includes('用户沉默中')">
                  <el-icon v-if="currentVoice[0] === itemData.id" :size="14" class="tw-absolute tw-top-[2px] tw-left-[2px]"><SvgIcon name="voice-playing" color="#fff"></SvgIcon></el-icon>
                  <el-icon v-else :size="14"><SvgIcon name="voice" color="#fff" class="tw-absolute tw-top-[2px] tw-left-[2px]"></SvgIcon></el-icon>
                  <span class="tw-mr-1 tw-pl-[6px]">{{ (Math.round((itemData.speakMs||0)/1000) || '1') + '”' }}</span>
                </template>
                <span class="tw-leading-[22px] tw-break-all" v-html="formatContent(itemData, 'blue')" />
              </div>
            </template>
            <template v-else>
              <div
                v-if="itemData.urls?.length"
                class="box-normal box-right box-dark"
              >
                <template v-if="!itemData.content?.includes('用户沉默中')">
                </template>
                <span class="tw-leading-[22px] tw-break-all" v-html="formatContent(itemData, 'blue')" />
              </div>
            </template>
            <!-- 用户对话气泡底部信息（未命中）：显示未命中 -->
            <!-- <div v-if="itemData.hitBranch == '未命中'" class="tw-text-left tw-text-[--el-text-color-primary] tw-text-[12px]" :class="props.train ? 'tw-pr-0.5' : 'tw-pl-0.5'">未命中</div> -->
            <!-- 用户对话气泡底部信息（非未命中）：命中短语、命中意图、命中事件（仅查询分支、沉默显示）、触发 -->
            <div v-if="!(!itemData.hitSemantic && !itemData.hitPhrase && !itemData.hitBranch)" class="tw-bg-white tw-rounded-[4px] tw-mt-[8px] tw-p-[8px] tw-flex tw-flex-col tw-items-start">
              <div v-if="itemData.hitPhrase"><span class="label">命中短语：</span><span>{{ itemData.hitPhrase || '' }}</span></div>
              <div v-if="itemData.hitSemantic">
                <span class="label">命中语义：</span>
                <span
                  v-for="(s,i) in itemData.hitSemantic?.split(',') || []"
                  :key="s"
                >
                  <span>{{ i > 0 ? '、' : '' }}</span>
                  <span :class="translateHitSemanticFromBranch(itemData.hitBranch || '') === s ? 'tw-text-[var(--el-color-primary)] tw-font-[600]' : ''">{{s || ''}}</span>
                </span>
              </div>
              <div v-if="itemData.hitBranch?.includes('沉默') || itemData.hitBranch?.includes('查询分支')">
                <span class="label">命中事件：</span>
                <div class="tw-inline-flex tw-flex-col tw-break-all tw-text-left">
                  <span v-for="v in translateBranchType(itemData.hitBranch)" :key="v" class="tw-flex-shrink">{{ v }}</span>
                </div>
              </div>
              <div v-if="itemData.hitBranch">
                <span class="label">触发：</span>
                <span class="tw-break-all tw-text-left">
                  {{ translateBranchMsg(itemData.hitBranch) || '' }}
                </span>
              </div>
            </div>
          </span>
        </div>

        <!-- 机器人(type: 0)\坐席(type: 3, 4)对话模块 -->
        <div v-else class="tw-mb-3 tw-flex tw-justify-end" :class="props.train ? 'tw-flex-row-reverse' : ''">
          <!--话术训练，AI可打断，插槽内容-->
          <div v-if="train" class="tw-flex-none tw-w-[120px]">
            <slot name="trainAiInterrupt" :itemData="itemData" :itemIndex="itemIndex" />
          </div>
          <span class="tw-flex tw-flex-col" :class="props.train ? 'tw-ml-1' : 'tw-mr-1'">
            <!-- 机器人(type: 0)\坐席(type: 3, 4)气泡标题：语料名称：时间 -->
            <div class="tw-flex tw-items-center tw-h-[16px] tw-m-0.5" :class="props.train ? 'tw-flex-row-reverse tw-justify-end' : ''">
              <span v-if="itemData?.corpusName" class="tw-bg-[#E6E6E6] tw-text-[#323233] tw-rounded-[4px] tw-p-0.5" :class="props.train ? 'tw-ml-1' : 'tw-mr-1'">{{ itemData.corpusName || '' }}</span>
              <span>{{ itemData?.dialogTime || '' }}</span>
            </div>
            <!-- 机器人(type: 0)\坐席(type: 3, 4)气泡内容 -->
            <div class="box-normal box-light" :class="props.train ? 'box-left' : 'box-right'" @click="handleAudioPlay(itemData)" v-html="formatContent(itemData, 'white')" />
            <!-- 机器人对话气泡底部信息（未命中）：显示未命中 -->
            <template v-if="props.train">
              <!-- <div v-if="itemData.hitBranch == '未命中'" class="tw-text-left tw-text-[--el-text-color-primary] tw-text-[12px]" :class="props.train ? 'tw-pr-0.5' : 'tw-pl-0.5'">未命中</div> -->
              <!-- 机器人对话气泡底部信息（非未命中）：命中短语、命中意图、命中事件（仅查询分支、沉默显示）、触发 -->
              <div v-if="accountType>=0 && !(!itemData.hitSemantic && !itemData.hitPhrase && !itemData.hitBranch)" class="tw-bg-white tw-rounded-[4px] tw-mt-[8px] tw-p-[8px] tw-flex tw-flex-col tw-items-start">
                <div v-if="itemData.hitPhrase"><span class="label">命中短语：</span><span>{{ itemData.hitPhrase || '' }}</span></div>
                <div v-if="itemData.hitSemantic">
                  <span class="label">命中语义：</span>
                  <span
                    v-for="(s,i) in itemData.hitSemantic?.split(',') || []"
                    :key="s"
                  >
                    <span>{{ i > 0 ? '、' : '' }}</span>
                    <span :class="translateHitSemanticFromBranch(itemData.hitBranch || '') === s ? 'tw-text-[var(--el-color-primary)] tw-font-[600]' : ''">{{s || ''}}</span>
                  </span>
                </div>
                <div v-if="itemData.hitBranch?.includes('沉默') || itemData.hitBranch?.includes('查询分支')">
                  <span class="label">命中事件：</span>
                  <div class="tw-inline-flex tw-flex-col tw-break-all tw-text-left">
                    <span v-for="v in translateBranchType(itemData.hitBranch)" :key="v" class="tw-flex-shrink">{{ v }}</span>
                  </div>
                </div>
                <div v-if="itemData.hitBranch">
                  <span class="label">触发：</span>
                  <span class="tw-break-all tw-text-left">
                    {{ translateBranchMsg(itemData.hitBranch) || '' }}
                  </span>
                </div>
              </div>
              <!--打断设置-->
              <div class="tw-self-start tw-mt-[8px] tw-pl-0.5 tw-text-[--el-text-color-primary] tw-text-[12px] tw-text-left">
                <div v-for="(configItem, configIndex) in getInterruptConfigList(itemData)" :key="configIndex">
                  {{ configItem }}
                </div>
              </div>
            </template>
          </span>
          <!-- 机器人(type: 0)\坐席(type: 3, 4)头像 -->
          <span class="dialog-o">{{itemData.type && [3, 4].includes(itemData.type) ? '坐席': (itemData?.hitIntention ? itemData?.hitIntention[0] : 'AI')}}</span>
        </div>
      </template>
      <!--<div v-if="count<rowDialogData?.length" class="tw-text-[#323233] tw-cursor-pointer" @click="count=count+2">加载中…</div>-->
    </div>
  </el-scrollbar>
  <el-empty v-else></el-empty>
</template>

<script lang="ts" setup>
import { ref, watch, onUnmounted, nextTick } from 'vue'
import { ElMessage, dayjs, } from 'element-plus'
import { InfoQueryItem, InterruptTypeEnum, scriptUnitContent } from '@/type/speech-craft'
import { RecordDialogueData } from '@/type/task'
import { useUserStore } from '@/store/user'
import { onBeforeRouteLeave } from 'vue-router'
import { changeAudioUrlOrigin, findValueInEnum } from '@/utils/utils'
// 组件入参props
const props = withDefaults(defineProps<{
  dataList: RecordDialogueData[], // 人机对话信息
  startEndInfo: {dialogTime: string, content: string}[], // 对话显示信息列表，会根据时间插入对话中，例：2021-02-02 10:12:55 电话接通
  keepLatest?: boolean, // 是否需要显示最新的对话，即自动滚动至最底部
  clientName?: string, // 用户姓名，用于头像显示姓名，默认为【客】
  infoQueryMap?: Map<string, InfoQueryItem>, // 信息查询字段Map，需外部根据话术id查询
  clearAudio: boolean, // 对于外部调用组件时，一些操作需要清空当前播放的对话音频
  paddingBottom?: boolean, // 列表底部是否添加内边距（用于坐席工作台显示底部按钮）
  isTransfer?: boolean, // 是否存在转人工
  train?: boolean, // 适用于话术训练（一般的对话详情，客户在左，AI在右；话术训练的对话详情，AI在左，客户在右）
  autoPlayLastAiAudio?: boolean, // 自动播放最后一条AI语音
}>(), {
  keepLatest: false,
  paddingBottom: false,
  isTransfer: false,
  train: false,
  autoPlayLastAiAudio: false,
})

// emit
const emits = defineEmits([
  'update:clearAudio', 'play'
])

// 全局变量
const userInfo  = useUserStore()
const accountType = userInfo.accountType??1 // 用户角色，0运营；1商户
// 对话数据
const rowDialogData = ref<Partial<RecordDialogueData>[] | null>([])
// 无限滚动
const count = ref(16) // 默认只展示16个对话，多得用无线滚动展开
const infiniteScrollbarRef = ref()
const contentRef = ref()

/** 用户音频播放模块 */
const audiosListRef = ref<HTMLAudioElement|null>(new Audio())
const currentVoice = ref<number[]>([-1, 0]) // 记录当前播放用户说话信息，[id, 音频urls的index]
// 播放用户说话
const handleAudioPlay = (item: Partial<RecordDialogueData>) => {
  if (item.hitBranch?.includes('沉默')) return
  if (!item.urls || item.urls.length < 1) {
    return ElMessage({
      message: '待播放链接为空',
      type: 'warning'
    })
  } else {
    !audiosListRef.value && (audiosListRef.value = new Audio())
  }
  if (item.id == currentVoice.value[0]) {
    if(audiosListRef.value && !!audiosListRef.value.paused) {
      audiosListRef.value?.play()
      audiosListRef.value.addEventListener('ended', function () {
        playList(item.urls || [], currentVoice.value[1] + 1);
      }, false)
    } else {
      audiosListRef.value?.pause()
    }
    return
  } else {
    currentVoice.value[0] = item.id!
    playList(item.urls)
    emits('play')
    return
  }
}
// 由于用户说话是一个列表，需要一次播放一个列表
const playList = (urls: string[], i: number=0) => {
  if (!urls || i >= urls.length) {
    if (audiosListRef.value) {
      audiosListRef.value.src = ''
      audiosListRef.value.pause()
      currentVoice.value = [-1, 0]
    }
    return
  }
  if (audiosListRef.value) {
    audiosListRef.value.src = changeAudioUrlOrigin(urls[i])
    currentVoice.value[1] = i
    audiosListRef.value.loop = false
    audiosListRef.value.addEventListener('loadedmetadata', () => {
      audiosListRef.value && audiosListRef.value.play();
    })
    audiosListRef.value.addEventListener('ended', function () {
      // console.warn('ended')
      emits('update:clearAudio', true)
      playList(urls, i + 1);
    }, false)
  }
}

/**
 * hitBranch组成
 * 1：沉默
 * 2：重复
 * 3：语料名->普通分支:分支名--命中语义
 * 4：语料名->查询分支:分支名{信息字段=信息字段值}
 * 5：语料名->统一回复
 */
/** 翻译分支命中事件类型，主要翻译沉默和信息查询 */
const translateBranchType = (branch: string) => {
  if (branch.includes('沉默')) {
    return ['沉默']
  }

  if (branch.includes('查询分支')) {
    const obj =branch?.split('{')[1].slice(0, -1)
    let res: string[] = []
    obj.split(',').map(item => {
      const [infoFieldName, val] = item?.trim()?.split('=')
      const definition = props.infoQueryMap?.get(infoFieldName)?.infoQueryValues?.find(info => info.value === val)?.definition || ''
      res.push(definition ? `${item}(${definition})` : item)
    })
    return res
  }
}
/** 翻译分支命中信息 */
const translateBranchMsg = (branch: string) => {
  const arrowIndex = branch.indexOf('->');
  const dashIndex = branch.indexOf('--');
  if (arrowIndex === -1) {
    return branch; // 如果没有 '->'，返回字符串
  }
  const preCorpusName = branch.slice(0, arrowIndex).trim();
  let info = ''
  if (dashIndex === -1 || dashIndex < arrowIndex) {
    info =  branch.slice(arrowIndex + 2).trim(); // 获取 '->' 后面的所有内容
  } else {
    info = branch.slice(arrowIndex + 2, dashIndex).trim(); // 获取 '->' 和 '--' 之间的内容
  }
  if (info?.includes('查询分支') || info?.includes('普通分支')) {
    const arr = info.split(':')
    return `${preCorpusName}-${arr[1]??''}(${arr[0]})`
  } else {
    return branch.replace('->', '-')
  }
}

/** 翻译分支命中信息 */
const translateHitSemanticFromBranch = (branch: string) => {
  const dashIndex = branch.indexOf('--');
  if (dashIndex === -1) {
    return ''; // 如果没有 '->'，返回字符串
  }
  return branch.slice(dashIndex + 2).trim(); // 获取 '--' 后面的所有内容
}
// 清空当前播放的用户说话
const clearAudioAction = () => {
  if (audiosListRef.value) {
    audiosListRef.value.pause()
    audiosListRef.value.src = ''
  }
  audiosListRef.value = null
  emits('update:clearAudio', false)
}

const getTimeClass = (str?: string) => {
  if (str?.includes('转人工')) {
    return 'pop-win-time-box'
  } else {
    return ''
  }
}
// 点击跳转至转人工时间
const loading = ref(false)
const goPopWinTime = async () => {
  loading.value = true
  count.value = rowDialogData.value?.length ?? 0
  await nextTick()
  const dom = document.querySelector('.pop-win-time-box')
  if (dom) {
    dom.scrollIntoView({
      block: 'start'
    })
  } else {
    ElMessage.warning('未触发转人工')
  }
  loading.value = false
}

/**
 * 格式化对话内容
 * 已说完的用黑色展示，未说出的内容用灰色展示
 * 打断/返回相关字段用来区分已说完和未说完
 * @param item 单条对话内容
 * @param bubbleColor 气泡颜色
 */
const formatContent = (item: Partial<RecordDialogueData>, bubbleColor: string = 'white') => {
  let str = item?.content ?? ''
  // 转义字符
  str = str.replace('\\\\', '\\')
  // 打断/返回
  if (item?.unitContent) {
    str = str.replace(
      item.unitContent,
      bubbleColor === 'white'
        ? `<span style="color: #323233;">${item.unitContent}</span>`
        : `<span style="color: #fff;">${item.unitContent}</span>`
    )
    str = bubbleColor === 'white'
      ? `<span style="color: var(--primary-black-color-300);">${str || '（空）'}</span>`
      : `<span style="color: var(--primary-black-color-300);">${str || '（空）'}</span>`
  }
  return str || '（空）'
}
/**
 * 格式化展示文本 语料打断设置
 * @param data
 */
const getInterruptConfigList = (data: Partial<RecordDialogueData> = {}) => {
  return data?.corpusConfig?.map((item: scriptUnitContent) => {
    const content = item.contentName
    const name = findValueInEnum(data.interruptType, InterruptTypeEnum) || data.interruptType
    const second = item.allowedInterruptTime + '秒'
    const preInterrupt = '打断垫句' + (item.preInterruptCorpusId ? '开启' : '关闭')
    const preContinue = '续播垫句' + (item.preContinueCorpusIdForInterrupt ? '开启' : '关闭')
    return `${content}：${name}、${second}、${preInterrupt}、${preContinue}`
  }) || []
}

/** watch开始 */
// 监听入参，更新选中数据和选项变化

watch(() => JSON.parse(JSON.stringify(props.dataList)), async (val, oldVal) => {
  // 时间线列表和对话详情，根据时间早晚进行归并排序 Merge Sort，
  // 组成两个队列，每次都比较队头元素的时间早晚，将时间较早的元素提取出来，
  // 放入通话记录列表，作为最终结果，展示在页面上。

  // 考虑到数据复用和性能优化，两个列表不用单独作为队列并修改原数据，而是使用索引记录队列的队头位置。

  // 通常情况下，
  // 哪个时间早，就提取哪个；
  // 如果时间相同，则先取时间线，取完了再取对话。

  // 但是有例外的特殊情况需要处理，此时忽略上述通常情况的规则：
  // 时间线无效值：按原来的顺序放到整个通话记录的最后面；
  // 触发转人工：要放在当前相同时间的最后面，也就是同一秒内，其他时间线在前，对话在中，触发转人工时间线在后；
  // 挂断通话：任何情况下，都应该放在整个通话记录的最后面作为结束标记。

  //      对话          比较   时间线
  // props.dataList             arr
  //       √            <
  //                    >        √

  //                    =    其他时间线 √
  //       √            =      无效值
  //       √            =    触发转人工
  //       √            =     挂断通话

  // 通话记录
  rowDialogData.value = []
  loading.value = false
  // 时间线列表按时间升序排序，从旧到新
  const arr = props.startEndInfo?.sort((a, b) => {
    return dayjs(a.dialogTime).isBefore(dayjs(b.dialogTime)) ? -1 : 1
  }) || []

  // 时间线队列索引，队头位置
  let timelineIndex = 0
  // 对话队列索引，队头位置
  let dialogIndex = 0

  const timelineLength = arr?.length ?? 0
  const dialogLength = props.dataList?.length ?? 0
  // 开始遍历
  for (let i = 0; i < dialogLength + timelineLength; i++) {
    // 对话当前元素（队头）
    const dialogHead = props.dataList[dialogIndex]
    // 时间线队列取完了，对话队列剩下的按原有顺序全部提取并放入
    if (timelineIndex >= timelineLength) {
      rowDialogData.value!.push(dialogHead)
      dialogIndex++
      continue
    }

    // 时间线当前元素（队头）
    const timelineHead = arr[timelineIndex]
    // 对话队列取完了，时间线队列剩下的按原有顺序全部提取并放入
    if (dialogIndex >= dialogLength) {
      rowDialogData.value!.push(timelineHead)
      timelineIndex++
      continue
    }

    // dayjs()的参数里null、空字符串是无效日期，做比较时始终为false
    // dayjs()的参数里undefined、日期时间字符串是有效日期，做比较时按比较结果返回true或false

    if (dialogHead?.dialogTime) {
      // 对话有时间

      const dialogDayjs = dayjs(dialogHead?.dialogTime)
      const timelineDayjs = dayjs(timelineHead?.dialogTime)

      if (dialogDayjs.isBefore(timelineDayjs)) {
        // 对话时间 早于 时间线时间，取 对话

        rowDialogData.value!.push(dialogHead)
        dialogIndex++

      } else if (dialogDayjs.isAfter(timelineDayjs)) {
        // 对话时间 晚于 时间线时间，取 时间线

        rowDialogData.value!.push(timelineHead)
        timelineIndex++

      } else if (dialogDayjs.isSame(timelineDayjs)) {
        // 对话时间 等于 时间线时间，优先处理特殊情况，取 对话；处理完再按通常情况，取 时间线
        if (/触发转人工|挂断通话/.test(timelineHead?.content)) {

          rowDialogData.value!.push(dialogHead)
          dialogIndex++

        } else {

          rowDialogData.value!.push(timelineHead)
          timelineIndex++

        }
      }
    } else {
      // 对话没有时间
      rowDialogData.value!.push(dialogHead)
      dialogIndex++
    }
  }

  // 通话记录始终滚动到最下面
  if (props.keepLatest) {
    count.value = rowDialogData.value?.length ?? 0
  }
  // audiosListRef.value = new Audio()
  await nextTick()
  // 根据props.keepLatest，内容更新后，滚动置顶、置最新
  if (infiniteScrollbarRef?.value && contentRef?.value) {
    if (props.keepLatest && val!.length !== oldVal?.length) {
      const contentHeight = contentRef.value.scrollHeight
      infiniteScrollbarRef.value.setScrollTop(contentHeight)
    }
    if (!props.keepLatest) {
      infiniteScrollbarRef.value.scrollTo({top: 0})
    }
  }

  // 在话术训练文字版中，这个函数被调用两次，两次是为了异步更新，第一次是对话详情，第二次是打断设置，
  // 如果第一次调用后，已经开始播放最新一条AI语句了，那么第二次调用时，需要先暂停播放，不然handleAudioPlay会把播放切换到暂停

  // 播放最后一条AI语音
  if (props.autoPlayLastAiAudio) {
    let lastAiItem: Partial<RecordDialogueData> = rowDialogData.value?.at(-1) ?? {}
    if (lastAiItem?.type === 0 && lastAiItem?.urls?.length) {
      // 最后一条是AI并且有可播放的地址
      // 把正在播放的暂停，可能是旧的，也可能是同一条
      audiosListRef.value?.pause()
      // 播放新的，或者重新播放同一条
      handleAudioPlay(lastAiItem)
    }
  }
}, {deep: true, immediate: true})
watch(() => props.clearAudio, n => {
  if (n) {
    clearAudioAction()
  }
})

onUnmounted(() => {
  clearAudioAction()
  rowDialogData.value = null
  infiniteScrollbarRef.value = null
  contentRef.value = null
})

onBeforeRouteLeave(() => {
  audiosListRef.value = null
  rowDialogData.value = null
  infiniteScrollbarRef.value = null
  contentRef.value = null
})
</script>

<style lang="postcss" scoped>
.dialog-o {
  flex: none;
  width: 40px;
  height: 40px;
  font-size: 18px;
  line-height: 40px;
  margin-top: 16px;
  border-radius: 4px;
  color: #fff;
  background-color: var(--el-color-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.grey-o {
  background-color: #e6e6e6;
  color: var(--el-color-primary);
}
.time-box {
  border-radius: 4px;
  background-color: #C8C9CC;
  color: #fff;
  display: flex;
  justify-content: space-around;
  width: 240px;
  padding: 2px 6px;
  line-height: 20px;
  align-self: center;
  font-size: 13px;
  margin-top: 12px;
  margin-bottom: 12px;
}
.box-normal {
  text-align: justify;
  line-height: 22px;
  max-width: 28vw;
  border-radius: 4px;
  margin-top: 8px;
  padding: 5px 10px;
  position: relative;
  cursor: pointer;
}
.box-right {
  align-self: end;
  &::before {
    content: "";
    position: absolute;
    top: 7px;
    right: -5px;
    width: 0;
    height: 0;
  }
}
.box-left {
  &::before {
    content: "";
    position: absolute;
    top: 7px;
    left: -5px;
    width: 0;
    height: 0;
  }
}
.box-dark {
  border: 1px #ddd solid;
  background-color: var(--el-color-primary);
  color: #fff;
  &::before {
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
  }
  &.box-left {
    &::before {
      border-right: 6px solid var(--el-color-primary);
    }
  }
  &.box-right {
    &::before {
      border-left: 6px solid var(--el-color-primary);
    }
  }
}
.box-light {
  background-color: #fff;
  color: #323233;
  &::before {
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
  }
  &.box-left {
    &::before {
      border-right: 6px solid #fff;
    }
  }
  &.box-right {
    &::before {
      border-left: 6px solid #fff;
    }
  }
}
.box-silence {
  background-color: #e6e6e6;
  color: #323233;
  cursor: auto;
  &::before {
    content: "";
    width: 0px;
    height: 0px;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-right: 6px solid #e6e6e6;
    position: absolute;
    top: 7px;
    left: -6px;
  }
}
</style>
