<template>
  <el-table
    :data="tableTempData"
    v-loading="loading"
    :header-cell-style="tableHeaderStyle"
    @sort-change="handleSortChange"
    stripe
    class="tw-grow"
    row-key="id"
  >
    <el-table-column property="secondIndustry" label="行业" align="left" :min-width="isMobile ? 100 : 120" :formatter="formatterEmptyData"></el-table-column>
    <el-table-column property="supplyConcurrent" label="线路供应并发" align="left" sortable="custom" :min-width="isMobile ? 100 : 120">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-start">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
      <template #default="{ row }">
        {{ formatNumber(row.supplyConcurrent) }}
      </template>
    </el-table-column>

    <el-table-column property="tenantConcurrent" label="任务锁定并发" align="left" sortable="custom" :min-width="isMobile ? 100 : 120">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-start">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
      <template #default="{ row }">
        {{ formatNumber(row.tenantConcurrent) }}
      </template>
    </el-table-column>

    <el-table-column property="realConcurrent" label="实际并发" align="left" sortable="custom" :min-width="isMobile ? 100 : 120">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-start">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
      <template #default="{ row }">
        {{ formatNumber(row.realConcurrent) }}
      </template>
    </el-table-column>

    <el-table-column property="pauseConcurrent" label="暂停并发" align="left" sortable="custom" :min-width="isMobile ? 100 : 120">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-start">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
      <template #default="{ row }">
        {{ formatNumber(row.pauseConcurrent) }}
      </template>
    </el-table-column>
    <el-table-column v-if="!isMobile" label="并发趋势" align="right" fixed="right" min-width="80">
      <template #default="{ row }">
        <el-button link @click="goDetail(row)" type="primary">查看</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
    </template>
  </el-table>
  <PaginationBox
    class="tw-flex-grow-0"
    :pageSize="pageSize"
    :currentPage="currentPage"
    :total="total"
    @search="search"
    @update="updatePage"
  >
  </PaginationBox>

  <el-drawer
    v-model="drawerVisible"
    :size="getDrawerWidth()"
    :with-header="true"
  >
    <template v-slot:header>
      <div class="tw-bg-white tw-h-[50px]  tw-flex tw-items-end tw-pb-[12px]">
        <div class="tw-font-semibold tw-text-left tw-text-[var(--primary-black-color-600)]">
          {{ currentItem || '' }}并发趋势
        </div>
        <el-tooltip content="刷新" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5 tw-pb-1" @click="needRefresh=true">
            <SvgIcon name="reset" color="inherit"/>
          </el-icon>
        </el-tooltip>
      </div>
    </template>
    <el-scrollbar wrap-class="tw-p-[12px] tw-bg-[#f2f3f5]">
      <LineConcurrentBox :lineInfo="currentItem" v-model:refresh="needRefresh"></LineConcurrentBox>
    </el-scrollbar>
  </el-drawer>
</template>

<script lang="ts" setup>
import { watch, computed, ref, reactive, defineAsyncComponent, onUnmounted } from 'vue'
import { CloseBold, CaretTop, CaretBottom, } from '@element-plus/icons-vue'
import { formatNumber, formatNumber1, formatDuration, formatterEmptyData, handleTableSort } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import to from 'await-to-js'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { ConcurrentInfoItem } from '@/type/monitor-statistic'
import { tableHeaderStyle } from '@/assets/js/constant'
import { exportTable2Excel } from '@/utils/export'
import dayjs from 'dayjs'
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router'

const LineConcurrentBox = defineAsyncComponent({loader: () => {return import('@/views/operator/line-manager/components/LineConcurrentBox.vue')}})

const props = defineProps<{
  needUpdate: boolean
}>();
const emits = defineEmits(['update:needUpdate',])

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

/** 底部账号维度列表 开始 */
const loading = ref(false)
const tableData = ref<ConcurrentInfoItem[] | null>([]) // 全部数据
// 实际显示数据
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], prop.value, order.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

/** 搜索和接口数据获取 */

const search = async () => {
  loading.value = true
  try {
    const [_, data] = await to(monitorStatisticModel.findMonitorConcurrentList()) as [any, ConcurrentInfoItem[]]
    tableData.value = data?.filter(item => item.supplyConcurrent) || []
    total.value = tableData.value?.length || 0
  } catch(err){} finally {
    loading.value = false
  }
}

/** 分页 */ 
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(20)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}

/** 排序 */
const prop = ref('')
const order = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  prop.value = params.prop
  order.value = params.order
}

/** 进入账号详情 */
const drawerVisible = ref(false)
const currentItem = ref<string>('')
const needRefresh = ref(false)
const goDetail = async (item: ConcurrentInfoItem) => {
  drawerVisible.value = true
  currentItem.value = item.secondIndustry || ''
  needRefresh.value = true
}

const getDrawerWidth = () => {
  return window.innerWidth > 1400 ? '75%' : '950px'
}

/** 底部账号维度列表 结束 */

const init = async () => {
  await search()
  emits('update:needUpdate', false)
}
watch(() => props.needUpdate, n => {
  n && !drawerVisible.value && init()
})
init()

onUnmounted(() => {
  tableData.value = null
})
onBeforeRouteLeave(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.el-table {
  font-size: var(--el-font-size-base);
  @media screen and (max-width: 600px) {
    font-size: 10px;
    .el-button {
      font-size: 10px
    }
  }

  :deep(.cell) {
    padding: 0 8px;
    @media screen and (max-width: 600px) {
      padding: 0 2px;
    }
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
.card-box {
  padding: 0;
}
</style>