<template>
  <el-drawer
    v-model="visible"
    :size="getDrawerWidth()"
    :with-header="true"
    class="dialog-form"
    @close="close"
  >
    <template v-slot:header>
      <div class="tw-bg-white tw-h-[50px]  tw-flex tw-items-end tw-pb-[12px]">
        <div class="tw-font-semibold tw-text-[15px] tw-text-left tw-text-[var(--primary-black-color-600)]">
          【{{props.supplyLineNumber || ''}}】的数据详情
        </div>
      </div>
    </template>

    <div class="tw-bg-white tw-w-full tw-flex tw-flex-col tw-h-full">
      <div class="search-box tw-px-[12px]">
        <div class="tw-grid tw-grid-cols-4 tw-gap-[12px]">
          <div class="item">
            <el-select v-model="searchForm.exceptTypes" placeholder="选择异常类型" multiple clearable collapse-tags collapse-tags-tooltip>
              <el-option v-for="item in enum2Options(ErrorTypeEnum)" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </div>
          <div class="item tw-col-span-2">
            <TimePickerBox
              v-model:start="searchForm.callOutTimeStart"
              v-model:end="searchForm.callOutTimeEnd"
              splitToday
              placeholder="外呼"
              :disabledDate="disabledDate"
              :maxRange="60*60*24*1000"
              :clearable="false"
            />
          </div>
          <div class="item-btn">
            <el-button type="primary" @click="search()" link>
              <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
              <span>查询</span>
            </el-button>
          </div>
        </div>
      </div>
      <div class="tw-flex tw-justify-end tw-items-center tw-mb-[12px] tw-px-[12px]">
        <el-button :loading="loading" type="default" @click="search(true)">
          <el-icon :size="14"><SvgIcon name="reset3" color="inherit"/></el-icon>
          <span>刷新数据</span>
        </el-button>
        <el-button :loading="loading" type="primary" @click="exportVisible=true">
          <el-icon :size="14"><SvgIcon name="download3" color="inherit"/></el-icon>
          <span>导出明文</span>
        </el-button>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        ref="tableRef"
        row-key="recordId"
        class="tw-grow tw-shrink"
        :header-cell-style="tableHeaderStyle"
        stripe
      >
        <el-table-column label="号码" property="recordId" fixed="left" align="left" width="160" :formatter="formatterEmptyData">
          <template #default="{ row }">
            <span class="tw-cursor-pointer" @click="copyText(row.recordId)">{{ filterPhone(row.recordId, 8, 6) }}</span>
          </template>
          
        </el-table-column>
        <el-table-column label="省市" align="left" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.province || row.city ? (row.province || '') + ' ' + (row.city || '') : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="呼叫状态" property="callStatusStr" align="center" min-width="80" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="callOutTime" label="呼出时间" align="center" min-width="160" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="talkTimeStart" label="接通时间" align="center" min-width="160" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="talkTimeEnd" label="挂断时间" align="center" min-width="160" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column label="通话时长" property="callDurationSec" align="left" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            {{ (row.callDurationSec??-1)>-1 ? formatDuration(row?.callDurationSec) || '0' : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="types" label="异常类型" min-width="160">
          <template #default="{ row }">
            {{ row.types?.length ? row.types?.map((v: ErrorTypeEnum) => findValueInEnum(v, ErrorTypeEnum)).join(',') : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="right" width="80" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="showPlaintextAction(row)">查看明文</el-button>
          </template>
        </el-table-column>
        <template #empty>
          <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
        </template>
      </el-table>
      <PaginationBox
        :pageSize="pageSize"
        :pageSizeList="pageSizeList"
        :currentPage="currentPage"
        :total="total"
        @search="search()"
        @update="updateList"
      >
      </PaginationBox>
    </div>
  </el-drawer>
  <!-- 单个明文号码展示 -->
  <el-dialog
    v-model="dialogVisible"
    width="400px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    append-to-body
    @close="dialogVisible=false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">查看明文</div>
    </template>
    <div v-loading="loading" class="tw-flex tw-flex-col tw-w-full tw-items-start tw-p-[12px] tw-text-[13px]">
      <div>
        <span>手机号码：</span>
        <span class="info-title">{{ plaintextData?.phone }}</span>
      </div>
      <div>
        <span>查看数量：</span>
        <span v-if="plaintextData && plaintextData?.remainCount >= 5" class="info-title">今日剩余【<span class="tw-text-[var(--el-color-primary)]">{{plaintextData?.remainCount}}</span>】次</span>
        <span v-else class="info-title">
          今日剩余【<span class="tw-text-[var(--el-color-danger)]">{{plaintextData?.remainCount}}</span>】次
        </span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible=false">关闭</el-button>
        <el-button v-if="plaintextData?.phone" type="primary" :loading="loading" @click="copyConfirm">复制并关闭</el-button>
      </span>
    </template>
  </el-dialog>
  <ExportDialog
    v-model:visible="exportVisible"
    :total="total"
    :searchParams="searchForm"
  />
</template>

<script lang="ts" setup>
import { reactive, ref, watch, defineAsyncComponent, } from 'vue'
import { supplierModel } from '@/api/supplier/supplier'
import { ErrorTypeEnum, SupplierPlatformLineRecordSearchParam, SupplierPlatformLineRecordItem, } from '@/type/supplier/supplier'
import to from 'await-to-js'
import dayjs from 'dayjs'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { enum2Options, formatterEmptyData, formatDuration, copyText, findValueInEnum, filterPhone } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import ExportDialog from './ExportDialog.vue'
import { ElMessage } from 'element-plus'
import { trace } from '@/utils/trace'

const props = defineProps<{
  supplyLineNumber : string | null,
  visible: boolean
}>()
const emits = defineEmits([
  'update:visible',
])

const searchForm = reactive<SupplierPlatformLineRecordSearchParam>({
  supplyLineNumber: undefined,
  exceptTypes: undefined,
  callOutTimeStart: dayjs().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss'),
  callOutTimeEnd: dayjs().format('YYYY-MM-DD HH:mm:ss')
})

/** 分页 开始 */
const pageSizeList = [20, 50, 100, 200]
const pageSize = ref(pageSizeList[0])
const currentPage = ref(1)
const total = ref(0)
const updateList = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}

const loading = ref(false)
const visible = ref(false)
const tableData = ref<SupplierPlatformLineRecordItem[] | null>(null)

const search = async (isRecent: boolean = false) =>{
  if (loading.value) return ElMessage.warning('请勿重复请求')
  if (isRecent) {
    searchForm.callOutTimeStart = dayjs().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss')
    searchForm.callOutTimeEnd = dayjs().format('YYYY-MM-DD HH:mm:ss')
  }
  loading.value = true
  const [_, data] = await to(supplierModel.getSupplierPlatformLineDetail({
    ...searchForm,
    pageNum: currentPage.value > 1 ? currentPage.value - 1 : 0,
    pageSize: pageSize.value,
  }))
  total.value = data?.total || 0
  tableData.value = data?.records as SupplierPlatformLineRecordItem[] || []
  loading.value = false
}

// 查看单条记录明文
const dialogVisible = ref(false)
const plaintextData = ref<null | {
  phone: string,
  remainCount: number,
}>(null)
const showPlaintextAction = async (row: SupplierPlatformLineRecordItem) => {
  if (!row?.recordId) return ElMessage.warning('获取记录信息失败')
  loading.value = true
  const params = {
    recordId: row.recordId,
    callOutTime: row.callOutTime,
    supplyLineNumber: props.supplyLineNumber!,
  }
  const [err, res] = await to(supplierModel.convertPlainPhoneByRecordId(params))
  trace({ page: '供应平台-线路数据-查看明文', params: {params, res: err || '成功'} })
  loading.value = false
  if (err) return
  plaintextData.value = res || null
  dialogVisible.value = true
}
// 复制并关闭
const copyConfirm = () => {
  if (plaintextData.value && plaintextData.value.phone) {
    copyText(plaintextData.value.phone)
    dialogVisible.value = false
  }
}

// 批量导出明文数据
const exportVisible = ref(false)
const getDrawerWidth = () => { return window.innerWidth > 1400 ? '75%' : '1000px' }

const disabledDate = (time: Date) => {
  const _minTime = dayjs().add(-7, 'days').startOf('day').valueOf()
  const _maxTime = dayjs().endOf('day').valueOf()
  return time.getTime() > _maxTime || time.getTime() < _minTime
}

const close = () => {
  visible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, () => {
  visible.value = props.visible
  exportVisible.value = false
  if (props.visible) {
    currentPage.value = 1
    searchForm.supplyLineNumber = props.supplyLineNumber || undefined
    search(true)
  }
})
</script>

<style scoped lang="postcss" type="text/postcss">
</style>