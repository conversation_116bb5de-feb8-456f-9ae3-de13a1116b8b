<template>
  <div class="tw-grid tw-grid-cols-2 sm:tw-flex sm:tw-flex-col tw-gap-4 tw-w-full tw-text-[13px]">
    <!--转化漏斗-->
    <div v-loading="loadingFunnel" class="tw-relative tw-h-[320px] tw-col-span-1">
      <template v-if="finishedFunnel">
        <FunnelChart :data="funnelChartData" :tooltipContent="funnelTooltipContent" title="转化漏斗">
          <el-radio-group v-model="funnelType" @change="updateFunnel">
            <el-radio-button :label="0">意向客户</el-radio-button>
            <el-radio-button :label="1">短信发送</el-radio-button>
          </el-radio-group>
        </FunnelChart>
      </template>
      <template v-else>
        <el-empty />
      </template>
    </div>

    <!--意向分类-->
    <div v-loading="loadingCategory" class="tw-col-span-1 tw-h-[320px]">
      <template v-if="finishedCategory">
        <PieChart :data="categoryChartData" title="意向分类" yName="分类占比" :tooltip-content="categoryTooltipContent"/>
      </template>
      <template v-else>
        <el-empty />
      </template>
    </div>

    <!--接通率分布-->
    <div v-loading="loadingRate" class="tw-col-span-2">
      <MixLineBarChart
        :data="lineChartMultList || []"
        :legends="['外呼量', '呼叫接通率', '接通数']"
        :tooltip-sort="[0, 2, 1]"
        title="分时段数据统计"
        xName="时间"
      >
        <div class="tw-absolute tw-right-1 tw-top-0 tw-z-10">
          <!-- <span>时间颗粒度：</span> -->
          <el-select v-model="interval" filterable style="width: 90px;height:24px" @change="onChangeRateInterval">
            <el-option v-for="item in intervalList" :key="item.val" :label="item.name" :value="item.val" />
          </el-select>
        </div>
      </MixLineBarChart>
    </div>

    <!--地区分布-->
    <div v-loading="loadingRegion" class="tw-col-span-2">
      <template v-if="finishedRegion">
        <!--地图组件-->
        <GeoChart
          :data="regionChartData"
          :legends="legends"
          :tooltipSort="tooltipSort"
          :tooltip-content="geoTooltipContent"
        >
          <!--计划剩余切换按钮-->
          <div class="tw-absolute tw-top-5 tw-left-1 tw-z-10">
          </div>
          <!--运营商切换按钮-->
          <div class="tw-absolute tw-top-1 tw-right-1 tw-z-10 tw-flex tw-flex-col tw-items-end">
            <el-radio-group v-model="operator" @change="onChangeOperator" :size="isMobile ? 'small' : 'default'" class="tw-mb-[8px]">
              <el-radio-button v-for="item in OperatorObj" :key="item.name" :label="item.name" :value="item.name" />
            </el-radio-group>
            <el-radio-group v-model="viewType" @change="onChangeViewType" :size="isMobile ? 'small' : 'default'">
              <el-radio-button label="计划" value="计划"></el-radio-button>
              <el-radio-button label="剩余" value="剩余"></el-radio-button>
            </el-radio-group>
          </div>
          <!--未知运营商数据-->
          <!-- <div class="unknown-operator">
            <div class="unknown-operator-title">省份：未知</div>
            <div class="tw-grid tw-grid-cols-2 tw-mt-[8px]">
              <div class="unknown-operator-content">计划呼叫数：{{ unknownOperatorStr.planned }}</div>
              <div class="unknown-operator-content">实际呼叫数：{{ unknownOperatorStr.called }}</div>
            </div>
          </div> -->
        </GeoChart>
      </template>
      <template v-else>
        <el-empty />
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, ref, watch, computed } from 'vue';
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { OperatorEnum, OperatorObj, MonitorChartItem } from '@/type/common'
import {
  CategoryChartItem,
  CategoryDataType,
  FunnelDataType,
  RateDataType,
  RegionChartItem,
  RegionDateType,
  TaskManageItem,
  AccountStatisticsTypeEnum,
} from '@/type/task'
import { aiOutboundTaskChartModel, statisticsModel } from '@/api/ai-report'
import { time2Minute, Throttle } from '@/utils/utils'
import dayjs from 'dayjs'
import { administrativeDivisionMap } from '@/assets/map/admin-division-map'
import FunnelChart from '@/components/charts/FunnelChart.vue'
import PieChart from '@/components/charts/PieChart.vue'
import to from 'await-to-js'
import { useTaskStore } from '@/store/taskInfo'
import { ElMessage } from 'element-plus';

// 动态引入组件
const GeoChart = defineAsyncComponent({loader: () => import('@/components/charts/GeoChart.vue')})
const MixLineBarChart = defineAsyncComponent({loader: () => import('@/components/charts/MixLineBarChart.vue')})

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const taskStore = useTaskStore()
const isMobile = globalStore.isMobile

const props = defineProps<{
  currentTask: TaskManageItem,
  needRefresh: boolean
}>();
const emits = defineEmits(['update:needRefresh'])

// 转换漏斗，展示的是-----0：意向客户 | 1：短信发送
const funnelType = ref(0)
// 转化漏斗注释
const funnelTooltipContent = computed(() => {
  const res = [
    '计划呼叫名单：各任务导入的名单数之和', '实际呼叫名单：各任务外呼次数>=1的名单数之和',
    '接通名单：各任务接通次数>=1的名单数之和',
  ]
  return funnelType.value === 1 ? [...res, '短信触发量：各任务中名单短信触发数之和', '短信发送量：各任务中短信状态为“发送成功”的名单数之和']
  : [...res, `意向客户数：各任务分类结果为${taskStore.intentionClassScope || ''}的名单数之和`]
})
// 意向分类注释
const categoryTooltipContent = ['总接通数：呼叫成功的号码数;', `意向分类：“意向分类：意向占比 接通数”；`, `意向占比：接通数/总接通数;`]
// 地区分布注释：
const geoTooltipContent = [
  '运营商：支持按全部/移动/联通/电信/未知的运营商纬度查看数据;',
  '省份：所选省份名称;',
  '计划呼叫名单数：所选运营商下，该省份导入的名单数;',
  '实际呼叫名单数：所选运营商下，该省份外呼次数>=1的名单数;',
  '接通名单数：所选运营商下，该省份接通次数>=1的名单数;',
  '名单接通率：接通名单数/实际呼叫名单数;',
  '剩余呼叫名单数：所选运营商下，该省份“外呼队列+补呼队列”的名单数;',
]

// 当前任务
const currentTask = reactive<TaskManageItem>(props.currentTask)

// 监听是否有强制刷新的命令
watch(() => props.needRefresh, n => {
  // console.log('watch', n)
  n && search()
})
// 切换任务时刷新
watch(
  () => currentTask.id,
  (val, oldVal) => {
    if (val && val !== oldVal && val > 0) {
      search()
    }
  }
)

// 正在加载漏斗数据
const loadingFunnel = ref(false)
// 漏斗数据节流锁
const throttleFunnel = new Throttle(loadingFunnel)
// 成功获取漏斗数据
const finishedFunnel = ref(false)
// 转换漏斗，接口数据
const funnelList = ref<FunnelDataType>({})
// 转换漏斗，图表数据
const funnelChartData = ref<{
  name: string,
  value: number,
  rate?: number
}[]>([])

/**
 * 更新转换漏斗数据
 */
const updateFunnel = async () => {
  if (!currentTask?.id) return ElMessage.warning('获取任务id失败')
  // 节流锁上锁
  if (throttleFunnel.check()) {
    return
  }
  throttleFunnel.lock()

  try {
    // 请求接口
    funnelList.value = <FunnelDataType>await statisticsModel.getFunnel({
      taskIdList: [currentTask.id!],
      groupId: currentTask.groupId ?? '',
      queryDate: dayjs(currentTask.createTime ?? new Date()).format('YYYY-MM-DD'),
    })

    funnelChartData.value = [
      { name: '计划呼叫名单', value: funnelList.value.phoneNum ?? NaN },
      { name: '实际呼叫名单', value: funnelList.value.calledNum ?? NaN },
      { name: '接通名单', value: funnelList.value.putThroughNum ?? NaN, rate: funnelList.value.putThroughRate || 0 },
    ]
    if (funnelType.value === 1) {
      funnelChartData.value = [...funnelChartData.value,
        { name: '短信触发量', value: funnelList.value.triggerSmsNum || 0, rate: funnelList.value.triggerSmsRate || 0 },
        { name: '短信发送量', value: funnelList.value.successSmsNum || 0, rate: funnelList.value.successSmsRate || 0},
      ]
    } else {
      funnelChartData.value = [...funnelChartData.value,
        { name: '意向客户', value: funnelList.value.intentionNum || 0, rate: funnelList.value.intentionRate || 0},
      ]
    }

    // 更新状态为获取成功
    finishedFunnel.value = true
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleFunnel.unlock()
  }
}

// 正在加载意向分类
const loadingCategory = ref(false)
// 意向分类节流锁
const throttleCategory = new Throttle(loadingCategory)
// 成功获取意向分类
const finishedCategory = ref(false)
// 意向分类，接口数据
const categoryList = ref<CategoryDataType[]>([])
// 意向分类，图表数据
const categoryChartData = ref<CategoryChartItem[]>([])

/**
 * 更新意向分类数据
 */
const updateCategory = async () => {
  if (!currentTask?.id) return ElMessage.warning('获取任务id失败')
  // 节流锁上锁
  if (throttleCategory.check()) {
    return
  }
  throttleCategory.lock()

  try {
    // 请求接口
    const res = <CategoryDataType[]>await statisticsModel.getCategory({
      taskIdList: [currentTask.id!],
      groupId: currentTask.groupId ?? '',
      queryDate: dayjs(currentTask.createTime ?? new Date()).format('YYYY-MM-DD'),
    })

    // 转换数据
    categoryList.value = Array.isArray(res) ? res : []
    categoryChartData.value = categoryList.value.map((item) => {
      return {
        name: item?.className ?? '',
        value: item?.ratio ?? 0,
        num: item?.num ?? 0
      }
    })

    // 更新状态为获取成功
    finishedCategory.value = true
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleCategory.unlock()
  }
}

// 正在加载接通率分布
const loadingRate = ref(false)
// 接通率分布节流锁
const throttleRate = new Throttle(loadingRate)
// 成功获取接通率分布
const finishedRate = ref(false)
// 接通率分布，时间颗粒度可选列表
const intervalList = [
  { name: '5min', val: 5 },
  { name: '10min', val: 10 },
  { name: '15min', val: 15 },
  { name: '30min', val: 30 },
  { name: '60min', val: 60 },
]
// 接通率分布，当前时间颗粒度，单位min
const interval = ref(5)
// 接通率分布，图表数据
const lineChartMultList = ref<MonitorChartItem[]>([])

/**
 * 更新接通率分布数据
 */
const updateRate = async () => {
  if (!currentTask?.id) return ElMessage.warning('获取任务id失败')
  // 节流锁上锁
  if (throttleRate.check()) {
    return
  }
  throttleRate.lock()

  try {
    // 请求接口
    const [_, data] = await to(statisticsModel.findTimeDistributionByCondition({
      taskIdList: [currentTask.id!],
      type: AccountStatisticsTypeEnum['外呼接通率'],
      groupId: currentTask.groupId ?? '',
      timeGap: interval.value ?? intervalList[0].val,
      queryDate: dayjs(currentTask.createTime ?? new Date()).format('YYYY-MM-DD'),
    }))

    // 清空内容，防止旧数据还在
    lineChartMultList.value = []
    if (data && Object.keys(data) && Object.keys(data).length > 0) {
    Object.keys(data).map(item => {
      const value1 = data[item].total || 0
      const value2 = +(data[item].num / data[item].total).toFixed(4)
      const value3 = data[item].num || 0
      lineChartMultList.value.push({
        name: item.trim(),
        xName: item.split('-')[0],
        value1,
        value2,
        value3,
      })
    })
    lineChartMultList.value.sort((a, b) => {
      const bb = time2Minute(b.xName)
      const aa = time2Minute(a.xName)
      return aa - bb
    })
  }

    // 更新状态为获取成功
    finishedRate.value = true
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleRate.unlock()
  }
}

/**
 * 切换接通率的颗粒度分布
 */
const onChangeRateInterval = () => {
  if (interval.value) {
    updateRate()
  }
}

// 正在加载地区分布
const loadingRegion = ref(false)
// 地区分布节流锁
const throttleRegion = new Throttle(loadingRegion)
// 成功获取地区分布
const finishedRegion = ref(false)
// 地区分布，当前运营商
const operator = ref('全部')
// 地区分布，接口数据
const regionList = ref<RegionDateType[]>([])
// 地区分布，图表数据
const regionChartData = ref<RegionChartItem[]>([])
// 未知运营商数据，不在地图上展示，单独放旁边，应产品需求放台湾
// const unknownOperatorStr = ref({
//   // 计划呼叫数
//   planned: 0,
//   // 实际呼叫数
//   called: 0
// })
// 查看方式
const viewType = ref<'计划' | '剩余'>('计划')
// 图例名称映射表
const legendList = {
  '计划': ['接通名单数', '名单接通率', '计划呼叫名单数', '实际呼叫名单数'],
  '剩余': ['剩余呼叫名单数', '接通名单数', '计划呼叫名单数'],
}
// 图例名称
let legends = legendList['计划']
// 图例顺序映射表
const tooltipSortList = {
  '计划': [2, 3, 0, 1],
  '剩余': [2, 0],
}
// 图例顺序
let tooltipSort = [2, 3, 0, 1]

/**
 * 切换查看方式
 */
const onChangeViewType = () => {
  if (viewType.value) {
    legends = legendList[viewType.value ?? '计划']
    tooltipSort = tooltipSortList[viewType.value ?? '计划']
    updateRegion()
  }
}

/**
 * 切换运营商
 */
const onChangeOperator = () => {
  if (operator.value) {
    updateRegion()
  }
}
/**
 * 更新地区分布数据
 */
const updateRegion = async () => {
  // 节流锁上锁
  if (throttleRegion.check()) {
    return
  }
  throttleRegion.lock()

  try {
    // “全部”将所有运营商用半角逗号拼接
    const allStr = Object.values(OperatorEnum).join(',')

    // 处理参数
    const operators = operator.value === OperatorObj['全部'].name
      ? allStr
      : operator.value

    // 请求接口
    const res = <RegionDateType[]>await aiOutboundTaskChartModel.getRegion({
      taskId: currentTask.id ?? -1,
      groupId: currentTask.groupId || '',
      operators,
      queryDate: dayjs(currentTask.createTime ?? new Date()).format('YYYY-MM-DD'),
    })

    // 转换数据
    regionList.value = Array.isArray(res) ? res : []

    regionChartData.value = []

    regionList.value.forEach((item) => {
      // 接口给的省份不带“省”之类的后缀，地图数据里需要带，这里用自定义的映射表转换一下
      const province = administrativeDivisionMap.get(item?.province ?? '') ?? ''
      if (province) {
        if (viewType.value === '计划') {
          regionChartData.value.push({
            name: province,
            value1: item?.putThroughNum ?? 0,
            value2: item?.putThroughRate ?? 0,
            value3: item?.phoneNum ?? 0,
            value4: item?.calledNum ?? 0,
          })
        } else if (viewType.value === '剩余') {
          // 剩余呼叫数为0时，当成暂无数据，所以不需要推进图表数据里
          if (item?.remainingNum) {
            regionChartData.value.push({
              name: province,
              value1: item?.remainingNum ?? 0,
              value2: item?.putThroughRate ?? 0,
              value3: item?.phoneNum ?? 0,
            })
          }
        }
      }
    })

    // console.log(legends, tooltipSort, regionChartData.value)

    // 更新状态为获取成功
    finishedRegion.value = true
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleRegion.unlock()
  }
}

/**
 * 更新页面数据
 */
const search = () => {
  // 响应父组件的强制刷新命令
  loading.value = true;
  emits('update:needRefresh', false)
  loading.value = false;

  // 更新四个图表
  updateFunnel()
  updateRate()
  updateCategory()
  updateRegion()
}

// 组件加载完成后立马更新页面数据
search()
</script>

<style scoped lang="postcss">
</style>
