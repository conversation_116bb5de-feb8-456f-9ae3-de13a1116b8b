import { defineStore } from 'pinia'
import { SupplierInfo, SupplierLineInfo } from '@/type/supplier'

// 当前选中的供应商信息
const currentSupplier: SupplierInfo = {}
// 当前正在编辑的供应商线路信息
const editingSupplierLine: SupplierLineInfo = {}
// 是否只读
const readonly: boolean = true
// 编辑页面需要提示挂载黑名单
const needRemindBlackGroup: boolean = false

export const useSupplierStore = defineStore({
  id: 'supplier',
  state() {
    return {
      currentSupplier,
      editingSupplierLine,
      readonly,
      needRemindBlackGroup,
    }
  },
  actions: {
    clear() {
      this.$state.currentSupplier = {}
      this.$state.editingSupplierLine = {}
      this.$state.readonly = false
      this.$state.needRemindBlackGroup = false
    },
  },
  persist: [
    {
      storage: sessionStorage,
    }
  ]
})
