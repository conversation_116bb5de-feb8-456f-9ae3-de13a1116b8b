<template>
  <!--模块标题-->
  <HeaderBox title="坐席组线索" />
  <!--模块主体-->
  <el-scrollbar v-if="hasPermission" class="module-container tw-min-w-[1080px]">
    <div class="tw-flex tw-w-full tw-gap-[12px]">
      <div class="tw-flex-grow tw-flex tw-flex-col tw-justify-between tw-h-full">
        <!-- 今日线索 -->
        <div class="tw-w-full tw-bg-white tw-py-[12px] tw-px-[16px] tw-rounded-[4px]" v-loading="loading[2]">
          <div class="tw-flex tw-items-center ">
            <span class="tw-font-semibold tw-text-[var(--primary-black-color-600)] tw-text-[14px]">今日线索</span>
            <el-tooltip content="刷新今日线索" placement="right" :show-after="500">
              <el-icon size="13" class="tw-cursor-pointer tw-ml-0.5" @click="refreshTodayStatics()" color="var(--el-color-primary)"><SvgIcon name="reset"/></el-icon>
            </el-tooltip>
          </div>
          <div class="tw-w-full tw-flex tw-mt-[12px] tw-justify-around tw-gap-[12px]">
            <div class="info-data-box tw-flex-grow tw-items-center">
              <div class="info-data-box-inner ">
                <span class="info-title">获得线索</span>
                <span class="info-data-content">
                  {{ clueTodayStatics.acquireCount||0 }}
                </span>
              </div>
              <div class="tw-bg-[#dfe8fa] tw-p-[6px] tw-rounded-[4px] tw-flex tw-items-center">
                <el-icon :size="26" color="var(--primary-blue-color)"><SvgIcon name="follow-acquire"/></el-icon>
              </div>
            </div>
            <div class="info-data-box tw-flex-grow tw-items-center">
              <div class="info-data-box-inner ">
                <span class="info-title">跟进线索</span>
                <span class="info-data-content">
                  {{ clueTodayStatics.followingCount||0 }}
                </span>
              </div>
              <div class="tw-bg-[#dfe8fa] tw-p-[6px] tw-rounded-[4px] tw-flex tw-items-center">
                <el-icon :size="26" color="var(--primary-blue-color)"><SvgIcon name="follow-following" /></el-icon>
              </div>
            </div>
            <div class="info-data-box tw-flex-grow tw-items-center">
              <div class="info-data-box-inner ">
                <span class="info-title">成功线索</span>
                <span class="info-data-content">
                  {{ clueTodayStatics.successCount||0 }}
                </span>
              </div>
              <div class="tw-bg-[#dfe8fa] tw-p-[6px] tw-rounded-[4px] tw-flex tw-items-center">
                <el-icon :size="26" color="var(--primary-blue-color)"><SvgIcon name="follow-success" /></el-icon>
              </div>
            </div>
            <div class="info-data-box tw-flex-grow tw-items-center">
              <div class="info-data-box-inner ">
                <span class="info-title">失败线索</span>
                <span class="info-data-content">
                  {{ clueTodayStatics.failCount||0 }}
                </span>
              </div>
              <div class="tw-bg-[#dfe8fa] tw-p-[6px] tw-rounded-[4px] tw-flex tw-items-center">
                <el-icon :size="26" color="var(--primary-blue-color)"><SvgIcon name="follow-fail" /></el-icon>
              </div>
            </div>
          </div>
        </div>
        <!-- 线索统计 -->
        <div class="tw-w-full tw-bg-white tw-py-[12px] tw-mt-[12px] tw-px-[16px] tw-rounded-[4px]" v-loading="loading[3]">
          <div class="tw-flex tw-items-center ">
            <span class="tw-font-semibold tw-text-[var(--primary-black-color-600)] tw-text-[14px]">线索统计</span>
            <el-tooltip content="刷新线索统计" placement="right" :show-after="500">
              <el-icon size="13" class="tw-cursor-pointer tw-ml-0.5" @click="refreshClueStatics()" color="var(--el-color-primary)"><SvgIcon name="reset"/></el-icon>
            </el-tooltip>
          </div>
          <div class="tw-w-full tw-grid tw-grid-cols-8 tw-gap-x-[8px] tw-mt-[8px]">
            <div class="info-data-box">
              <div class="info-data-box-inner">
                <span class="info-title">总线索</span>
                <span class="info-data-content">{{formatNumber1(clueStatics.totalCount)??'-'}}</span>
              </div>
            </div>
            <div class="info-data-box tw-col-span-2">
              <div class="info-data-box-inner">
                <span class="info-title">待分配</span>
                <span class="info-data-content">{{formatNumber1(clueStatics.toBeDistributeCount)??'-'}}</span>
              </div>
              <div class="info-data-box-inner">
                <span class="info-title">已分配</span>
                <span class="info-data-content">{{formatNumber1(clueStatics.beDistributeCount)??'-'}}</span>
              </div>
            </div>
            <div class="info-data-box tw-col-span-2">
              <div class="info-data-box-inner">
                <span class="info-title">已回收</span>
                <span class="info-data-content">{{formatNumber1(clueStatics.beRecoveredCount)??'-'}}</span>
              </div>
              <div class="info-data-box-inner">
                <span class="info-title">已归档</span>
                <span class="info-data-content">{{formatNumber1(clueStatics.beArchivedCount)??'-'}}</span>
              </div>
            </div>
            <div class="info-data-box tw-col-span-2">
              <div class="info-data-box-inner">
                <span class="info-title">已成功</span>
                <span class="info-data-content">{{formatNumber1(clueStatics.successCount)??'-'}}</span>
              </div>
              <div class="info-data-box-inner">
                <span class="info-title">已失败</span>
                <span class="info-data-content">{{formatNumber1(clueStatics.failedCount)??'-'}}</span>
              </div>
            </div>
            <div class="info-data-box">
              <div class="info-data-box-inner">
                <span class="info-title">待首跟</span>
                <span class="info-data-content">{{formatNumber1(clueStatics.toBeFirstFollowUpCount)??'-'}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 坐席情况、关联任务 -->
      <div class="tw-w-[300px] tw-bg-white tw-py-[12px] tw-px-[16px] tw-rounded-[4px] tw-flex-shrink-0 tw-flex-grow-0" v-loading="loading[0]">
        <div class="tw-flex tw-items-center tw-justify-between">
          <div>
            <span class="tw-font-semibold tw-text-[var(--primary-black-color-600)] tw-text-[14px]">坐席情况</span>
            <el-tooltip content="刷新坐席情况" placement="right" :show-after="500">
              <el-icon size="13" class="tw-cursor-pointer tw-ml-0.5" color="var(--el-color-primary)" @click="refreshSeatStatics()"><SvgIcon name="reset"/></el-icon>
            </el-tooltip>
          </div>
          <el-button type="primary" link @click="goSeatDetail">详情</el-button>
        </div>
        <div class="info-title tw-text-left tw-mt-[8px]">已上线</div>
        <div class="tw-text-left">
          <span class="tw-text-[28px] tw-font-[700] tw-text-[#313233]">{{ seatInfoDetail.onlineSeats?.length ?? '-' }}</span>
          <span class="tw-text-[20px] tw-font-[700] tw-text-[#626366]">/{{ seatInfoDetail.totalSeats?.length ?? '-' }}</span>
        </div>
        <div class="tw-grid tw-grid-cols-4 tw-gap-[8px] tw-mt-[12px]">
          <div class="info-data-box-inner tw-p-[8px] tw-bg-[#F5F7FA]">
            <span class="info-title">休息中</span>
            <span class="info-data-content">{{ seatInfoDetail.inRestSeats?.length ?? '-' }}</span>
          </div>
          <div class="info-data-box-inner tw-p-[8px] tw-bg-[#F5F7FA]">
            <span class="info-title">空闲中</span>
            <span class="info-data-content">{{ seatInfoDetail.idleSeats?.length ?? '-' }}</span>
          </div>
          <div class="info-data-box-inner tw-p-[8px] tw-bg-[#F5F7FA]">
            <span class="info-title">通话中</span>
            <span class="info-data-content">{{ seatInfoDetail.dialingSeats?.length ?? '-' }}</span>
          </div>
          <div class="info-data-box-inner tw-p-[8px] tw-bg-[#F5F7FA]">
            <span class="info-title">话后中</span>
            <span class="info-data-content">{{ seatInfoDetail.postingSeats?.length ?? '-' }}</span>
          </div>
        </div>
        <div class="tw-mt-[12px] tw-pt-[12px] tw-border-t-[1px]" v-loading="loading[1]">
          <div class="tw-flex tw-items-center tw-justify-between">
            <div class="tw-flex tw-items-center ">
              <span class="tw-font-semibold tw-text-[var(--primary-black-color-600)] tw-text-[14px]">关联任务</span>
              <el-tooltip content="刷新关联任务数据" placement="right" :show-after="500">
                <el-icon size="13" class="tw-cursor-pointer tw-ml-0.5" color="var(--el-color-primary)" @click="refreshTaskStatics()"><SvgIcon name="reset"/></el-icon>
              </el-tooltip>
            </div>
            <div>
              <el-button type="primary" link @click="goTaskDetail">详情</el-button>
            </div>
          </div>
          <div class="info-data-content tw-text-left tw-mt-[4px]">{{ relatedTaskList?.length??0 }}</div>
        </div>
      </div>
    </div>
    <TabsBox v-model:active="activeTab" :tabList="tabList" class="tw-mt-[16px]"></TabsBox>
    <keep-alive>
      <ToBeDistributeCluesQueue v-if="activeTab==='待分配'" :clueType="ClueStatusEnum[activeTab]" :groupType="1" @update:clue="refreshClueStatics()"/>
      <BeDistributeCluesQueue v-else-if="activeTab==='已分配'" :clueType="ClueStatusEnum[activeTab]" :groupType="1" @update:clue="refreshClueStatics()"/>
      <BeRecoveredCluesQueue v-else-if="activeTab==='已回收'" :clueType="ClueStatusEnum[activeTab]" :groupType="1" @update:clue="refreshClueStatics()"/>
      <BeArchivedCluesQueue v-else-if="activeTab==='已归档'" :clueType="ClueStatusEnum[activeTab]" :groupType="1" @update:clue="refreshClueStatics()"/>
    </keep-alive>
  </el-scrollbar>
  <div v-else class="module-container tw-min-w-[1080px] tw-items-center tw-justify-center">
    <el-empty></el-empty>
  </div>
  <AssociatedTasksDrawer v-model:visible="associatedTasksVisible" :taskList="relatedTaskList" @update:task="refreshTaskStatics"/>
  <SeatDrawer v-model:visible="seatVisible" :seatData="seatInfoDetail"/>
</template>

<script lang="ts" setup>
import HeaderBox from '@/components/HeaderBox.vue'
import { reactive, onUnmounted, ref, onMounted, defineAsyncComponent, } from 'vue'
import { clueManagerModel } from '@/api/clue'
import { formatNumber, formatNumber1 } from '@/utils/utils'
import TabsBox from '@/components/TabsBox.vue'
import { useUserStore } from '@/store/user'
import { SeatMember, SeatTeam } from'@/type/seat'
import { ClueStaticInfo, ClueStatusEnum, SeatStaticInfo, RelatedTaskInfo, RelatedTaskItem, TodayClueStaticInfo, } from '@/type/clue'
import SeatDrawer from './SeatDrawer.vue'
import { useTaskStore } from '@/store/taskInfo'
import { ElMessage } from 'element-plus'
import { router } from "@/router";
import AssociatedTasksDrawer from './AssociatedTasksDrawer.vue'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'

// 动态引入组件
const ToBeDistributeCluesQueue = defineAsyncComponent({loader: () => import('../components/ToBeDistributeCluesQueue.vue')})
const BeDistributeCluesQueue = defineAsyncComponent({loader: () => import('../components/BeDistributeCluesQueue.vue')})
const BeRecoveredCluesQueue = defineAsyncComponent({loader: () => import('../components/BeRecoveredCluesQueue.vue')})
const BeArchivedCluesQueue = defineAsyncComponent({loader: () => import('../components/BeArchivedCluesQueue.vue')})

const useStore = useUserStore()
/** 线索统计 模块 */
// 今日线索
const clueTodayStatics = ref<TodayClueStaticInfo>({})
// 线索统计
const clueStatics = reactive<ClueStaticInfo>({
  totalCount: undefined,
  toBeSendCount: undefined, toBeDistributeCount: undefined, beDistributeCount: undefined,
  beRecoveredCount: undefined, beArchivedCount: undefined,
  successCount: undefined, failedCount: undefined,
  toBeFirstFollowUpCount: undefined
})

const loading = ref([false, false, false, false])
// 获取坐席情况
const seatInfoDetail = reactive<{
  dialingSeats?: SeatMember[],
  inRestSeats?: SeatMember[],
  totalSeats?: SeatMember[],
  onlineSeats?: SeatMember[],
  idleSeats?: SeatMember[],
  postingSeats?: SeatMember[],
}>({
  dialingSeats: [], inRestSeats: [], totalSeats: [], onlineSeats: [], idleSeats: [], postingSeats: []
})
const refreshSeatStatics = async () => {
  loading.value[0] = true
  const [_, data] = await to(clueManagerModel.getCallTeamCondition()) as [any, SeatStaticInfo]
  seatInfoDetail.dialingSeats = [
    ...data.humanMachineDialingSeats??[],
    ...data.humanMachineListenSeats??[],
    ...data.humanMachineWindowSeats??[],
    ...data.manualDirectCallingSeats??[],
    ...data.manualDirectDialingSeats??[],
  ] // 通话中
  seatInfoDetail.totalSeats = []
  seatInfoDetail.onlineSeats = []
  Object.keys(data).map(item => {
    if (item !== 'offLineSeats') {
      // @ts-ignore
      seatInfoDetail.onlineSeats?.push(...(data[item]??[]))
    }
    // @ts-ignore
    seatInfoDetail.totalSeats?.push(...(data[item]??[]))
  })
  seatInfoDetail.idleSeats = [
    ...data.humanMachineIdleSeats??[],
    ...data.manualDirectIdleSeats??[],
  ] // 空闲
  seatInfoDetail.postingSeats = [
    ...data.humanMachinePostingSeats??[],
    ...data.manualDirectPostingSeats??[],
  ] // 话后
  seatInfoDetail.inRestSeats = data.inRestSeats || [] // 休息
  loading.value[0] = false
}
// 获取关联任务
const relatedTaskList = ref<RelatedTaskItem[]>([])
const refreshTaskStatics = async () => {
  loading.value[1] = true
  const [_, data] = await to(clueManagerModel.getCallTeamRelatedTasks()) as [any, RelatedTaskInfo[]]
  relatedTaskList.value = data?.map(item => {
    if (item.aiOutboundTask) {
      item.aiOutboundTask.callSeatCondition = item.callSeatCondition
      item.aiOutboundTask.activeCallSeatIds = item.activeCallSeatIds
    }
    return item.aiOutboundTask
  }) || []
  loading.value[1] = false
}
// 获取今日线索数据
const refreshTodayStatics = async () => {
  loading.value[2] = true
  const [err, data] = await to(clueManagerModel.getClueTodayStaticInfo()) as [any, TodayClueStaticInfo]
  clueTodayStatics.value = data || {
    acquireCount: 0,
    failCount: 0,
    followingCount: 0,
    successCount: 0,
  }
  loading.value[2] = false
}
// 获取线索统计数据
const refreshClueStatics = async () => {
  loading.value[3] = true
  const [_, data] = await to(clueManagerModel.getTeamClueStaticInfo()) as [any, ClueStaticInfo]
  Object.assign(clueStatics, data)
  loading.value[3] = false
}
/** 前往坐席详情 */
const seatVisible = ref(false)
const goSeatDetail = () => {
  seatVisible.value = true
}
/** 前往任务详情 */
const associatedTasksVisible = ref(false)
const goTaskDetail = () => {
  associatedTasksVisible.value = true
}
/** 线索tab */
const tabList = ['待分配', '已分配', '已回收', '已归档']
const activeTab = ref(tabList[0])
const callTeamList = ref<SeatTeam[]>([])
const initAction = async () => {
  const taskStore = useTaskStore()
  refreshSeatStatics()
  refreshTodayStatics()
  refreshTaskStatics()
  refreshClueStatics()
  await taskStore.getCallTeamSeatListOptions(true)
  await taskStore.getAllScriptListOptions(true)
  await taskStore.getEnableFormSetting(true)
}
const hasPermission = ref(false)
const checkPermission = async () => {
  const taskStore = useTaskStore()
  callTeamList.value = await taskStore.getCallTeamListOptions(undefined, true)
  if (callTeamList.value?.findIndex(item => item.leaderAccountId === useStore.userId) === -1) {
    hasPermission.value = false
    ElMessage.warning('该账号不是任何坐席组的组长，无坐席组线索页面权限')
    return router.back()
  } else {
    hasPermission.value = true
  }
}
checkPermission()
// 执行区
onMounted(() => {
  initAction()
})
onUnmounted(() => {
  const taskStore = useTaskStore()
  taskStore.$reset()
})
onBeforeRouteLeave(() => {
  const taskStore = useTaskStore()
  taskStore.$reset()
})
</script>

<style scoped lang="postcss">
.info-data-content {
  font-size: 24px;
  line-height: 24px;
}
</style>
