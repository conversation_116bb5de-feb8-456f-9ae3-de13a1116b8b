import { http } from '@/axios'
import {
  BlackListGroupItem,
  BlackListTypeEnum,
  BlackRestrictionItem,
  BlackRestrictionTypeEnum,
  ForbiddenWordItem,
  ForbiddenWordParam,
  FrequencyRestrictionInfo,
  FrequencyRestrictionParam,
  GoodNumberRestrictionBindMerchantParam,
  GoodNumberRestrictionInfo,
  GoodNumberRestrictionTestParam,
  WhiteBlackListItem,
  WhiteBlackListRecordItem,
  WhiteListGroupItem,
} from '@/type/dataFilter'
import { filterEmptyParams } from '@/utils/utils'
import { AxiosResponse } from 'axios'

export const whitelistModel = {
  // 查询产品列表（暂定支持分页）
  getGroupList: () => {
    return http({
      url: '/AiSpeech/whiteListGroup/findList',
      method: 'POST',
    }).then(res => res as unknown as WhiteListGroupItem[])
  },
  getRecordList: (data: {
    startTime?: string,
    endTime?: string,
  }) => {
    return http({
      url: '/AiSpeech/whiteListGroup/findImportHistoryList',
      method: 'POST',
      data,
    }).then(res => res as unknown as WhiteBlackListRecordItem[])
  },
  findPhoneList: (data: { phoneList: string[] }) => {
    return http({
      url: '/AiSpeech/whiteListGroup/findWhiteListRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown as WhiteBlackListItem[])
  },
  // 更新白名单分组号码数量
  updateCount: () => {
    return http({
      url: '/AiSpeech/whiteListGroup/refreshWhiteListGroupPhoneCount',
      method: 'POST',
    }).then(res => res as unknown as number)
  },
  saveSinglePhone: (data: {
    expireDate: string,
    id: number,
  }) => {
    return http({
      url: '/AiSpeech/whiteListGroup/editWhiteListRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  importSinglePhone: (data: {
    phone: string,
    expireDate: string,
  }) => {
    return http({
      url: '/AiSpeech/whiteListGroup/importSingleWhiteListRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  import: (data: any, params: {
    expireDate?: string,
  }) => {
    return http({
      url: '/AiSpeech/whiteListGroup/importPhonesFromExcel',
      method: 'POST',
      data,
      params,
    }).then(res => res as unknown)
  },
  deletePhone: (data: { id: number }) => {
    return http({
      url: '/AiSpeech/whiteListGroup/removeRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  cancelRecord: (params: { importHistoryId: number }) => {
    return http({
      url: '/AiSpeech/whiteListGroup/cancelWhiteListRecord',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
}

export const blacklistModel = {
  // 查询黑名单分组列表
  getGroupList: (data: { groupName?: string, phone?: string, groupType?: BlackListTypeEnum }) => {
    return http({
      url: '/AiSpeech/blackListGroup/findList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as BlackListGroupItem[])
  },
  // 查询黑名单分组列表（包含历史）
  getAllGroupList: () => {
    return http({
      url: '/AiSpeech/blackListGroup/findAllBlackListGroup',
      method: 'POST',
    }).then(res => res as unknown as BlackListGroupItem[])
  },
  // 保存黑名单分组
  saveGroup: (data: BlackListGroupItem) => {
    return http({
      url: data.id ? '/AiSpeech/blackListGroup/edit' : '/AiSpeech/blackListGroup/add',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 删除黑名单分组
  deleteGroup: (params: { id: number }) => {
    return http({
      url: '/AiSpeech/blackListGroup/remove',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  // 更新黑名单分组号码数量
  updateCount: (params: { id: number }) => {
    return http({
      url: '/AiSpeech/blackListGroup/refreshBlackListGroupPhoneCount',
      method: 'POST',
      params,
    }).then(res => res as unknown as number)
  },

  // 查询当前黑名单分组下的名单
  findPhoneList: (data: { phoneList: string[], groupId: string }) => {
    return http({
      url: '/AiSpeech/blackListGroup/findBlackListRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown as WhiteBlackListItem[])
  },
  // 编辑当前黑名单分组下的名单
  saveSinglePhone: (params: {
    id: number,
    expireDate: string,
  }) => {
    return http({
      url: '/AiSpeech/blackListGroup/editBlackListRecord',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  importSinglePhone: (data: {
    phone: string,
    blackGroupId: string,
    expireDate?: string
  }) => {
    return http({
      url: '/AiSpeech/blackListGroup/importSingleBlackListRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },

  getRecordList: (data: {
    startTime?: string,
    endTime?: string,
    groupId: number,
  }) => {
    return http({
      url: '/AiSpeech/blackListGroup/findImportHistoryList',
      method: 'POST',
      data,
    }).then(res => res as unknown as WhiteBlackListRecordItem[])
  },

  checkGroup: (data: { id: number }) => {
    return http({
      url: '/AiSpeech/blacklist/checkGroup',
      method: 'GET',
      data,
    }).then(res => res as unknown)
  },

  import: (data: any, params: {
    id: number,
    expireDate?: string,
  }) => {
    return http({
      url: '/AiSpeech/blackListGroup/importPhonesFromExcel',
      method: 'POST',
      data,
      params,
    }).then(res => res as unknown)
  },
  deletePhone: (data: { id: number, groupId: number }) => {
    return http({
      url: '/AiSpeech/blackListGroup/removeRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  cancelRecord: (params: { importHistoryId: number }) => {
    return http({
      url: '/AiSpeech/blackListGroup/cancelBlackListRecord',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
}

export const merchantBlacklistModel = {
  // 查询黑名单分组列表（商户端使用）
  getGroupList: (data: { groupName?: string, phone?: string, groupType?: BlackListTypeEnum }) => {
    return http({
      url: '/AiSpeech/tenantBlack/findList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as BlackListGroupItem[])
  },
  // 运营端通过groupId查询黑名单分组列表,groupId为空时查询全部
  getGroupListByGroupId: (data: { groupId?: string }) => {
    return http({
      url: '/AiSpeech/tenantBlack/findListForOperator',
      method: 'POST',
      data,
    }).then(res => res as unknown as BlackListGroupItem[])
  },
  // 保存黑名单分组
  saveGroup: (data: BlackListGroupItem) => {
    return http({
      url: data.id ? '/AiSpeech/tenantBlack/edit' : '/AiSpeech/tenantBlack/add',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 删除黑名单分组
  deleteGroup: (params: { id: number }) => {
    return http({
      url: '/AiSpeech/tenantBlack/remove',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  // 更新黑名单分组号码数量
  updateCount: (params: { id: number }) => {
    return http({
      url: '/AiSpeech/tenantBlack/refreshBlackListGroupPhoneCount',
      method: 'POST',
      params,
    }).then(res => res as unknown as number)
  },

  // 查询当前黑名单分组下的名单
  findPhoneList: (data: { phoneList: string[], groupId: string }) => {
    return http({
      url: '/AiSpeech/tenantBlack/findBlackListRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown as WhiteBlackListItem[])
  },
  // 编辑当前黑名单分组下的名单
  saveSinglePhone: (params: {
    id: number,
    expireDate: string,
  }) => {
    return http({
      url: '/AiSpeech/tenantBlack/editBlackListRecord',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  importSinglePhone: (data: {
    phone: string,
    blackGroupId: string,
  }) => {
    return http({
      url: '/AiSpeech/tenantBlack/importSingleBlackListRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },

  getRecordList: (data: {
    startTime?: string,
    endTime?: string,
    groupId: number,
  }) => {
    return http({
      url: '/AiSpeech/tenantBlack/findImportHistoryList',
      method: 'POST',
      data,
    }).then(res => res as unknown as WhiteBlackListRecordItem[])
  },

  syncClusterBlack: (data: { id: number }) => {
    return http({
      url: '/AiSpeech/tenantBlack/syncClusterBlack',
      method: 'GET',
      data,
    }).then(res => res as unknown)
  },

  import: (data: any, params: {
    id: number,
    expireDate?: string,
  }) => {
    return http({
      url: '/AiSpeech/tenantBlack/importPhonesFromExcel',
      method: 'POST',
      data,
      params,
    }).then(res => res as unknown)
  },
  deletePhone: (data: { id: number, groupId: number }) => {
    return http({
      url: '/AiSpeech/tenantBlack/removeRecord',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  cancelRecord: (params: { importHistoryId: number }) => {
    return http({
      url: '/AiSpeech/tenantBlack/cancelBlackListRecord',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
}

export const blacklistRestrictionModel = {
  findList: (data: {
    groupType: BlackRestrictionTypeEnum,
    productIndustryId?: number,
  }) => {
    return http({
      url: '/AiSpeech/blackListGroup/findBlackGroupControl',
      method: 'POST',
      data,
    }).then(res => res as unknown as BlackRestrictionItem[])
  },
  addGroups: (data: BlackRestrictionItem) => {
    return http({
      url: '/AiSpeech/blackListGroup/addBlackGroupControl',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  deleteGroups: (data: BlackRestrictionItem) => {
    return http({
      url: '/AiSpeech/blackListGroup/removeBlackGroupControlList',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}

export const frequencyRestrictionModel = {
  //获取全部频率限制列表
  findAllFrequent: async () => {
    return http({
      url: '/AiSpeech/frequentControl/findAllFrequent',
      method: 'POST',
    }).then(res => res as unknown as FrequencyRestrictionInfo[])
  },
  getGlobalList: async () => {
    return http({
      url: '/AiSpeech/frequentControl/findGlobalFrequent',
      method: 'POST',
    }).then(res => res as unknown)
  },
  getIndustryList: async (params: FrequencyRestrictionParam) => {
    return http({
      url: '/AiSpeech/frequentControl/findByIndustryId',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  getProductList: async (params: FrequencyRestrictionParam) => {
    return http({
      url: '/AiSpeech/frequentControl/findByProductId',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
  add: async (data: FrequencyRestrictionInfo) => {
    return http({
      url: '/AiSpeech/frequentControl/saveFrequent',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  edit: async (data: FrequencyRestrictionInfo) => {
    return http({
      url: '/AiSpeech/frequentControl/editFrequent',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  delete: async (params: FrequencyRestrictionInfo) => {
    return http({
      url: '/AiSpeech/frequentControl/deleteFrequent',
      method: 'POST',
      params,
    }).then(res => res as unknown)
  },
}

export const goodNumberRestrictionModel = {
  getList: async () => {
    return http({
      url: '/AiSpeech/lightPhone/findAllActiveLightPhones',
      method: 'GET',
    }).then(res => res as unknown as GoodNumberRestrictionInfo[])
  },
  add: async (data: GoodNumberRestrictionInfo) => {
    return http({
      url: '/AiSpeech/lightPhone/addOneLightPhone',
      method: 'POST',
      data
    }).then(res => res as unknown as GoodNumberRestrictionInfo[])
  },
  edit: async (data: GoodNumberRestrictionInfo) => {
    return http({
      url: '/AiSpeech/lightPhone/editLightPhoneRank',
      method: 'POST',
      data
    }).then(res => res as unknown as GoodNumberRestrictionInfo[])
  },
  delete: async (params: GoodNumberRestrictionInfo) => {
    return http({
      url: '/AiSpeech/lightPhone/deleteOneLightPhoneById',
      method: 'DELETE',
      params
    }).then(res => res as unknown as GoodNumberRestrictionInfo[])
  },
  test: async (params: GoodNumberRestrictionTestParam) => {
    return http({
      url: '/AiSpeech/lightPhone/checkPhone',
      method: 'POST',
      params
    }).then(res => res as unknown as GoodNumberRestrictionInfo[])
  },
  // 查询绑定商户
  getBindMerchant: async (params: GoodNumberRestrictionBindMerchantParam) => {
    return http({
      url: '/AiSpeech/lightPhone/verifyLineSupplier',
      method: 'GET',
      params
    }).then(res => res as unknown)
  },
}

// 违禁词管理，变量违禁词
export const forbiddenWordVariableModel = {
  getList: async (params: ForbiddenWordParam) => {
    return http({
      url: '/AiSpeech/forbiddenWord/findForbiddenWord',
      method: 'GET',
      params
    }).then(res => res as unknown as ForbiddenWordItem[])
  },
  add: async (data: ForbiddenWordParam) => {
    return http({
      url: '/AiSpeech/forbiddenWord/addForbiddenWord',
      method: 'POST',
      data
    }).then(res => res as unknown as ForbiddenWordItem)
  },
  addBatch: async (data: FormData) => {
    return http({
      url: '/AiSpeech/forbiddenWord/addForbiddenWordBatch',
      method: 'POST',
      data
    }).then(res => res as unknown as AxiosResponse)
  },
  edit: async (data: ForbiddenWordParam) => {
    return http({
      url: '/AiSpeech/forbiddenWord/editForbiddenWord',
      method: 'POST',
      data
    }).then(res => res as unknown as ForbiddenWordItem)
  },
  delete: async (params: ForbiddenWordParam) => {
    return http({
      url: '/AiSpeech/forbiddenWord/deleteForbiddenWord',
      method: 'DELETE',
      params
    }).then(res => res as unknown as AxiosResponse)
  },
}

// 违禁词管理，短链违禁词
export const forbiddenWordUrlModel = {
  getList: async (params: ForbiddenWordParam) => {
    return http({
      url: '/AiSpeech/forbiddenWord/findShortForbiddenWord',
      method: 'GET',
      params
    }).then(res => res as unknown as ForbiddenWordItem[])
  },
  add: async (data: ForbiddenWordParam) => {
    return http({
      url: '/AiSpeech/forbiddenWord/addShortForbiddenWord',
      method: 'POST',
      data
    }).then(res => res as unknown as ForbiddenWordItem)
  },
  addBatch: async (data: FormData) => {
    return http({
      url: '/AiSpeech/forbiddenWord/addShortForbiddenWordBatch',
      method: 'POST',
      data
    }).then(res => res as unknown as AxiosResponse)
  },
  edit: async (data: ForbiddenWordParam) => {
    return http({
      url: '/AiSpeech/forbiddenWord/editShortForbiddenWord',
      method: 'POST',
      data
    }).then(res => res as unknown as ForbiddenWordItem)
  },
  delete: async (params: ForbiddenWordParam) => {
    return http({
      url: '/AiSpeech/forbiddenWord/deleteShortForbiddenWord',
      method: 'DELETE',
      params
    }).then(res => res as unknown as AxiosResponse)
  },
}
