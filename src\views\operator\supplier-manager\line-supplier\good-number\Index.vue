<template>
  <div v-loading="loadingGoodNumber" class="tab-container">
    <div class="tab-header">
      <div class="tw-flex tw-justify-end">
        <el-button type="primary" link @click="onClickTestGoodNumber">
          测试规则
        </el-button>
        <el-button type="primary" @click="onClickAddGoodNumber">
          新增靓号规则
        </el-button>
        <el-button type="danger" :loading="loadingBatchDelete" @click="onClickDeleteBatchGoodNumber">
          批量删除
        </el-button>
      </div>
    </div>

    <!--表格-->
    <el-table
      ref="tableRef"
      v-loading="loadingGoodNumber||loadingBatchDelete"
      srtipe
      :data="goodNumberCurrentList"
      :header-cell-style="tableHeaderStyle"
      class="tw-mt-[12px]"
    >
      <template #empty>
        暂无数据
      </template>

      <el-table-column align="left" type="selection" width="36"></el-table-column>
      <el-table-column align="left" prop="regexName" label="靓号名称" min-width="100" show-overflow-tooltip>
        <template #default="{row}:{row:SupplierGoodNumberInfo}">
          {{ row?.regexName || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="left" prop="phoneRegex" label="靓号规则" min-width="200" show-overflow-tooltip />
      <el-table-column align="left" prop="rank" label="规则等级" min-width="100" show-overflow-tooltip>
        <template #default="{row}:{row:SupplierGoodNumberInfo}">
          {{ row?.rank || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="left" prop="comment" label="备注" min-width="100" show-overflow-tooltip>
        <template #default="{row}:{row:SupplierGoodNumberInfo}">
          {{ row?.comment || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="left" prop="scope" label="生效范围" min-width="300">
        <template #default="{row}:{row:SupplierGoodNumberInfo}">
          <div class="good-number-scope tw-flex tw-items-center tw-flex-nowrap">
            <span>
              {{ formatGoodNumberScopeText(row) }}
            </span>
            <div class="edit-button tw-flex-none">
              <el-button link @click="onClickEditGoodNumberScope(row)">
                <el-icon :size="18" color="#165DFF">
                  <SvgIcon name="edit2" />
                </el-icon>
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="right" fixed="right" width="60" label="操作">
        <template #default="{row}">
          <el-button type="danger" :disabled="loadingBatchDelete" link @click="onClickDeleteGoodNumber(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页条-->
    <PaginationBox
      :pageSize="goodNumberPageSize"
      :pageSizeList="goodNumberSizeList"
      :currentPage="goodNumberPageNum"
      :total="goodNumberPageTotal"
      @search="updateGoodNumberAllList"
      @update="updateGoodNumberCurrentList"
    />
  </div>

  <!--新增靓号规则弹窗-->
  <GoodNumberAddDialog
    :visible="goodNumberAddDialogVisible"
    :list="goodNumberAllList"
    @close="onGoodNumberAddDialogClose"
    @update="onGoodNumberAddDialogUpdate"
  />

  <!--靓号测试弹窗-->
  <GoodNumberTestDialog
    :visible="goodNumberTestDialogVisible"
    :data="goodNumberTestDialogData"
    :list="goodNumberAllList"
    @close="onGoodNumberTestDialogClose"
    @update="onGoodNumberTestDialogUpdate"
  />

  <!--靓号生效范围弹窗-->
  <GoodNumberScopeDialog
    :visible="goodNumberScopeDialogVisible"
    :data="goodNumberScopeDialogData"
    @close="onGoodNumberScopeDialogClose"
    @update="onGoodNumberScopeDialogUpdate"
  />
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import {
  SearchLineParams,
  SupplierGoodNumberInfo,
  SupplierGoodNumberMultipleParam,
  SupplierGoodNumberOneParam,
  SupplierLineInfo
} from '@/type/supplier'
import { computed, defineAsyncComponent, ref, watch } from 'vue'
import { Throttle, updateCurrentPageList } from '@/utils/utils'
import to from 'await-to-js'
import { supplierModel } from '@/api/supplier'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import { useSupplierStore } from '@/store/supplier'
import PaginationBox from '@/components/PaginationBox.vue'
import SvgIcon from '@/components/SvgIcon.vue'

// 动态引入组件
const GoodNumberAddDialog = defineAsyncComponent(() => import('./GoodNumberAddDialog.vue'))
const GoodNumberTestDialog = defineAsyncComponent(() => import('@/components/GoodNumberTestDialog.vue'))
const GoodNumberScopeDialog = defineAsyncComponent(() => import('./GoodNumberScopeDialog.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  needUpdate: boolean,
}>(), {
  needUpdate: false,
})
const emits = defineEmits(['update'])

const supplierStore = useSupplierStore()

watch(() => props.needUpdate, (val) => {
  if (val) {
    updateGoodNumberAllList()
  }
})

// ---------------------------------------- 通用 开始 ----------------------------------------

// ---------------------------------------- 靓号限制 开始 ----------------------------------------

// 表格DOM
const tableRef = ref()

// 靓号限制，全部，正在加载
const loadingGoodNumber = ref<boolean>(false)
// 靓号限制，全部，加载节流锁
const throttleGoodNumber = new Throttle(loadingGoodNumber)

// 靓号限制，全部，接口数据
const goodNumberAllList = ref<SupplierGoodNumberInfo[]>([])
// 靓号限制，总数
const goodNumberPageTotal = computed(() => {
  return goodNumberAllList.value.length ?? 0
})

// 靓号限制，当前页，全部的子集
const goodNumberCurrentList = ref<SupplierGoodNumberInfo[]>([])
// 靓号限制，当前页，页码
const goodNumberPageNum = ref(1)
// 靓号限制，当前页，每页大小
const goodNumberPageSize = ref(20)
// 靓号限制，当前页，每页大小可选数值
const goodNumberSizeList: number[] = [10, 20, 50, 100]

// 靓号限制，批量删除，正在加载
const loadingBatchDelete = ref<boolean>(false)
// 靓号限制，批量删除，加载节流锁
const throttleBatchDelete = new Throttle(loadingBatchDelete)

/**
 * 靓号限制，更新列表，全部
 */
const updateGoodNumberAllList = async () => {
  // 节流锁上锁
  if (throttleGoodNumber.check()) {
    return
  }
  throttleGoodNumber.lock()

  // 处理参数
  const params: SupplierGoodNumberMultipleParam = {
    callLineSupplierId: supplierStore.currentSupplier.id ?? undefined,
  }
  // 请求接口
  const [err, res] = <[any, SupplierGoodNumberInfo[]]>await to(supplierModel.getGoodNumber(params))
  if (err) {
    ElMessage.error('无法获取靓号列表')
    goodNumberAllList.value = []
    // 节流锁解锁
    throttleGoodNumber.unlock()
    return
  }

  // 更新列表
  goodNumberAllList.value = res?.length ? res : []
  // 更新当前页列表
  updateGoodNumberCurrentList(goodNumberPageNum.value, goodNumberPageSize.value)

  emits('update')

  // 节流锁解锁
  throttleGoodNumber.unlock()
}
/**
 * 靓号限制，更新列表，当前页
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateGoodNumberCurrentList = (p?: number, s?: number) => {
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined, null, 0等逻辑假值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    goodNumberPageNum.value = p!
    goodNumberPageSize.value = s!
  }
  // 更新当前页码
  goodNumberCurrentList.value = updateCurrentPageList(goodNumberAllList.value, goodNumberPageNum.value, goodNumberPageSize.value)
}
/**
 * 靓号限制 点击新增按钮
 */
const onClickAddGoodNumber = () => {
  // 更新弹窗表单数据
  goodNumberScopeDialogData.value = {
    isAllLinesActive: true,
    supplyLines: []
  }
  // 显示弹窗
  goodNumberAddDialogVisible.value = true
}
/**
 * 点击靓号的生效范围编辑按钮
 * @param {SupplierGoodNumberInfo} row 靓号限制信息
 */
const onClickEditGoodNumberScope = (row: SupplierGoodNumberInfo) => {
  // 更新弹窗表单数据
  goodNumberScopeDialogData.value = row
  // 打开弹窗
  goodNumberScopeDialogVisible.value = true
}
/**
 * 靓号限制 点击测试按钮
 * @param {SupplierGoodNumberInfo} row 靓号限制信息
 */
const onClickTestGoodNumber = (row: SupplierGoodNumberInfo) => {
  // 更新表单数据
  goodNumberTestDialogData.value = row
  // 显示弹窗
  goodNumberTestDialogVisible.value = true
}
/**
 * 靓号限制 点击删除按钮
 * @param row 当前行数据
 */
const onClickDeleteGoodNumber = (row: SupplierGoodNumberInfo) => {
  const text = row?.regexName
    ? `您确定要删除靓号规则【${row.regexName ?? ''}】吗？<br>${row.phoneRegex ?? ''}`
    : `您确定要删除靓号规则【${row.phoneRegex ?? ''}】吗？`
  // 显示确认弹窗
  Confirm({
    title: '删除确认',
    text,
    type: 'danger',
    confirmText: '确定',
    cancelText: '取消',
  }).then(async () => {
    // 处理参数
    const params: SupplierGoodNumberOneParam = {
      lightPhoneId: row.lightPhoneId ?? undefined,
      callLineSupplierId: supplierStore.currentSupplier.id ?? undefined,
    }
    // 请求接口
    const [err, _] = <[any, SupplierGoodNumberInfo]>await to(supplierModel.deleteGoodNumber(params))
    if (err) {
      ElMessage({
        type: 'error',
        message: '删除失败'
      })
      return
    }
    ElMessage({
      message: '删除成功',
      type: 'success',
    })
    // 更新列表
    await updateGoodNumberAllList()
  }).catch(() => {
  })
}
/**
 * 靓号限制，生效范围，格式化文本
 * @param {SupplierGoodNumberInfo} row 靓号限制信息
 * @param {boolean} showAll 展示完整信息
 */
const formatGoodNumberScopeText = (row: SupplierGoodNumberInfo, showAll: boolean = false) => {
  // 全部
  if (row.isAllLinesActive) {
    return '全部'
  }

  // 部分
  if (!row?.supplyLines?.length) {
    return '-'
  }

  // 靓号生效范围（已选线路列表）
  const list = row.supplyLines

  // 从线路信息里提取出名字并合并成字符串
  if (showAll) {
    // 展示完整信息
    let arr: string[] = []
    list.forEach((line: SupplierLineInfo) => {
      line?.lineName && arr.push(line?.lineName)
    })
    return arr.join('、') || '-'
  }
  // 展示简洁信息
  return '【' + list[0]?.lineName + '】等总共' + list.length + '个线路'
}
/**
 * 点击批量删除按钮
 */
const onClickDeleteBatchGoodNumber = () => {
  if (!tableRef.value) {
    ElMessage({
      type: 'warning',
      message: '表格未初始化',
    })
  }

  const list = tableRef.value?.getSelectionRows() ?? []
  if (!list.length) {
    ElMessage({
      type: 'warning',
      message: '请选择要删除的靓号规则',
    })
    return
  }

  // 格式化展示的靓号规则名称
  const nameList = list.map((item: SupplierGoodNumberInfo) => {
    return '【' + (item.regexName ?? item.phoneRegex ?? '') + '】'
  })
  const nameStr = nameList.join('<br>')

  // 确认弹窗
  Confirm({
    title: '批量删除确认',
    text: `您确定要批量删除选中的靓号规则吗？<br>${nameStr}`,
    type: 'danger',
    confirmText: '确定',
    cancelText: '取消',
  }).then(async () => {
    // 节流锁上锁
    if (throttleBatchDelete.check()) {
      return
    }
    throttleBatchDelete.lock()

    let errArr: string[] = []
    // 遍历列表批量请求接口，保证上一个执行完后再执行下一个
    for (const item of list) {
      const params: SupplierGoodNumberOneParam = {
        lightPhoneId: item.lightPhoneId ?? undefined,
        callLineSupplierId: supplierStore.currentSupplier.id ?? undefined,
      }
      // 请求接口
      const [err, _] = <[any, SupplierGoodNumberInfo]>await to(supplierModel.deleteGoodNumber(params))
      if (err) {
        errArr.push(item.regexName ?? item.phoneRegex ?? '')
      }
    }
    if (!errArr || !errArr.length) {
      ElMessage.success('批量删除成功')
    } else {
      ElMessage.error('批量删除失败：' + errArr.join(','))
    }
  }).catch(() => {
  }).finally(() => {
    // 节流锁解锁
    throttleBatchDelete.unlock()
    // 清空选中
    tableRef.value?.clearSelection()
    // 更新列表
    updateGoodNumberAllList()
  })
}

// ---------------------------------------- 靓号限制管理 结束 ----------------------------------------

// ---------------------------------------- 新增靓号规则弹窗 开始 ----------------------------------------

// 新增靓号规则弹窗 显示
const goodNumberAddDialogVisible = ref<boolean>(false)

/**
 * 新增靓号规则弹窗 关闭
 */
const onGoodNumberAddDialogClose = () => {
  goodNumberAddDialogVisible.value = false
}
/**
 * 新增靓号规则弹窗 更新
 */
const onGoodNumberAddDialogUpdate = () => {
  // 更新父组件列表
  updateGoodNumberAllList()
}

// ---------------------------------------- 新增靓号规则弹窗 结束 ----------------------------------------

// ---------------------------------------- 靓号测试弹窗 开始 ----------------------------------------

// 靓号测试弹窗 显示
const goodNumberTestDialogVisible = ref<boolean>(false)
// 靓号测试弹窗 数据
const goodNumberTestDialogData = ref<SupplierGoodNumberInfo>({})

/**
 * 靓号测试弹窗 关闭
 */
const onGoodNumberTestDialogClose = () => {
  goodNumberTestDialogVisible.value = false
}
/**
 * 靓号测试弹窗 更新
 * @param {SupplierGoodNumberInfo} data 新数据
 */
const onGoodNumberTestDialogUpdate = (data: SupplierGoodNumberInfo) => {
  // 更新父组件表单数据
  goodNumberTestDialogData.value = data
  // 更新父组件列表
  updateGoodNumberAllList()
}

// ---------------------------------------- 靓号测试弹窗 结束 ----------------------------------------

// ---------------------------------------- 靓号生效范围弹窗 开始 ----------------------------------------

// 靓号生效范围弹窗 显示
const goodNumberScopeDialogVisible = ref<boolean>(false)
// 靓号生效范围弹窗 数据
const goodNumberScopeDialogData = ref<SupplierGoodNumberInfo>({})

/**
 * 靓号生效范围弹窗 关闭
 */
const onGoodNumberScopeDialogClose = () => {
  goodNumberScopeDialogVisible.value = false
}
/**
 * 靓号生效范围弹窗 更新
 * @param {SupplierGoodNumberInfo} data 新数据
 */
const onGoodNumberScopeDialogUpdate = (data: SupplierGoodNumberInfo) => {
  // 更新父组件表单数据
  goodNumberScopeDialogData.value = data
  // 更新父组件列表
  updateGoodNumberAllList()
}

// ---------------------------------------- 靓号生效范围弹窗 结束 ----------------------------------------

// ---------------------------------------- 供应线路列表 开始 ----------------------------------------

// 当前供应商的全部供应线路列表
const lineAllList = ref<SupplierLineInfo[]>([])

/**
 * 供应线路列表，更新全部
 */
const updateLineAllList = async () => {
  // 处理参数
  const params: SearchLineParams = {
    callLineSupplierId: supplierStore.currentSupplier.id ?? undefined
  }
  const [err, res] = <[any, SupplierLineInfo[]]>await to(supplierModel.getLineList(params))
  if (err) {
    ElMessage({
      type: 'error',
      message: '无法获取当前线路供应商的供应线路列表'
    })
    return
  }
  // 更新列表
  lineAllList.value = res?.length ? res : []
}

// ---------------------------------------- 供应线路列表 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

updateLineAllList()
updateGoodNumberAllList()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
/* 标签卡 */
.tab-container {
  padding: 0;
  text-align: left;
  /* 标签卡顶部 */
  .tab-header {
    padding: 12px 16px 0;
  }
}
/* 表格列 生效范围 */
.good-number-scope {
  /* 编辑按钮 */
  /* 鼠标悬浮时显示 其它情况隐藏 */
  .edit-button {
    visibility: hidden;
  }
  &:hover {
    .edit-button {
      visibility: visible;
    }
  }
}
</style>
