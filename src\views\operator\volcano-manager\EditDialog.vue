<template>
  <el-dialog
    :model-value.sync="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">编辑人群包名</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
      >
        <el-form-item label="人群包ID：">
          <span class="info-title">{{ props.data?.packageId || '-' }}</span>
        </el-form-item>
        <el-form-item label="人群包名：" prop="modelName">
          <el-input
            v-model="editData.modelName"
            :maxlength="60"
            show-word-limit
            clearable
            @keyup.enter="confirm()"
            placeholder="请输入人群包名"
          ></el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive,} from 'vue'
import type { FormInstance, } from 'element-plus'
import { ElMessage } from 'element-plus'
import { volcanoModel } from '@/api/volcano'
import { Select, CloseBold } from '@element-plus/icons-vue'
import { trace } from '@/utils/trace';
import { VolcanoTaskLogItem, } from '@/type/volcano'
import to from 'await-to-js'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  data: VolcanoTaskLogItem | null,
}>();
const visible = ref(false)
const loading = ref(false)

class EditDataOrigin {
  modelName = undefined
  ids = []
}
const editData = reactive<{
  modelName?: string,
  ids?: number[],
}>(new EditDataOrigin())

const editRef = ref<FormInstance | null>(null)

const rules = {
  modelName: [
    { required: true, message: '请输入人群包名', trigger: 'blur' },
  ],
}
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}


const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const [err] = await to(volcanoModel.editModelName(editData))
      await trace({ page: '火山运营-人群包列表-修改人群包名', params: {err, params: editData}})
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        !err && emits('confirm')
        cancel()
      }
    }
  })
}

/** watch */ 
// 监听visible
watch(() => props.visible, async n => {
  visible.value = n
  if (n) {
    editData.ids = props.data ? [props.data.id] : []
    editData.modelName = props.data?.modelName || ''
  } else {
    Object.assign(editData, new EditDataOrigin())
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    .el-form-item__content {
      align-items: flex-start;
      justify-content: flex-start;
    }
    &:first-child {
      margin-top: 14px;
    }
    &.muti-items {
      margin-top: 0;
      width: 320px;
      &:last-child {
        margin-left: 20px;
      }
    }
  }
  .el-table {
    font-size: var(--el-font-size-base);
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
