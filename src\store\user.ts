import { defineStore } from 'pinia'
import { UserLoginForm, UserLoginResponse, UserInfo, TokenInfo } from '@/type/user'
import { userModel } from '@/api/user'
import { useGlobalStore } from '@/store/globalInfo'

const roleName: string = ''
const account: string = ''
const timeRange: string[] = ['08:00', '22:00'] // 账号可配置的任务运行时间
const colInfo: Record<string, string[]> = {}
export const useUserStore = defineStore({
  id: 'user',
  state: () => {
    return {
      pageApiMap: {} as Record<string, {
        route: string,
        api: string,
        service: string,
      }[]>,
      token: {
        token: '',
        expire: undefined,
      } as TokenInfo,
      versionTimeTag: '',
      mc: 'uuid',
      userId: null as number | null,
      timeRange,
      roleId: null as number | null,
      roleName,
      account,
      accountType: 1, // 商户端1，运营端0
      tenantId: null as number | null, // 商户ID
      homePage: ['', ''],
      groupId: '',
      permissions: {} as {[key: string]: string[]},
      colInfo,
      expireTime: null as string | null,
    }
  },
  getters: {
    isMasterAccount: (state) => +(state.groupId?.split('_')[2] || '-1') == state.userId && state.accountType === 1
  },
  actions: {
    async login(loginForm: UserLoginForm) {
      const res = await userModel.login(loginForm) as UserLoginResponse
      const { token, id, roleId, roleName, accountType, account, tenantId, groupId, expireTime } = res
      Object.assign(this.token, {
        expire: new Date().getTime() + 1000 * 60 * 60 * 12,
        token,
      })
      this.userId = id
      this.roleId = roleId
      this.roleName = roleName
      this.account = account
      this.groupId = groupId
      this.colInfo = {}
      this.accountType = accountType??1
      this.tenantId = tenantId??null
      this.expireTime = expireTime || null
    },
    async updateUserTimeRange() {
      const data = (await userModel.getTaskTimeRange() as string || '08:00,20:00').split(',')
      if(data?.length === 2) {
        this.timeRange = data
      } else {
        this.timeRange = ['08:00', '20:00']
      }
    },
    async logout() {
      this.token = {
        token: '',
        expire: undefined,
      }
      this.account = ''
      localStorage.clear()
      sessionStorage.clear()
    }
  },
  persist: [
    {
      paths: [
        'userId',
        'roleId',
        'roleName',
        'account',
        'accountType',
        'homePage',
        'token',
        'tenantId',
        'groupId',
        'timeRange',
        'versionTimeTag',
        'colInfo',
        'pageApiMap'
      ],
      storage: sessionStorage,
    },
  ]
})
