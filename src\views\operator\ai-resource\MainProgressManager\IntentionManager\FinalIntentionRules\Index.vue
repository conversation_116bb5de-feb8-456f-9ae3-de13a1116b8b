<template>
  <div class="rules-container">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <div class="info-title">
        最终意向将在过滤以下排除规则后产生
      </div>
      <div>
        <el-button @click="search()">刷新数据</el-button>
        <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="edit()">新增最终意向</el-button>
      </div>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%"
      class="tw-grow"
      row-key="id"
      border
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column align="left" property="intentionLevelName" width="120" label="分类结果" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column align="left" label="排除条件"  min-width="300">
        <template #default="{ row, $index }">
          <div v-if="row.excludeText && row.excludeText.length" v-for="(item, index) in row.excludeText||[]" :key="`${$index}-${index}`">
            <el-divider v-if="index" border-style="dashed">or</el-divider>
            <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
              {{ item2 || '-' }}
            </li>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="right" :width="isChecked ? 70 :160">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">{{!isChecked ? '编辑' : '查看'}}</el-button>
          <el-button v-if="!isChecked" type="primary" link @click="edit(row, true)">复制</el-button>
          <el-button v-if="!isChecked" type="danger" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
  </div>
  <EditDialog
    v-model:visible="addDialogVisible"
    :editData="editData"
    @confirm="search"
  ></EditDialog>
</template>

<script lang="ts" setup>
import { scriptIntentionModel, } from '@/api/speech-craft'
import { Plus,} from '@element-plus/icons-vue'
import { ref, onActivated, onDeactivated, } from 'vue'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import { useScriptStore } from '@/store/script'
import EditDialog from './EditDialog.vue'
import { FinalIntentionRuleItem, } from '@/type/IntentionType'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { formatterEmptyData } from '@/utils/utils'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'
import { translateMultRules } from '@/components/script-rules/constant'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked

// 表格区
const tableData = ref<FinalIntentionRuleItem[] | null>([])
// 搜索区
const search = async () => {
  loading.value = true
  const [_, data] = await to(scriptIntentionModel.findFinalIntentionList({ scriptId: editId }))
  tableData.value = (data || []).sort((a, b) => b.intentionLevelName ? a.intentionLevelName?.localeCompare(b.intentionLevelName) || -1 : 1).map(item => {
    const excludeText = translateMultRules(item.excludeConditionList)
    return { ...item, excludeText, }
  })
  loading.value = false
}

// 编辑
const editData = ref<FinalIntentionRuleItem | null>(null)
const addDialogVisible = ref(false)
const edit = (row?: FinalIntentionRuleItem, isCopy: boolean = false) => {
  if (row && row.id) {
    editData.value = JSON.parse(JSON.stringify(row));
    if (isCopy && editData.value) {
      editData.value.id = undefined
      editData.value.excludeConditionList = editData.value.excludeConditionList?.map(item => {
        item.conditionUniqueId = undefined
        item.advancedRuleConditionDTOList = item.advancedRuleConditionDTOList.map(item2 => {
          item2.conditionUniqueId = undefined
          item2.id = undefined
          return item2
        })
        return item
      })
    }
  } else {
    editData.value = null
  }
  addDialogVisible.value = true
}


// 拖动
const initTable = async () => {
  loading.value = true
  search()
  loading.value = false
}
onBeforeRouteLeave(() => {
  tableData.value = null
})
// 操作区
const delAction = async (id: number) => {
  loading.value = true
  const [err] = await to(scriptIntentionModel.deleteFinalIntentionList({id: id}))
  !err && ElMessage.success('删除成功')
  search()
  loading.value = false
}
const del = (row: FinalIntentionRuleItem) => {
  Confirm({
    text: `您确定要删除吗【${row.intentionLevelName}】?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}

// 执行
onActivated(() => {
  initTable()
})
onDeactivated(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.rules-container {
  width: 100%;
  height: calc(100vh - 250px);
  padding: 16px 12px 0;
  position: relative;
  box-sizing: border-box;
  padding-top: 10px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  font-size: var(--el-font-size-base);
  .el-table {
    font-size: var(--el-font-size-base);
    :deep(.cell) {
      padding: 0 16px;
      .el-divider--horizontal {
        margin: 12px 0;
      }
    }
  }
}
</style>
