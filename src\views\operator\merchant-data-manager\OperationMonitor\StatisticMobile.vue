<template>
  <div class="title-normal tw-self-start">
    <span>平台运行数据</span>
    <el-tooltip content="刷新平台运行数据" placement="right" :show-after="500">
      <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updateMonitorData">
        <SvgIcon name="reset" color="inherit"/>
      </el-icon>
    </el-tooltip>
  </div>
  <div class="tw-grid tw-grid-cols-4 tw-gap-[4px]">
    <div class="tw-flex tw-flex-col tw-items-center">
      <span class="number-normal">{{ formatNumber1(props.monitorData?.phoneNum || 0) }}</span>
      <span class="title-small">今日导入名单</span>
    </div>
    <div class="tw-flex tw-flex-col tw-items-center">
      <span class="number-normal">{{ formatNumber1(props.monitorData?.runningTaskNum || 0) + '/' + formatNumber1(props.monitorData?.totalTaskNum || 0) }}</span>
      <span class="title-small">今日任务数</span>
    </div>
    <div class="tw-flex tw-flex-col tw-items-center">
      <span class="number-normal">{{ formatNumber1(props.monitorData?.putThroughPhoneNum || 0) }}</span>
      <span class="title-small">今日接通名单</span>
    </div>
    <div class="tw-flex tw-flex-col tw-items-center">
      <span class="number-normal">{{ formatNumber1(props.monitorData?.calledPhoneNum || 0) }}</span>
      <span class="title-small">今日外呼名单</span>
    </div>
  </div>
  <div class="tw-grid tw-grid-cols-4 tw-gap-[4px]">
    <div class="tw-flex tw-flex-col tw-items-center">
      <span class="number-normal">{{ formatNumber1(props.monitorData?.callRecordPutThroughNum || 0) }}</span>
      <span class="title-small">今日接通次数</span>
    </div>
    <div class="tw-flex tw-flex-col tw-items-center">
      <span class="number-normal">{{ formatNumber1(props.monitorData?.callRecordNum || 0) }}</span>
      <span class="title-small">今日外呼次数</span>
    </div>
    <div class="tw-flex tw-flex-col tw-items-center">
      <span class="number-normal">{{ formatNumber1(props.monitorData?.sendSmsNum || 0) }}</span>
      <span class="title-small">今日发送短信</span>
    </div>
    <div class="tw-flex tw-flex-col tw-items-center">
      <span class="number-normal">{{ formatNumber1(props.monitorData?.triggerSmsNum || 0) }}</span>
      <span class="title-small">今日触发短信</span>
    </div>
  </div>
  <div class="tw-grid tw-grid-cols-4 tw-gap-[4px]">
    <div class="tw-flex tw-flex-col tw-items-center">
      <span class="number-normal">{{ formatNumber1(props.concurrentData?.supplyConcurrent||0) }}</span>
      <span class="title-small">线路供应并发</span>
    </div>
    <div class="tw-flex tw-items-center tw-flex-col">
      <span class="number-normal">{{ formatNumber1(props.concurrentData?.tenantConcurrent||0) }}</span>
      <span class="title-small">任务锁定并发</span>
    </div>
    <div class="tw-flex tw-items-center tw-flex-col">
      <span class="number-normal">{{ formatNumber1(props.concurrentData?.realConcurrent||0) }}</span>
      <span class="title-small">实际并发</span>
    </div>
    <div class="tw-flex tw-items-center tw-flex-col">
      <span class="number-normal">{{ formatNumber1(props.concurrentData?.pauseConcurrent||0) }}</span>
      <span class="title-small">暂停并发</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, ref, } from 'vue'
import { formatNumber1 } from '@/utils/utils'
import { MonitorTotalInfo, MonitorConcurrentInfo } from '@/type/monitor-statistic'

const props = defineProps<{
  monitorData: MonitorTotalInfo,
  concurrentData: MonitorConcurrentInfo,
}>();
const emits = defineEmits(['update:total',])

/** 头部数据 开始 */ 
// 头部数据loading
const updateMonitorData = () => {
  emits('update:total')
}

</script>

<style scoped lang="postcss" type="text/postcss">
.title-normal {
  font-size: 15px;
  font-weight: 600;
  color: var(--primary-black-color-600);
  line-height: 18px;
  display: flex;
  align-items: center;
}
.number-normal {
  font-size: 17px;
  font-weight: 700;
  color: var(--primary-blue-color);
  line-height: 20px;
  margin-top: 8px;
}
.title-small {
  font-size: 11px;
  color: var(--primary-black-color-400);
  line-height: 20px;
}
.number-small {
  font-size: 11px;
  color: var(--primary-black-color-400);
}
.content-normal {
  color: var(--primary-black-color-600);
  font-size: 11px;
  line-height: 18px;
  text-align: justify;
}
</style>