<template>
  <el-dialog
    :model-value="dialogVisible"
    width="640px"
    class="dialog-form"
    :close-on-click-modal="false"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">
        {{ title }}
      </div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-p-[12px]"
      ref="scrollbarRef"
    >
      <el-form
        :model="content"
        :rules="merchantRules"
        label-width="90px"
        ref="editRef"
      >
        <div class="form-section">
          <div class="form-section-header">
            基本信息
          </div>
          <el-form-item label="商户名称：" prop="tenantName">
            <el-input v-model.trim="content.tenantName" placeholder="填写商户名称" clearable maxlength="40" show-word-limit />
          </el-form-item>
          <el-form-item label="商户简称：" prop="tenantShortName">
            <el-input v-model.trim="content.tenantShortName" placeholder="填写商户简称" clearable maxlength="15" show-word-limit />
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="form-section-header">
            联系人信息
          </div>
          <el-form-item label="联系人：" prop="contactName">
            <el-input v-model.trim="content.contactName" placeholder="填写商户联系人" clearable />
          </el-form-item>
          <el-form-item label="联系电话：" prop="contactPhone">
            <el-input v-model.trim="content.contactPhone" placeholder="填写联系电话" clearable />
          </el-form-item>
          <el-form-item label="邮箱：" prop="contactMail">
            <el-input v-model.trim="content.contactMail" placeholder="填写邮箱地址" clearable />
          </el-form-item>
          <el-form-item label="联系地址：" prop="contactAddress">
            <el-input
              v-model.trim="content.contactAddress"
              type="textarea"
              placeholder="填写联系地址，不超过250字"
              clearable
              maxlength="250"
              show-word-limit
              autosize
              resize="none"
            />
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="form-section-header">
            合作状态
          </div>
          <el-form-item label="合作状态：" prop="status">
            <el-select v-model.trim="content.status" class="tw-w-full" placeholder="选择合作状态">
              <el-option
                v-for="merchantStatusItem in enum2Options(MerchantStatusEnum)"
                :key="merchantStatusItem.name"
                :value="merchantStatusItem.value"
                :label="merchantStatusItem.name"
              />
            </el-select>
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="form-section-header">
            接口类型
          </div>
          <el-form-item label="接口类型：" prop="callbackType">
            <template #label>
              <div class="tw-flex tw-items-center">
                <span>接口类型</span>
                <el-tooltip content="接口类型提交后，不可修改">
                  <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
                </el-tooltip>
                <span>：</span>
              </div>
            </template>
            <el-select v-model.trim="content.callbackType" :disabled="props.id > -1" class="tw-w-full" placeholder="选择接口类型">
              <el-option
                v-for="item in enum2Options(MerchantCallbackTypeEnum)"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="form-section-header">
            运营备注
          </div>
          <el-form-item label="备注：">
            <el-input
              v-model.trim="content.remark"
              type="textarea"
              placeholder="填写备注，不超过250字（选填）"
              clearable
              maxlength="250"
              show-word-limit
              autosize
              resize="none"
            />
          </el-form-item>
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed } from 'vue'
import { MerchantInfo, MerchantStatusEnum, MerchantCallbackTypeEnum } from '@/type/merchant'
import { merchantModel } from '@/api/merchant'
import { MerchantOriginalData, merchantRules } from './constant'
import { enum2Options, pickAttrFromObj } from '@/utils/utils'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js'
import { trace } from '@/utils/trace'
import { ResponseData } from '@/axios/request/types'

const props = defineProps<{
  visible: boolean,
  id: number,
  content: MerchantInfo
}>()
const emits = defineEmits([
  // 关闭弹窗
  'close',
  // 通知父组件更新弹窗状态并更新列表
  'update'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)
const loading = ref<boolean>(false)

// 表单内容
const content = reactive<MerchantInfo>(new MerchantOriginalData())
const title = computed(() => {
  return props.id > -1 ? '编辑商户' : '新增商户'
})


/**
 * 表单弹窗组件的表单提交回调函数
 */
const editRef = ref()
const scrollbarRef = ref()
const confirm = async () => {
  // 表单校验
  const [err] = await to(editRef.value?.validate())
  if (err) return

  loading.value = true
  // 处理参数
  const params = pickAttrFromObj(content, [
    'id', 'tenantNo', 'tenantName', 'tenantShortName',
    'contactName', 'contactPhone', 'contactMail', 'contactAddress',
    'remark', 'status', 'callbackType'
  ])
  if (params.id === -1) {
    // 新建
    params.id = undefined
  }

  // 请求接口
  trace({
    params,
    page: `商户管理-${props.id > -1 ? '编辑' : '创建'}商户`,
  })
  const [err1, res1] = await to(merchantModel.saveMerchant(params)) as [Error | null, ResponseData]
  loading.value = false
  if (err1) {
    return
  }

  // 将提交保存后的实体信息更新到当前编辑实体
  emits('update', res1.data || {})
  cancel()
}


/**
 * 关闭弹窗
 */
const cancel = () => {
  // 关闭弹窗
  emits('close')
}

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      // 有ID就使用传入的数据，没ID或者ID不正确就用表单默认值
      props.id > -1
        ? Object.assign(content, JSON.parse(JSON.stringify(props.content)))
        : Object.assign(content, new MerchantOriginalData());

      setTimeout(() => {
        scrollbarRef.value?.setScrollTop(0)
        editRef.value?.clearValidate()
      }, 100)
    }
  }
)
</script>

<style lang="postcss" scoped>
.form-section {
  .form-section-header {
    text-align: left;
    margin: 6px 0;
  }
}
</style>
