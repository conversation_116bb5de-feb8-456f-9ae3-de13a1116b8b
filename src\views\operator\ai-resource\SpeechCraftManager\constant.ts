// 话术管理相关的类型定义
export interface SpeechCraftNameFormat {
  applicationCode: string; // 甲方代码
  productName: string;    // 产品名称
  pathName: string;       // 转化路径
  entryName: string;      // 入口名称
  outboundType: string;   // 外呼类型
  businessType: string;   // 业务类型
  scriptEditor: string;   // 话术师
  version: string;        // 版本号
  voiceRecorder: string;  // 录音师
  extraWords: string;     // 额外备注
}

// 话术管理类
export class SpeechCraftNameFormatOrigin implements SpeechCraftNameFormat {
  applicationCode: string = ''; // 甲方代码
  productName: string = '';    // 产品名称
  pathName: string = '';       // 转化路径
  entryName: string = '';      // 入口名称
  outboundType: string = 'AI';   // 外呼类型
  businessType: string = '拉新';   // 业务类型
  scriptEditor: string = '';   // 话术师
  version: string = '';        // 版本号
  voiceRecorder: string = '';  // 录音师
  extraWords: string = '';     // 额外备注

  // 生成标准命名
  generateName(): string {
    const parts = [
      this.applicationCode,    // 甲方代码
      this.productName,        // 产品名称
      this.pathName,           // 转化路径
      this.entryName,          // 入口名称
      this.outboundType,       // 外呼类型
      this.businessType,       // 业务类型
      this.scriptEditor,       // 话术师
      this.version,            // 版本号
      this.voiceRecorder,      // 录音师
      this.extraWords          // 额外备注
    ].filter(Boolean);         // 过滤掉空值

    return parts.join('_');    // 使用下划线连接
  }

  // 从完整命名解析
  static parseFromName(fullName: string): SpeechCraftNameFormatOrigin {
    const parts = fullName.split('_');
    const instance = new SpeechCraftNameFormatOrigin();
    
    // 按顺序赋值，如果部分值缺失则保持默认值
    if (parts[0]) instance.applicationCode = parts[0];
    if (parts[1]) instance.productName = parts[1];
    if (parts[2]) instance.pathName = parts[2];
    if (parts[3]) instance.entryName = parts[3];
    if (parts[4]) instance.outboundType = parts[4];
    if (parts[5]) instance.businessType = parts[5];
    if (parts[6]) instance.scriptEditor = parts[6];
    if (parts[7]) instance.version = parts[7];
    if (parts[8]) instance.voiceRecorder = parts[8];
    if (parts[9]) instance.extraWords = parts[9];

    return instance;
  }

  // 验证命名格式是否完整
  validateRequired(): string[] {
    const requiredFields: Array<keyof SpeechCraftNameFormat> = [
      'applicationCode',
      'productName',
      'pathName',
      'entryName'
    ];
    
    return requiredFields.filter(field => !this[field]);
  }
}