<template>
  <el-dialog
    :model-value="props.visible"
    width="720px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        关联短信模板
      </div>
    </template>

    <!--表格-->
    <el-table :data="allList" :header-cell-style="tableHeaderStyle" border>
      <el-table-column align="left" prop="id" label="模板编号" width="90"/>
      <el-table-column align="left" prop="templateName" label="模板名称" min-width="160" show-overflow-tooltip />
      <el-table-column align="center" prop="templateStatus" label="启用状态" width="90">
        <template #default="{row}:{row:SmsTemplateItem}">
          <span class="status-box-mini tw-m-auto" :class="row.templateStatus === SmsTemplateStatusEnum['启用'] ? 'green-status' : 'red-status'">
            {{ findValueInEnum(row.templateStatus, SmsTemplateStatusEnum) || '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="businessType" label="业务类型" width="90">
        <template #default="{row}:{row:SmsTemplateItem}">
          {{ findValueInEnum(row.businessType, BusinessTypeEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="left" prop="messageSign" label="短信签名" :formatter="formatterEmptyData" width="120" show-overflow-tooltip />
      <el-table-column align="left" prop="messageContent" label="短信内容" :formatter="formatterEmptyData" min-width="200" show-overflow-tooltip />
    </el-table>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button type="primary" :icon="CloseBold" @click="onClickCancel">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, ref, watch } from 'vue'
import { CloseBold } from '@element-plus/icons-vue'
import { findValueInEnum, formatterEmptyData, Throttle } from '@/utils/utils'
import {
  MerchantAccountInfo,
  SmsChannelStatusParams,
  SmsTemplateFilter,
  SmsTemplateItem,
  SmsTemplateParams,
  SmsTemplateStatusEnum
} from '@/type/merchant'
import { BusinessTypeEnum } from '@/type/sms'
import { tableHeaderStyle } from '@/assets/js/constant'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  data: SmsTemplateItem[],
}>(), {
  visible: false,
  data: () => ([]),
})
const emits = defineEmits([
  'update:visible',
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 弹窗 开始 ----------------------------------------

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 表格 开始 ----------------------------------------

// 列表，正在加载
const loadingList = ref<boolean>(false)
// 列表，加载节流锁
const throttleList = new Throttle(loadingList)

// 列表，全部，接口数据
const allList = ref<SmsTemplateItem[]>([])

/**
 * 更新全部列表
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleList.check()) {
    return
  }
  throttleList.lock()

  allList.value = JSON.parse(JSON.stringify(props.data))

  // 节流锁解锁
  throttleList.unlock()
}

// ---------------------------------------- 表格 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    await updateAllList()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
