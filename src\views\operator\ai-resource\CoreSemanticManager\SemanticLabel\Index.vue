<template>
  <div class="module-main module-vertical">
    <div v-if="activeIndustry?.id" v-loading="loading" class="module-main module-vertical">
      <!--顶部-->
      <div class="tw-flex tw-justify-between tw-items-center tw-grow-0 tw-border-b-[1px] tw-py-1 tw-bg-white tw-px-[12px]">
        <div class="aside-title">{{ `【${activeIndustry?.secondIndustry || ''}】语义标签` }}</div>
        <div>
          <el-button @click="onClickTestSemantic">
            语义测试
          </el-button>
          <el-button v-if="activeIndustry && semanticsLabelList && semanticsLabelList.length > 0" @click="exportXls()">
            导出语义标签
          </el-button>
        </div>
      </div>

      <div class="module-main module-main-inner">
        <!--左侧-->
        <div class="tw-flex tw-flex-col tw-w-[240px]">
          <el-button class="tw-mb-[12px]" type="primary" :icon="Plus" @click="edit()">
            创建语义标签
          </el-button>
          <div class="tw-flex tw-items-center">
            <el-input
              v-model.trim="searchData.queryStr"
              clearable
              :placeholder="`${findValueInEnum(searchData.type || SemanticsLabelSearchTypeEnum['语义标签'], SemanticsLabelSearchTypeEnum)}名称`"
              style="width: 250px;"
              @keyup.enter.native="search"
            >
              <template #prepend>
                <el-select v-model="searchData.type" style="width: 100px;" class="tw-ml-1">
                  <el-option
                    v-for="item in Object.entries(SemanticsLabelSearchTypeEnum)"
                    :key="item.at(1)"
                    :label="item.at(0)"
                    :value="item.at(1)"
                  />
                </el-select>
              </template>
            </el-input>
          </div>
          <div class="aside-list">
            <el-scrollbar class="tw-pr-[14px]" view-class="tw-min-h-full">
              <!--列表为空-->
              <div
                v-if="!semanticsLabelList?.length"
                class="tw-bg-white tw-min-h-full tw-flex tw-item-center tw-justify-center"
              >
                <el-empty description="暂无数据" />
              </div>

              <div
                v-for="item in semanticsLabelList||[]"
                v-else
                :key="item.id"
                class="aside-item"
                :class="{'aside-item--active': item.id == currentSemantics?.id}"
              >
                <div
                  class="tw-box-border tw-w-[100%] tw-h-[100%] tw-py-[8px] tw-pr-[32px] tw-pl-[8px] tw-line-clamp-2"
                  @click="handleActive(item)"
                >
                  {{ item.semanticLabel }}
                </div>

                <div class="btn-box">
                  <el-button type="primary" :icon="Edit" link @click="edit(item)"></el-button>
                  <el-button type="primary" :icon="Delete" link @click="del(item)"></el-button>
                </div>

              </div>
            </el-scrollbar>
          </div>
        </div>

        <!--右侧-->
        <div v-if="currentSemantics && currentSemantics.id" class="module-content" style="margin-right: 0;">
          <SemanticLabelContent
            :currentSemantics="currentSemantics"
            :relatedSemanticsList="relatedSemanticsList"
            :allList="allSemanticsList"
            @renovate="handleActive"
          />
        </div>
      </div>
    </div>
  </div>

  <!--创建语义标签弹窗-->
  <EditDialog
    v-model:visible="addVisible"
    :addData="addData"
    @confirm="confirmAdd"
  />
</template>

<script lang="ts" setup>
import { onUnmounted, reactive, ref, watch, } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Edit, Plus } from '@element-plus/icons-vue'
import { scriptCoreSemanticModel } from '@/api/speech-craft'
import Confirm from '@/components/message-box'
import {
  AiSemantics,
  SecondIndustrySemanticItem,
  SemanticsLabelItem,
  SemanticsLabelSearchTypeEnum
} from '@/type/core-semantic'
import EditDialog from './EditDialog.vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router'
import to from 'await-to-js'
import dayjs from 'dayjs'
import { generateExcelByAoa } from '@/utils/export'
import { addDataOrigin } from '../constant'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from '@/store/user'
import SemanticLabelContent from './Content.vue'
import { findValueInEnum } from '@/utils/utils'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)

// 用户权限获取
const userStore = useUserStore()

const props = defineProps<{
  industry: SecondIndustrySemanticItem | null,
}>()
const emits = defineEmits([
  'test',
])

const semanticsLabelList = ref<SemanticsLabelItem[] | null>([])
const activeIndustry = ref<SecondIndustrySemanticItem | null>(null)
const relatedSemanticsList = ref<AiSemantics[]>([])

const currentSemantics = ref<SemanticsLabelItem | null>(null)
const addData = ref<SemanticsLabelItem>(new addDataOrigin(activeIndustry.value?.id || undefined))

const searchData = reactive<{
  queryStr: string,
  type: SemanticsLabelSearchTypeEnum
}>({
  type: SemanticsLabelSearchTypeEnum['语义标签'],
  queryStr: ''
})
const addVisible = ref(false)

/**
 * 根据语义标签获取关联语义列表
 * @param item 语义标签
 */
const handleActive = async (item: SemanticsLabelItem) => {
  currentSemantics.value = item

  loading.value = true
  const [err, res] = <[any, AiSemantics[]]>await to(scriptCoreSemanticModel.findSemanticRelationList({
    id: item.id,
  }))

  if (err) {
    ElMessage({
      message: '获取关联语义列表失败',
      type: 'error',
    })
    relatedSemanticsList.value = []
    loading.value = false
    return
  }

  relatedSemanticsList.value = res?.length ? res : []
  loading.value = false
}

/**
 * 点击搜索按钮
 */
const search = async () => {
  if (!activeIndustry.value || !activeIndustry.value.id) return ElMessage({
    type: 'warning', message: '请先选择所属行业'
  })

  loading.value = true
  const [err, res] = <[any, AiSemantics[]]>await to(scriptCoreSemanticModel.findSemanticLabelList({
    type: searchData.type,
    queryStr: searchData.queryStr.trim(),
    secondIndustryId: activeIndustry.value?.id,
  }))

  if (err) {
    ElMessage({
      message: '获取语义标签列表失败',
      type: 'error',
    })
    semanticsLabelList.value = []
    loading.value = false
    return
  }

  // 按创建时间倒序排序
  semanticsLabelList.value = (res?.length ? res : []).sort((a: SemanticsLabelItem, b: SemanticsLabelItem) => {
    return dayjs(a.createTime).isAfter(dayjs(b.createTime)) ? -1 : 1
  })
  // 更新当前选中
  if (semanticsLabelList.value?.length) {
    currentSemantics.value = currentSemantics.value || semanticsLabelList.value?.at(0) || {}
    await handleActive(currentSemantics.value)
  } else {
    currentSemantics.value = null
    relatedSemanticsList.value = []
  }

  loading.value = false
}

const confirmAdd = (data: SemanticsLabelItem) => {
  if (data) {
    currentSemantics.value = data
  }
  currentSemantics.value = null
  search()
}
const del = (row: SemanticsLabelItem) => {
  Confirm({
    text: `您确定要删除【${row.semanticLabel}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(async () => {
    try {
      const res = await scriptCoreSemanticModel.deleteSemanticLabel({
        id: row.id as number
      })
      if (res) {
        ElMessage({
          message: '删除成功',
          type: 'success',
        })
      } else {
        ElMessage({
          message: '删除失败',
          type: 'error',
        })
      }
      currentSemantics.value = null
      await search()
    } catch (err) {
      ElMessage({
        message: '删除失败',
        type: 'error',
      })
    }
  }).catch(() => {
  })
}
const edit = (row?: SemanticsLabelItem) => {
  addVisible.value = true
  if (row && row.id) {
    addData.value = row || new addDataOrigin(activeIndustry.value?.id || undefined)
  } else {
    addData.value = new addDataOrigin(activeIndustry.value?.id || undefined)
  }
}

const exportXls = () => {
  generateExcelByAoa(
    [['语义标签名称'], ...(semanticsLabelList.value || [])?.map(item => [(item.semanticLabel || '')])],
    `【${activeIndustry.value?.secondIndustry || ''}】语义标签-${activeIndustry.value?.updateTime ? dayjs(activeIndustry.value?.updateTime).format('YYYY-MM-DD') || '-' : '-'}.xls`
  )
}

const onClickTestSemantic = () => {
  emits('test')
}

const allSemanticsList = ref<AiSemantics[]>([])
const updateAllSemanticList = async () => {
  const [_, res] = <[any, AiSemantics[]]>await to(scriptCoreSemanticModel.findSemanticList({
    type: 'semantic',
    queryStr: '',
    secondIndustryId: activeIndustry.value?.id!,
  }))
  if (res) {
    allSemanticsList.value = res?.length ? res : []
  }
}

watch(() => props.industry, (val) => {
  if (val) {
    activeIndustry.value = val
    updateAllSemanticList()
    search()
  }
}, {
  immediate: true
})

const clearAll = () => {
  semanticsLabelList.value = null
  activeIndustry.value = null
}
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style scoped lang="postcss">
.semantic-container {
  display: flex;
  height: calc(100% - 50px);
  min-width: 1080px;
  .module-main {
    padding: 16px 0;
    &.module-main-inner {
      padding: 0 12px;
    }
    &.module-vertical {
      padding-top: 0;
    }
  }
  .aside-list-box {
    padding: 0;
    width: 250px;
  }
  .aside-title {
    font-size: 16px;
    font-weight: 600;
    margin-top: 6px;
    text-align: left;
  }
  .aside-item {
    position: relative;
    /* padding: 8px 8px 8px 12px; */
    padding: 0;
    .btn-box {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      justify-content: space-around;
      visibility: hidden;
      width: 32px;
      height: 12px;
      .el-button {
        width: 16px;
        height: 16px;
        border-radius: 0;
        font-size: 14px;
        color: #aaa;
      }
    }
    &:hover {
      .btn-box {
        visibility: visible;
        .el-button {
          color: #409eff;
        }
      }
    }
  }
  .industry--active {
    background-color: #409eff;
    color: #fff;
  }
  .module-content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
  }
  :deep(.el-collapse-item__header) {
    color: var(--primary-black-color-500);
    padding-left: 10px;
    font-weight: 600;
  }
  :deep(.el-collapse-item__content) {
    padding-bottom: 10px;
  }
}
</style>
