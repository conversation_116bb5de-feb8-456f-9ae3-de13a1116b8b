<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    class="restriction-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        {{ props.isEdit ? '编辑' : '新增' }}靓号规则
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">

        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="90px"
        >
          <el-form-item label="靓号名称：" prop="regexName">
            <el-input
              v-model="form.regexName"
              type="text"
              placeholder="请填写靓号名称"
              clearable
              maxlength="100"
              show-word-limit
              style="width: 100%;"
            />
          </el-form-item>
          <el-form-item label="靓号规则：" prop="phoneRegex">
            <el-input
              v-model="form.phoneRegex"
              :disabled="isBindMerchant"
              type="textarea"
              placeholder="请使用正则表达式填写靓号规则"
              :autosize="{ minRows: 2, maxRows: 10 }"
              resize="none"
              clearable
              style="width: 100%;"
            />
          </el-form-item>
          <el-form-item label="规则等级：">
            <el-select
              v-model="form.rank"
              placeholder="（无）"
              clearable
              style="width: 100%;"
            >
              <el-option label="高" value="高"></el-option>
              <el-option label="中" value="中"></el-option>
              <el-option label="低" value="低"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注：" prop="comment">
            <el-input
              v-model="form.comment"
              type="textarea"
              placeholder="请填写备注"
              :autosize="{ minRows: 2, maxRows: 10 }"
              resize="none"
              clearable
              maxlength="200"
              show-word-limit
              style="width: 100%;"
            />
          </el-form-item>

          <div class="form-section">
            <div class="form-section-header">
              测试靓号规则
            </div>
            <el-form-item label="测试号码：">
              <div class="tw-flex tw-flex-row">
                <el-input
                  v-model="phoneForTest"
                  maxlength="20"
                  show-word-limit
                  clearable
                  placeholder="请输入正确的手机号码格式"
                  style="width: 100%;"
                  :disabled="loadingTest"
                />
                <el-button type="primary" class="tw-ml-[8px]" :loading="loadingTest" @click="onClickTestPhone">
                  测试
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="命中规则：">
              <el-table
                v-loading="loadingConfirm"
                :data="ruleList"
                :header-cell-style="tableHeaderStyle"
                max-height="50vh"
              >
                <template #empty>
                  暂无数据
                </template>

                <el-table-column label="靓号名称" prop="regexName" show-overflow-tooltip />
                <el-table-column label="靓号规则" prop="phoneRegex" show-overflow-tooltip />
              </el-table>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { goodNumberRestrictionModel } from '@/api/data-filter'
import to from 'await-to-js'
import { GoodNumberRestrictionInfo } from '@/type/dataFilter'
import { tableHeaderStyle } from '@/assets/js/constant'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  isEdit: boolean,
  data: GoodNumberRestrictionInfo,
}>()
const emits = defineEmits([
  'close',
  'update'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(() => props.visible, async (val) => {
  dialogVisible.value = val
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    Object.assign(form, props.data)
    // 是否绑定商户
    if (props.isEdit) {
      // 编辑时检查
      isBindMerchant.value = await checkBindMerchant()
    } else {
      // 新增时一定没有绑定
      isBindMerchant.value = false
    }
  }
})

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): GoodNumberRestrictionInfo => {
  return {
    id: undefined,
    regexName: '',
    phoneRegex: '',
    rank: undefined,
    comment: '',
  }
}
// 表单数据
const form: GoodNumberRestrictionInfo = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  regexName: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('靓号名称不能为空'))
      } else {
        callback()
      }
    }
  },
  phoneRegex: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('靓号规则不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: GoodNumberRestrictionInfo = {
    id: form.id ?? undefined,
    regexName: form.regexName ?? undefined,
    phoneRegex: form.phoneRegex ?? undefined,
    // 如果没有规则等级，则值为null
    rank: form.rank ?? null,
    comment: form.comment ?? undefined,
  }
  // 请求接口
  let err
  // 是否存在ID
  if (form.id !== undefined && form.id !== null) {
    // 编辑
    const [e, _] = <[any, GoodNumberRestrictionInfo]>await to(goodNumberRestrictionModel.edit(params))
    err = e
  } else {
    // 新建
    const [e, _] = <[any, GoodNumberRestrictionInfo]>await to(goodNumberRestrictionModel.add(params))
    err = e
  }
  if (err) {
    ElMessage({
      type: 'error',
      message: '保存失败'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }
  ElMessage({
    message: '保存成功',
    type: 'success',
  })
  emits('update', params)
  // 关闭弹窗
  closeDialog()

  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
  phoneForTest.value = ''
  ruleList.value = []
  isBindMerchant.value = true
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 绑定商户 开始 ----------------------------------------

// 是否绑定商户，有绑定则不允许编辑靓号规则正则表达式，反之允许
const isBindMerchant = ref<boolean>(true)

/**
 * 检查是否绑定商户
 * @return true 绑定，接口出错 false 未绑定
 */
const checkBindMerchant = async () => {
  if (typeof props.data.id !== 'number') {
    return true
  }
  const [err, res] = <[any, boolean]>await to(goodNumberRestrictionModel.getBindMerchant({
    id: props.data.id ?? -1
  }))
  if (err) {
    ElMessage({
      type: 'error',
      message: '无法获取靓号规则绑定的商户'
    })
    return true
  }
  return res
}

// ---------------------------------------- 绑定商户 结束 ----------------------------------------

// ---------------------------------------- 测试靓号 开始 ----------------------------------------

// 测试靓号，正在加载
const loadingTest = ref<boolean>(false)
// 测试靓号，加载节流锁
const throttleTest = new Throttle(loadingTest)

// 用于测试靓号的电话
const phoneForTest = ref('')
// 匹配的规则列表
const ruleList = ref<GoodNumberRestrictionInfo[]>([])

/**
 * 点击靓号限制测试按钮
 */
const onClickTestPhone = () => {
  // 节流锁上锁
  if (throttleTest.check()) {
    return
  }
  throttleTest.lock()

  if (!form.phoneRegex) {
    ElMessage.warning('请填写靓号规则')
    ruleList.value = []
    // 节流锁解锁
    throttleTest.unlock()
    return
  }
  if (!phoneForTest.value) {
    ElMessage.warning('请填写测试号码')
    ruleList.value = []
    // 节流锁解锁
    throttleTest.unlock()
    return
  }

  // 重置结果
  ruleList.value = []
  // 将输入的电话号码进行正则匹配
  const regExp = new RegExp(form.phoneRegex ?? '')
  if (regExp.test(phoneForTest.value)) {
    ruleList.value = [{
      phoneRegex: form.phoneRegex ?? '',
    }]
  }

  // 节流锁解锁
  throttleTest.unlock()
}

// ---------------------------------------- 测试靓号 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// 如果是每次显示弹窗时都要进行操作，应该放在 dialogVisible 变量的 watch 函数里执行
// 这里是在 vue 组件创建时执行（setup 相当于生命周期的 beforeCreate 和 created），路由不变或者页面不刷新，只会执行一次

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
</style>
