export interface IndustryItem {
  id: number,
  createTime: string,
  updateTime: string,
  primaryIndustry: string,
  secondaryIndustries: { name: string, id: number }[],
  secondaryIndustriesIdMap: {
    [key: string]: number
  },
}

export interface IndustryOption extends IndustryItem {
  name: string,
}

export interface ProductItem {
  id?: number,
  createTime?: string,
  updateTime?: string,
  industrySecondFieldId?: number,
  industrySecondFieldName?: string,
  productName?: string,
  name?: string,
}

export class ProductItemOrigin {
  id = undefined
  productName = ''
  industrySecondFieldId = undefined
  industrySecondFieldName = undefined
}
