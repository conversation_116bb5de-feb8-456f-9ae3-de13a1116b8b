<template>
  <el-dialog
    :model-value="visible"
    width="540px"
    class="dialog-form"
    :close-on-click-modal="false"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-items-center tw-flex">
          <el-icon :size="20" color="#E59000"><WarningFilled /></el-icon>

        <span class="tw-ml-[6px]">
          请注意，该产品被其它账号关联，不支持删除
        </span>
      </div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-p-[12px] tw-text-[13px]"
    >
      <div v-for="(item, name) in props.list" :key="name" class="">
        <div class="tw-text-left tw-font-[600] tw-text-[14px] tw-leading-[20px] tw-my-[4px]">{{ '商户：' + name }}</div>
        <div class="tw-w-full tw-grid tw-gap-x-[8px] tw-gap-y-[12px] tw-grid-cols-3">
          <div v-for="sunItem in item" :key="sunItem" class="name-box">
            <span class="rectangle-left"></span>
            <span>{{ sunItem || '' }}</span>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch} from 'vue'
import { CloseBold, WarningFilled } from '@element-plus/icons-vue'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  list: Record<string, string[]>
}>();
const visible = ref(false)
const step = ref(1)
const title = computed(() =>  step.value === 2 ? '关联账号' : '请注意')
const cancel = () => {
  emits('update:visible', false)
}

watch(() => props.visible, n => {
  visible.value = n
  n && (step.value = 1)
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.name-box {
  display: flex;
  height: 36px;
  padding: 8px 12px;
  align-items: center;
  gap: 10px;
  font-size: 13px;
  border-radius: 4px;
  background: #F0F2F5;
  position: relative;
  line-height: 20px;
  color: var(--primary-black-color-600);
}
.rectangle-left {
  border-radius: 0px 2px 2px 0px;
  width: 2px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 6px;
  background-color: var(--primary-orange-color)
}
</style>
