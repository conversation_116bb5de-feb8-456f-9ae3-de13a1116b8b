import { SmsAccountGroupItem } from '@/type/merchant'
export class SmsAccountOrigin implements SmsAccountGroupItem {
  constructor(smsTemplateId: number) {
    this.tenantSmsTemplateId = smsTemplateId
  }

  cityCodes = []
  id = undefined
  smsAccountNumbers = []
  smsServiceProvider = undefined
  tenantSmsTemplateId: number

}


// 表格校验
export const channelRules = {
  smsServiceProvider: [{ required: true, message: '请选择运营商', trigger: 'change' }],
  cityCodes: [{ required: true, message: '请选择地区组', trigger: 'blur'}],
  // 账号组成
  smsAccountNumbers: [
    { required: true, message: '请选择账号组', trigger: ['change' ]}
  ],
}