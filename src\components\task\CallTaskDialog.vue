<template>
  <el-dialog
    v-model="dialogVisible"
    width="480px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">启动任务</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="95px"
        ref="editRef"
      >
        <el-form-item label="选择线路：" prop="lineId">
          <el-select v-model="editData.lineId" class="tw-w-[352px]" placeholder="请选择线路" @change="handleSelectLine">
            <el-option v-for="item in lineList" :key="item.id" :label="`${item.lineName}(${item.lineRemainConcurrent})`" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="editData.lineId && props.currentTask?.taskType!==TaskTypeEnum['人机协同']" label="所需并发：" prop="concurrent">
          <el-input-number v-model="editData.concurrent" :precision="0" :controls="false"  style="width:352px" placeholder="请输入所需并发" clearable :min="1"/>
        </el-form-item>
        <div v-if="!showCalculate && editData.lineId && props.currentTask?.taskType!==TaskTypeEnum['人机协同']" class="tw-float-right tw-mt-[-8px] tw-mb-[6px]"><el-button link type="primary" @click="showCalculate=true">并发预测</el-button></div>
        
        <div v-if="editData.lineId && props.currentTask?.taskType!==TaskTypeEnum['人机协同'] && showCalculate" class="tw-bg-[#eee] tw-p-[12px] tw-rounded-[4px] tw-mb-[6px]">
          <div class="tw-flex tw-items-center tw-w-full tw-justify-between tw-text-[13px] tw-mb-[4px]">
            <div class="tw-font-[600]">并发预测</div>
            <el-button type="primary" link  size="small" @click="showCalculate=false">
              隐藏
            </el-button>
          </div>
          <el-form-item  label="预期完成时间：">
            <el-time-picker v-model="expectedFinishTime" format="YYYY-MM-DD HH:mm:ss" placeholder="预期完成时间" @change="handleChangeFinishTime" class="tw-flex-grow"/>
            
          </el-form-item>
          <el-form-item v-show="!!expectedFinishTime" label="预测结果：">
            <el-button v-if="neededNumLoading" :loading="neededNumLoading" type="primary" link>并发计算中</el-button>
            <span v-else-if="!neededNumLoading && neededNum && neededNum<lineRemainConcurrent" class="tw-text-[#13BF77] tw-flex tw-items-center">
              <el-icon><CircleCheckFilled /></el-icon>
              <span>{{ `并发充足，约需${neededNum || 0}， 线路剩余${lineRemainConcurrent || 0}` }}</span>

              <el-button link type="primary" class="tw-ml-[16px]" @click="editData.concurrent=neededNum">
                填入
              </el-button>
            </span>
            <span v-else-if="!neededNumLoading && neededNum===0" class="tw-text-[#E54B17] tw-flex tw-items-center">
              预期完成时间过近，请重新选择
            </span>
            <span v-else class="tw-text-[#E54B17] tw-flex tw-items-center">
              {{ `并发不足，约需${neededNum}， 线路剩余${lineRemainConcurrent|| 0}` }}
            </span>
          </el-form-item>
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer tw-flex tw-justify-end">
        <div v-if="!!props.currentTask?.isAutoStop" class="tw-text-[13px] tw-text-[#E54B17] tw-items-center tw-flex tw-pl-[16px]">
          <el-icon :size="14"><SvgIcon name="warning"/> </el-icon>
          <span class="tw-ml-[2px]">该任务为止损状态，请确认无误后再启动！</span>
        </div>
        <div>
          <el-button @click="cancel">取消</el-button>
          <el-button :loading="loading" type="primary" @click="confirm">确认</el-button>
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive,} from 'vue'
import { CircleCheckFilled } from '@element-plus/icons-vue'
import { TaskManageItem, TaskStatusEnum, TaskTypeEnum } from '@/type/task'
import { merchantModel } from '@/api/merchant'
import { ElMessage, } from 'element-plus'
import { aiOutboundTaskModel,  } from '@/api/ai-report'
import { useUserStore } from '@/store/user'
import type { FormInstance, } from 'element-plus'
import { MerchantLineInfo } from '@/type/merchant'
import { findValueInEnum, Throttle } from '@/utils/utils'
import dayjs from 'dayjs'
import to from 'await-to-js'
import { trace } from '@/utils/trace'
import Confirm from '@/components/message-box'


const emits = defineEmits(['update:visible', 'confirm'])
const userInfo  = useUserStore()
const loading = ref(false)
const throttleLoading = new Throttle(loading)
const props = defineProps<{
  currentTask: TaskManageItem,
  visible: boolean
}>();
class dataOrigin {
  lineId = undefined
  lineName = undefined
  lineNum = undefined
  lineCode = undefined
  concurrent = undefined
}
const editData = reactive<{
  lineId?: number,
  lineName?: string,
  lineNum?: number,
  lineCode?: string,
  concurrent?: number
}>(new dataOrigin())
const lineRemainConcurrent = ref(0)
const lineList = ref<MerchantLineInfo[]>([])
const dialogVisible = ref(props.visible)
const editRef = ref<FormInstance  | null>(null)
const handleSelectLine = () => {
  const line = lineList.value.find(item => item.id === editData.lineId)
  if (line) {
    editData.lineCode = line.lineNumber
    editData.lineName = line.lineName
    lineRemainConcurrent.value = line.lineRemainConcurrent || 0;
  }
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const validateConcurrent = (rule: any, value: any, callback: any) => {
  if (value <= 0 || value > lineRemainConcurrent.value) {
    callback(new Error('所需并发必须大于0且小于线路剩余并发'))
  } else {
    callback()
  }
}
const rules = {
  lineId: [
    { required: true, message: '请选择线路', trigger: 'change' },
  ],
  concurrent: [
    { required: true, message: '请输入所需并发', trigger: 'blur' },
    { validator: validateConcurrent, trigger: 'blur' },
  ],
}


// 通过输入预期时间检查并发情况
const neededNumLoading = ref(false)
const neededNum = ref(-1)
const showCalculate = ref(false)
const expectedFinishTime = ref<string | null>(null)
const handleChangeFinishTime = async () => {
  if (!expectedFinishTime.value || !props.currentTask.id) return
  neededNumLoading.value = true
  const num = await aiOutboundTaskModel.getNeededConcurrent({
    taskIds: props.currentTask.id + '',
    expectedFinishTime: dayjs(expectedFinishTime.value).format('YYYY-MM-DD HH:mm:ss'),
  })
  neededNumLoading.value = false
  neededNum.value = num||0
}


const confirm = async () => {
  if (throttleLoading.check()) {
    return
  }
  editRef.value && editRef.value.validate(async (valid) => {
    throttleLoading.lock()
    if (valid && props.currentTask?.id! > 0) {
      const res = await aiOutboundTaskModel.findTaskByIds(props.currentTask.id!) as TaskManageItem[]
      if (res && res[0].callStatus === TaskStatusEnum['进行中']) {
        emits('confirm')
        throttleLoading.unlock()
        return ElMessage.warning('任务已处于进行中')
      }
      if (!editData.lineId) {
        throttleLoading.unlock()
        return ElMessage.warning('请选择线路')
      }
      
      const params = {
        taskId: props.currentTask.id as number,
        callStatus: TaskStatusEnum['进行中'],
        lineId: editData.lineId,
        lineName: editData.lineName || '',
        lineCode: editData.lineCode,
        concurrent: editData.concurrent || 0,
      }
      const [err1, num] = await to(aiOutboundTaskModel.preProcessTaskBeforeStart(params))
      await trace({
        page: `${findValueInEnum(props.currentTask.taskType, TaskTypeEnum)}任务-开启任务-量级检查-【${num ?? '空'}】`,
        params
      })
      if (err1) {
        throttleLoading.unlock()
        return
      }
      // 量级对不上，需要二次确认
      if (num && num !== -1) {
        const [err] = await to(Confirm({
          text: `任务名单数量:${num}，请和业务方核对数量是否正确，确认无误后，可开启任务`,
          type: 'warning',
          title: `操作确认`,
          confirmText: '确认',
        }))
        if (err) {
          throttleLoading.unlock()
          return
        }
        await trace({
          page: `${findValueInEnum(props.currentTask.taskType, TaskTypeEnum)}任务-开启任务-重置量级`,
          params
        })
        // 二次确认通过重置量级
        const [err2] = await to(aiOutboundTaskModel.resetTaskPhoneNum(params))
        if (err2) {
          ElMessage.error('重置量级失败，请联系管理员')
          throttleLoading.unlock()
          return
        }
      }
      await trace({
        page: `${findValueInEnum(props.currentTask.taskType, TaskTypeEnum)}任务-开启任务-开启`,
        params
      })
      const [err] = await to(props.currentTask.taskType === TaskTypeEnum['人机协同']
        ? aiOutboundTaskModel.startMixTask(params)
        : aiOutboundTaskModel.startAiTask(params))
      throttleLoading.unlock()
      if (!err) {
        ElMessage.success('操作成功')
        emits('confirm')
      }
    }
  })
}
const init = async () => {
  if (props.currentTask.id) {
    loading.value = true
    const data = await merchantModel.getMerchantLineListById({
      groupId: userInfo.groupId || '',
      scriptId: props.currentTask.speechCraftId!,
      lineType: 'AI_OUTBOUND_CALL'
    }) as MerchantLineInfo[]
    lineList.value = data.filter(item => item.enableStatus === 'ENABLE') || []
    neededNum.value = -1
    neededNumLoading.value = false
    showCalculate.value = false
    expectedFinishTime.value = null
    loading.value = false
  } else {
    ElMessage({
      type: 'error',
      message: '获取线路失败！'
    })
  }
}
watch(props, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    Object.assign(editData, new dataOrigin())
    init()
  }
}, {
  deep: true
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.dialog-form .el-form .el-form-item {
  margin-bottom: 13px;
}
</style>
