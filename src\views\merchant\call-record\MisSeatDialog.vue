<template>
  <el-dialog
    v-model="dialogVisible"
    width="480px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">漏接坐席</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-py-[12px] tw-px-[2px]"
      view-class="tw-flex tw-gaps-[8px] tw-flex-col"
    >
      <div v-for="(item, index) in seatList" :key="index" class="name-box">
        <span class="rectangle-left"></span>
        <span class="tw-truncate">{{ item||'' }}</span>
      </div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive,} from 'vue'
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  list: string[],
  visible: boolean
}>();

const dialogVisible = ref(props.visible)
const seatList = ref(props.list)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, n => {
  dialogVisible.value = props.visible
  if (n) {
    seatList.value = props.list
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.info-data-box-inner {
  padding: 4px 8px;
  gap: 0;
}
.name-box {
  display: flex;
  width: 100%;
  height: 36px;
  padding: 8px 12px;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: #F0F2F5;
  position: relative;
  line-height: 20px;
  margin-bottom: 12px;
  color: var(--primary-black-color-600);
}
.rectangle-left {
  border-radius: 0px 2px 2px 0px;
  width: 2px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 6px;
  background-color: var(--primary-orange-color)
}
</style>
