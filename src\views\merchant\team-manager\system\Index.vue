<template>
  <HeaderBox title="系统设置" />
  <div v-loading="loading" class="module-container-inner">
    <!-- 表单 -->
    <el-form
      :disabled="!editStatus"
      label-width="90px"
      :rules="rules"
      ref="editRef"
      class="tw-w-[600px] tw-grow"
      :model="editData"
    >
      <div class="info-title-deep-large">token策略</div>
      <el-form-item label="过期策略：" prop="tokenExpiredStatus">
        <el-radio-group v-model="editData.tokenExpiredStatus">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="editData.tokenExpiredStatus" label="过期时间：" prop="tokenDuration">
        <InputNumberBox v-model:value="editData.tokenDuration" placeholder="请输入token过期时间，6-999小时" append="小时" :min="6" :max="999" />
      </el-form-item>
      <hr class="tw-mb-[12px] tw-mt-[6px]">
      <div class="info-title-deep-large">用户密码策略</div>
      <el-form-item label="复杂程度：" prop="passwordComplexity">
        <el-radio-group v-model="editData.passwordComplexity">
          <el-radio :label="2">2种字符</el-radio>
          <el-radio :label="3">3种字符</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="最小长度：" prop="passwordMinLength">
        <InputNumberBox v-model:value="editData.passwordMinLength" placeholder="请输入密码最小长度，6-50" :min="6" :max="50" />
      </el-form-item>
      <el-form-item label="有效时间：" prop="passwordDuration">
        <InputNumberBox v-model:value="editData.passwordDuration" placeholder="请输入密码有效期，1-999天" append="天" :min="1" :max="999" />
      </el-form-item>
      <hr class="tw-mb-[12px] tw-mt-[6px]">
      <div class="info-title-deep-large">登录锁定策略</div>
      <el-form-item label="时间间隔：" prop="loginLockInterval">
        <InputNumberBox v-model:value="editData.loginLockInterval" placeholder="请输入连续登录失败时间间隔，5-999分钟" append="分钟" :min="5" :max="999" />
      </el-form-item>
      <el-form-item label="连续失败：" prop="loginLockCount">
        <InputNumberBox v-model:value="editData.loginLockCount" placeholder="请输入连续登录失败次数，3-999次" append="次" :min="3" :max="999" />
      </el-form-item>
      <el-form-item label="锁定时长：" prop="lockDuration">
        <InputNumberBox v-model:value="editData.lockDuration" placeholder="请输入触发策略的锁定时长，5-9999分钟" append="分钟" :min="5" :max="9999" />
      </el-form-item>
    </el-form>
    <!-- 按钮 -->
    <div class="tw-flex tw-justify-end tw-border-t-[1px] tw-px-[16px] tw-pt-[16px] tw-mt-[16px] tw-shrink-0">
      <el-button v-if="!editStatus" type="primary" @click="editStatus=true">编辑</el-button>
      <div v-else>
        <el-button type="primary" class="tw-ml-[16px]" @click="save">保存</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </div>
  </div>
  
 </template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
import Confirm from '@/components/message-box'
import { ElMessage } from 'element-plus'
import type { FormInstance, } from 'element-plus'
import { useGlobalStore } from '@/store/globalInfo'
import InputNumberBox from '@/components/InputNumberBox.vue'

const globalStore = useGlobalStore()
/** 表单规则开始 */
const rules = {
  passwordComplexity: [
    { required: true, message: '密码复杂度不能为空', trigger: 'change' }
  ],
  passwordMinLength: [
    { required: true, message: '密码最小长度不能为空', trigger: ['change', 'blur'] }
  ],
  passwordDuration: [
    { required: true, message: '密码有效期不能为空', trigger: ['change', 'blur'] }
  ],
  tokenDuration: [
    { required: true, message: 'token过期时间不能为空', trigger: ['change', 'blur'] }
  ],
  tokenExpiredStatus: [
    { required: true, message: 'token过期策略不能为空', trigger: 'change' }
  ],
  loginLockInterval: [
    { required: true, message: '连续登录失败间隔不能为空', trigger: ['change', 'blur'] }
  ],
  loginLockCount: [
    { required: true, message: '连续登录失败次数不能为空', trigger: ['change', 'blur'] }
  ],
  lockDuration: [
    { required: true, message: '触发策略后的锁定时长不能为空', trigger: ['change', 'blur'] }
  ]
}
/** 表单规则结束 */

/** 表单提交 */
const editData = reactive<{
  passwordComplexity: number,
  passwordMinLength: number,
  passwordDuration: number,
  tokenDuration: number,
  tokenExpiredStatus: number,
  loginLockInterval: number,
  loginLockCount: number,
  lockDuration: number
}>({...globalStore.sysTestInfo})
const editRef = ref<FormInstance  | null>(null)
// 正在提交
const loading = ref<boolean>(false)
const editStatus = ref(false) // 是否处于编辑状态
/** 保存，需二次确认 */
const save = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      Confirm({
        text: `修改系统设置将立即生效，请确认系统设置内容`,
        type: 'warning',
        title: `请注意`,
        confirmText: `确认并保存`
      }).then(async () => {
        loading.value = true
        setTimeout(() => {
          Object.assign(globalStore.sysTestInfo, editData)
          loading.value = false
          ElMessage.success('操作成功')
          cancel()
        }, 400);
        
     })
    }
  })
}
/** 取消，刷新数据并调整为非编辑状态 */
const cancel = async () => {
  editStatus.value = false
  updateSetting()
}

/** 查询账号设置信息 */
const updateSetting = async () => {
  loading.value = true
  setTimeout(() => {
    Object.assign(editData, globalStore.sysTestInfo)
    loading.value = false
  }, 200);
  editRef.value?.clearValidate()
}

/** 生命周期函数 */
onMounted(() => {
  updateSetting()
})
</script>

<style scoped lang="postcss">
.module-container-inner {
  padding: 16px;
  margin: 16px;
  min-width: 1080px;
  width: calc(100% - 32px);
  flex-grow: 1;
  flex-shrink: 1;
}
.info-title-deep-large {
  text-align: left;
  margin-bottom: 12px;
}
</style>
