<template>
  <div class="call-record-container">
    <div class="tw-w-full tw-bg-white tw-mb-[8px]">
      <div class="tw-grid tw-grid-cols-4 tw-gap-[8px]" :class="isExpand ? 'tw-pb-[8px]':'tw-pb-[12px]'">
        <div class="item">
          <InputPhonesBox
            v-model:value="searchForm.phone"
            @search="search()"
          />
        </div>
        <div class="item tw-col-span-2">
          <span class="tw-w-[64px] tw-text-[12px] tw-shrink-0">外呼时间：</span>
          <TimePickerBox
            v-model:start="searchForm.calloutStartTime"
            v-model:end="searchForm.calloutEndTime"
            :splitToday="true"
            placeholder="外呼"
            :maxRange="60*60*24*7*1000"
            :clearable="false"
          />
        </div>
        <div class="item">
          <el-select
            v-model="callStatusArr"
            placeholder="呼叫状态"
            multiple
            collapse-tags
            :max-collapse-tags="1"
            collapse-tags-tooltip
            clearable
            class="tw-w-[200px]"
          >
            <el-option v-for="item in callStatusOptions" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
      </div>
      <div v-show="isExpand" class="tw-grid tw-grid-cols-4 tw-gap-[8px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.name"
            placeholder="姓名"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.company"
            placeholder="公司"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.remarks"
            placeholder="备注"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <OperationAndCitySelectBox
          v-model:operator="searchForm.operator"
          v-model:province="searchForm.province"
          v-model:city="searchForm.city"
        />
        <div class="item">
          <el-select
            v-model="intentionClassArr"
            placeholder="AI分类结果"
            multiple
            collapse-tags
            :max-collapse-tags="2"
            collapse-tags-tooltip
            clearable
          >
            <el-option v-for="item in IntentionClassEnum" :key="item" :label="item" :value="item"/>
          </el-select>
        </div>
        <div class="item">
          <InputSelectBox
            v-model:value="intentionLabel"
            v-model:selectVal="prefixSelectVals.intentionLabelNameContains"
            :selectList="containList"
            placeholder="AI标签"
            @search="search()"
          />
        </div>
        <div class="item">
          <el-select v-model="searchForm.emptyLabelSearch" placeholder="包含空标签" clearable>
            <el-option label="排除空标签" value="N"/>
          </el-select>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <el-select
            v-model="manualIntentionClassArr"
            placeholder="人工分类结果"
            multiple
            collapse-tags
            :max-collapse-tags="2"
            collapse-tags-tooltip
            clearable
          >
            <el-option v-for="item in IntentionClassEnum" :key="item" :label="item" :value="item"/>
          </el-select>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <el-input
            v-model.trim="searchForm.manualIntentionLabels"
            placeholder="人工标签"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <el-select v-model="searchForm.isPostingOutOfTime" placeholder="话后处理超时" clearable>
            <el-option label="是" :value="true"/>
            <el-option label="否" :value="false"/>
          </el-select>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <el-select v-model="searchForm.isTransToCallSeat" placeholder="是否转人工" clearable>
            <el-option label="是" :value="true"/>
            <el-option label="否" :value="false"/>
          </el-select>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <SelectBox
            v-model:selectVal="callTeamIds"
            :options="callTeamList||[]"
            name="callTeamName"
            val="id"
            placeholder="坐席组"
            filterable
            class="tw-flex-grow"
            multiple
          >
          </SelectBox>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <SelectBox
            v-model:selectVal="callSeatIds"
            :options="callSeatList||[]"
            name="account"
            val="id"
            placeholder="坐席"
            filterable
            class="tw-flex-grow"
            multiple
          >
            <template v-slot:option-tips="{ option }">
              <span class="tw-text-gray-400">{{ option.name || '' }}</span>
            </template>
          </SelectBox>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <el-input
            v-model.trim="searchForm.noReceptionReason"
            placeholder="未介入原因"
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <el-input
            v-model.trim="searchForm.followUpNote"
            placeholder="跟进备注"
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <el-select v-model="searchForm.followUpStatus" placeholder="跟进状态" clearable>
            <el-option v-for="item in followUpStatusOption" :label="item.name" :value="item.value" :key="item.value"/>
          </el-select>
        </div>
        <div v-if="taskType===TaskTypeEnum['人机协同']" class="item">
          <el-select v-model="searchForm.isConvertToClue" placeholder="转为线索" clearable>
            <el-option label="是" :value="true"/>
            <el-option label="否" :value="false"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.ifSendSms" placeholder="是否触发短信" clearable>
            <el-option label="是" value="是"/>
            <el-option label="否" value="否"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.whoHangup" placeholder="挂断方" clearable>
            <el-option v-for="item in HangupOption" :label="item.name" :value="item.value" :key="item.value"/>
          </el-select>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.customerReplyContent"
            placeholder="客户回复"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
      
        <!-- <div class="item">
          <el-checkbox label="只看未读" />
          <el-checkbox label="触发短信" />
        </div> -->
        <div class="item">
          <span class="label">通时</span>
          <InputNumberBox v-model:value="searchForm.callDurationLeft" :max="searchForm.callDurationRight || 10000" placeholder="最低通时" style="width: 47%" append="秒"/>
          <span>&nbsp;至&nbsp;</span>
          <InputNumberBox v-model:value="searchForm.callDurationRight" placeholder="最高" style="width: 47%" append="秒" :min="searchForm.callDurationLeft || 0" :max="10000"/>
        </div>
        <div class="item">
          <span class="label">对话</span>
          <InputNumberBox v-model:value="searchForm.cycleCountLeft" :max="searchForm.cycleCountRight || 10000" placeholder="最少对话" style="width: 47%" append="轮"/>
          <span>&nbsp;至&nbsp;</span>
          <InputNumberBox v-model:value="searchForm.cycleCountRight" placeholder="最多" style="width: 47%" append="轮" :min="searchForm.cycleCountLeft || 0" :max="10000"/>
        </div>
        <div class="item">
          <span class="label">说话</span>
          <InputNumberBox v-model:value="searchForm.sayCountLeft" :max="searchForm.sayCountRight || 10000" placeholder="最少说话" style="width: 47%" append="次"/>
          <span>&nbsp;至&nbsp;</span>
          <InputNumberBox v-model:value="searchForm.sayCountRight" placeholder="最多" style="width: 47%" append="次" :min="searchForm.sayCountLeft || 0" :max="10000"/>
        </div>
    
        <template v-if="taskType===TaskTypeEnum['人机协同']">
          <div class="item">
            <span class="tw-w-[50px] tw-text-[12px] tw-shrink-0">人工等待</span>
            <InputNumberBox v-model:value="searchForm.transToCallSeatDurationLeft" :max="searchForm.transToCallSeatDurationRight || 10000" placeholder="最少等待" style="width: 45%" append="秒"/>
            <span>&nbsp;至&nbsp;</span>
            <InputNumberBox v-model:value="searchForm.transToCallSeatDurationRight" placeholder="最多等待" style="width: 45%" append="秒" :min="searchForm.transToCallSeatDurationLeft || 0" :max="10000"/>
          </div>
          <div class="item">
            <span class="label">监听</span>
            <InputNumberBox v-model:value="searchForm.listenDurationLeft" :max="searchForm.listenDurationRight || 10000" placeholder="最少监听" style="width: 45%" append="秒"/>
            <span>&nbsp;至&nbsp;</span>
            <InputNumberBox v-model:value="searchForm.listenDurationRight" placeholder="最多监听" style="width: 45%" append="秒" :min="searchForm.listenDurationLeft || 0" :max="10000"/>
          </div>
          <div class="item">
            <span class="label">接待</span>
            <InputNumberBox v-model:value="searchForm.receptionDurationLeft" :max="searchForm.receptionDurationRight || 10000" placeholder="最少接待" style="width: 45%" append="秒"/>
            <span>&nbsp;至&nbsp;</span>
            <InputNumberBox v-model:value="searchForm.receptionDurationRight" placeholder="最多接待" style="width: 45%" append="秒" :min="searchForm.receptionDurationLeft || 0" :max="10000"/>
          </div>
          <div class="item">
            <span class="label">话后</span>
            <InputNumberBox v-model:value="searchForm.postingDurationLeft" :max="searchForm.postingDurationRight || 10000" placeholder="最少话后" style="width: 45%" append="秒"/>
            <span>&nbsp;至&nbsp;</span>
            <InputNumberBox v-model:value="searchForm.postingDurationRight" placeholder="最多话后" style="width: 45%" append="秒" :min="searchForm.postingDurationLeft || 0" :max="10000"/>
          </div>
        </template>
      </div>
      <div class="tw-flex tw-justify-end tw-pt-[12px] tw-items-center tw-border-t-[1px] tw-h-[32px]">
        <div>
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
          <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon></el-button>
          <el-button type="primary" v-else @click="isExpand=true" link>展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon></el-button>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      ref="tableRef"
      :header-cell-style="tableHeaderStyle"
      stripe
      :row-key="(row: TaskCallRecordItem) => (row.recordId??'') + (row.id??'')"
    >
      <el-table-column label="号码" fixed="left" align="left" width="170">
        <template #default="{ row, $index }">
          <div class="phone-msg" :class="currentIndex===$index ? 'tw-text-[#165DFF]':''">
            <span>{{ filterPhone(row.recordId) + ' ' + (row.operator || '') }}</span>
            <el-tooltip content="复制" placement="right" :show-after="500">
              <el-icon :size="14" class="hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer" @click="copy(row.recordId)"><SvgIcon name="copy" color="inherit"></SvgIcon></el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="name" label="姓名" min-width="80" align="left" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="company" label="公司" min-width="120" align="left" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="remarks" label="备注" min-width="120" align="left" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column label="省市" align="left" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.province || row.city ? (row.province || '') + ' ' + (row.city || '') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="callStatus" label="呼叫状态" align="center" min-width="100">
        <template #default="{ row }">
          <span
              v-if="row?.callStatus"
              class="status-box-mini tw-mx-auto"
              :class="getCallStatusClass(row?.callStatus)"
            >
            {{ translateCallStatus(row?.callStatus) }}
           </span>
           <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="intentionClass" label="AI分类结果" align="center" min-width="120" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="intentionLabels" label="AI标签" align="left" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
            {{ row.intentionLabels ? row.intentionLabels.split(',').join('、') : '-' }}
        </template>
      </el-table-column>
      <template v-if="taskType === TaskTypeEnum['人机协同']">
        <el-table-column property="manualIntentionClass" label="人工分类结果" align="center" min-width="120" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="manualIntentionLabels" label="人工标签" align="left" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
              {{ row.manualIntentionLabels ? row.manualIntentionLabels.split(',').join('、') : '-' }}
          </template>
        </el-table-column>
      </template>
      <el-table-column property="ifSendSms" label="触发短信" align="center" min-width="80" :formatter="formatterEmptyData">
        <template #default="{ row }">
          <span v-if="!row.ifSendSms">-</span>
          <el-button v-else-if="row.ifSendSms === '是'" link type="primary" @click="openSmsDialog(row)">是</el-button>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column property="sayCount" label="说话次数" align="left" min-width="80" :formatter="formatterEmptyData"> </el-table-column>
      <el-table-column property="cycleCount" label="轮次" align="left" min-width="80" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="callDuration" label="通话时长" align="left" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          {{ (row.callDuration??-1)>-1 ? formatDuration(row?.callDuration/1000) || '0' : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="callOutTime" label="呼出时间" align="center" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="talkTimeStart" label="接通时间" align="center" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="talkTimeEnd" label="挂断时间" align="center" min-width="163" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="whoHangup" label="挂断方" align="center" min-width="80">
        <template #default="{ row }">
          {{ findValueInEnum(row.whoHangup, HangupEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="scriptStringId" label="执行话术" align="left" min-width="280" show-overflow-tooltip>
        <template #default="{ row }">
          {{ speechCraftMap.get(row.scriptStringId) || '-' }}
        </template>
      </el-table-column>
      <template v-if="taskType === TaskTypeEnum['人机协同']">
        <el-table-column property="isTransToCallSeat" label="是否转人工" align="center" min-width="100">
          <template #default="{ row }">
            {{ findValueInStatus(row.isTransToCallSeat) || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="转人工等待" align="left" min-width="120">
          <template #default="{ row }">
            {{ formatDiffDuration(row.startPopWinTime, row.endPopWinTime) || '-' }}
          </template>
        </el-table-column>
        <el-table-column property="misCallSeatIdsText" label="漏接坐席" align="left" :formatter="formatterEmptyData" min-width="80">
          <template #default="{ row }">
            <el-button v-if="row.misCallSeatIdsText" link type="primary" @click="checkMisCallSeat(row)">{{ JSON.parse(row.misCallSeatIdsText)?.length??'-' }}</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column property="listenDuration" label="监听时长" align="left" min-width="100">
          <template #default="{ row }">
            {{ formatDiffDuration(row.startMonitorTime, row.endMonitorTime) || '-' }}
          </template>
        </el-table-column>
        <el-table-column property="receptionDuration" label="接待时长" align="left" min-width="100">
          <template #default="{ row }">
            {{ formatDiffDuration(row.startAnswerTime, row.endAnswerTime) || '-' }}
          </template>
        </el-table-column>
        <el-table-column property="noReceptionReason" label="未介入原因" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        <el-table-column property="callTeamId" label="接待坐席" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
          <template #default="{ row }">
            {{ callSeatList.find(item => item.id === row.callSeatId)?.account || '-' }}
          </template>
        </el-table-column>
        <el-table-column property="callSeatId" label="所属坐席组" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
          <template #default="{ row }">
            {{ callTeamList.find(item => item.id === row.callTeamId)?.callTeamName || '-' }}
          </template>
        </el-table-column>
        <el-table-column property="postingDuration" label="话后处理时长" align="left" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            {{ (row.postingDuration??-1)>-1 ? formatDuration(row.postingDuration/1000) : '-' }}
          </template>
        </el-table-column>
        <el-table-column property="isPostingOutOfTime" label="话后处理超时" align="left" min-width="120">
          <template #default="{ row }">
            {{ findValueInStatus(row.isPostingOutOfTime) || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="表单收集" align="center" min-width="100">
          <template #default="{ row }">
            <el-button v-if="row.clueId" link :type="row.clueId ? 'primary': 'default'" :disabled="!row.clueId" @click="checkFormRecord(row)">查看</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column property="followUpNote" label="跟进备注" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
        <el-table-column property="followUpStatus" label="跟进状态" align="center" min-width="80" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #default="{ row }">
            <span
                v-if="row?.followUpStatus"
                class="status-box-mini tw-mx-auto"
                :class="getFollowStatusClass(row?.followUpStatus)"
              >
                {{findValueInEnum(row?.followUpStatus, FollowUpStatusEnum)}}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column property="isConvertToClue" label="转为线索" align="center" min-width="80" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #default="{ row }">
            {{ findValueInStatus(row.isConvertToClue) || '-' }}
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" align="right" fixed="right" width="90">
        <template #default="{ row, $index }">
          <div class="tw-flex tw-justify-end tw-items-center">
            <el-button v-if="row?.recordId && tempAudio.callId == row.callId && audioStatus == 'play'" type="primary" link @click="handleAudioPlay(row)">
              <el-icon :size="20"><svg-icon name="stop-circle"></svg-icon></el-icon>
            </el-button>
            <el-button
              :type="!row?.wholeAudioFileUrl ? 'default' : 'primary'"
              :disabled="!row?.wholeAudioFileUrl"
              link
              v-else
              @click="handleAudioPlay(row)"
            >
              <el-icon :size="20"><svg-icon name="play-circle"></svg-icon></el-icon>
            </el-button>
            <el-button
              :type="!(row && row.callStatus == CallStatusEnum['呼叫成功'] ) ? 'default' : 'primary'"
              :disabled="!(row && row.callStatus == CallStatusEnum['呼叫成功'] )"
              link
              @click="showCallRecord($index)"
            >
              <el-icon :size="20">
                <svg-icon name="search"></svg-icon>
              </el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      class="stick-pagination"
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      :dynamic-total="dynamicTotal"
      @search="search(currentPage)"
      @update="updatePage"
    >
    </PaginationBox>
    <div v-if="tempAudio && tempAudio.callId" class="audio-mode">
      <AudioMode
        :closeable="true"
        :audioUrl="tempAudio.wholeAudioFileUrl || ''"
        :audioName="`${tempAudio.taskName ? (tempAudio.taskName+'_') : ''}${tempAudio.phone|| '未知号码'}` "
        v-model:audioStatus="audioStatus"
        v-model:audioVolume="audioVolume"
        @close="tempAudio.callId = ''"
      >
      </AudioMode>
    </div>
  </div>
  <CallRecordDetailsDrawer
    v-model:visible="visible"
    :recordType="(currentTask.taskType as unknown as RecordTypeEnum)"
    :tableData="tableData"
    :total="total"
    :currentIndex="currentIndex"
    :searchForm="searchForm"
    @update:record="handleDetailsChange"
  />
  <MisSeatDialog
    v-model:visible="seatVisible"
    :list="seatMisList"
  />
  <TaskSmsDialog
    v-model:visible="smsVisible"
    :type="2"
    :data="currentRecord"
  />
  <FormRecordDialog v-if="formViewVisible" title="表单查看" type="check" v-model:visible="formViewVisible" :formSetting="formSetting" :formRecord="formRecordData"/>
</template>

<script lang="ts" setup>
import { TaskCallRecordItem, TaskCallSearchModal, TaskManageItem, SearchRecordFormOrigin, RecordTypeEnum, CallStatusEnum, HangupEnum, TaskTypeEnum } from '@/type/task'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { translateCallStatus, getCallStatusOptions, getCallStatusClass, containList } from '@/views/merchant/call-record/constants'
import { reactive, computed, ref, watch, onDeactivated, onActivated, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowUp, ArrowDown, } from '@element-plus/icons-vue'
import { IntentionClassEnum } from '@/type/common'
import { useGlobalStore } from '@/store/globalInfo'
import MisSeatDialog from '@/views/merchant/call-record/MisSeatDialog.vue'
import { formRecordModel, } from '@/api/clue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { copyText, filterPhone, formatterEmptyData, formatDuration, formatDiffDuration, findValueInEnum, enum2Options, findValueInStatus } from '@/utils/utils'
import { ResponseData } from '@/axios/request/types'
import PaginationBox from '@/components/PaginationBox.vue'
import InputNumberBox from '@/components/InputNumberBox.vue'
import CallRecordDetailsDrawer from '@/components/record/CallRecordDetailsDrawer.vue'
import { useTaskStore } from '@/store/taskInfo'
import { FormRecordItem, CollectionFormItem, FollowUpStatusEnum, } from '@/type/clue'
import { SeatTeam, SeatMember, } from '@/type/seat'
import { tableHeaderStyle } from '@/assets/js/constant'
import dayjs from 'dayjs'
import { getFollowStatusClass } from '@/views/merchant/manual-call/components/constant'
import SelectBox from '@/components/SelectBox.vue'
import TaskSmsDialog from './TaskSmsDialog.vue'
import InputSelectBox from '@/components/InputSelectBox.vue'
import to from 'await-to-js'
import InputPhonesBox from '@/components/InputPhonesBox.vue'

const AudioMode = defineAsyncComponent({loader: () => import('@/components/AudioMode.vue')})
const FormRecordDialog = defineAsyncComponent({loader: () => import('@/components/clue/FormRecordDialog.vue')})

const globalStore = useGlobalStore()
const taskStore = useTaskStore()
const loading = ref(false)
const props = defineProps<{
  currentTask: TaskManageItem;
  needRefresh: boolean;
}>();
const currentTask = props.currentTask
const taskType = computed(() => props.currentTask?.taskType)
const emits = defineEmits(['update:needRefresh'])
watch(() => props.currentTask.id, () => {
  if (props.currentTask?.id && props.currentTask?.id > 0) {
    clearSearchForm()
  }
})
watch(() => props.needRefresh, n => {
  if (props.currentTask?.id && props.currentTask?.id > 0) {
    n && search()
  }
})
const isExpand = ref(false)
// 表格和分页
const tableData = ref<TaskCallRecordItem []>([])
const pageSizeList = [20, 50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search(currentPage.value, true)
}

const audioVolume = ref<number>(70)

const copy =(val: string) => {
  copyText(val || '')
}

/** 查询漏接坐席 */
const seatVisible = ref(false)
const seatMisList = ref<string[]>([])
const checkMisCallSeat = (row: TaskCallRecordItem) => {
  if (!row.misCallSeatIdsText) return
  const arr: number[] =  JSON.parse(row.misCallSeatIdsText) || []
  seatMisList.value = arr.map(item => callSeatList.value.find(v => v.id === item)?.name || '')
  seatVisible.value = true
}

/** 查询表单记录 */
const formViewVisible = ref(false)
class FormRecordOriginItem {
  callSeatId = undefined
  clueId = undefined
  formRecordId = undefined
  fromCollectionContentList = []
}
const formRecordData = reactive<FormRecordItem>(new FormRecordOriginItem())
const formSetting = ref<CollectionFormItem[]>([])
const checkFormRecord = async (row: TaskCallRecordItem) => {
  const data = await formRecordModel.getFormRecordByClueId({
    clueId: row.clueId!
  })
  // 获取最新表单设置
  formSetting.value = await taskStore.getEnableFormSetting(true)
  Object.assign(formRecordData, data)
  formViewVisible.value = true
}

const searchForm = reactive<TaskCallSearchModal>(new SearchRecordFormOrigin(1, props.currentTask?.createTime))
const dynamicTotal = ref(false)
const intentionClassArr = ref<string[]>([])
const manualIntentionClassArr = ref<string[]>([])
const callStatusArr = ref<string[]>([])
const intentionLabel = ref<string>('')
  const prefixSelectVals = reactive({
  'intentionLabelNameContains': containList[0].value,
})
const search = async (page: number = 1, onlyPageChange: boolean = false) => {
  if (loading.value) return ElMessage.warning('请勿频繁操作')
  loading.value = true
  dynamicTotal.value = !onlyPageChange
  searchForm.taskId = currentTask.id
  searchForm.needOrder = '1' // 任务历史数据也默认排序；
  searchForm.callStatus = callStatusArr.value?.length > 0 ? callStatusArr.value.join(',') : undefined
  searchForm.intentionClass = intentionClassArr.value?.length > 0 ? intentionClassArr.value.join(',') : undefined
  
  searchForm.intentionLabelName = intentionLabel.value ? (intentionLabel.value?.replace(',', '|') || undefined) : undefined
  searchForm.intentionLabelNameContains = intentionLabel.value ? prefixSelectVals.intentionLabelNameContains : undefined

  searchForm.manualIntentionClass = manualIntentionClassArr.value?.length > 0 ? manualIntentionClassArr.value.join(',') : undefined
  searchForm.callSeatIds = callSeatIds.value?.join(',') || undefined
  searchForm.callTeamIds = callTeamIds.value?.join(',') || undefined
  searchForm.startPage = page > 1 ? page - 1 : 0
  currentPage.value = page
  searchForm.pageNum = pageSize.value
  const [err, res] = await to(taskType.value === TaskTypeEnum['人机协同'] ? aiOutboundTaskModel.findCallRecordMixList(searchForm) : aiOutboundTaskModel.findCallRecordList(searchForm))
  if (searchForm.phone) {
    tableData.value = (res?.data as TaskCallRecordItem[] || []).sort((a, b) => dayjs(a.callOutTime).isAfter(dayjs(b.callOutTime)) ? -1 : 1)
  } else {
    tableData.value = res?.data as TaskCallRecordItem[] || []
  }
  currentIndex.value = -1
  emits('update:needRefresh', false)
  loading.value = false
  if (!onlyPageChange && !err) {
    total.value = tableData.value.length === pageSize.value ? (res?.total || 0) : tableData.value.length
    const res2 = await (taskType.value === TaskTypeEnum['人机协同'] ? aiOutboundTaskModel.getCallRecordMixNum(searchForm) : aiOutboundTaskModel.getCallRecordAiNum(searchForm)) as ResponseData
    total.value = res2?.total || 0
    dynamicTotal.value = false
  }
}
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchRecordFormOrigin(1, props.currentTask?.createTime))
  callSeatIds.value = []
  callTeamIds.value = []
  intentionClassArr.value = []
  manualIntentionClassArr.value = []
  callStatusArr.value = []
}
const tempAudio = reactive<{
  callId?: string,
  taskName?: string,
  wholeAudioFileUrl?: string,
  phone?: string,
}>({
  callId: undefined,
  taskName: '',
  phone: '',
  wholeAudioFileUrl: '' // 音频文件URL
})
const audioStatus = ref<'pause' | 'play' | 'none'>('none')
const handleAudioPlay = (row?: TaskCallRecordItem) => {
  if (!row) {
    Object.assign(tempAudio,  {
      callId: undefined,
      taskName: '',
      wholeAudioFileUrl: '',
      phone: '',
    })
    audioStatus.value = 'none'
  } else {
    currentIndex.value = -1
    if (tempAudio.callId === row?.callId && tempAudio.wholeAudioFileUrl === row?.wholeAudioFileUrl) {
      audioStatus.value = audioStatus.value == 'play' ? 'pause' : 'play'
    } else {
      Object.assign(tempAudio, {
        callId: row.callId,
        taskName: row.taskName || currentTask.taskName || '',
        wholeAudioFileUrl: row.wholeAudioFileUrl,
        phone: row.recordId
      })
      audioStatus.value = 'play'
    }
  }
}
const handleAudioChange = (flag: -1 | 1) => {
  const index2 = tableData.value.findIndex(item => item.callId === tempAudio.callId) + flag
  if (!!tableData.value[index2]) {
    if (tableData.value[index2].wholeAudioFileUrl) {
      return Object.assign(tempAudio,  tableData.value[index2])
    } else {
      // Object.assign(tempAudio, tableData.value[index2])
      handleAudioChange(flag)
    }
  } else {
    Object.assign(tempAudio, {
      callId: undefined,
      taskName: '',
      wholeAudioFileUrl: '',
      phone: '',
    })
    audioStatus.value = 'none'
    return ElMessage({
      type: 'warning',
      message: `已经到列表${flag > 0 ? '最底部' : '最顶部'}`
    })
  }
}
const handleKeyup = (e: {code: string, preventDefault: Function}) => {
  if (['ArrowRight',].includes(e.code)) {
    handleAudioChange(1)
  }
  if (['ArrowLeft',].includes(e.code)) {
    handleAudioChange(-1)
  }
  if (['ArrowDown'].includes(e.code)) {
    audioVolume.value = audioVolume.value - 10 < 0 ? 0 : audioVolume.value - 10
  }
  if (['ArrowUp'].includes(e.code)) {
    audioVolume.value = audioVolume.value + 10 > 100 ? 100 : audioVolume.value + 10
  }
  if (['Space',].includes(e.code)) {
    handleAudioPlay(tempAudio)
  }
}
const handleKeydown = (e: {code: string, preventDefault: Function}) => {
  if(['ArrowDown', 'ArrowUp', 'ArrowRight', 'ArrowLeft','Space',].includes(e.code)) {
    e.preventDefault();
  }
}
onDeactivated(() => {
  document.removeEventListener('keydown', handleKeydown);
  document.removeEventListener('keyup', handleKeyup);
})
const speechCraftMap = ref<Map<string, string>>(new Map([]))
const callTeamList = ref<SeatTeam[]>([])
const callSeatList = ref<SeatMember[]>([])
const callSeatIds = ref<number[]>([])
const callTeamIds = ref<number[]>([])
const followUpStatusOption = enum2Options(FollowUpStatusEnum)
const HangupOption = computed(() => props.currentTask?.taskType === TaskTypeEnum['人机协同'] ? enum2Options(HangupEnum) : [
  {name: 'AI', value: HangupEnum['AI']},
  {name: '客户', value: HangupEnum['客户']},
])
const callStatusOptions = getCallStatusOptions()
const init = async () => {
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('keyup', handleKeyup)
  if (props.currentTask.taskType === TaskTypeEnum['人机协同']) {
    callTeamList.value = await taskStore.getCallTeamListOptions(undefined ,true)
    callSeatList.value = await taskStore.getCallGroupSeatListOptions(true)
  }
  const res = await taskStore.getAllScriptListOptions(true)
  speechCraftMap.value = new Map([])
  res?.forEach(item => {
    item.scriptStringId && speechCraftMap.value.set(item.scriptStringId, item.scriptName || '')
  })
}
// 表格区
const visible = ref(false)
const tableRef = ref()
const currentIndex = ref(-1)
const showCallRecord = (index: number) => {
  visible.value = true
  currentIndex.value = index || 0
  handleAudioPlay()
}
const handleDetailsChange = (s: TaskCallSearchModal, index: number, t: number, data: TaskCallRecordItem[]) => {
  Object.assign(searchForm, s)
  currentPage.value = s.startPage ? s.startPage + 1 : 1
  pageSize.value = s.pageNum || pageSizeList[0]
  currentIndex.value = index || 0
  total.value = t || 0
  tableData.value = data || []
  tableRef.value?.scrollTo({top: currentIndex.value * 42, behavior: 'smooth', });
}

// 触发短信详情
const currentRecord = ref<null | {callRecordId: string, callOutTime: string}>(null)
const smsVisible = ref(false)
const openSmsDialog = (row: TaskCallRecordItem) => {
  if (!row || !row.recordId || !row.callOutTime) return ElMessage.warning('获取记录信息失败')
  currentRecord.value = {
    callRecordId: row.recordId,
    callOutTime: row.callOutTime!,
  }
  smsVisible.value = true
}

watch(visible, n => {
  if (n) {
    document.removeEventListener('keydown', handleKeydown);
    document.removeEventListener('keyup', handleKeyup)
  } else {
    document.addEventListener('keydown', handleKeydown)
    document.addEventListener('keyup', handleKeyup);
  }
})
onActivated(() => {
  init()
  search()
})

</script>

<style scoped lang="postcss" type="text/postcss">
.call-record-container {
  padding: 16px 0 0;
  width: 100%;
  box-sizing: border-box;
  font-size: 13px;
  :deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) .el-input__wrapper {
    width: 100%;
  }
  .el-button {
    font-size: 13px;
  }
  .phone-msg {
    display: flex;
    align-items: center;
    span {
      width: 130px;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon {
      display: none;
    }
    &:hover .el-icon {
      display: inline-block;
    }
  }
  .audio-mode {
    position: fixed;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);
    z-index: 99;
  }
  .item {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;

    .label {
      flex-shrink: 0;
      flex-grow: 0;
      width: 26px;
    }
    :deep(.el-date-editor.el-input) {
      width: 47%;
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  .stick-pagination {
    position: sticky;
    bottom: 0;
    left: 0;
    background-color: #fff;
    z-index: 10;
  }
  .status-box-mini {
    min-width: 80px;
  }
}
</style>
