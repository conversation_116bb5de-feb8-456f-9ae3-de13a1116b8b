import { defineStore } from "pinia";
import { ElMessage, } from 'element-plus'
import { areaModel } from '@/api/area'
import dayjs from 'dayjs'
import { RestrictCopyModal, RestrictModal } from '@/type/common'
import { industryModel, productModel } from '@/api/industry'
import { IndustryItem, ProductItem } from '@/type/industry'
import { CorpusTypeEnum } from '@/type/speech-craft'
import to from 'await-to-js'
import { frequencyRestrictionModel } from '@/api/data-filter'
import { FrequencyRestrictionInfo } from '@/type/dataFilter'
import { useTaskStore } from '@/store/taskInfo'
import { merchantProjectModel } from '@/api/merchant'
import { MerchantProjectItem } from '@/type/merchant'
import { monitorStatisticModel } from '@/api/monitor-statistic'

const loading: boolean = false
const loadingModule: boolean = false
const provinceAllMap: {
  [propName: string]: string[]
} = {}
const copyCellList: {
  isMasterCanvas: boolean,
  nodes: {
    id: number, x?: number, y?: number, canvasId: number | null, corpusType: CorpusTypeEnum
  }[],
  edges: {
    id: number, pre?: number, next?: number,
  }[],
  pasteIds?: number[]
} = { isMasterCanvas: true, nodes: [], edges: [] }
let sysTestInfo = {
  passwordComplexity: 2,
  passwordMinLength: 6,
  passwordDuration: 1,
  tokenDuration: 6,
  tokenExpiredStatus: 1,
  loginLockInterval: 5,
  loginLockCount: 3,
  lockDuration: 5,
}
const copyAllRestrictData: RestrictCopyModal = {
  allRestrictProvince: null,
  allRestrictCity: null,
  ydRestrictProvince: null,
  ydRestrictCity: null,
  ltRestrictProvince: null,
  ltRestrictCity: null,
  dxRestrictCity: null,
  dxRestrictProvince: null,
  virtualRestrictCity: null,
  virtualRestrictProvince: null,
  unknownRestrictCity: null,
  unknownRestrictProvince: null,
  time: '',
}
const copyRestrictData: {
  province: string[],
  city: string[],
  time: string
} = {
  province: [],
  city: [],
  time: ''
}
const copyInfoArr: {
  name: string | null,
  value?: any[]
}[] = []
const pageSize: number = 20
const primaryIndustryList: string[] = []
const allIndustryList: IndustryItem[] = []
const productList: ProductItem[] = []
const copyLineIds: number[] = []
const frequencyRestrictionList: FrequencyRestrictionInfo[] = []
const lineNumber: string = ''
const masterAccountGroupIds: string[] = []
const masterAccountGroups: string[] = []
const isMobile: boolean = false
const projectGoupIdMap:Map<string, MerchantProjectItem> = new Map([])
export const useGlobalStore = defineStore({
  id: "globalInfo",
  state() {
    return {
      loading,
      loadingModule,
      pageSize,
      provinceAllMap,
      copyLineIds,
      copyRestrictData,
      copyAllRestrictData,
      primaryIndustryList,
      allIndustryList,
      productList,
      copyCellList,
      isMobile,
      copyCallSeatIds: null as null | number[],
      copyCallTeamIds: null as null | number[],
      projectGoupIdMap, // 所有主账号，对应配置的项目，当前一个主账号只有一个
      frequencyRestrictionList,
      lineNumber, // 用于运营监控-商户线路监控，默认选择的线路编号的缓存
      masterAccountGroupIds, // 用于运营监控-商户坐席监控，默认选择的主账号id列表
      masterAccountGroups,
      sysTestInfo, // 系统测试配置
      copyInfoArr, // 用于SelectBox复制
    };
  },
  getters: {
    // 获取省份列表
    getProvinceList: (state) => Object.keys(state.provinceAllMap) || [],
    getProvinceMap: (state) => {
      const res = new Map<string, string>([]);
      (Object.keys(state.provinceAllMap) || []).forEach(item => {
        const arr = item?.split(',')
        res.set(arr[1], arr[0])
      })
      return res
    },
    /** 获取城市list，用于城市名称模糊匹配，故不能用map */
    getCityNameList: (state) => {
      const res: {
        province: string,
        provinceName: string,
        city: string,
        cityName: string,
      }[] = [];
      (Object.keys(state.provinceAllMap) || []).forEach(item => {
        state.provinceAllMap[item].forEach(child => {
          const arr = child.split(',')
          const provinceArr = item.split(',')
          if (arr[0] && provinceArr[1] && arr[1]) {
            res.push({
              province: provinceArr[1],
              provinceName: provinceArr[0],
              city: arr[1],
              cityName: arr[0],
            })
          }
        })
      })
      return res
    },
    // 获取省市级联选项
    getProvinceCascaderOption: (state) => {
      return state.provinceAllMap ? Object.keys(state.provinceAllMap).map(item => {
        const arr = item.split(',')
        return {
          label: arr[0],
          value: arr[1],
          children: state.provinceAllMap[item].map(child => {
            const childArr = child.split(',')
            return {
              label: childArr[0],
              value: childArr[1],
            }
          })
        }
      }) : []
    },
    getIndustryOption: (state) => {
      return (state.allIndustryList || [])?.map(item => {
        return {
          name: item.primaryIndustry,
          ...item
        }
      }) || []
    },
    getPrimaryIndustryIdAndNameMap: (state) => {
      let res: Map<number, string> = new Map([])
      state.allIndustryList.forEach((item: IndustryItem) => {
        res.set(item.id, item.primaryIndustry)
      })
      return res
    },
    getIndustryIdAndNameMapList: (state) => {
      let list: { id: number, name: string }[] = []
      state.allIndustryList.forEach((primary: IndustryItem) => {
        (primary?.secondaryIndustries || []).forEach((secondary: { name: string, id: number }) => {
          list.push({
            id: secondary?.id,
            name: secondary?.name,
          })
        })
      })
      return list
    },
    getProductIdAndNameMapList: (state) => {
      let list: { id: number, name: string }[] = []
      state.productList.forEach((item: ProductItem) => {
        if (item?.id !== undefined && item?.id !== null && item?.name !== undefined && item?.name !== null) {
          list.push({
            id: item?.id,
            name: item?.name,
          })
        }
      })
      return list
    },
    getSecondIndustryList: (state) => {
      return (state.allIndustryList || [])?.flatMap(item => {
        return item.secondaryIndustries || []
      }) || []
    },
    getCopyCallSeatAccounts: (state) => {
      if (!state.copyCallSeatIds || state.copyCallSeatIds?.length < 1) return ''
      const taskStore = useTaskStore()
      const arr = taskStore.callTeamSeatList.flatMap(item => {
        return item.id && state.copyCallSeatIds?.includes(item.id) ? [item.account||''] : []
      })
      return arr.length === state.copyCallSeatIds.length ? arr?.join('、') || '' : ''
    },
    getCopyCallTeamNames: (state) => {
      if (!state.copyCallTeamIds  || state.copyCallTeamIds?.length < 1) return ''
      const taskStore = useTaskStore()
      const arr = taskStore.callTeamList.flatMap(item => item.id && state.copyCallTeamIds?.includes(item.id) ? [item.callTeamName||''] : [])
      return arr.length === state.copyCallTeamIds?.length ? arr?.join('、') || '' : ''
    },
    getCityNumberMap: (state) => {
      const res = new Map<string, {
        province: string,
        provinceName: string,
        city: string,
        cityName: string,
      }[]>([]);
      (Object.keys(state.provinceAllMap) || []).forEach(item => {
        state.provinceAllMap[item].forEach(child => {
          const arr = child.split(',')
          const provinceArr = item.split(',')
          if (arr[2] && provinceArr[1] && arr[1] && arr[0]) {
            const list = res.get(arr[2]) || []
            res.set(arr[2], [...list, {
              province: provinceArr[1],
              provinceName: provinceArr[0],
              city: arr[1],
              cityName: arr[0],
            }])
          }
        })
      })
      return res
    },
  },
  actions: {
    async getProvinceInfo(needUpdate: boolean = false): Promise<{
      [propName: string]: string[]
    }> {
      if (!(this.provinceAllMap && Object.keys(this.provinceAllMap).length > 1) || needUpdate) {
        this.provinceAllMap = await areaModel.findAreasList() as {
          [propName: string]: string[]
        }
      }
      return this.provinceAllMap
    },
    copyRestrictAction(tempOperatorProvinces: string[], tempOperatorCities: string[]) {
      this.copyRestrictData.province = tempOperatorProvinces
      this.copyRestrictData.city = tempOperatorCities
      this.copyRestrictData.time = dayjs().format('YYYY-MM-DD HH:mm:ss')
    },
    copyAllRestrictAction(dataAll: RestrictModal,) {
      this.copyAllRestrictData = {
        ...dataAll,
        time: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
    },
    updateLoading(val: boolean = false) {
      this.loading = val
      this.loading && setTimeout(() => {
        if (this.loading) {
          this.loading = false
          ElMessage({
            type: 'error',
            message: '加载超时'
          })
        }
      }, 5000);
    },
    // 更新一级行业列表
    async getPrimaryIndustryList() {
      try {
        const res = <string[]>await industryModel.getPrimaryIndustryField()
        // 如果接口返回的不是数组，则设置成默认值空数组
        this.primaryIndustryList = Array.isArray(res) ? res : []
      } catch (e) {
      }
    },
    // 更新所有行业列表
    async getAllIndustryList() {
      try {
        const res = <IndustryItem[]>await industryModel.getAllIndustryField()
        // 如果接口返回的不是数组，则设置成默认值空数组
        this.allIndustryList = Array.isArray(res) ?
          res.map(item => {
            item.secondaryIndustries = []
            item.secondaryIndustriesIdMap && Object.keys(item.secondaryIndustriesIdMap).map(k => {
              item.secondaryIndustries.push({
                name: k,
                id: item.secondaryIndustriesIdMap[k],
              })
            })
            return item
          }) || [] : []
      } catch (e) {
      }
    },
    async updateProjectList (needUpdate: boolean = false) {
      if (!(this.projectGoupIdMap && this.projectGoupIdMap.size > 1) || needUpdate) {
        const [err1, res1] = await to(merchantProjectModel.getAllProjectList())
        const [err2, res2] = await to(monitorStatisticModel.getAllMainAccount())
        this.projectGoupIdMap = new Map([])
        res1?.forEach(item => {
          if (item.groupId) {
            const account = res2?.find(a => a.groupId === item.groupId)?.account || ''
            account && this.projectGoupIdMap.set(account + '', item)
          }
        })
      }
      return this.projectGoupIdMap || []
    },
    /**
     * 更新省市行政代码与名称映射表
     */
    async updateCityCode2NameMap() {
      // 如果没有行政区划代码信息，立马更新
      if (!Object.keys(this.$state.provinceAllMap)?.length) {
        await this.getProvinceInfo()
      }

      // 名称作为属性名，代码作为属性值
      // 转换成
      // 代码作为属性名，名称作为属性值
      const code2NameMap: {
        [code: string]: string
      } = {}

      Object.keys(this.$state.provinceAllMap).forEach((province) => {
        // 省份代码
        const provinceCode = province.split(',')[1]
        // 省份名称
        const provinceName = province.split(',')[0]
        // 该省份下所有城市代码和名称
        this.$state.provinceAllMap[province].forEach((city) => {
          const cityCode = city.split(',')[1]
          code2NameMap[cityCode] = provinceName + ',' + city.split(',')[0]
        })
        // 最后更新省份本身的名称
        // 这样会更新成形如上海市、江苏省这样的格式
        // 如果在更新城市代码名称前执行此动作，则直辖市会更新成形如上海市,上海市这样的格式
        code2NameMap[provinceCode] = provinceName
      })

      return code2NameMap
    },
    // 更新产品
    async updateProductList(needUpdate: boolean = false) {
      if (!(this.productList && this.productList.length > 1) || needUpdate) {
        const res = await to(productModel.getList())
        this.productList = (res[1] || []).map(item => {
          return {
            id: item.id!,
            name: item.productName || '',
            ...item
          }
        })
      }
      return this.productList
    },
    async getAllFrequencyRestrictionList(needUpdate: boolean = false) {
      if (needUpdate || !this.frequencyRestrictionList || this.frequencyRestrictionList.length < 1) {
        const [_, res] = await to(frequencyRestrictionModel.findAllFrequent())
        this.frequencyRestrictionList = res || []
      }
      return this.frequencyRestrictionList
    },
    addCopyInfo(data: {name: string, value: any[]}) {
      const index = this.copyInfoArr?.findIndex(item => item.name === data.name)
      if (index !== -1) {
        this.copyInfoArr[index].value = [...data.value]
      } else {
        this.copyInfoArr.push(JSON.parse(JSON.stringify(data)))
      }
      if (this.copyInfoArr?.length > 2) {
        this.copyInfoArr.shift()
      }
    },
    findCopyInfoByName(name: string) {
      return this.copyInfoArr?.find(item => item.name === name)?.value || []
    },
    deleteCopyeInfoByName(name: string) {
      this.copyInfoArr = this.copyInfoArr?.filter(item => item.name !== name) || []
    },
  },
  persist: [
    {
      paths: [
        'provinceAllMap',
        'copyRestrictData',
        'pageSize',
        'lineNumber',
        'masterAccountGroups',
        'copyInfoArr',
        'masterAccountGroupIds',
      ],
      storage: localStorage,
    },
    {
      paths: [
        'loading',
        'loadingModule',
        'copyAllRestrictData',
        'primaryIndustryList',
        'allIndustryList',
        'copyCellList',
        'copyCallSeatIds',
        'copyCallTeamId',
      ],
      storage: sessionStorage,
    },
    // {
    //   paths: ['initStatus',],
    //   storage: sessionStorage,
    // },
  ]
});
