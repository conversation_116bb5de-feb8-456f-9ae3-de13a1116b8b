import { CallStatusEnum, } from '@/type/task'
import { FollowUpStatusEnum, ExamineStatusEnum, ClueCallStatusEnum } from '@/type/clue'
import dayjs from 'dayjs'

export const nameReg = /^[\u4e00-\u9fa5]{2,20}$/
export const phoneReg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
export const emailReg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
export const passwordReg =  /^(?=.*\d)(?=.*[a-z]).{6,16}$/
export const passwordReg2 =  /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_.!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\\W_.!@#$%^&*`~()-+=]+$)(?![0-9\\W_.!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\\W_.!@#$%^&*`~()-+=]{8,30}$/
export const ipReg = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
/**
 * ^(http|ftp|https):\/\/                      # 协议
 * ([a-zA-Z0-9\.-_]+)                    # 域名
 * (:[0-9]{1,5})?                       # 端口号（可选）
 * (\/[a-zA-Z0-9\._\-~\/]*)?            # 路径（可选）
 *(\?([a-zA-Z0-9\-_=&]+)*)?            # 查询参数（可选）
 *(#.*)?$                              # 片段标识符（可选）
 */
export const urlReg = /^(http|ftp|https):\/\/([a-zA-Z0-9\._\-]+)(:[0-9]{1,5})?(\/[a-zA-Z0-9\._\-~\/]*)?(\?([a-zA-Z0-9\-_=&]+)*)?(#.*)?$/
export const standardLens = {
  table: [36, 80, 120, 160, 240],
  dialog: [620, 720, 960],
  form: [80, 160, 240, 320],
}

// 支持筛选的时间范围，近一个月
export const disabledForRecent30Days = (date: Date) => {
  return dayjs(date).isAfter(dayjs().endOf('day')) || dayjs(date).isBefore(dayjs().subtract(30, 'days').endOf('day'))
}

export const shortcuts = [
  {
    text: '明天',
    value: dayjs().add(1, 'd').endOf('d').format('YYYY-MM-DD'),
  },
  {
    text: '7天',
    value: dayjs().add(7, 'd').endOf('d').format('YYYY-MM-DD'),
  },
  {
    text: '30天',
    value: dayjs().add(30, 'd').endOf('d').format('YYYY-MM-DD'),
  },
  {
    text: '1年',
    value: dayjs().add(1, 'y').endOf('d').format('YYYY-MM-DD'),
  },
]
