<template>
  <el-dialog
    v-model="dialogVisible"
    width="480px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">加入任务</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        class="tw-px-[12px]"
        :rules="rules"
        label-width="80px"
        ref="editRef"
      >
        <el-form-item label="选择任务：" prop="taskId">
          <el-select v-model="editData.taskId" class="tw-flex-grow" placeholder="请选择任务" @change="handleTaskSelect" filterable :filter-method="(val: string) => updateTaskList(val)">
            <el-option v-for="item in taskList" :key="item.id" :label="item.taskName" :value="item.id" />
          </el-select>
        </el-form-item>
        <div v-if="currentTask.id" class="tw-p-[8px] tw-rounded-[4px] tw-flex tw-flex-col tw-items-start tw-bg-[#f5f7fa]">
            <!-- <div class="title" >
              <span class="name tw-line-clamp-2">{{ currentTask.taskName || '-' }}</span>
              <span class="status-box" :class="filterStatusClass(currentTask.callStatus as TaskStatusEnum)">{{ currentTask.callStatus }}</span>
            </div> -->
          <div class="task-content">
            <span class="info-content">任务名称：</span>
            <span class="info-title-deep tw-font-[600]">{{ currentTask.taskName || '-' }}</span>
          </div>
          <div class="task-content">
            <span class="info-content">任务类型：</span>
            <span class="info-title-deep">{{ currentTask.taskType === TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务' }}</span>
          </div>
          <div class="task-content">
            <span class="info-content">任务状态：</span>
            <span class="info-title-deep">{{ currentTask.callStatus || '-' }}</span>
          </div>
          <div class="task-content">
            <span class="info-content">执行话术：</span>
            <span class="info-title-deep">{{ currentTask.speechCraftName || '-' }}</span>
          </div>
            <div class="task-content">
              <span class="info-content">创建时间：</span>
              <span class="info-title-deep">{{ dayjs(currentTask.createTime).format('YYYY-MM-DD HH:mm:ss') || '-' }}</span>
            </div>
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onDeactivated, } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { clueManagerModel } from '@/api/clue'
import { ElMessage, } from 'element-plus'
import { TaskManageItem, TaskManageOrigin, TaskTypeEnum, } from '@/type/task'
import dayjs from 'dayjs'
import { onBeforeRouteLeave } from 'vue-router'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import to from 'await-to-js';
import { aiOutboundTaskModel } from '@/api/ai-report'
import { trace } from '@/utils/trace'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean,
  list: number[],
}>();

const globalStore = useGlobalStore()
const  {loading} = storeToRefs(globalStore)

const editData = reactive<{
    taskId?: number,
    clueIds: number[]
}>({
  clueIds: props.list || [],
  taskId: undefined,
})
const taskList = ref<{id: number, taskName: string}[] | null>([]) // 可选择的任务列表，过滤出人机协同任务
const currentTask = reactive<TaskManageItem>(new TaskManageOrigin(TaskTypeEnum['人机协同']))
const handleTaskSelect = async () => {
  if (!editData.taskId) return
  const [_ , row] = await to(aiOutboundTaskModel.findTaskByIds(editData.taskId))
  row && row[0] && Object.assign(currentTask, row[0])
}
const dialogVisible = ref(props.visible)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const editRef = ref<FormInstance  | null>(null)
const rules = {
  taskId: [
    { required: true, message: '请选择任务', trigger: 'change' },
  ],
}
const confirm = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = {
        taskId: editData.taskId!,
        clueIds: editData.clueIds
      }
      const [err] = await to(clueManagerModel.joinToTaskClues(params))
      trace({
        page: '线索管理-加入任务',
        params
      })
      loading.value = false
      if (!err) {
        emits('confirm')
        cancel()
        ElMessage({
          type: 'success',
          message: '加入成功！'
        })
      }
    }
  })
}
const init = async () => {
  updateTaskList()
}
const updateTaskList = async (val: string = '') => {
  const data = await aiOutboundTaskModel.findTaskList({
    startTime: dayjs().add(-1, 'month').format('YYYY-MM-DD HH:mm:ss'),
    taskType: TaskTypeEnum['人机协同'],
    endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    taskName: val ? val : undefined,
    startPage: 0,
    pageNum: 100,
  })
  taskList.value = data?.data as {id: number, taskName: string}[] || []
}
watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
    Object.assign(currentTask, new TaskManageOrigin(TaskTypeEnum['人机协同']))
    editData.clueIds = props.list || []
    editData.taskId = undefined
    editRef.value && editRef.value.clearValidate()
  }
})

onDeactivated(() => {
  editRef.value = null
  taskList.value = null
})
onBeforeRouteLeave(() => {
  editRef.value = null
  taskList.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.task-content {
  display: flex;
  align-items: center;
  height: 28px;
  line-height: 28px;
  width: 100%;
  overflow: hidden;
}
</style>
