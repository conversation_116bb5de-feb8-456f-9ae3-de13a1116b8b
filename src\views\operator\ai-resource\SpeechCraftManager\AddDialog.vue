<template>
  <el-dialog
    v-model="dialogVisible"
    width="660px"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ addData.id ? '编辑话术' : '新增话术' }}</div>
    </template>
    <el-form
      :model="addData"
      :rules="rules"
      label-width="77px"
      ref="addFormRef"
    >
      <el-form-item label="话术名称：" prop="scriptName">
        <div class="tw-flex tw-items-center tw-gap-x-1 tw-w-full">
          <el-input
            v-model="addData.scriptName"
            type="text"
            class="tw-grow"
            clearable
            input-style="font-size:12px"
            maxlength="40"
            show-word-limit
            placeholder="请输入话术的名称,40汉字以内"
          />
          <el-button type="primary" link @click="showFormatBox">
            {{ formatBoxVisible ? '隐藏' : '规范命名' }}
          </el-button>
        </div>
      </el-form-item>
      <div v-if="formatBoxVisible" class="tw-bg-[#eee] tw-p-[12px] tw-rounded-[4px] tw-mb-[12px]">
        <div class="tw-font-[600] tw-text-[13px] tw-text-left tw-mb-[6px]">
          规范命名
        </div>
        <!-- 规范命名：输入区域 -->
        <div class="tw-grid tw-grid-cols-3 tw-gap-y-[6px] tw-gap-x-[4px] tw-w-full">
          <!-- 第一行 -->
          <el-input 
            v-model.trim="formatBoxData.applicationCode" 
            placeholder="甲方代码，例如：DT"
            maxlength="3"
            clearable
          />
          <el-input 
            v-model.trim="formatBoxData.productName" 
            placeholder="产品名称，例如：众安贷"
            maxlength="6"
            clearable
          />
          <el-input 
            v-model.trim="formatBoxData.pathName" 
            placeholder="转化路径，例如：挂短"
            maxlength="6"
            clearable
          />

          <!-- 第二行 -->
          <el-input 
            v-model.trim="formatBoxData.entryName" 
            placeholder="入口名称，例如：xx号"
            maxlength="6"
            clearable
          />
          <el-select 
            v-model="formatBoxData.outboundType" 
            placeholder="外呼类型" 
            class="full-width"
          >
            <el-option label="AI" value="AI" />
            <el-option label="人机" value="人机" />
            <el-option label="呼入" value="呼入" />
          </el-select>
          <el-select 
            v-model="formatBoxData.businessType" 
            placeholder="业务类型" 
            class="full-width"
          >
            <el-option label="拉新" value="拉新" />
            <el-option label="促活" value="促活" />
            <el-option label="其他" value="其他" />
          </el-select>

          <!-- 第三行 -->
          <el-input 
            v-model.trim="formatBoxData.scriptEditor" 
            placeholder="话术师代码，例如：C"
            maxlength="2"
            clearable
          />
          <el-input 
            v-model.trim="formatBoxData.version" 
            placeholder="版本号，例如：1.2"
            maxlength="5"
            clearable
          />
          <el-input 
            v-model.trim="formatBoxData.voiceRecorder" 
            placeholder="录音师代码，例如：yy"
            maxlength="2"
            clearable
          />

          <!-- 第四行 -->
          <el-input
            class="tw-col-span-3"
            v-model.trim="formatBoxData.extraWords" 
            placeholder="请输入额外字段（选填）" 
            clearable
            maxlength="6"
            show-word-limit
          />
        </div>

        <!-- 按钮区域 -->
        <div class="tw-flex tw-items-center tw-justify-between tw-mt-1">
          <div class="tw-text-left tw-ml-[4px]">
            <span class="info-content">当前命名：</span>
            <span class="info-title">{{ formatedScriptName || '' }}</span>
          </div>
          <div>
            <el-button type="primary" link @click="fillFormatedScriptName">填入</el-button>
            <el-button type="danger" link @click="clearFormatBox">清空</el-button>
          </div>
        </div>
      </div>
      <el-form-item label="所属行业：" prop="secondaryIndustryId">
        <div class="tw-grid tw-grid-cols-2 tw-gap-x-1 tw-w-full">
          <el-select
            v-model="addData.primaryIndustry"
            placeholder="一级行业"
            @change="handleChangePrimary"
          >
            <el-option
              v-for="item in primaryIndustryList"
              :key="item.id"
              :label="item.primaryIndustry"
              :value="item.primaryIndustry"
            />
          </el-select>
          <el-select v-model="addData.secondaryIndustryId" placeholder="二级行业" @change="handleChangeSecondary">
            <el-option
              v-for="item in secondaryIndustryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="交互版本：" prop="multiContentVersion">
        <el-switch
          v-model="addData.multiContentVersion"
          :disabled="props.editData && props.editData?.id && !!props.editData?.multiContentVersion"
          inline-prompt
          active-text="v2.0"
          inactive-text="v1.0"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, watchEffect, } from 'vue'
import { SpeechCraftInfoItem, SpeechCraftStatusEnum } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { scriptTableModel } from '@/api/speech-craft'
import type { FormInstance, FormRules } from 'element-plus'
import { useGlobalStore } from '@/store/globalInfo'
import { IndustryItem } from '@/type/industry'
import Confirm from '@/components/message-box'
import { useScriptStore } from '@/store/script'
import to from 'await-to-js';
import { trace } from '@/utils/trace';
import { SpeechCraftNameFormatOrigin, SpeechCraftNameFormat } from './constant'

const globalStore = useGlobalStore()
const scriptStore = useScriptStore()
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  editData?: SpeechCraftInfoItem | null;
}>();
const loading = ref(false)
const dialogVisible = ref(false)
class SpeechCraftInfoOrigin {
  scriptName = ''
  status = SpeechCraftStatusEnum['编辑中']
  multiContentVersion = true // 新增默认是新版本
  head_corpus_id = null
  isDeleted = false
  primaryIndustry = undefined
  secondaryIndustry = undefined
  secondaryIndustryId = undefined
}
const addData = reactive<SpeechCraftInfoItem>(new SpeechCraftInfoOrigin())
const addFormRef = ref<FormInstance | null>(null)
const rules = {
  scriptName: [
    { required: true, message: '请输入话术的名称', trigger: 'blur' },
    { min: 1, max: 40, message: '话术名称长度必须在1-40个字符', trigger: 'blur' }
  ],
  secondaryIndustryId: [
    { required: true, message: '请选择所属行业', trigger: 'change' },
  ],
  multiContentVersion: [
    { required: true, message: '请选择交互版本', trigger: 'change' },
  ]
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = () => {  
  addFormRef.value && addFormRef.value.validate((valid) => {
    if (valid) {
      if (addData.id && scriptStore.secondIndustryId !== addData.secondaryIndustryId) {
        Confirm({
          text: `您确定要修改话术的二次行业吗，修改后该话术中所配置核心语义都将被清空?`,
          type: 'warning',
          title: '修改确认',
        }).then(() => {
          addAction()
        }).catch(() => {})
      } else {
        addAction()
      }
    }
  })
}
const addAction = async () => {
  loading.value = true
  await trace({ page: addData.id ? `话术编辑-编辑话术(${addData.id})` : '话术制作-新增话术', params: addData })
  const [err, data] = await to(addData.id ? scriptTableModel.updateOneScript(addData) : scriptTableModel.createOneScript(addData))
  if (data && data.id) {
    ElMessage.success('操作成功')
    emits('confirm', data)
    cancel()
  }
  loading.value = false
}
const primaryIndustryList = ref<IndustryItem[]>([])
const secondaryIndustryList = computed(() => {
  return addData.primaryIndustry ? primaryIndustryList.value.find(item => item.primaryIndustry === addData.primaryIndustry)?.secondaryIndustries || [] : []
})

const init = async () => {
  if ( globalStore.allIndustryList && globalStore.allIndustryList.length > 0) {
    primaryIndustryList.value = globalStore.allIndustryList
  } else {
    await globalStore.getAllIndustryList()
    primaryIndustryList.value =  globalStore.allIndustryList || []
  }
}
const handleChangePrimary = () => {
  addData.secondaryIndustry = secondaryIndustryList.value[0].name || undefined
  addData.secondaryIndustryId = secondaryIndustryList.value[0].id || undefined
}
const handleChangeSecondary = () => {
  addData.secondaryIndustry = secondaryIndustryList.value.find(item => item.id === addData.secondaryIndustryId)?.name || undefined
}

const formatBoxVisible = ref(false)
const formatBoxData = reactive<SpeechCraftNameFormat>(new SpeechCraftNameFormatOrigin())
const showFormatBox = () => {
  formatBoxVisible.value = !formatBoxVisible.value
}
const clearFormatBox = () => {
  Object.assign(formatBoxData, new SpeechCraftNameFormatOrigin())
}
const formatedScriptName = ref('')
watchEffect(() => {
  formatedScriptName.value = `${formatBoxData.applicationCode}${formatBoxData.productName}${formatBoxData.pathName}-${formatBoxData.entryName}${formatBoxData.outboundType}${formatBoxData.businessType}-${formatBoxData.scriptEditor}${formatBoxData.version}${formatBoxData.voiceRecorder}${formatBoxData.extraWords ? `#${formatBoxData.extraWords}` : ''}`
})
const fillFormatedScriptName = () => {
  if (
    !formatBoxData.applicationCode ||
    !formatBoxData.productName ||
    !formatBoxData.pathName ||
    !formatBoxData.entryName ||
    !formatBoxData.scriptEditor ||
    !formatBoxData.version ||
    !formatBoxData.voiceRecorder
  ) {
    ElMessage.warning('请输入正确的规范命名，除了额外字段均必填')
    return
  }
  if (formatedScriptName.value.length > 40) {
    ElMessage.warning('当前命名长度超过40个字符，请重新输入')
    return
  }
  formatBoxVisible.value = false
  addData.scriptName = formatedScriptName.value || ''
}

watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    init()
    if (props.editData) {
      Object.assign(addData, props.editData)
      addData.multiContentVersion = props.editData.multiContentVersion || false // 编辑时默认是旧版本
    } else {
      Object.assign(addData, new SpeechCraftInfoOrigin())
    }
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
</style>