<template>
  <div class="tw-w-full tw-text-[13px] tw-flex tw-gap-x-[6px] tw-items-center">
    <!-- 选项类型 -->
    <el-select
      class="tw-mr-[4px] tw-shrink-0 tw-grow-0 tw-w-[180px] tw-self-start"
      v-model="editData.semListPackType"
      filterable
      @change="handleTypeChange"
    >
      <el-option
        v-for="item in enum2Options(CorpusConditionEnum)"
        :key="item.value"
        :label="item.name"
        :value="item.value"
      ></el-option>
    </el-select>
    <!-- 语义 -->
    <SelectBox 
      v-if="editData.semListPackType !== CorpusConditionEnum['本句短语命中']"
      v-model:selectVal="semanticIds"
      :options="options"
      :canCopy="!props.readonly"
      :canSelectAll="!props.readonly"
      copyName="semanticIds"
      :name="'name'"
      :val="'id'"
      suffix="suffix"
      placeholder="请选择核心语义"
      filterPlaceholder="请输入核心语义/语义标签"
      class="tw-grow"
      @update:select-val="handleMultValueChange"
      multiple filterable
    >
    </SelectBox>
    <!--补充短域 -->
    <InputListBox
      v-if="editData.semListPackType === CorpusConditionEnum['本句短语命中']"
      v-model:value="extraPhrases"
      placeholder="请输入补充短语，回车确认"
      :disabled="props.readonly"
      clearable
      canCopy
      copyName="extraPhrases"
      @update:value="handleMultValueChange"
    />
    <!-- 命中最小、最大数量 -->
    <template v-if="editData.semListPackType === CorpusConditionEnum['历史语义命中']">
      <InputNumberBox
        v-model:value="editData.minNum"
        placeholder="最低"
        :min="0"
        :max="editData.maxNum || 10"
        style="width: 90px"
        append="次"
        @update:value="handleValueChange"
      />
      <span>&nbsp;至&nbsp;</span>
      <InputNumberBox
        v-model:value="editData.maxNum"
        placeholder="最高"
        :min="editData.minNum || 0"
        :max="10"
        style="width: 90px"
        append="次"
        @update:value="handleValueChange"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, } from 'vue';
import { CorpusConditionEnum, CorpusConditionSubItem } from '@/type/corpus'
import { enum2Options, } from '@/utils/utils'
import { useScriptStore } from '@/store/script'
import InputNumberBox from '@/components/InputNumberBox.vue'
import SelectBox from '@/components/SelectBox.vue'
import InputListBox from '@/components/InputListBox.vue'
type IType = {
  semListPackType: CorpusConditionEnum,
  singlePhraseList: CorpusConditionSubItem[],
  minNum?: number,
  maxNum?: number
}
// props & emits
const emits = defineEmits(['update:data',])
const props = defineProps<{
  data: IType; // 当前规则，无法使用v-model，请用update:data手动更新
  readonly?: boolean; // 是否只读
}>();

const editData = reactive<IType>({
  semListPackType: CorpusConditionEnum['本句语义命中'],
  singlePhraseList: [],
  minNum: undefined,
  maxNum: undefined
})
const extraPhrases = ref<string[]>([]) // 多选的数值
const semanticIds = ref<number[]>([]) // 多选的数值
const options = ref<null | {
  id: number,
  name: string,
  suffix?: string
}[]>([]) // 选项

/** 常量 */
const scriptStore = useScriptStore() // 话术信息
const handleMultValueChange = () => {
  if (editData.semListPackType === CorpusConditionEnum['本句短语命中']) {
    editData.singlePhraseList = extraPhrases.value.map(item => ({ word: item }))
  } else {
    editData.singlePhraseList = semanticIds.value.map(item => ({ id: item }))
  }
  handleValueChange()
}


// 数据变化，更新父组件数据
const handleValueChange = () => {
  if (editData.semListPackType !== CorpusConditionEnum['历史语义命中']) {
    editData.minNum = undefined
    editData.maxNum = undefined
  }
  emits('update:data', editData)
}

// 切换规则类型，需要更新选项信息、数据信息、外部数据等
const handleTypeChange = () => {
  editData.minNum = undefined
  editData.maxNum = undefined
  emits('update:data', editData)
}

/**
 * 初始化
 * 从ruleTypePropteryMap中获取该规则的配置，新增规则理论上只需要增加map对应选项，并更新组件入参的option即可
 * 详见ruleTypePropteryMap
 */
const init = () => {
  options.value = scriptStore.semanticOptions || []
  Object.assign(editData, props.data)
  if (editData.semListPackType === CorpusConditionEnum['本句短语命中']) {
    extraPhrases.value = editData.singlePhraseList?.map(item => item.word!) || []
  } else {
    semanticIds.value = editData.singlePhraseList?.map(item => item.id!) || []
  }
  
  handleValueChange()
}

init()

/** watch结束 */ 
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form .el-form-item:first-child {
  margin-top: 0;
}
.el-input-number .el-input__inner {
  text-align: left;
}
:deep(.el-form-item__label) {
  padding-right: 0;
}
:deep(.el-form-item__content) {
  font-size: var(--el-font-size-base);
}
</style>
