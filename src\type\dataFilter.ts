import type { UploadRawFile, } from 'element-plus'

export enum LimitTypeEnum {
  '外呼', '普短'
}

export enum ImportTypeEnum {
  '批量导入' = '0',
  '单个导入' = '1',
  '集群导入' = '2',
}

export enum BlackPersonTypeEnum {
  '易投诉人群' = '0',
  '低价值人群' = '1',
}

export enum BlackRestrictionTypeEnum {
  '全局限制' = 0,
  '行业限制' = 1,
  '产品限制' = 2,
}

export enum BlackListTypeEnum {
  '普通' = '0',
  '供应商' = '1',
}

// 白名单分组
export interface WhiteListGroupItem {
  id?: number,
  groupName?: string,
  phoneCount?: number,
  createTime?: string,
  createPerson?: string,
  updateTime?: string,
  updatePerson?: string,
  expirationTime?: string,
}

// 白名单、黑名单，具体导入的某个号码
export interface WhiteBlackListItem {
  id?: number,
  groupId?: number,
  groupName?: string,
  createTime?: string,
  updateTime?: string,
  expireDate?: string,
  phone?: string,
  province?: string,
  city?: string,
}

// 白名单、黑名单，导入记录
export interface WhiteBlackListRecordItem {
  id?: number,
  fileName?: string,
  groupId?: number,
  groupName?: string,
  importType: ImportTypeEnum,
  status?: string, // 是否已经撤销了,1未撤销,0已撤销
  expireDate?: string
  importCount?: number,
  importTime?: string,
  createTime?: string,
  updateTime?: string,
}

// 白名单、黑名单，导入记录创建时的传参
export interface WhiteBlackListImportItem extends WhiteBlackListRecordItem {
  uploadFiles?: UploadRawFile[]
}

// 黑名单分组
export interface BlackListGroupItem extends WhiteListGroupItem {
  groupType?: BlackListTypeEnum, // 黑名单类型
  targetType?: BlackPersonTypeEnum, // 人群类型
  targetLevel?: number, // 人群等级
  targetComment?: string, // 分组规则
  limitDuration?: number,
  putThroughComment?: string,
  benefitComment?: string,
  costBenefitComment?: string,
  comment?: string,
  groupStatus?: string, // '0'未删除，'1'已删除
}

export interface BlackSearchForm {
  phone?: string,
  status?: string,
  expirationTime?: string,
  limitType?: LimitTypeEnum,
  limitIndustry?: string,
}

export interface BlackListItem {
  id?: number,
  phone?: string,
  createTime?: string,
  createPerson?: string,
  updateTime?: string,
  updatePerson?: string,
  status?: string,
  expirationTime?: string,
  limitType?: LimitTypeEnum,
  limitIndustry: string,
  remark?: string,
}

// 黑名单限制（全局、产品、行业共用）
export interface BlackRestrictionItem {
  id?: number,
  groupType: BlackRestrictionTypeEnum,
  productIndustryId?: number,
  productIndustryName?: string,
  blackListGroupList?: {
    groupId: number,
    groupName: string,
  }[],
}

// 频率限制类型
export enum FrequencyRestrictionTypeEnum {
  GLOBAL = '全局限制',
  INDUSTRY = '行业限制',
  PRODUCT = '产品限制',
}

// 频率限制，拨打限制
export interface CallLimit {
  callLimit?: number | null,
  callLimitTime?: number | null,
}

// 频率限制，拨通限制
export interface PutThroughLimit {
  putThroughLimit?: number | null,
  putThroughLimitTime?: number | null,
}

// 频率限制
export interface FrequencyRestrictionInfo {
  id?: number | null,
  createTime?: string | null,
  updateTime?: string | null,
  frequentType?: string | null,
  productIndustryId?: string | null,
  productIndustryName?: string | null,
  callLimit?: CallLimit[],
  putThroughLimit?: PutThroughLimit[],
}

// 频率限制，请求参数
export interface FrequencyRestrictionParam {
  industryId?: string | null,
  productId?: string | null,
}

// 靓号限制
export interface GoodNumberRestrictionInfo {
  id?: number | null,
  phoneRegex?: string | null,
  rank?: string | null,
  deleted?: boolean | null,
  createTime?: string | null,
  updateTime?: string | null,
  regexName?: string | null
  comment?: string | null
}

// 靓号限制，测试靓号规则，请求参数
export interface GoodNumberRestrictionTestParam {
  phone: string,
}

// 靓号限制，查询绑定商户，请求参数
export interface GoodNumberRestrictionBindMerchantParam {
  id: number,
}

// 违禁词
export interface ForbiddenWordItem {
  id?: number
  // 是——正则，否——字符串
  isRegex?: boolean
  // 违禁词
  forbiddenWord?: string
  // 更新人
  updateBy?: string
  // 备注
  remarks?: string
  // 创建时间
  createTime?: string
  // 更新时间
  updateTime?: string
}

// 违禁词，接口参数
export interface ForbiddenWordParam {
  word?: string
  id?: number
  forbiddenWord?: string
  remarks?: string
  isRegex?: boolean
}

// 违禁词，标签卡类型枚举
export enum ForbiddenWordTabEnum {
  VARIABLE = '变量违禁词',
  URL = '短链违禁词'
}

// 违禁词，标签卡类型列表
export const ForbiddenWordTabList = [
  ForbiddenWordTabEnum.VARIABLE,
  ForbiddenWordTabEnum.URL,
]
