import type { TableColumnCtx } from 'element-plus'

export interface SummaryMethodProps<T> {
  columns: TableColumnCtx<T>[]
  data: T[]
}

export interface ColumnsItem {
  property: string,
  label: string,
}

export type MonitorChartItem = {
  name: string;
  xName?: string
  value1: number;
  value2: number;
  value3?: number;
}

export interface TimeItem {
  startTime: string
  endTime: string
  isSelected?: boolean
}

export interface Tree {
  id: string
  // name: string
  children?: Tree[]
}

export interface PortItem {
  id: string,
  name?: string,
  group: string,
  attrs?: {
    [key: string]: any
  }
}

export interface EdgeItem {
  id: string
  shape: string,
  source: {
    cell: string
    port: string
  },
  target: {
    cell: string
    port: string
  },
  zIndex?: number,
  // data?: {
  //   color: number,
  // },
  attrs: object
}

export const ZhMap = [
  { en: 'a', min: '阿', max: '八' },
  { en: 'b', min: '八', max: '嚓' },
  { en: 'c', min: '嚓', max: '哒' },
  { en: 'd', min: '哒', max: '妸' },
  { en: 'e', min: '妸', max: '发' },
  { en: 'f', min: '发', max: '旮' },
  { en: 'g', min: '旮', max: '哈' },
  { en: 'h', min: '哈', max: '讥' },
  // 'i': { en: 'a',min: '讥', max: '八' },
  { en: 'j', min: '讥', max: '咔' },
  { en: 'k', min: '咔', max: '垃' },
  { en: 'l', min: '垃', max: '痳' },
  { en: 'm', min: '痳', max: '拏' },
  { en: 'n', min: '拏', max: '噢' },
  { en: 'o', min: '噢', max: '妑' },
  { en: 'p', min: '妑', max: '七' },
  { en: 'q', min: '七', max: '呥' },
  { en: 'r', min: '呥', max: '扨' },
  { en: 's', min: '扨', max: '它' },
  { en: 't', min: '它', max: '穵' },
  { en: 'w', min: '穵', max: '夕' },
  { en: 'x', min: '夕', max: '丫' },
  { en: 'y', min: '丫', max: '帀' },
  { en: 'z', min: '帀', max: null },
]

export enum OperatorEnum {
  '移动' = '移动',
  '联通' = '联通',
  '电信' = '电信',
  // '虚拟' = '虚拟',
  '未知' = '未知',
}

export const OperatorObj = {
  '全部': {
    province: 'allRestrictProvince',
    city: 'allRestrictCity',
    name: '全部'
  },
  [OperatorEnum['移动']]: {
    province: 'ydRestrictProvince',
    city: 'ydRestrictCity',
    name: OperatorEnum['移动']
  },
  [OperatorEnum['联通']]: {
    province: 'ltRestrictProvince',
    city: 'ltRestrictCity',
    name: OperatorEnum['联通']
  },
  [OperatorEnum['电信']]: {
    province: 'dxRestrictProvince',
    city: 'dxRestrictCity',
    name: OperatorEnum['电信']
  },
  // [OperatorEnum['虚拟']]: {
  //   province: 'virtualRestrictProvince',
  //   city: 'virtualRestrictCity',
  //   name: OperatorEnum['虚拟']
  // },
  [OperatorEnum['未知']]: {
    province: 'unknownRestrictProvince',
    city: 'unknownRestrictCity',
    name: OperatorEnum['未知']
  },
}

export interface RestrictModal {
  allRestrictProvince?: string | null
  allRestrictCity?: string | null
  ydRestrictProvince?: string | null
  ydRestrictCity?: string | null
  ltRestrictProvince?: string | null
  ltRestrictCity?: string | null
  dxRestrictCity?: string | null
  dxRestrictProvince?: string | null
  virtualRestrictCity?: string | null
  virtualRestrictProvince?: string | null
  unknownRestrictCity?: string | null
  unknownRestrictProvince?: string | null
}
export class RestrictModalOrigin {
  allRestrictProvince = null
  allRestrictCity = null
  ydRestrictProvince = null
  ydRestrictCity = null
  ltRestrictProvince = null
  ltRestrictCity = null
  dxRestrictCity = null
  dxRestrictProvince = null
  virtualRestrictCity = null
  virtualRestrictProvince = null
  unknownRestrictCity = null
  unknownRestrictProvince = null
}
export interface RestrictCopyModal extends RestrictModal {
  time: string,
}

export enum IntentionClassEnum {
  'A' = 'A',
  'B' = 'B',
  'C' = 'C',
  'D' = 'D',
  'E' = 'E',
  'F' = 'F',
  'G' = 'G',
  '其他' = '其他',
}
