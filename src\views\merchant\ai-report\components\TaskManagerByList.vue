<template>
  <!--列表模式任务管理-->
  <div class="tw-flex tw-w-full tw-justify-end tw-items-center tw-p-[12px]">
    <CardListRadioBox v-model:active="listType" class="tw-ml-[8px]" @update:active="handleListTypeChange"/>
  </div>
  <div class="tw-bg-white tw-flex tw-grow tw-flex-col tw-mx-[16px] tw-mb-[12px] tw-shrink tw-overflow-hidden tw-min-w-[1080px]">
    <div class="tw-px-[12px] tw-mt-[12px] tw-mb-[8px]">
      <div class="tw-grid tw-grid-cols-6 tw-gap-[10px] tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model="searchForm.taskName"
            clearable
            placeholder="任务名称"
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.speechCraftName"
            clearable
            placeholder="话术名称"
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <SelectBox
            v-model:selectVal="searchForm.lineCodes"
            :options="lineList"
            name="lineName"
            val="lineNumber"
            placeholder="外呼线路"
            filterable
            can-select-all
            class="tw-w-full"
            multiple
            @blur="search()"
          />
        </div>
        <div class="item">
          <el-select v-model="searchForm.callStatus" class="tw-w-full" placeholder="任务状态" clearable @change="search()">
            <el-option v-for="item in callStatusList" :key="item.value" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div class="item tw-col-span-2">
          <TimePickerBox
            v-model:start="searchForm.startTime"
            v-model:end="searchForm.endTime"
            :maxRange="60*60*24*7*1000"
            style="width: 100%;"
            type="datetimerange"
            :clearable="false"
            separator="-"
            @change="search()"
          />
        </div>
      </div>
      <div class="tw-h-[32px]">
        <div class="tw-float-right tw-leading-[32px]">
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>
    <TaskTable
      v-loading="loading"
      class="tw-grow tw-shrink"
      :tableData="taskList || []"
      :taskType="props.taskType"
      :groupId="userStore.groupId"
      showSelectTask
      showTotal
      showPagination
      @show-block="showBlock"
      @update:table="search()"
    >
      <template v-slot:btn="{rows}">
        <el-button v-if="createPermission" type="primary" @click="createTask()">创建任务</el-button>
        <el-button v-if="startPermission" type="danger" @click="batchStopTask(rows)">批量停止</el-button>
      </template>
    </TaskTable>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, defineAsyncComponent, onUnmounted, watch } from 'vue'
import { TaskManageModel, TaskManageItem, TaskStatusEnum, TaskTypeEnum, } from '@/type/task'
import dayjs from 'dayjs'
import { enum2Options, findValueInEnum } from '@/utils/utils'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import TimePickerBox from '@/components/TimePickerBox.vue'
import to from 'await-to-js'
import { aiOutboundTaskModel } from '@/api/ai-report'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import CardListRadioBox from '@/components/CardListRadioBox.vue'
import SvgIcon from '@/components/SvgIcon.vue';
import { MerchantLineInfo, MerchantLineEnableStatusEnum } from '@/type/merchant'
import { merchantModel } from '@/api/merchant'
import Confirm from '@/components/message-box'
import { ElMessage } from 'element-plus'
import { trace } from '@/utils/trace';
import SelectBox from '@/components/SelectBox.vue'

const props = defineProps<{
  taskType: TaskTypeEnum,
  needRefresh: boolean,
  type: 'card'|'list'
}>();

const emits = defineEmits(['update:type', 'create-task', 'show-block', 'update:needRefresh'])

const taskTypeStr = computed(() => {
  return props.taskType === TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务'
})


// 组件性能消耗较大，动态引入
const TaskTable = defineAsyncComponent({ loader:() => { return import('@/components/task/TaskTable.vue')}})

// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap[taskTypeStr.value].id]
const startPermission =  computed(() => permissions.includes(routeMap[taskTypeStr.value].permissions['启停任务']))
const createPermission =  computed(() => permissions.includes(routeMap[taskTypeStr.value].permissions['创建任务']))

const globalStore = useGlobalStore()
const loading = ref(false)
const taskStore = useTaskStore()
const callStatusList = enum2Options(TaskStatusEnum)

// 左侧任务搜索栏
class SearchFormOrigin {
  taskName = ''
  lineCodes = undefined
  callStatus = undefined
  taskType = props.taskType
  speechCraftName = ''
  startTime = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  endTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
}

const searchForm = reactive<TaskManageModel>(new SearchFormOrigin())
const listType = ref<'card'|'list'>(props.type) // 默认卡片形式
const taskList = ref<TaskManageItem[] | null>(null)

const handleListTypeChange = (val: 'card'|'list') => {
  emits('update:type', val)
}

const createTask = () => {
  emits('create-task')
}

const showBlock = (currentTask: TaskManageItem) => {
  emits('show-block', currentTask, true)
}

const batchStopTask = (rows: TaskManageItem[] | null) => {
  if (!rows || !rows.length) return ElMessage.warning('请选择需要操作的任务')
  Confirm({
    text: `<p>您确定要批量停止【${rows?.length || 0}个】任务嘛?</p>`,
    type: 'warning',
    title: `批量停止确认`,
    confirmText: `停止`
  }).then(async () => {
    const params = {
      taskIds: rows.flatMap(item => (item.id && item.id > 0) ? [item.id] : [])?.join(',') || '',
      callStatus: TaskStatusEnum['已停止'],
    }
    trace({
      page:`${findValueInEnum(props.taskType, TaskTypeEnum)}任务-批量停止`,
      params,
    })
    const [err, res] = await to(props.taskType === TaskTypeEnum['人机协同']
      ? aiOutboundTaskModel.batchStopMixTask(params)
      : aiOutboundTaskModel.batchStopAiTask(params)
    )
    !err && ElMessage.success('操作成功')
  }).catch(() => {
  }).finally(() => {
    search()
  })
}

const clearSearchForm = () => {
  Object.assign(searchForm, new SearchFormOrigin())
}
const search = async (status?: TaskStatusEnum) => {
  loading.value = true
  if (status) {
    searchForm.callStatus = status
  }
  const [err, res] = await to(aiOutboundTaskModel.searchByAdditionCondition({
    ...searchForm,
    callStatus: searchForm.callStatus === '全部' ? undefined : searchForm.callStatus
  })) as [any, TaskManageItem[]]
  taskList.value = res?.sort((a, b) => {
    return dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1
  }) || []
  emits('update:needRefresh', false)
  loading.value = false
}


const lineList = ref<{lineName?: string, lineNumber?: string}[]>([]) // 可选的商户线路列表
const initData = async () => {
  loading.value = true
  const [err, res] = await to(merchantModel.getMerchantLineListByCondition({
    groupId: userStore.groupId || '',
    enableStatus: MerchantLineEnableStatusEnum['启用'],
    
  }))
  lineList.value = (res || []).filter(item => item.lineType === 'AI_OUTBOUND_CALL')
  search()
}
initData()
watch(() => props.needRefresh, n => {
  n && search()
})
onUnmounted(() => {
  taskList.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">

</style>
