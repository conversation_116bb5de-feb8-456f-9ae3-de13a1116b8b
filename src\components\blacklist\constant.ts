import dayjs from 'dayjs'
import { phoneReg } from '@/utils/constant'

const validateFiles = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    callback(new Error('请选择需要上传的文件!'))
  } else {
    if (value.length === 1 && value[0]!.size! > 1024 * 1024 * 80) {
      callback(new Error('文件大小不得超过80M!'))
    }
    const type = value[0].name?.split('.')?.at(-1)
    if (!type || !['csv', 'xls', 'xlsx'].includes(type)) {
      callback(new Error('文件类型必须为csv、xlsx、xls!'))
    }
  }
  callback()
}

const validatePhone = (rule: any, value: any, callback: any) => {
  // 兼容11位常规手机号和+68开头的特殊16位手机号
  if (!value.match(phoneReg) && !value.startsWith('+')) {
    callback(new Error('手机号不合规'))
  } else {
    callback()
  }
}

export const phoneRules = {
  phone: [
    { required: true, message: '请输入号码', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' },
  ],
  expireDate: [
    { required: true, message: '请选择过期时间', trigger: 'change' },
  ],
  uploadFiles: [
    { validator: validateFiles, trigger: ['change', 'blur'] },
  ]
}

export class BlackGroupOrigin {
  id = undefined
  groupName = ''
  limitDuration = 30
  comment =  ''
}

export class BlackPhoneOrigin {
  id = undefined
  phone = ''
  expirationTime = undefined
  expireDate = undefined
  groupId = undefined
  groupName = undefined
}
