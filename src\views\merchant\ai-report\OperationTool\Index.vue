<template>
  <HeaderBox title="外呼工具" />
  <div class="operation-tool-container">
    <div class="tw-flex tw-justify-end tw-mb-[8px] tw-grow-0">
      <el-button plain @click="handleExport(1)"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>导出任务</el-button>
      <el-button plain @click="handleExport(2)"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>导出意向</el-button>
      <input ref="fileRef" type="file" @change="handleFileChange" accept=".xls,.xlsx" class="batch-upload tw-hidden"/>
      <el-button @click="handleUpload(1)" class="tw-ml-1"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="upload"></SvgIcon></el-icon>上传文件</el-button>
      <el-button @click="handleUpload(2)" class="tw-ml-1"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="upload"></SvgIcon></el-icon>上传任务名</el-button>
      <el-button @click="importVisible = true" class="tw-ml-1"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="import"></SvgIcon></el-icon>导入任务</el-button>
    </div>
    <template v-if="taskTypeOptions?.length > 0">
      <TabsBox
        class="tw-w-full tw-text-[13px]"
        v-model:active="activeTaskType"
        :tabList="taskTypeOptions"
        @update:active="updateTable()"
      ></TabsBox>
      <div v-loading="loading" class="tw-h-[56px] tw-flex tw-justify-between tw-bg-white tw-px-[16px] tw-py-[12px]">
        <div class="info-title tw-self-end">{{speechCraftIndustryList[activeTaskType].length > 1 ? `任务存在${speechCraftIndustryList[activeTaskType].length}个行业，请谨慎操作！`: ' '}}</div>
        <div v-if="tableData && tableData.length">
          <el-button v-if="onStop" type="primary" @click="batchStartCall"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="start"></SvgIcon></el-icon>批量启动</el-button>
          <el-button v-if="onStart" @click="batchStop" type="danger"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="start"></SvgIcon></el-icon>批量停止</el-button>
          <el-dropdown class="tw-mx-0.5" @command="batchLock">
            <el-button>
              <el-icon :size="16" ><SvgIcon name="lock" color="inherit"></SvgIcon></el-icon>
              <span>批量锁定</span>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="lock">批量锁定</el-dropdown-item>
                <el-dropdown-item command="unlock">批量解锁</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button @click="batchEdit"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="batch-edit"></SvgIcon></el-icon>批量编辑</el-button>
          <!-- 针对生产环境火山账号，测试环境shanghu_shanghu_商户1 -->
          <el-button v-if="userStore.tenantId === 14"  @click="batchEditScript"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="batch-edit"></SvgIcon></el-icon>批量修改话术</el-button>
          <el-button @click="batchBlock"><el-icon :size="16" class="tw-mr-0.5" color="inherit"><SvgIcon name="batch-block" color="none"></SvgIcon></el-icon>批量屏蔽</el-button>
          <el-button @click="batchAddCall"><el-icon :size="16" class="tw-mr-0.5" color="--primary-black-color-400"><SvgIcon name="add1"></SvgIcon></el-icon>批量添加呼叫</el-button>
          <el-button v-if="!!userStore.isMasterAccount" @click="batchCancelCall"><el-icon :size="16" class="tw-mr-0.5" color="--primary-black-color-400"><SvgIcon name="cancel" color="inherit"></SvgIcon></el-icon>批量取消呼叫</el-button>
          <el-button @click="exportFail"><el-icon :size="16" class="tw-mr-0.5" color="--primary-black-color-400"><SvgIcon name="export-error"></SvgIcon></el-icon>导出失败</el-button>
        </div>
      </div>
      <TaskTable
        v-loading="loading"
        class="tw-grow tw-shrink"
        :tableData="tableData || []"
        :taskType="TaskTypeEnum[activeTaskType]"
        :groupId="userStore.groupId"
        showPagination
        showTotal
        showBatchStatus
        @show-block="handleScopeDetails"
      />
    </template>
    <el-empty v-else description="暂无数据" class="tw-m-auto tw-bg-white tw-w-full tw-grow"></el-empty>
  </div>
  <!-- 导出任务弹窗 -->
  <el-dialog
    v-model="exportVisible"
    width="480px"
    :close-on-click-modal="false"
    align-center
    @close="closeAllDialog"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">选择导出任务时间范围</div>
    </template>
    <el-form label-width="100px" class="tw-px-[12px] tw-w-full">
      <el-form-item label="加入时间：" prop="startTime">
        <TimePickerBox
          v-model:start="exportInfo.startTime"
          v-model:end="exportInfo.endTime"
          placeholder="加入时间"
          :maxRange="60*60*24*7*1000"
          format="YYYY-MM-DD HH:mm:ss"
          class="tw-w-full"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeAllDialog" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirmExport"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>导出</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 批量添加呼叫弹窗 -->
  <BatchAddCallDialog
    v-model:visible="batchAddCallVisible"
    :tableData="tableData || []"
    :taskType="TaskTypeEnum[activeTaskType]"
    @confirm="confirmBatchAdd"
    @confirmEnd="updateTable()"
    @export="export2Excel"
  ></BatchAddCallDialog>
  <!-- 批量启动任务弹窗 -->
  <CallTaskDialog
    v-model:visible="callVisible"
    :tableData="tableData || []"
    :taskType="TaskTypeEnum[activeTaskType]"
    @close="closeAllDialog"
    @confirm="confirmBatchStart"
    @export="export2Excel"
  ></CallTaskDialog>
  <!-- 批量编辑任务弹窗 -->
  <EditTaskDialog
    v-model:visible="editVisible"
    :dataRow="editTaskItem"
    type="batch"
    @confirm="confirmEditBatch"
  ></EditTaskDialog>
  <!-- 批量修改话术弹窗 -->
  <EditScriptDialog
    v-model:visible="editScriptVisible"
    :taskType="TaskTypeEnum[activeTaskType]"
    :taskIds="tableData?.map(item => item?.id).join(',') || ''"
    @confirm="confirmBatchEditScript"
  ></EditScriptDialog>
  <!-- 批量屏蔽城市弹窗 -->
  <el-dialog
    v-model="blockVisible"
    width="960px"
    align-center
    @close="cancelBlock"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">批量屏蔽</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-p-[12px]"
    >
      <div v-if="!!blocktype" class="tw-flex tw-mb-[12px] tw-items-center">
        <span class="tw-text-[13px] tw-w-[100px] tw-text-right">屏蔽方式：</span>
        <el-radio-group v-model="blocktype" class="tw-ml-[8px]">
          <el-radio :label="1">覆盖</el-radio>
          <el-radio :label="2">递增</el-radio>
          <el-radio :label="3">递减</el-radio>
        </el-radio-group>
      </div>
      <CitySettingBox
        :taskRestrictData="taskRestrictData"
        :selectedOperatorList="selectedOperatorList"
        :readonly="!blocktype"
        loadByCity
        @update:data="handleCityUpdate"
      />
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="blockVisible=false">取消</el-button>
        <el-button v-if="!!blocktype" :loading="loading" type="primary" @click="confirmBlock">确认</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 导入任务 -->
  <ImportTaskByConditionDialog
    v-model:visible="importVisible"
    @confirm="confirmImport"
  />
  <!-- 导入任务 -->
  <CancalCallDialog
    v-model:visible="batchCancelCallVisible"
    :taskIds="tableData?.map(item => item.id!) || []"
    @confirm="updateTable()"
  />
  <TaskCacheBox @loadTask="confirmImport" />
</template>

<script lang="ts" setup>
import { reactive, computed, ref, watch, defineAsyncComponent, onMounted, onUnmounted } from 'vue'
import { aiOutboundTaskModel } from '@/api/ai-report'
import dayjs from 'dayjs'
import { readXlsx, exportExcel } from "@/utils/export"
import { storeToRefs } from 'pinia'
import { TemplateBaseItem, TaskStatusEnum, SheetNameEnum, TaskBatchItem, TaskTypeEnum, TaskManageOrigin, TaskIntentionStatisticsItem } from '@/type/task'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import CallTaskDialog from './CallTaskDialog.vue'
import BatchAddCallDialog from './BatchAddCallDialog.vue'
import EditTaskDialog from '@/components/task/EditTaskDialog.vue'
import { pickAttrFromObj, enum2Options, } from '@/utils/utils'
import { CloseBold, } from '@element-plus/icons-vue'
import { ElMessage, } from 'element-plus'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { OperatorEnum, RestrictModal, RestrictModalOrigin } from '@/type/common'
import TabsBox from '@/components/TabsBox.vue'
import to from 'await-to-js'
import { useUserStore } from "@/store/user";
import { trace } from '@/utils/trace';
import EditScriptDialog from './EditScriptDialog.vue'
import ImportTaskByConditionDialog from './ImportTaskByConditionDialog.vue'
import TaskTable from '@/components/task/TaskTable.vue'
import CancalCallDialog from './CancalCallDialog.vue'
import TaskCacheBox from '@/components/task/TaskCacheBox.vue'

// 组件性能消耗较大，动态引入
const CitySettingBox = defineAsyncComponent({
  loader:() => {
    return import('@/components/CitySettingBox.vue')
  }
})
const globalStore = useGlobalStore()
const taskStore = useTaskStore()
const userStore = useUserStore();
const { batchTaskInfo } = storeToRefs(taskStore)

// 表格
const { loading } = storeToRefs(globalStore)
const onStart = computed(() => tableData.value && tableData.value?.findIndex(item => item.callStatus === TaskStatusEnum['进行中']) > -1) // true：列表存在进行中的任务
const onStop = computed(() => tableData.value && tableData.value?.findIndex(item => item.callStatus !== TaskStatusEnum['进行中']) > -1) // true：列表存在停止的任务
const hasAutoStop = ref(false) // true：列表存在止损的任务
const speechCraftMap = ref<Map<string, string>>(new Map()) // 话术map
const speechCraftIndustryList = reactive<Record<keyof typeof TaskTypeEnum, string[]>>({
  'AI外呼': [], '人机协同': []
}) // 列表中话术大版本个数
const taskTypeOptions = ref<string[]>([])
const activeTaskType = ref<keyof typeof TaskTypeEnum>(taskTypeOptions.value[0] as keyof typeof TaskTypeEnum || 'AI外呼' )

// 请求接口更新列表任务的数据：默认为使用后端状态，手动传则带更新为当前状态。
const updateTable = async (status: string | Map<number, string> = '', fromFile?: boolean) => {
  tableData.value = null
  if (!tableTotalData.value || tableTotalData.value.length < 1) {
    tableTotalData.value = []
    taskTypeOptions.value = []
    await trace({
      page: `外呼工具-实际导入为空`,
      params: '上传文件为空或不符合格式',
    })
    return ElMessage.warning('上传文件为空或不符合格式！')
  }
  loading.value = true
  // 根据tableTotalData的id获取任务最新数据
  const data = (await aiOutboundTaskModel.findTaskByIds(tableTotalData.value.map(item => item?.id).join(',')) as TaskBatchItem[] || [])
  const len1 = tableTotalData.value?.length || 0
  const len2 = data?.length || 0
  if (len2 < 1) {
    tableTotalData.value = []
    taskTypeOptions.value = []
    loading.value = false
    await trace({
      page: `外呼工具-实际导入为空`,
      params: '非本账号数据或任务已被删除',
    })
    return ElMessage.warning('上传文件为空或任务已被删除！')
  }
  if (len1 !== len2) {
    ElMessage({
      type: 'warning',
      message: '部分上传任务已被过滤！可能由于任务被删除或不属于该账户！'
    })
  }
  speechCraftIndustryList['AI外呼'] = []
  speechCraftIndustryList['人机协同'] = []
  tableTotalData.value = data.map(item => {
    item.batchStatus = typeof status === 'string' ? status || '等待执行' : status.get(item.id!) || '等待执行'
    if (!hasAutoStop.value && item.isAutoStop && +item.isAutoStop === 1) {
      hasAutoStop.value = true
    }
    item.secondaryIndustry = speechCraftMap.value.get(item.scriptStringId!) || ''
    if (item.taskType === TaskTypeEnum['人机协同'] ) {
      !speechCraftIndustryList['人机协同'].includes(item.secondaryIndustry) && speechCraftIndustryList['人机协同'].push(item.secondaryIndustry)
    } else {
      !speechCraftIndustryList['AI外呼'].includes(item.secondaryIndustry) && speechCraftIndustryList['AI外呼'].push(item.secondaryIndustry)
    }
    return item
  })
  // 记录到缓存，方便下次进入读取
  taskStore.batchTaskIds = tableTotalData.value.map(item => item.id!)
  // 更新列表的tab，获取每个tab（任务类型）的数量
  const loadNumMap: Record<any, { data: string, num: number }> = {}
  taskTypeOptions.value = enum2Options(TaskTypeEnum).flatMap(item => {
    const arr = tableTotalData.value?.flatMap(v => {
      if (v.taskType == (item.value as unknown as TaskTypeEnum)) {
        return [v.id]
      } else {
        return []
      }
    }) || []
    loadNumMap[item.value] = {
      data: arr.join(','),
      num: arr.length,
    }
    return arr.length > 0 ? [`${item.name}`] : []
  })
  activeTaskType.value = taskTypeOptions.value?.includes(activeTaskType.value) ? (activeTaskType.value || 'AI外呼') : (taskTypeOptions.value[0] as keyof typeof TaskTypeEnum || 'AI外呼')
  // 当是文件导入时，后端对任务进行处理，防止和ai-data覆盖
  if (fromFile) {
    const taskIds = tableTotalData.value.map(item => item?.id).join(',') || ''
    if (!taskIds) return (loading.value = false)
    const [err1] = await to(aiOutboundTaskModel.batchPreProcess({taskIds}))
    if (err1) {
      ElMessage.error('预处理失败，请务必检查异常任务，并重新导入，以免后续操作出现异常！')
      await trace({
        page: `外呼工具-上传文件后预处理-失败`,
        params: loadNumMap,
      })
    } else {
      await trace({
        page: `外呼工具-上传文件后预处理-${tableTotalData.value?.length}个任务`,
        params: loadNumMap,
      })
    }
  }
  await trace({
    page: `外呼工具-实际导入-${tableTotalData.value?.length}个任务`,
    params: loadNumMap,
  })
  loading.value = false
}

const updateTableByNames = async () => {
  if (!tableTotalData.value || tableTotalData.value.length < 1) {
    tableTotalData.value = []
    taskTypeOptions.value = []
    await trace({
      page: `外呼工具-实际导入为空`,
      params: '上传文件为空或不符合格式',
    })
    return
  }
  const taskNameList = tableTotalData.value.map(item => item.taskName)
  loading.value = true
  const data = (await aiOutboundTaskModel.search({
    startTime: dayjs().startOf('D').format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
  }) as TaskBatchItem[] || [])
  speechCraftIndustryList['AI外呼'] = []
  speechCraftIndustryList['人机协同'] = []
  tableTotalData.value = data?.flatMap(item => {
    const { autoReCall, callRatioType, firstRecallTime, secondRecallTime, startWorkTimeList, endWorkTimeList,} = item
    item.firstRecallTimeStr = firstRecallTime ? (firstRecallTime || '-') + '； ' + (secondRecallTime || '') : '-'
    item.callRatioTypeStr = autoReCall ? (callRatioType == 1 ? '首呼优先' : '多轮次呼叫按比例') : '-'
    item.autoReCallStr = autoReCall ? '是' : '否'
    item.isAutoStopStr = item.isAutoStop == 1 ? '是' : '否',
    item.timeStr = (startWorkTimeList && endWorkTimeList) ? startWorkTimeList.map((v, index) => {
      return v + '-' + endWorkTimeList![index]
    }).join(',') : '-'
    item.batchStatus = '等待执行'
    if (!hasAutoStop.value && item.isAutoStop && +item.isAutoStop === 1) {
      hasAutoStop.value = true
    }
    item.secondaryIndustry = speechCraftMap.value.get(item.scriptStringId!) || ''
    if (item.taskType === TaskTypeEnum['人机协同'] ) {
      !speechCraftIndustryList['人机协同'].includes(item.secondaryIndustry) && speechCraftIndustryList['人机协同'].push(item.secondaryIndustry)
    } else {
      !speechCraftIndustryList['AI外呼'].includes(item.secondaryIndustry) && speechCraftIndustryList['AI外呼'].push(item.secondaryIndustry)
    }
    return item.taskName && taskNameList.includes(item.taskName) ? [item] : []
  }) || []
  
  taskStore.batchTaskIds = tableTotalData.value.map(item => item.id!)
  const loadNumMap: Record<any, { data: string, num: number }> = {}
  taskTypeOptions.value = enum2Options(TaskTypeEnum).flatMap(item => {
    const arr = tableTotalData.value?.flatMap(v => {
      if (v.taskType == (item.value as unknown as TaskTypeEnum)) {
        return [v.id]
      } else {
        return []
      }
    }) || []
    loadNumMap[item.value] = {
      data: arr.join(','),
      num: arr.length,
    }
    return arr.length > 0 ? [`${item.name}`] : []
  })
  activeTaskType.value = taskTypeOptions.value?.includes(activeTaskType.value) ? (activeTaskType.value || 'AI外呼') : (taskTypeOptions.value[0] as keyof typeof TaskTypeEnum || 'AI外呼')
  // 上传文件，需要调用接口，防止ai-data更新覆盖
  const taskIds = tableTotalData.value.map(item => item?.id).join(',') || ''
  if (!taskIds) return (loading.value = false)
  const [err1] = await to(aiOutboundTaskModel.batchPreProcess({taskIds}))
  if (err1) {
    ElMessage.error('预处理失败，请务必检查异常任务，并重新导入，以免后续操作出现异常！')
    await trace({
      page: `外呼工具-上传文件后预处理-失败`,
      params: loadNumMap,
    })
  } else {
    await trace({
      page: `外呼工具-上传任务名后预处理-${tableTotalData.value?.length}个任务`,
      params: loadNumMap,
    })
  }
  await trace({
    page: `外呼工具-实际导入-${tableTotalData.value?.length}个任务`,
    params: loadNumMap,
  })
  loading.value = false
}
// 通过后端接口返回更新当前类型任务列表及状态
const updateTableByData = (data: TaskBatchItem[]) => {
  if (!data || !data.length) return
  const errorTaskNames: string[] = []
  speechCraftIndustryList[activeTaskType.value] = []
  tableTotalData.value = tableTotalData.value?.filter(item => item.taskType != TaskTypeEnum[activeTaskType.value]) || []
  data.forEach(item => {
    if (!hasAutoStop.value && item.isAutoStop && +item.isAutoStop === 1) {
      hasAutoStop.value = true
    }
    if (item.batchStatus != '执行成功') {
      errorTaskNames.push(`【${item.taskName}】` || '')
    }
    item.secondaryIndustry = speechCraftMap.value.get(item.scriptStringId!) || ''
    !speechCraftIndustryList[activeTaskType.value].includes(item.secondaryIndustry) && speechCraftIndustryList[activeTaskType.value].push(item.secondaryIndustry)
    tableTotalData.value?.push(item)
  })
  if (errorTaskNames.length > 0) {
    ElMessage.warning(errorTaskNames.join('') + '执行失败')
  } else {
    ElMessage.success('操作成功')
  }
}

// 任务表格参数
const tableTotalData = ref<TaskBatchItem[] | null>([])
const tableData = ref<TaskBatchItem[] | null>([]) // 当前操作tab下的全部任务(分页前)

// 分页表格当前页任务数据更新
watch([tableTotalData, activeTaskType], () => {
  tableData.value = tableTotalData.value?.filter(item => item.taskType === TaskTypeEnum[activeTaskType.value]) || []
  const waitingList = tableData.value.filter(item => item.batchStatus === '等待执行')
  const executingList = tableData.value.filter(item => item.batchStatus === '正在执行')
  const successList = tableData.value.filter(item => item.batchStatus === '执行成功')
  const errorList = tableData.value.filter(item => item.batchStatus === '执行失败')
  const otherList = tableData.value.filter(item => !item.batchStatus || !['等待执行', '正在执行', '执行成功', '执行失败'].includes(item.batchStatus))
  tableData.value = [...waitingList, ...executingList, ...errorList, ...otherList, ...successList]
}, {
  deep: true
})


// 批量编辑
const editVisible = ref(false)
// 仅用于批量编辑初始化数据
const editTaskItem = reactive<TemplateBaseItem>(new TaskManageOrigin(TaskTypeEnum[activeTaskType.value]|| TaskTypeEnum['AI外呼']))
const batchEdit = () => {
  editTaskItem.taskType = TaskTypeEnum[activeTaskType.value]
  Object.assign(editTaskItem, new TaskManageOrigin(TaskTypeEnum[activeTaskType.value]|| TaskTypeEnum['AI外呼']))
  editVisible.value = true
}
// 批量修改话术，仅支持商户id=14（火山账号使用）
const editScriptVisible = ref(false)
const batchEditScript = () => {
  editTaskItem.taskType = TaskTypeEnum[activeTaskType.value]
  editScriptVisible.value = true
}
const confirmBatchEditScript = (status: string | Map<number, string> = '') => {
  batchTaskInfo.value = {
    expire: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    lastOperator: '批量修改话术'
  }
  updateTable(status)
}
// 批量编辑弹窗确认处理函数
const confirmEditBatch = async (editBatchTask: TemplateBaseItem) => {
  loading.value = true
  editBatchTask.taskIds = tableData.value?.map(item => item?.id).join(',') || ''
  const len = editBatchTask.taskIds?.split(',').length || 0
  await trace({
    page: `外呼工具-批量编辑-${len}个任务：开始`,
    params: editBatchTask,
  })
  tableData.value = tableData.value?.map(item => {
    item.batchStatus = '正在执行'
    return item
  }) || []
  batchTaskInfo.value = {
    expire: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    lastOperator: '批量编辑'
  }
  const res = await to(aiOutboundTaskModel.batchEdit(editBatchTask))
  if (res[0]) {
    updateTable('执行失败')
    trace({
      page: `外呼工具-批量编辑-${len}个任务：完成`,
      params: res[0],
    })
  } else {
    const statusMap = new Map<number, string>([])
    res[1]?.map(item => {
      statusMap.set(item.id, item.batchStatus)
    })
    updateTable(statusMap)
    ElMessage.success('【批量编辑】成功')
    trace({
      page: `外呼工具-批量编辑-${editBatchTask.taskIds?.split(',')?.length}个任务：完成`,
      params: (res[1]?.flatMap(item => item?.batchStatus !== '执行成功' ? [{ id: item?.id, batchStatus: item?.batchStatus }] : [])?.slice(0, 60) || ''),
    })
  }
  editVisible.value = false
  loading.value = false
}
// 批量屏蔽地区
const blockVisible = ref(false)
const blocktype = ref(0) // 0:查看 1:覆盖 2:增加； 3:递减
const taskRestrictData = reactive(new RestrictModalOrigin())
const selectedOperatorList = ref<('全部' | OperatorEnum)[]>([])
const handleCityUpdate = (data: RestrictModal , operators: ('全部' | OperatorEnum)[]) => {
  Object.assign(taskRestrictData, data)
  selectedOperatorList.value = operators
}
// 批量屏蔽地区处理函数
const confirmBlock = async () => {
  if (!blocktype.value) return
  loading.value = true
  // 校验当前操作任务数量和今日导创建的任务数量是否一致，不一致则增加一个二次确认
  const [_, res] = await to(aiOutboundTaskModel.findTaskNum({
    startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
    taskType: TaskTypeEnum[activeTaskType.value],
  }))
  if (res && res !== tableData.value?.length) {
    const [err] = await to(Confirm({ 
      text: `操作任务数量【${tableData.value?.length}】${res > (tableData.value?.length || 0) ? '小于' : '大于'}今日当前账号总数量【${res}】，请确认?`,
      type: 'warning',
      title: `操作确认`,
      confirmText: '确认',
    }))
    if (err) {
      loading.value = false
      return
    }
  }
  tableData.value = tableData.value?.map(item => {
    item.batchStatus = '正在执行'
    return item
  }) || []
  batchTaskInfo.value = {
    expire: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    lastOperator: '批量屏蔽'
  }
  const params = {
    taskIds: tableData.value?.map(item => item?.id).join(','),
    ...taskRestrictData
  }
  const typeList = {
    1: { name: '覆盖', api: aiOutboundTaskModel.batchSetRestrictArea },
    2: { name: '增加', api: aiOutboundTaskModel.batchSetRestrictAreaAppend },
    3: { name: '递减', api: aiOutboundTaskModel.batchSetRestrictAreaReduce },
  }
  const { name = '', api = () => Promise.resolve() } = typeList[blocktype.value as keyof typeof typeList]
  await trace({
    page: `外呼工具-批量屏蔽地区(${name})-${tableData.value?.length}个任务：开始`,
    params: params,
  })
 
  const [err] = await to(api(params))
  trace({
    page: `外呼工具-批量屏蔽地区(${name})-${tableData.value?.length}个任务：完成`,
    params: err || {},
  })
  if (!err) {
    ElMessage.success(`【批量屏蔽】成功`)
    updateTable('执行成功')
    cancelBlock()
  } else {
    updateTable('执行失败')
  }
  loading.value = false
}
const cancelBlock = () => {
  blockVisible.value = false
  handleCityUpdate(new RestrictModalOrigin(), [])
}
const batchBlock = async () => {
  blockVisible.value = true
  blocktype.value = 1
}
// 查看当前任务的地区
const handleScopeDetails = (row: TaskBatchItem) => {
  Object.assign(taskRestrictData, pickAttrFromObj(row, [
    'allRestrictProvince', 'allRestrictCity', 'ydRestrictProvince', 'ydRestrictCity', 'ltRestrictProvince', 'ltRestrictCity', 'dxRestrictCity', 'dxRestrictProvince', 'virtualRestrictCity', 'virtualRestrictProvince', 'unknownRestrictCity', 'unknownRestrictProvince'
  ]))
  selectedOperatorList.value = []
  blockVisible.value = true
  blocktype.value = 0
}
// 批量添加呼叫
const batchAddCallVisible = ref(false)
const batchAddCall = () => {
  if (onStart.value) {
    return ElMessage.warning('批量添加呼叫前，请确保任务均不处于进行中')
  }
  batchAddCallVisible.value = true
}
// 批量取消呼叫
const batchCancelCallVisible = ref(false)
const batchCancelCall = () => {
  if (onStart.value) {
    return ElMessage.warning('批量取消呼叫前，请确保任务均不处于进行中')
  }
  batchCancelCallVisible.value = true
}
const confirmBatchAdd = async (data: TaskBatchItem[]) => {
  batchTaskInfo.value = {
    expire: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    lastOperator: '批量添加呼叫'
  }
  tableData.value = data || []
}
// 批量启动
const callVisible = ref(false)
const batchStartCall = () => {
  if (tableData.value && tableData.value?.length > 0) {
    callVisible.value = true
  }
}
/**
 * 执行批量启动
 * @param params ： 启动参数
 */
const confirmBatchStart = async (data: TaskBatchItem[]) => {
  batchTaskInfo.value = {
    expire: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    lastOperator: '批量启动'
  }
  callVisible.value = false
  if (data && data.length>0) {
    updateTableByData(data)
  } else {
    ElMessage({
      type: 'error',
      message: '执行失败',
    })
    updateTable('执行失败')
  }
}
// 批量停止
const batchStop = async () => {
  Confirm({ 
    text: `您确定要批量停止任务吗?`,
    type: 'warning',
    title: `停止确认`,
    confirmText: '停止',
  }).then(async () => {
    loading.value = true
    tableData.value = tableData.value?.map(item => {
      item.batchStatus = '正在执行'
      return item
    }) || []
    batchTaskInfo.value = {
      expire: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      lastOperator: '批量停止'
    }
    const params = {
      taskIds: tableData.value?.map(item => item?.id).join(',') || '',
      callStatus: TaskStatusEnum['已停止'],
    }
    await trace({
      page: `外呼工具-批量停止-${tableData.value?.length}个任务：开始`,
      params: params,
    })
    const [err, res] = await to(activeTaskType.value === '人机协同'
      ? aiOutboundTaskModel.batchStopMixTask(params)
      : aiOutboundTaskModel.batchStopAiTask(params)
    ) as [any, TaskBatchItem[]]
    if (res && res.length > 0) {
      updateTableByData(res)
    } else {
      updateTable('执行失败')
    }
    trace({
      page: `外呼工具-批量停止-${tableData.value?.length}个任务：完成`,
      params: err || (res?.flatMap(item => item?.batchStatus !== '执行成功' ? [{ id: item?.id, batchStatus: item?.batchStatus }] : [])?.slice(0, 60) || ''),
    })
    loading.value = false
  }).catch((err) => {})
}

// 批量锁定
const batchLock = async (type: 'lock' | 'unlock') => {
  const [err1, res1] = await to(Confirm({
    text: `您确定要批量【${type === 'unlock' ? '解锁' : '锁定'}】任务吗?`,
    type: 'warning',
    title: '确认'
  })) as [Error | undefined, any]
  if (err1) return
  const params:{
    taskIds: string,
    taskType: TaskTypeEnum,
    groupId: string,
  } = {
    taskIds:  tableData.value?.map(item => item?.id!).join(',') || '',
    taskType: TaskTypeEnum[activeTaskType.value],
    groupId: userStore.groupId,
  }
  await trace({
    page: `外呼工具-批量${type === 'lock' ? '锁定' : '解锁'}-${tableData.value?.length}个任务`,
    params: params
  })
  const [err2] = await to(type === 'lock' ?
  aiOutboundTaskModel.lockTask(params) : aiOutboundTaskModel.unlockTask(params)) as [any, any]
  updateTable(!err2 ? '执行成功' : '执行失败')
  if (!err2) {
    ElMessage({
      type: 'success',
      message: '操作成功'
    })
  }
}

// 导出
const exportVisible = ref(false)
const exportInfo = reactive<{
  startTime: string,
  endTime: string,
  type: number
}>({
  startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
  type: 1 // 导出任务详情
})
/** 导出任务： 1：任务详情 2：任务意向分布统计 */
const handleExport = (type: number) => {
  exportVisible.value = true
  exportInfo.type = type
}

// 导出数据转化为xls
const export2Excel = (data: TaskBatchItem[], fileName: string) => {
  if (!data || data.length === 0) {
    return ElMessage({
      type: 'warning',
      message: '导出数据为空'
    })
  }
  loading.value = true
  const csvData = data.map(item => {
    const totalData:{
      [propName: string]: any
    } = {
      ...item,
      timeStr: (item.startWorkTimeList && item.endWorkTimeList) ? item.startWorkTimeList.map((v, index) => {
        return v + '-' + item.endWorkTimeList![index]
      }).join('；') : '-',
      ifLockStr: !item.ifLock ? '未锁定' : '已锁定',
      firstRecallTimeStr: item.firstRecallTime ? (item.firstRecallTime || '-') + '； ' + (item.secondRecallTime || '') : '-',
      callRatioTypeStr: item.autoReCall ? (item.callRatioType == 1 ? '首呼优先' : '多轮次呼叫按比例') : '-',
      autoReCallStr: item.autoReCall ? '是' : '否',
      isAutoStopStr: item.isAutoStop == 1 ? '是' : '否',
      operateTime: dayjs().format('YYYY-MM-DD'),
      aiAnswerNum: item.aiAnswerNum || '',
      expectedFinishTime: item.expectedFinishTime ? dayjs(item.expectedFinishTime).format('YYYY-MM-DD HH:mm:ss') : '-',
      smsTemplateAbnormal: item.smsTemplateAbnormal == 1 ? '异常' : '正常',
      triggerSmsNumber: `${item.triggerSmsNumber && item.putThroughPhoneNum ? +(item.triggerSmsNumber / item.putThroughPhoneNum * 100).toFixed(1)  + '%' : '-'} ${item.triggerSmsNumber??'-'} / ${item.putThroughPhoneNum??'-'}`,
      sendSmsNumber: `${item.triggerSmsNumber && item.sendSmsNumber ? +(item.sendSmsNumber / item.triggerSmsNumber * 100).toFixed(1)  + '%' : '-'} ${item.sendSmsNumber??'-'} / ${item.triggerSmsNumber??'-'}`,
    }
    const res: {
      [propName: string]: any
    } = {}
    Object.keys(SheetNameEnum).map((name) => {
      // @ts-ignore
      res[name] = totalData[SheetNameEnum[name]]??''
    })
    return res
  })
  exportExcel(csvData, fileName);
  loading.value = false
  exportVisible.value = false
}

/** 导出任务意向分布统计 */
const exportIntentions2Excel = (data: TaskIntentionStatisticsItem[], fileName: string) => {
  if (!data || data.length === 0) {
    return ElMessage({
      type: 'warning',
      message: '导出数据为空'
    })
  }
  loading.value = true
  const csvData = data.map(item => {
    return {
      '任务ID': item.taskId || '',
      '任务名称': item.taskName || '',
      '接通数': item.throughputNum || 0,
      'A类数量': item.intentionANum || 0,
      'A类占比': item.throughputNum ? ((item.intentionANum || 0) / (item.throughputNum) * 100).toFixed(2) + '%' : 0,
      'B类数量': item.intentionBNum || 0,
      'B类占比': item.throughputNum ? ((item.intentionBNum || 0) / (item.throughputNum) * 100).toFixed(2) + '%' : 0,
      'C类数量': item.intentionCNum || 0,
      'C类占比': item.throughputNum ? ((item.intentionCNum || 0) / (item.throughputNum) * 100).toFixed(2) + '%': 0,
      'D类数量': item.intentionDNum || 0,
      'D类占比': item.throughputNum ? ((item.intentionDNum || 0) / (item.throughputNum) * 100).toFixed(2) + '%' : 0,
      'E类数量': item.intentionENum || 0,
      'E类占比': item.throughputNum ? ((item.intentionENum || 0) / (item.throughputNum) * 100).toFixed(2) + '%' : 0,
      'F类数量': item.intentionFNum || 0,
      'F类占比': item.throughputNum ? ((item.intentionFNum || 0) / (item.throughputNum) * 100).toFixed(2) + '%' : 0,
      'G类数量': item.intentionGNum || 0,
      'G类占比': item.throughputNum ? ((item.intentionGNum || 0) / (item.throughputNum) * 100).toFixed(2) + '%' : 0,
      '其他数量': item.intentionOthersNum || 0,
      '其他类占比': item.throughputNum ? ((item.intentionOthersNum || 0) / (item.throughputNum)).toFixed(2) + '%' : 0,
    }
  })
  exportExcel(csvData, fileName);
  loading.value = false
  exportVisible.value = false
}
// 导出任务弹窗确认处理函数
const confirmExport = async () => {
  loading.value = true
  try {
    const params = {
      startTime: exportInfo.startTime,
      endTime: exportInfo.endTime,
    }
    if (exportInfo.type === 1) {
      const data = await aiOutboundTaskModel.search(params) as TaskBatchItem[] || []
      export2Excel(data, `导出任务${exportInfo.startTime}-${exportInfo.endTime}.xlsx`)
    } else {
      const data = await aiOutboundTaskModel.findTaskIntentionsStatistics(params) as TaskIntentionStatisticsItem[] || []
      exportIntentions2Excel(data, `导出任务意向分布${exportInfo.startTime}-${exportInfo.endTime}.xlsx`)
    }
  } catch (error) {
    ElMessage({
      message: `导出失败`,
      type: 'error',
    })
  }
  loading.value = false
}
// 导出失败处理函数
const exportFail = async () => {
  loading.value = true
  try {
    const data = tableData.value?.filter(item => item.batchStatus === '执行失败') || []
    if (data.length === 0) {
      loading.value = false
      return ElMessage({
        message: `执行失败任务列表为空`,
        type: 'warning',
      })
    }
    export2Excel(data, `导出失败任务${exportInfo.startTime}-${exportInfo.endTime}.xlsx`)
  } catch (error) {
    ElMessage({
      message: `导出失败任务操作失败`,
      type: 'error',
    })
  }
  loading.value = false
}
// 上传文件
const fileRef = ref()
const uploadType = ref(1) // 1: 上传文件；2：上传任务名
const handleUpload = (type: number) => {
  uploadType.value =type
  // @ts-ignore
  document.querySelector('.batch-upload')!.click()
}
// 上传文件转化为表格数据
const handleFileChange = async (e: Event) => {
  const {data} = await readXlsx(e) as { data: Record<string, any>[] }
  tableTotalData.value = data.flatMap(item => {
    if (uploadType.value == 2) {
      return item['任务名称'] ? [{
        batchStatus: '等待执行',
        taskName: item['任务名称'] || '',
      }] : []
    }
    if (uploadType.value == 1) {
      return item['任务ID'] ? [{
        batchStatus: '等待执行',
        id: item['任务ID'] || '',
        taskName: '',
      }] : []
    }
    return []
  }) || []

  await trace({
    page: `外呼工具-点击${uploadType.value === 2 ? '上传任务名' : '上传文件'}-导入${tableTotalData.value?.length}个任务：开始`,
    params: tableTotalData.value.map(item => uploadType.value === 2 ? item.taskName : item?.id).join(',') || null,
  })
  uploadType.value === 2 ? updateTableByNames() : updateTable('', true)

  batchTaskInfo.value = {
    expire: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    lastOperator: '上传文件'
  }
  fileRef.value.value = null
}

// 通过筛选条件直接导入任务
const importVisible = ref(false)
const confirmImport = async (data: number[], type: 0 | 1) => {
  if (!data?.length) return ElMessage.warning('导入任务为空！')
  let totalIds: number[] = data || []
  if (type === 1) {
    totalIds = [...new Set([...totalIds, ...tableTotalData.value?.map(item => item.id!) || []])]
  }
  tableTotalData.value = totalIds?.map(item => ({ id: item }))
  trace({
    page: `外呼工具-导入任务-读取${tableTotalData.value?.length}个任务`,
    params: tableTotalData.value,
  })
  batchTaskInfo.value = {
    expire: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    lastOperator: '上传文件'
  }
  updateTable('', true)
}



// 关闭所有弹窗
const closeAllDialog = () => {
  callVisible.value = false
  exportVisible.value = false
}
onMounted(async () => {
  loading.value = true
  // 获取全部行业
  await globalStore.getAllIndustryList()
  const secondaryIndustries = globalStore.getIndustryIdAndNameMapList
  // 获取坐席组
  await taskStore.getCallTeamListOptions(undefined, true)
  // 获取全部商户绑定的话术（包含历史）
  const speechCraftAllList = await taskStore.getAllScriptListOptions(true)
  speechCraftMap.value = new Map([])
  speechCraftAllList?.forEach(item => {
    item.scriptStringId && speechCraftMap.value.set(item.scriptStringId, secondaryIndustries.find(v => v.id == item.secondaryIndustryId!)?.name||'')
  })

  // 读取上一次缓存任务列表，更新其batchStatus状态
  if (batchTaskInfo.value.expire && taskStore.batchTaskIds.length > 0 && dayjs(batchTaskInfo.value.expire).add(6, 'hour') > dayjs()) {
    tableTotalData.value = taskStore.batchTaskIds?.map(item => ({ id: item }))
    await trace({
      page: `外呼工具-进入页面读取缓存-${taskStore.batchTaskIds?.length}个任务`,
      params: taskStore.batchTaskIds?.join(',') || null,
    })
    updateTable()
  } else {
    taskStore.batchTaskIds = []
    batchTaskInfo.value = {
      expire: '',
      lastOperator: ''
    }
  }
  loading.value = false
})

const clearAll = () => {
  speechCraftMap.value = new Map([])
  tableTotalData.value = null
  taskTypeOptions.value = []
  tableData.value = null
}
onUnmounted(() => {
  clearAll()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.operation-tool-container {
  margin: 16px;
  width: calc(100% - 32px);
  height: 100%;
  box-sizing: border-box;
  min-width: 1048px;
  overflow: hidden;
  font-size: 13px;
  display: flex;
  flex-direction: column;
  :deep(.el-upload-list) {
    display: none;
  }
  .el-table {
    font-size: 13px;
  }
  .status-box-mini {
    margin: 0 auto;
  }
  :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
    background-color: rgba(0, 0, 0, 0) !important;
  }
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) .el-input__wrapper {
  width: 100%;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>