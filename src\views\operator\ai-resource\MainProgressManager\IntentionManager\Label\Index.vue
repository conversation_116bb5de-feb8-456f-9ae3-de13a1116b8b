<template>
  <div class="label-container">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <span class="info-title">请严格按照话术制作规范设置标签，勿随意添加</span>
      <div>
        <el-button @click="search">刷新数据</el-button>
        <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="edit()">新增标签</el-button>
        <el-button v-if="!isChecked && tableData && tableData.length > 0" class="tw-mr-0.5" type="danger" @click="del()">
          <el-icon :size="16" class="tw-mr-0.5" color="inherit">
            <SvgIcon name="delete"></SvgIcon>
          </el-icon>
          批量删除
        </el-button>
        <el-button v-if="tableData?.length && canDownload" @click="export2Excel()"><el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>导出标签</el-button>
        <input type="file" ref="fileRef" @change="handleFileChange" accept=".xls,.xlsx" class="batch-upload tw-hidden"/>
        <el-button v-if="!isChecked" @click="handleUpload" class="tw-ml-1"><el-icon :size="16"><SvgIcon name="upload"></SvgIcon></el-icon>批量上传</el-button>
      </div>
    </div>
    <el-table
      id="label-draggable-table"
      :data="tableData"
      ref="tableRef"
      style="width: 100%"
      class="tw-grow"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column align="left" type="selection" width="36"></el-table-column>
      <el-table-column v-if="!isChecked" align="center" width="90">
        <div class="handle tw-cursor-pointer">
          <el-icon>
            <Switch />
          </el-icon>
        </div>
      </el-table-column>
      <el-table-column property="sequence" align="center" width="120" label="优先顺序"></el-table-column>
      <el-table-column align="left" label="标签名称" property="labelName" min-width="300"></el-table-column>
      <el-table-column align="left" label="使用次数" min-width="120">
        <template #default="{ row }">
          <el-button v-if="!!row.scriptCorpusList?.length || !!row.advancedRuleList?.length" type="primary" link @click="showUsage(row)">
            {{ (row.scriptCorpusList?.length || 0) + (row.advancedRuleList?.length || 0) }}
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column v-if="!isChecked" label="操作" align="right" min-width="120">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">编辑</el-button>
          <el-button type="danger" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
  </div>
  <el-dialog
    v-model="addDialogVisible"
    width="480px"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ editData.id ? '编辑' : '新增' }}标签</div>
    </template>
    <el-form
      ref="labelEditRef"
      :model="editData"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item prop="labelName" label="标签名称：">
        <el-input
          v-model="editData.labelName"
          :placeholder="`请输入标签名称，${nameInputLimit}字以内`"
          clearable
          maxlength="20"
          show-word-limit
          :formatter="(value: string) => value.trim()"
          @keyup.enter.native="save"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span v-if="!isChecked" class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="save" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <LabelUsageDrawer
    v-model:visible="usageDialogVisible"
    v-model:loading="updateLabelLoading"
    :data="editData"
    @update:data="handleUsageOperationConfirm"
  />
</template>

<script lang="ts" setup>
import { LabelItem, } from '@/type/IntentionType'
import { scriptIntentionModel } from '@/api/speech-craft'
import { CloseBold, Delete, Edit, Plus, Select, Switch, } from '@element-plus/icons-vue'
import { onActivated, reactive, ref, watch, onDeactivated } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import Sortable from "sortablejs"
import { useScriptStore } from '@/store/script'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { tableHeaderStyle } from '@/assets/js/constant'
import { exportExcel, readXlsx } from '@/utils/export'
import to from 'await-to-js'
import dayjs from 'dayjs'
import { trace } from '@/utils/trace'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
import LabelUsageDrawer from './LabelUsageDrawer.vue'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const editId = scriptStore.id as number
const isChecked = scriptStore.isChecked
// 用户权限获取
const userStore = useUserStore();
const canDownload = userStore.permissions[routeMap['话术制作'].id]?.includes(routeMap['话术制作'].permissions['导出表格'])

// 输入框最大长度
const nameInputLimit = ref<number>(20)

// 表格区
const tableData = ref<LabelItem[] | null>([])
const tableRef = ref()
const labelEditRef = ref<FormInstance | null>(null)
const validateLabelName = (rule: any, value: any, callback: any) => {
  if (value && tableData.value?.find(item => item.labelName === value && editData.id !== item.id)) {
    callback(new Error('标签名称不能重复！'))
  } else {
    callback()
  }
}
const rules = {
  labelName: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { validator: validateLabelName, trigger: 'blur' },
  ]
}

// 搜索区
const search = async () => {
  loading.value = true
  const [err, data] = await to(scriptIntentionModel.findLabelAndUsageList(editId))
  tableData.value = data || []
  // 更新缓存中的标签列表
  !!tableData.value.length && (scriptStore.intentionTagOptions = (data || []).map(item => {
    return {
      id: item.id,
      labelName: item.labelName,
      sequence: item.sequence,
      scriptId: item.scriptId
    }
  }))
  loading.value = false
}

// 编辑
class EditDataOrigin {
  sequence = undefined
  scriptId = editId
  id = undefined
  labelName = ''
}

const editData = reactive<LabelItem>(new EditDataOrigin())
const addDialogVisible = ref(false)
const edit = (row?: LabelItem) => {
  if (row && row.id) {
    const { sequence, id, labelName } = row
    Object.assign(editData, {
      sequence, id, labelName
    })
  } else {
    Object.assign(editData, new EditDataOrigin())
  }
  addDialogVisible.value = true
  labelEditRef.value && labelEditRef.value.clearValidate()
}
const cancel = () => {
  Object.assign(editData, new EditDataOrigin())
  addDialogVisible.value = false
}
const save = () => {
  labelEditRef.value && labelEditRef.value.validate(async (valid) => {
    if (valid) {
      editData.labelName = editData.labelName.trim()
      const { id, sequence, labelName, scriptId } = editData
      const params = {
        id: id && id > 0 ? id : undefined,
        sequence: sequence && sequence > 0 ? sequence : undefined,
        labelName: labelName,
        scriptId
      }
      loading.value = true
      trace({
        page: `话术编辑-标签-修改标签(${editId})`,
        params: params,
      })
      const [err] = await to(scriptIntentionModel.saveOneLabel(params))
      if (!err) {
        ElMessage.success('操作成功')
        cancel()
        search()
      }
      loading.value = false
    }
  })
  
}

// 导入文件模拟点击
const handleUpload = () => {
  // @ts-ignore
	document.querySelector('.batch-upload')!.click()
}
const fileRef = ref(null)
// 导入文件：批量导入标签
const handleFileChange = async (e: Event) => {
  const {data: xlsData} = await readXlsx(e) as { data: Record<string, any>[] }
  if (!xlsData || xlsData?.length === 0) return ElMessage.warning('文档上传失败，请检查')
  const errMsg: string[] = []
  const data = xlsData?.flatMap((item, index) => {
    const row = tableData.value?.find(v => v.labelName === item['标签名称'])
    if (row) errMsg.push(`${item['标签名称']}`)
    return item['标签名称'] && !row ? [item['标签名称']] : []
  })
  errMsg.length > 0 && ElMessage.warning('存在重复标签：' + errMsg.join('、'))
  const data2 = [...new Set(data)]
  if (!data2 || data2.length === 0) {
    // @ts-ignore
    fileRef.value.value = null
    return search()
  }
  loading.value = true
  trace({
    page: `话术编辑-标签-批量修改标签(${editId})`,
    params: data2,
  })
  Promise.all(data2?.map(async item => {
    const params = {
      labelName: item as string,
      scriptId: editId,
    }
    await to(scriptIntentionModel.saveOneLabel(params))
  })).then(() => {
    ElMessage.success(`共计${data2.length || 0}个标签上传成功`)
    search()
  }).finally(() => {
    loading.value = false
    // @ts-ignore
    fileRef.value.value = null
  })
}

// 标签导出
const export2Excel = () => {
  const data = tableData.value?.map(item => {
    const { labelName, sequence } = item
    const res: {[key: string]: any} = {
      '优先顺序': sequence,
      '标签名称': labelName,
    }
    return res
  }) || []
  exportExcel(data, `【${scriptStore.name}】标签-${dayjs().format('YYYY-MM-DD')}.xlsx`);
}

// 拖动
const sortable = ref<null | Sortable>(null)
const initDraggableTable = () => {
  sortable.value = Sortable.create(
    document.querySelector('#label-draggable-table .el-table__body tbody') as HTMLElement, {
      animation: 300,
      // disabled: false,
      // draggable: "tr",
      handle: ".handle",
      // sort: true,
      onEnd: async (evt) => {
        if (!tableData.value) return
        const newIndex = evt.newIndex as number
        const oldIndex = evt.oldIndex as number
        loading.value = true
        const id2Index = newIndex > oldIndex ? newIndex + 1 : newIndex
        try {
          await scriptIntentionModel.switchLabelSequence({
            id1: tableData.value[oldIndex].id as number,
            id2: tableData.value[id2Index] ? tableData.value[id2Index].id as number : null,
            scriptId : editId,
          })
        } catch (err) {
          ElMessage.error('拖拽操作失败')
        }
        tableData.value = []
        search()
        loading.value = false
      },
    })
}
// 操作区
const delAction = async (ids: number[]) => {
  loading.value = true
  let errNum = 0
  await Promise.all(ids.map(async item => {
    const [err, _] = await to(scriptIntentionModel.deleteOneLabel({ id: item }))
    if (!!err) {
      errNum++
    }
  }))
  !errNum && ElMessage.success('删除成功')
  search()
  loading.value = false
}

const del = (row?: LabelItem) => {
  let rows: LabelItem[] = row ? [row] : (tableRef.value?.getSelectionRows() || [])
  if (!rows || rows.length < 1) return ElMessage.warning('请选择需要删除的标签')
  Confirm({ 
    text: rows.length === 1 ? `您确定要删除标签【${rows[0].labelName}】吗?` : `您确定要批量删除共计${rows.length}个标签吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(rows.map(item => item.id!))
  }).catch(() => {})
}

//
const usageDialogVisible = ref(false)
const updateLabelLoading = ref(false)

const showUsage = (row: LabelItem) => {
  if (!row) return
  usageDialogVisible.value = true
  Object.assign(editData, row)
}
const handleUsageOperationConfirm = async () => {
  await search()
  const row = tableData.value?.find(v => v.id === editData.id)
  row && Object.assign(editData, row)
  updateLabelLoading.value = false
}


// 生命周期
onActivated(() => {
  search()
  !isChecked && initDraggableTable()
})

onDeactivated(() => {
  sortable.value?.destroy()
  sortable.value = null
  tableData.value = null
  fileRef.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.label-container {
  width: 100%;
  height: calc(100vh - 250px);
  position: relative;
  box-sizing: border-box;
  padding: 16px 12px 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  font-size: var(--el-font-size-base);
  .el-table {
    font-size: var(--el-font-size-base);
  }
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  padding: 0 12px;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
}
</style>
