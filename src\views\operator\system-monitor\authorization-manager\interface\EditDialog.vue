<template>
  <el-dialog
    :model-value="visible"
    width="640px"
    class="dialog-form"
    :close-on-click-modal="false"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="90px"
        ref="editRef"
      >
        <el-form-item label="接口名称：" prop="name">
          <el-input v-model.trim="editData.name" show-word-limit maxlength="30" clearable placeholder="请输入接口名称，30个字以内"></el-input>
        </el-form-item>
        <el-form-item label="接口类型：" prop="type">
          <el-radio-group v-model="editData.type" class="tw-ml-[6px]">
            <el-radio v-for="item in enum2Options(InterfaceTypeEnum)" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="服务名：" prop="serviceId">
          <el-radio-group v-model="editData.serviceId" class="tw-ml-[6px]">
            <el-radio v-for="item in enum2Options(ServiceEnum)" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="接口地址：" prop="identifier">
          <el-input v-model.trim="editData.identifier" maxlength="100" clearable placeholder="请输入接口地址"></el-input>
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input v-model.trim="editData.remark" type="textarea" show-word-limit maxlength="200" clearable placeholder="请输入备注，200个字以内"></el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <div>
          <el-button @click="cancel" :icon="CloseBold">取消</el-button>
          <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { authorizationModel } from '@/api/authorization'
import type { FormInstance, } from 'element-plus'
import to from 'await-to-js';
import { InterfaceItemOrigin } from './constant'
import { InterfaceItem, InterfaceTypeEnum, ServiceEnum } from '@/type/authorization'
import { enum2Options, pickAttrFromObj } from '@/utils/utils'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  data: InterfaceItem | null
}>();
const loading = ref(false)
const visible = ref(props.visible)
const editData = reactive<Partial<InterfaceItem>>(props.data || new InterfaceItemOrigin())
const editRef = ref<FormInstance  | null>(null)
const title = computed(() => (`${editData.id ? '编辑' : '新增'}接口`))
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}

const rules = {
  
  name: [
    { required: true, message: '请输入接口名称', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请输入接口类型', trigger: 'change' },
  ],
  serviceId: [
    { required: true, message: '请输入服务名', trigger: 'blur' },
  ],
  identifier: [
    { required: true, message: '请输入接口地址', trigger: 'blur' },
  ],
  // description: [
  //   { required: true, message: '请输入接口描述', trigger: 'blur' },
  // ],
}

const confirm = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      // @ts-ignore
      const params = pickAttrFromObj(editData, [
        'id', 'type', 'name', 'identifier', 'remark', 'serviceId'
      ])
      const [err] = await to(authorizationModel.saveInterface(params))
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        cancel()
        emits('confirm')
      }
    }
  })
}

watch(() => props.visible, n => {
  visible.value = n
  if (n) {
    Object.assign(editData, new InterfaceItemOrigin(), props.data)
    editRef.value?.clearValidate()
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>