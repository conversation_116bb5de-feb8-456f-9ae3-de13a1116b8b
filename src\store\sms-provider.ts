import { defineStore } from 'pinia'
import { SmsAccountItem, SmsProviderItem } from '@/type/sms'

// 当前选中的短信供应商
const currentProvider: SmsProviderItem = {}
// 当前选中的短信供应商的全部短信对接账号列表
const accountList: SmsAccountItem[] = []
// 正在编辑的短信对接账号
const editingSmsAccount: SmsAccountItem = {}

export const useSmsProviderStore = defineStore({
  id: 'smsProvider',
  state() {
    return {
      currentProvider,
      accountList,
      editingSmsAccount,
    }
  },
  actions: {
    clear() {
      this.$state.currentProvider = {}
      this.$state.accountList = []
      this.$state.editingSmsAccount = {}
    },
  },
  persist: [
    {
      storage: sessionStorage,
    }
  ]
})
