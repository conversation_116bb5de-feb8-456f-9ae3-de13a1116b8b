import { JanusJ<PERSON> } from 'janus-gateway'

export type InitOptions = JanusJS.InitOptions
export type ConstructorOptions = JanusJS.ConstructorOptions
export type DestroyOptions = JanusJS.DestroyOptions
export type PluginOptions = JanusJS.PluginOptions
export type PluginHandle = JanusJS.PluginHandle
export type SipEventMessage = JanusJS.Message & {
  call_id?: string,
  result?: {
    code?: string,
    reason?: string,
    event?: string,
    content?: string,
    username?: string,
    displayname?: string,
  },
}
export type JSEP = JanusJS.JSEP
export type RemoteTrackMetadata = JanusJS.RemoteTrackMetadata
export type PluginDtmfParam = JanusJS.PluginDtmfParam & {
  success?: (data: unknown) => void | Promise<void>
  error?: (error: string) => void | Promise<void>
}
export type OfferParams = JanusJS.OfferParams
export type PluginCreateAnswerParam = JanusJS.PluginCreateAnswerParam
