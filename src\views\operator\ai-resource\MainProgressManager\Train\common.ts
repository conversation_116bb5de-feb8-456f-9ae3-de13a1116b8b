import { ScriptCorpusItem, scriptUnitContent, TrainDialogItem } from '@/type/speech-craft'
import to from 'await-to-js'
import { scriptCorpusModel } from '@/api/speech-craft'
import { useScriptTrainStore } from '@/store/script-train'

const scriptTrainStore = useScriptTrainStore()

// 语料ID和语料打断设置的映射
export const corpusConfigMap = new Map<number, scriptUnitContent[]>([])
// 所有AI侧的语料ID集合，用于请求接口并展示语料的打断设置
export const corpusIdSet = new Set<number>()

/**
 * 更新语料打断设置
 * @param list 对话详情列表
 */
export const updateCorpusConfig = async (list: TrainDialogItem[] = []) => {
  // 遍历对话详情列表，得到AI语料ID集合
  list.forEach((item: TrainDialogItem) => {
    if (item.corpusId !== undefined && item.corpusId !== null) {
      corpusIdSet.add(item.corpusId)
    }
  })

  // for循环支持异步
  // 如果语料设置映射里没有这个语料ID所对应的打断设置，就请求接口获取
  const arr = Array.from(corpusIdSet)
  for (let i = 0; i < arr.length; i++) {
    const id = arr[i]
    if (!(<number[]><unknown>Array.from(corpusConfigMap.keys())).includes(id)) {
      const [_, res] = <[any, ScriptCorpusItem]>await to(scriptCorpusModel.findMasterCorpus({
        corpusId: id,
      }))
      // 将新的AI语料打断设置添加到映射里
      if (res) {
        const list = res?.scriptMultiContents?.at(0)?.scriptUnitContents
        corpusConfigMap.set(id, list?.length ? list : [])
      }
    }
  }
  // 更新话术训练的对话详情里的语料打断设置
  scriptTrainStore.dialogData.forEach((item: TrainDialogItem) => {
    if (typeof item.corpusId === 'number') {
      item.corpusConfig = corpusConfigMap.get(item.corpusId) ?? []
    }
  })
}
