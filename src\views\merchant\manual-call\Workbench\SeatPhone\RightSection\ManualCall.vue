<template>
  <ClientInfo />

  <TabsBox v-model:active="manualCallActiveTab" class="workbench-tab" :tabList="tabList" @update:active="onUpdateTab" />

  <div v-show="manualCallActiveTab===WorkbenchTabEnum['跟进记录']" class="block">
    <template v-if="followLogList?.length">
      <el-scrollbar wrap-class="tw-px-[12px]" class="tw-bg-white tw-border-r-[1px] tw-flex-grow-0 tw-flex-shrink-0 ">
        <TimeLineBox
          :list="followLogList.map(item => item.clueFollowUpLog)"
          :active="activeFollowLog?.id"
          activeProps="id"
          sortProps="followUpTime"
          :gap="12"
          :enableClick="false"
        >
          <template v-slot:content="{row}">
            <div
              class="tw-relative tw-grid tw-grid-cols-2 tw-gap-[12px] tw-box-border
                    tw-text-left tw-bg-[#F5F7FA] tw-w-[347px]
                    tw-p-[16px] tw-rounded-[4px] tw-border-[1px] tw-mt-[12px]"
              :class="row?.id==activeFollowLog?.id ? 'tw-border-[#165DFF]': 'tw-border-[#F5F7FA]'"
            >
              <div class="tw-absolute tw-top-[16px] tw-right-[16px]">
                <el-button link type="primary" @click.prevent="onClickView(row)">
                  查看
                </el-button>
              </div>

              <div class="tw-flex tw-flex-col">
                <span class="info-title">跟进时间：</span>
                <span class="info-title-deep tw-mt-[4px]">
                  {{ row?.followUpTime ? dayjs(row?.followUpTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
                </span>
              </div>
              <div class="tw-flex tw-flex-col">
                <span class="info-title">跟进状态：</span>
                <span class="info-title-deep tw-mt-[4px]">
                  {{ findValueInEnum(row?.followUpStatus, FollowUpStatusEnum) || '-' }}
                </span>
              </div>
              <div class="tw-flex tw-flex-col">
                <span class="info-title">跟进坐席： </span>
                <span class="info-title-deep tw-mt-[4px]">
                  {{ callSeatList.find(item => item.id === row?.callSeatId)?.account || '-' }}
                </span>
              </div>
              <div class="tw-flex tw-flex-col">
                <span class="info-title">跟进类型：</span>
                <span class="info-title-deep tw-mt-[4px]">
                  {{ findValueInEnum(row?.followUpType, FollowUpTypeEnum) || '-' }}
                </span></div>
              <div class="tw-flex tw-flex-col">
                <span class="info-title">通话时长：</span>
                <span class="info-title-deep tw-mt-[4px]">
                  {{ formatDuration(row?.callDuration / 1000) || '-' }}
                </span>
              </div>
              <div class="tw-flex tw-flex-col">
                <span class="info-title">下次跟进时间：</span>
                <span class="info-title-deep tw-mt-[4px]">
                  {{ row?.nextFollowUpTime ? dayjs(row?.nextFollowUpTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                </span>
              </div>
              <div class="tw-col-span-2">
                <span class="info-title">跟进备注：</span>
                <span class="info-title-deep">
                  {{ row?.note || '-' }}
                </span>
              </div>
            </div>
          </template>
        </TimeLineBox>
      </el-scrollbar>
    </template>
    <template v-else>
      <el-empty />
    </template>
  </div>

  <div v-show="manualCallActiveTab===WorkbenchTabEnum['客户状态记录']" class="block tw-flex tw-flex-col tw-justify-start tw-self-stretch tw-flex-nowrap">
    <ClientStatus />
  </div>

  <div v-show="manualCallActiveTab===WorkbenchTabEnum['AI跟进记录']" class="block">
    <div class="tw-flex tw-justify-center tw-items-center tw-p-[16px]">
      <el-button
        :loading="loadingAiFollowUpRecord"
        style="width: 100%; max-width: 240px;"
        :type="hasAiFollowUpRecordTodayData?'primary':'default'"
        @click="onClickAiFollowUpRecordButton(AiFollowUpRecordData.TODAY)"
      >
        <template v-if="hasAiFollowUpRecordTodayData">
          {{ AiFollowUpRecordData.TODAY }} (1)
        </template>
        <template v-else>
          {{ AiFollowUpRecordData.TODAY }} (0)
        </template>
      </el-button>
      <el-button
        :loading="loadingAiFollowUpRecord"
        style="width: 100%; max-width: 200px;"
        @click="onClickAiFollowUpRecordButton(AiFollowUpRecordData.HISTORY)"
      >
        {{ AiFollowUpRecordData.HISTORY }}
      </el-button>
    </div>

    <el-drawer
      v-model="aiFollowUpRecordDrawerVisible"
      size="50%"
      title="AI跟进记录"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :with-header="false"
      :modal="true"
      :z-index="30"
      :destroy-on-close="true"
    >
      <div class="drawer-header">
        <div class="drawer-header-title">AI跟进记录</div>

        <el-button :icon="Close" class="header-button" @click="aiFollowUpRecordDrawerVisible=false">
          关闭
        </el-button>
      </div>

      <AiFollowUpRecord :clueData="seatPhoneStore.currentClue" :tabName="activeInnerTab" />
    </el-drawer>
  </div>

  <FollowUpCallRecordDrawer
    v-model:visible="callRecordDrawerVisible"
    :tableData="recordInfoList"
    :recordData="currentRecordInfo"
    :recordType="activeCallType"
    :currentIndex="currentIndex"
    :total="followLogList?.length??0"
    @update:visible="updateCallRecordDrawerVisible"
  />
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, ref } from 'vue'
import { ClueFollowItem, ClueFollowLog, FollowUpStatusEnum, FollowUpTypeEnum } from '@/type/clue'
import { RecordDialogueData, RecordTypeEnum, TaskCallRecordItem } from '@/type/task'
import dayjs from 'dayjs'
import { findValueInEnum, formatDuration, Throttle } from '@/utils/utils'
import TimeLineBox from '@/components/TimeLineBox.vue'
import { SeatMember, WorkbenchTabEnum } from '@/type/seat'
import { useTaskStore } from '@/store/taskInfo'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { scriptInfoModel } from '@/api/speech-craft'
import { InfoQueryItem } from '@/type/speech-craft'
import { useSeatPhoneStore } from '@/store/seat-phone'
import { storeToRefs } from 'pinia'
import FollowUpCallRecordDrawer from './components/FollowUpCallRecordDrawer.vue'
import { Close } from '@element-plus/icons-vue'
import to from 'await-to-js'
import { ResponseData } from '@/axios/request/types'
import ClientInfo from './components/ClientInfo.vue'
import ClientStatus from './components/ClientStatus.vue'
import TabsBox from '@/components/TabsBox.vue'
import { seatCallRecordModel, seatWorkbenchClueModel } from '@/api/seat'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { useInteractionStore } from "@/store/seat/interaction";

// 动态引入组件
const AiFollowUpRecord = defineAsyncComponent(() => import('@/views/merchant/manual-call/components/AiFollowUpRecord.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const userInfo = useUserStore()
const taskStore = useTaskStore()
const seatPhoneStore = useSeatPhoneStore()
const { currentClue } = storeToRefs(seatPhoneStore)
const interactionStore = useInteractionStore()
const { manualCallActiveTab } = storeToRefs(interactionStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 标签页 开始 ----------------------------------------

// 标签页名称合集
const tabList: WorkbenchTabEnum[] = [
  WorkbenchTabEnum['跟进记录'],
  WorkbenchTabEnum['客户状态记录'],
  WorkbenchTabEnum['AI跟进记录']
]

/**
 * 标签页组件 更新标签页
 * @param val 新标签页名称
 */
const onUpdateTab = (val: WorkbenchTabEnum) => {
  interactionStore.updateManualCallActiveTab(val)
}

// ---------------------------------------- 标签页 结束 ----------------------------------------

// ---------------------------------------- 跟进记录 开始 ----------------------------------------

// -------------------- 跟进记录列表 开始 --------------------

// 跟进记录列表
const followLogList = ref<ClueFollowItem[]>([])
// 当前激活的跟进记录
const activeFollowLog = reactive<ClueFollowLog>({})
// 当前激活的跟进记录通话类型
const activeCallType = ref(RecordTypeEnum['人工直呼'])
// 当前激活的跟进记录索引
const currentIndex = ref(0)
// 通话记录详情
const currentRecordInfo = reactive<TaskCallRecordItem>({})
// 通话记录详情列表
const recordInfoList = reactive<TaskCallRecordItem[]>([])
// 坐席列表
const callSeatList = ref<SeatMember[]>([])

/**
 * 切换激活跟进记录
 * @param {ClueFollowLog} item 点击的跟进记录信息
 */
const handleActiveFollowLog = async (item: ClueFollowLog) => {
  console.log('切换激活跟进记录', item)
  Object.assign(activeFollowLog, item)
  Object.assign(recordInfoList, followLogList.value.map(item => item?.callRecordForHumanMachine ?? item?.callRecordForManualDirect ?? {}))
  const index = followLogList.value.findIndex(item => item.clueFollowUpLog.id === activeFollowLog.id)
  currentIndex.value = index === -1 ? 0 : index
  const row = followLogList.value[index]
  if (row?.clueFollowUpLog?.id) {
    Object.assign(currentRecordInfo, row.callRecordForHumanMachine ?? row.callRecordForManualDirect ?? {})
    if (row.callRecordForHumanMachine) {
      activeCallType.value = RecordTypeEnum['人机协同']
    } else if (row.callRecordForManualDirect) {
      activeCallType.value = RecordTypeEnum['人工直呼']
    }
  }
  await updateRecordData()
}
/**
 * 点击查看按钮
 * @param {ClueFollowLog} row 当前点击的跟进记录信息
 */
const onClickView = (row: ClueFollowLog) => {
  handleActiveFollowLog(row)
}

// -------------------- 跟进记录列表 结束 --------------------

// -------------------- 通话记录 开始 --------------------

// 针对右侧对话部分的的loading
const dialogLoading = ref(false)
const infoQueryMap = ref<Map<string, InfoQueryItem>>(new Map([]))
// 对话数据列表
const dialogData = ref<RecordDialogueData[]>([])

/**
 * 更新机器人用户对话数据
 */
const updateRecordData = async () => {
  try {
    dialogLoading.value = true
    const callId = currentRecordInfo?.callId || ''
    const recordId = currentRecordInfo?.recordId || ''
    if (!callId) {
      infoQueryMap.value = new Map([])
      dialogData.value = []
    }
    switch (activeFollowLog?.followUpType) {
      case RecordTypeEnum['AI外呼']: {
        dialogData.value = (await aiOutboundTaskModel.getAiDialogueDataList({
          callId: callId,
          recordId: recordId,
        }) as RecordDialogueData[]) || []
        break
      }
      case RecordTypeEnum['人工直呼']: {
        dialogData.value = (await seatCallRecordModel.getManualDialogueDataList({
          callId: callId,
          recordId: recordId,
        }) as RecordDialogueData[]) || []
        break
      }
      case RecordTypeEnum['人机协同']: {
        dialogData.value = (await seatCallRecordModel.getMixDialogueDataList({
          callId: callId,
          recordId: recordId,
        }) as RecordDialogueData[]) || []
        break
      }
    }
    const res = await scriptInfoModel.findInfoQueryList({ id: currentRecordInfo?.speechCraftId! }) as InfoQueryItem[] || []
    infoQueryMap.value = new Map([])
    res?.map(item => {
      infoQueryMap.value.set(item.infoFieldName, item)
    })
    // console.log('activeFollowLog', JSON.parse(JSON.stringify(activeFollowLog)), 'currentRecordInfo', JSON.parse(JSON.stringify(currentRecordInfo)))
    callRecordDrawerVisible.value = true
  } catch (err) {
  } finally {
    dialogLoading.value = false
  }
}

// -------------------- 通话记录 结束 --------------------

// ---------------------------------------- 跟进记录 结束 ----------------------------------------

// ---------------------------------------- 通话记录抽屉 开始 ----------------------------------------

// 是否显示通话记录抽屉 更新是否显示
const callRecordDrawerVisible = ref(false)

/**
 * 通话记录抽屉 更新是否显示
 * @param {boolean} val
 */
const updateCallRecordDrawerVisible = (val: boolean) => {
  callRecordDrawerVisible.value = val
  if (!val) {
    dialogData.value = []
  }
}

// ---------------------------------------- 通话记录抽屉 结束 ----------------------------------------

// ---------------------------------------- AI跟进记录 开始 ----------------------------------------

enum AiFollowUpRecordData {
  TODAY = '今日',
  HISTORY = '历史'
}

// AI跟进记录 抽屉 显示隐藏
const aiFollowUpRecordDrawerVisible = ref<boolean>(false)
// AI跟进记录 激活的标签卡
const activeInnerTab = ref(AiFollowUpRecordData.TODAY)
// AI跟进记录 加载动画
const loadingAiFollowUpRecord = ref(false)
// AI跟进记录 加载节流锁
const throttleAiFollowUpRecord = new Throttle(loadingAiFollowUpRecord)
// AI跟进记录 今日有数据
const hasAiFollowUpRecordTodayData = ref(false)

const getTodayAiFollowUpRecord = async () => {
  if (!currentClue.value.clueUniqueId) {
    hasAiFollowUpRecordTodayData.value = false
    return ElMessage.warning('未获取到线索手机号')
  }
  if (throttleAiFollowUpRecord.check()) {
    return
  }
  throttleAiFollowUpRecord.lock()
  const [_, res] = <[any, ResponseData]>await to(aiOutboundTaskModel.findFollowCallRecordList({
    phone: currentClue.value.clueUniqueId,
    callStatus: '7',
    account: userInfo.account,
    calloutStartTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    calloutEndTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
  })) as [any, ResponseData]
  hasAiFollowUpRecordTodayData.value = !!((res.data as TaskCallRecordItem[])?.length)
  throttleAiFollowUpRecord.unlock()
}
const onClickAiFollowUpRecordButton = (tab: AiFollowUpRecordData) => {
  activeInnerTab.value = tab
  aiFollowUpRecordDrawerVisible.value = true
}

// ---------------------------------------- AI跟进记录 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

/**
 * 初始化
 */
const init = async () => {
  // 坐席列表
  callSeatList.value = await taskStore.getCallGroupSeatListOptions()
  console.log('准备更新人工跟进记录', 'currentClue.value.id', currentClue.value.id)
  // 人工跟进记录
  if (typeof currentClue.value.id === 'number') {
    console.log('可以更新人工跟进记录')
    const res = <ClueFollowItem[]>await seatWorkbenchClueModel.getFollowById({
      clueId: currentClue.value.id
    })
    // 按时间倒序排序
    followLogList.value = (res?.length ? res : []).sort((a, b) => {
      return dayjs(a.clueFollowUpLog.followUpTime).isAfter(dayjs(b.clueFollowUpLog.followUpTime)) ? -1 : 1
    })
    console.log('人工跟进记录', JSON.parse(JSON.stringify(followLogList.value)))
  } else {
    // 清空
    followLogList.value = []
  }
  // 查询今日AI跟进记录
  await getTodayAiFollowUpRecord()
}
init()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.block {
  overflow: hidden;
  height: calc(100% - 37px);
}
.tab-box {
  height: 37px;
  /* 右侧模块 标签卡 */
  &.workbench-tab {
    :deep(.normal-tab) {
      width: 118px;
      height: 32px;
      font-size: 14px;
      line-height: 28px;
    }
  }
}
/* AI跟进记录 抽屉顶部 */
.drawer-header {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-shrink: 0;
  width: 100%;
  height: 56px;
  padding: 0 16px;
  border-bottom: 1px solid #EBEDF0;
  background: #FFF;
  color: var(--primary-black-color-600);
  font-size: 16px;
  font-weight: 600;
  text-align: left;
}
/* AI跟进记录 抽屉顶部按钮 */
.header-button {
  height: auto;
  /* margin-left: 24px; */
  margin-left: auto;
  padding: 10px 8px;
  font-size: 13px;
}
</style>
