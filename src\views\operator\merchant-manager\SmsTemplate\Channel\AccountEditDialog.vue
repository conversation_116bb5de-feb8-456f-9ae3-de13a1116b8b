<template>
  <el-dialog
    :model-value.sync="visible"
    width="1000px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="props.isCheck ? undefined : channelRules"
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
        label-width="80px"
        scroll-to-error
        :disabled="props.isCheck"
        ref="editRef"
      >
        <el-form-item v-if="props.type !== 'smsAccountNumbers'" label="运营商：" prop="smsServiceProvider">
          <el-select
            v-model="editData.smsServiceProvider"
            placeholder="请选择运营商"
            style="width: 100%;"
            :disabled="props.type==='cityCodes'"
            @change="handleServiceProviderChange"
          >
            <el-option
              v-for="operatorItem in props.serviceProviderList || []"
              :key="operatorItem.value"
              :label="operatorItem.name"
              :value="operatorItem.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="props.type !== 'smsAccountNumbers'" :label-width="1" prop="cityCodes">
          <CitySettingBox
            :taskRestrictData="scopeData"
            :selectedOperatorList="selectedOperatorList"
            :allOperatorList="selectedOperatorList"
            :readonly="props.isCheck"
            needUnknown
            @update:data="handleCityUpdate"
          />
        </el-form-item>
        <el-form-item v-if="props.type !== 'cityCodes'" label="账号组：" prop="smsAccountNumbers">
          <SelectBox
            v-if="!props.isCheck"
            v-model:selectVal="editData.smsAccountNumbers"
            @update:select-val="handleUpdateSmsAccountNumbers"
            :options="accountListByProvidor||[]"
            name="smsAccountName"
            val="smsAccountNumber"
            placeholder="请选择需要添加的账号（多选）"
            filterable
            class="tw-w-full"
            multiple
            clearable
          >
          </SelectBox>
        </el-form-item>
      </el-form>
      <el-table
        v-show="props.type !== 'cityCodes'"
        :data="selectedList || []"
        id="account-draggable-table"
        :header-cell-style="tableHeaderStyle"
      >
        <el-table-column v-if="!props.isCheck" fixed="left" align="center" label="优先级" width="70">
          <div class="handle tw-cursor-pointer"><el-icon ><Switch /></el-icon></div>
        </el-table-column>
        <el-table-column align="left" fixed="left" prop="smsAccountName" label="账号名称" width="160" show-overflow-tooltip />
        <el-table-column align="left" prop="smsAccountNumber" label="账号编号" width="160"/>
        <el-table-column align="left" prop="enableStatus" label="启用状态" min-width="80" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        <span v-if="row.enableStatus===SmsAccountStatusEnum['启用']" class="status-box green-status tw-text-center">启用</span>
        <span v-else-if="row.enableStatus===SmsAccountStatusEnum['停用']" class="status-box orange-status tw-text-center">停用</span>
        <span v-else class="status-box gray-status tw-text-center">{{ row.enableStatus }}</span>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="secondIndustries" label="适用行业" min-width="100" show-overflow-tooltip />
    <el-table-column align="left" prop="smsAccountBusinessType" label="适用业务" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        <span v-if="row.smsAccountBusinessType===SmsAccountBusinessTypeEnum['群发']">群发</span>
        <span v-else-if="row.smsAccountBusinessType===SmsAccountBusinessTypeEnum['挂短']">挂短</span>
        <span v-else>{{ row.smsAccountBusinessType }}</span>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="returnTimeout" label="回执超时时间" min-width="110" show-overflow-tooltip />
    <el-table-column align="left" prop="sendDelay" label="发送延迟" min-width="100" show-overflow-tooltip />
    <el-table-column align="left" prop="singleSubmitLimit" label="单次提交上限" min-width="110" show-overflow-tooltip />
    <el-table-column align="left" prop="singleDaySubmitLimit" label="单日提交上限" min-width="110" show-overflow-tooltip />
    <el-table-column align="left" prop="billingCycle" label="计费周期" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        {{ row.billingCycle ? row.billingCycle + '字' : '-' }}
      </template>
    </el-table-column>
    <el-table-column align="left" prop="unitPrice" label="单价（元）" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:SmsAccountItem}">
        {{ row.unitPrice ? row.unitPrice + '元/条' : '-' }}
      </template>
    </el-table-column>
        <el-table-column v-if="props.isCheck"  align="center" fixed="right" label="临停状态" width="90">
          <template v-slot="{ row }">
            <el-switch
              v-model="row.tempPending"
              inline-prompt
              active-text="已临停"
              inactive-text="未临停"
              @click="changeTempPending(row)"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="props.isCheck" align="center" fixed="right" label="挂起状态" width="90">
          <template v-slot="{ row }">
            <el-switch
              v-model="row.pending"
              inline-prompt
              active-text="已挂起"
              inactive-text="未挂起"
              @click="changePending(row)"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="!props.isCheck" align="right" fixed="right" label="操作" min-width="80">
          <template v-slot="{ row }">
            <el-button 
              type="danger"
              @click="delAction(row)"
              link
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button v-if="props.isCheck" type="primary" @click="goEdit">
          <el-icon><SvgIcon name="edit" color="#fff"></SvgIcon></el-icon>
          <span>编辑</span>
        </el-button>
        <el-button @click="cancel" :icon="CloseBold">{{ props.isCheck ? '关闭' : '取消'}}</el-button>
        <el-button v-if="!props.isCheck" :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive, onUnmounted, defineAsyncComponent, toRaw, nextTick } from 'vue'
import type { FormInstance, } from 'element-plus'
import SelectBox from '@/components/SelectBox.vue'
import { onBeforeRouteLeave } from 'vue-router'
import { SmsAccountGroupItem } from '@/type/merchant'
import { CloseBold, Select, Switch } from '@element-plus/icons-vue'
import {
  SmsAccountItem,
  SmsAccountServiceProviderEnum,
  SmsAccountBusinessTypeEnum,
  SmsAccountStatusEnum,
} from '@/type/sms'
import { SmsAccountOrigin, channelRules } from './constant'
import { tableHeaderStyle } from '@/assets/js/constant'
import { merchantSmsChannelModel } from '@/api/merchant'
import { pickAttrFromObj } from '@/utils/utils'
import { supplierOperatorMap, supplierOperatorEnum } from '@/assets/js/map-supplier'
import { RestrictModal, OperatorEnum, RestrictModalOrigin, } from '@/type/common'
import Confirm from '@/components/message-box'
import { to } from 'await-to-js'
import { ElMessage } from 'element-plus'
import Sortable from "sortablejs";
import { trace } from '@/utils/trace'

// 组件性能消耗较大，动态引入
const CitySettingBox = defineAsyncComponent({
  loader:() => {
    return import('@/components/CitySettingBox.vue')
  }
})

// props和emits
const emits = defineEmits(['confirm', 'update:visible', 'update:isCheck'])
const props = defineProps<{
  visible: boolean,
  data: SmsAccountGroupItem | null,
  isCheck: boolean,
  type: 'new' | 'cityCodes' | 'smsAccountNumbers'
  serviceProviderList: {name: string, value: SmsAccountServiceProviderEnum}[],
  accountList: SmsAccountItem[],
  smsTemplateId: number
}>();

const loading = ref(false)
const title = computed(() => `${props.isCheck ? '查看' : '编辑'}${props.type !== 'cityCodes' ? '运营地区组' : '短信账号组'}`)

/** 变量 */
const visible = ref(false)
const selectedList = ref<SmsAccountItem[] | null>([]) // 已选择的线路（外部传入）
const accountListByProvidor = computed(() => { return props.accountList?.filter(item => item.serviceProvider === editData.smsServiceProvider || editData.smsServiceProvider === SmsAccountServiceProviderEnum['未知']) })
const editData = reactive<SmsAccountGroupItem>(new SmsAccountOrigin(props.smsTemplateId)) // 表格数据
const editRef = ref<FormInstance  | null>(null) // 表格ref

/** 账号列表拖拽 */
const sortableDom = ref<Sortable | null>(null)
const initDraggableTable = async () => {
  await nextTick()
  const ele: HTMLElement | null = document.querySelector('#account-draggable-table .el-table__body tbody')
  !sortableDom.value && (sortableDom.value = ele && Sortable.create(
    ele, {
    animation: 300,
    sort: !props.isCheck,
    handle: ".handle",
    onEnd: async (evt) => {
      if (!selectedList.value) return
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      const currRow = selectedList.value.splice(oldIndex, 1)[0];
      selectedList.value.splice(newIndex, 0, currRow);
      const data = toRaw(selectedList.value)
      selectedList.value = []
      await nextTick()
      selectedList.value = data
      editData.smsAccountNumbers = selectedList.value?.map(item => item.smsAccountNumber!) || []
    },
  }))
}

const handleUpdateSmsAccountNumbers = () => {
  selectedList.value = accountListByProvidor.value?.filter(item => item.smsAccountNumber && editData.smsAccountNumbers?.includes(item.smsAccountNumber))
}
const delAction = (row: SmsAccountItem) => {
  selectedList.value = selectedList.value?.filter(item => item.smsAccountNumber !== row.smsAccountNumber) || []
  editData.smsAccountNumbers = selectedList.value?.map(item => item.smsAccountNumber!) || []
}

const handleServiceProviderChange = () => {
  if (!editData.smsServiceProvider) return
  const obj: Record<SmsAccountServiceProviderEnum, OperatorEnum> = {
    [SmsAccountServiceProviderEnum['中国移动']]: OperatorEnum['移动'],
    [SmsAccountServiceProviderEnum['中国联通']]: OperatorEnum['联通'],
    [SmsAccountServiceProviderEnum['中国电信']]: OperatorEnum['电信'],
    [SmsAccountServiceProviderEnum['未知']]: OperatorEnum['未知'],
  }
  selectedOperatorList.value = [obj[editData.smsServiceProvider]]
  initScopeData()
  // 运营商若调整，已选择的短信账号必然不符合支持运营商，直接清空，未选择的运营商重新匹配
  selectedList.value = []
  editData.smsAccountNumbers = []
}


/** 地区组 */
const selectedOperatorList = ref<(OperatorEnum)[]>([])
const scopeData = reactive<RestrictModal>(new RestrictModalOrigin())
const initScopeData = () => {
  Object.assign(scopeData, new RestrictModalOrigin());
  const provinceCodes = new Set<string>([]);
  (editData.cityCodes || []).map(item => {
    provinceCodes.add(item.slice(0, 2) + '0000')
  })
  scopeData[supplierOperatorMap.get(editData.smsServiceProvider as unknown as supplierOperatorEnum)?.province as keyof RestrictModal] = [...provinceCodes]?.join(',') || undefined
  scopeData[supplierOperatorMap.get(editData.smsServiceProvider as unknown as supplierOperatorEnum)?.city as keyof RestrictModal] = editData.cityCodes?.join(',') || undefined
}
const handleCityUpdate = (data: RestrictModal, operators: (OperatorEnum)[]) => {
  Object.assign(scopeData, data)
  selectedOperatorList.value = operators
  editData.cityCodes = scopeData[supplierOperatorMap.get(editData.smsServiceProvider as unknown as supplierOperatorEnum)?.city as keyof RestrictModal]?.split(',') || []
}
/** 地区组 结束 */


/** 查看状态 - 挂起当前短信账号 */
const changeTempPending = async (row: SmsAccountItem & {tempPending: boolean}) => {
  Confirm({
    text: `<p>您确定要${row.tempPending? '临停' : '取消临停'}【${row?.smsAccountName || ''}】?</p>`,
    type: 'warning',
    title: `${row.tempPending ? '临停' : '取消临停'}确认`,
    confirmText: `${row.tempPending ? '临停' : '确认'}`
  }).then(async () => {
    const params = {
      templateId: props.smsTemplateId!,
      status: row.tempPending!,
      smsAccountNumber: row.smsAccountNumber!,
      smsAccountGroupId: editData.id!,
    }
    trace({
      page: `商户管理-${row.tempPending? '临停' : '取消临停'}【${row?.smsAccountName || ''}】`,
      params: params
    })
    await merchantSmsChannelModel.changeSmsChannelAccountStatus(params)
   ElMessage.success(`${row.tempPending? '临停' : '取消临停'}成功`)
   emits('confirm')
  }).catch(() => {
    row.tempPending  = !row.tempPending
  })
}
/** 查看状态 - 挂起短信账号 */
const changePending = async (row: SmsAccountItem) => {
  Confirm({
    text: `<p>您确定要${ row.pending? '挂起' : '取消挂起'}【${row?.smsAccountName || ''}】?</p>`,
    type: 'warning',
    title: `${row.pending ? '挂起' : ('取消挂起')}确认`,
    confirmText: `${row.pending ? '挂起' : '确认'}`
  }).then(async () => {
    const params = {
      status: row.pending!,
      smsAccountNumber: row.smsAccountNumber!,
    }
    trace({
      page: `商户管理-${ row.pending? '挂起' : '取消挂起'}【${row?.smsAccountName || ''}】`,
      params: params
    })
    await merchantSmsChannelModel.changeSmAccountStatus(params)
   ElMessage.success(`${row.pending ? '挂起' : '确认'}成功`)
   emits('confirm')
  }).catch(() => {
    row.pending  = !row.pending
  })
}


const goEdit = () => {
  emits('update:isCheck', false)
}
// 弹窗-取消
const cancel = () => {
  Object.assign(editData, new SmsAccountOrigin(props.smsTemplateId))
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}
// 弹窗-确认操作
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      const params = {
        smsAccountGroup: pickAttrFromObj(editData, [
          'smsAccountNumbers', 'id', 'cityCodes', 'smsServiceProvider', 'tenantSmsTemplateId'
        ]),
        templateId: props.smsTemplateId
      }
      loading.value = true
      trace({
        page: `商户管理-${editData.id ? '编辑' : '创建'}短信通道账号组`,
        params: params
      })
      const [err] = await to(merchantSmsChannelModel.saveSmsChannelAccountGroup(params))
      loading.value = false
      if (err) return
      emits('confirm')
      emits('update:visible', false)
    }
  })
}

/** watch */ 
// 监听visible等组件入参
watch(() => props.visible, () => {
  visible.value = props.visible
  if (props.visible) {
    Object.assign(editData, props.data ? JSON.parse(JSON.stringify(props.data)) : new SmsAccountOrigin(props.smsTemplateId))
    editData.smsServiceProvider = props.data?.smsServiceProvider || props.serviceProviderList[0].value
    handleServiceProviderChange()
    selectedList.value = JSON.parse(JSON.stringify(props.data?.smsAccountGroupEntities?.map(item => ({
      ...item.smsAccount,
      tempPending: item.pending,
    })) || []))
    editData.smsAccountNumbers = props.data?.smsAccountNumbers || []
    initDraggableTable()
    editRef.value && editRef.value.clearValidate()
  }
})

const clearAllData = () => {
  Object.assign(editData, new SmsAccountOrigin(props.smsTemplateId))
  sortableDom.value?.destroy()
  sortableDom.value = null
}
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    .el-form-item__content {
      align-items: flex-start;
      justify-content: flex-start;
    }
    &:first-child {
      margin-top: 14px;
    }
    &.muti-items {
      margin-top: 0;
      width: 320px;
      &:last-child {
        margin-left: 20px;
      }
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-table {
  font-size: var(--el-font-size-base);
}
</style>
