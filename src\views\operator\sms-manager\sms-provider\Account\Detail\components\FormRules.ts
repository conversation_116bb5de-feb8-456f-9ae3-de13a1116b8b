import {
  SmsAccountBusinessTypeEnum,
  SmsAccountStatusEnum,
  SmsCmppProtocolVersionEnum,
  SmsProtocolEnum
} from '@/type/sms'

export const accountDetailFormRules = {
  account: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      !value ? callback(new Error('所属商户主账号不能为空')) : callback()
    }
  }],
  smsAccountName: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      !value ? callback(new Error('账号名称不能为空')) : callback()
    }
  }],
  enableStatus: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      (!Object.values(SmsAccountStatusEnum).includes(value)) ? callback(new Error('启用状态不正确')) : callback()
    }
  }],
  pending: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      (typeof value !== 'boolean') ? callback(new Error('挂起状态不正确')) : callback()
    }
  }],
  smsProtocol: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      (!Object.values(SmsProtocolEnum).includes(value)) ? callback(new Error('对接协议不正确')) : callback()
    }
  }],
  version: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      (!Object.values(SmsCmppProtocolVersionEnum).includes(value)) ? callback(new Error('协议版本不正确')) : callback()
    }
  }],
  connectAddress: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      !value ? callback(new Error('对接地址不能为空')) : callback()
    }
  }],
  connectPort: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      (typeof value !== 'number') ? callback(new Error('对接端口不能为空')) : callback()
    }
  }],
  connectAccount: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      !value ? callback(new Error('账号不能为空')) : callback()
    }
  }],
  password: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      !value ? callback(new Error('密码不能为空')) : callback()
    }
  }],
  isReturn: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      (typeof value !== 'boolean') ? callback(new Error('是否回执不正确')) : callback()
    }
  }],
  returnTimeout: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      (typeof value !== 'number') ? callback(new Error('回执超时时间不能为空')) : callback()
    }
  }],
  sendDelay: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      (typeof value !== 'number') ? callback(new Error('发送延迟不能为空')) : callback()
    }
  }],
  secondIndustries: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      !value?.length ? callback(new Error('适用行业不能为空')) : callback()
    }
  }],
  smsAccountBusinessType: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      (!Object.values(SmsAccountBusinessTypeEnum).includes(value)) ? callback(new Error('适用业务不正确')) : callback()
    }
  }],
  disableTimeSlots: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      callback()
    }
  }],
  billingCycle: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      value !== 70 ? callback(new Error('计费方式不正确')) : callback()
    }
  }],
  unitPrice: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      typeof value === 'number' ? callback() : callback(new Error('短信单价不能为空'))
    }
  }],
}
