<template>
   <div class="tw-relative tw-flex tw-items-center tw-h-full tw-w-full">
    <div class="chart-title">
      <span>{{ props.title }}</span>
      <TooltipBox v-if="props.tooltipContent && props.tooltipContent?.length>0" :title="props.title" :num="1" :content="props.tooltipContent" :useSlot="true">
        <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
      </TooltipBox>
      
      
    </div>
    <div class="tw-absolute tw-right-[4px] tw-top-[10px] tw-z-[10]">
      <slot></slot>
    </div>
    <div id="funnel-chart-container" ref="chartDom" :style="{width: ('100%'), height: '100%', margin: 'auto'}">
      
    </div>
    <div class="tw-flex tw-w-full tw-h-[74%] tw-justify-around tw-flex-col tw-absolute tw-items-center tw-top-[16%] tw-bottom-[10%] tw-left-0">
      <div v-for="item in props.data" class="tw-relative">
        <div :class="!!item.rate ? '' : 'tw-hidden'" class="funnel-aside-rect">
          {{ formatNumber(item.rate || 0, 1) + '%' }}
        </div>
        <div class="funnel-aside-data">
          {{ item.value || 0 }}
        </div>
      </div>
    </div>
    <div v-if="!(props.data?.length>0) || !(props.data?.filter(item => !!item.value).length>0)" description="暂无数据" class="tw-absolute tw-left-0 tw-top-0 tw-w-full tw-h-full tw-z-10 tw-bg-white tw-flex tw-justify-center tw-items-center">
      <el-empty />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onDeactivated, onActivated, onMounted, onUnmounted, ref, watch, } from 'vue'
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { formatNumber } from '@/utils/utils'
import TooltipBox from '@/components/TooltipBox.vue'
import { useGlobalStore } from '@/store/globalInfo'

const props = defineProps<{
  data: { name: string, value: number, rate?: number }[],
  title: string,
  tooltipContent?: string[],
}>()

// 图表DOM
const chartDom = ref<HTMLElement | null>(null)
// 图表实例化对象
let myChart: ECharts | null = null

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

// 若图表数据更新，则重新绘制图表
watch(
  () => props.data,
  () => {
    if (chartDom.value) {
      init()
    }
  }, {
    immediate: true,
    deep: true,
  }
)

/**
 * 组件挂载后
 */
onMounted(() => {
  if (chartDom.value) {
    // 立马绘制图表
    init()
    window.addEventListener('resize', resize)
  }
})
onActivated(() => {
  if (chartDom.value) {
    // 立马绘制图表
    init()
  }
})

/**
 * 组件离开后
 */
onDeactivated(() => {
  window.removeEventListener('resize', resize)
  myChart?.clear()
})

/**
 * 组件卸载后
 */
onUnmounted(() => {
  window.removeEventListener('resize', resize)
  chartDom.value = null
  myChart?.dispose()
  myChart = null
})

/**
 * 重新绘制图表的规格尺寸
 */
const timer = ref<ReturnType<typeof setTimeout> | null>(null)
const resize = () => {
  timer.value && clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    if(myChart) {
      myChart.resize()
    }
    timer.value = null
  }, 200)
}

/**
 * 绘制图表
 */
const getMinSize = computed(() => {
  let minSize: number = 0.15
  props.data.reduce((previousValue, currentItem) => {
    if (previousValue) {
      minSize = Math.min((currentItem?.value || 0) / previousValue, minSize)
    }
    return currentItem.value
  }, 0)
  return minSize * 100 + '%'
})
const init = () => {
  // 如果DOM存在并且实例化对象未初始化
  if (chartDom.value && !myChart) {
    // 初始化图表对象
    myChart = echarts.init(chartDom.value)
  }

  // 设置图表参数
  const option = {
    tooltip: {
      trigger: 'item',
      show: false,
      padding: 8,
      borderWidth: 0,
      borderColor: 'none',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      formatter: function (params: {
        data: {name: string, value: number}
      }) {
        const str1 = (params.data.name.includes('率') || params.data.name.includes('占比')) ? formatNumber(params.data.value * 100, 2) + '%' : formatNumber(params.data.value, 2)
        return `
        <div class="tw-text-white tw-text-p-[13px] tw-flex tw-flex-col tw-leading-[18px] ">
          <div class="tw-text-left tw-truncate">${params.data.name}：${str1 || ''}</div>
        </div>
        `
      }
    },
    // grid: {top: 'middle',},
    color: ['#165DFF', '#39F', '#3BF', '#3DF',],
    series: [
      {
        name: props.title,
        type: 'funnel',
        top: '16%',
        right: '17%',
        left: '17%',
        bottom: '10%',
        funnelAlign: 'center',
        minSize: getMinSize.value,
        height: '74%',
        // 既不降序descending，也不升序ascending，保持组件提供的数据顺序即可
        sort: 'none',
        gap: 2,
        label: {
          show: true,
          color: '#000',
          position: 'outside',
          formatter: (val: {data: {name: string, value: number}}) => {
            // return `{a|${val.data?.value || ''}}{b|${val.data?.name || ''}}`
            return `{b|${val.data?.name || ''}}`
          },
          rich: {
            a: {
              fontSize: isMobile ? 12 : 24,
              fontWeight: 700,
              lineHeight: 28,
              height: 28,
            },
            b: {
              fontSize: isMobile ? 10 : 14,
              fontWeight: 600,
              lineHeight: 22,
              height: 28,
              padding: [4, 0, 0, 4],
            },
          }
        },
        labelLine: {
          length: isMobile ? 10 :30,
          lineStyle: {
            width: 1,
            type: 'solid',
            color: '#000',
            dashOffset: 5,
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: props.data,
      }
    ]
  };

  // 刷新图表
  if (option && myChart) {
    myChart.clear()
    myChart.setOption(option)
    myChart.resize()
  }
}
</script>

<style scoped lang="postcss">
.funnel-aside-rect {
  position: absolute;
  left: 50%;
  top: -50%;
  transform: translate(-50%, -50%);
  font-weight: 700;
  font-size: 14px;
  color: var(--el-color-primary);
  background-color: #fff;
  width: 54px;
  height: 24px;
  -webkit-clip-path: polygon(100% 0, 100% 60%, 50% 100%, 0 60%, 0 0);
  clip-path: polygon(100% 0, 100% 60%, 50% 100%, 0 60%, 0 0);
}
.funnel-aside-data {
  /* position: absolute; */
  height: 25px;
  left: 50%;
  font-weight: 700;
  line-height: 28px;
  /* transform: translate(-50%, -50%); */
  font-size: 24px;
  color: #fff;
  text-shadow: -1px -1px 0 #666, 1px 0 0 #666, 0 1px 0 #666, -1px 1px 0 #666; /* 阴影效果，模拟边框 */
}
</style>