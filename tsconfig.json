{
  // 继承基础配置
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    // ===== 编译目标与模块系统 =====
    "target": "esnext",                    // 编译目标：使用最新的 ECMAScript 特性
    "module": "esnext",                    // 模块系统：使用最新的 ES 模块语法
    "moduleResolution": "node",            // 模块解析策略：使用 Node.js 风格的模块解析
    "lib": ["esnext", "dom"],              // 包含的库文件：最新 ES 特性 + DOM API

    // ===== 模块导入导出 =====
    "esModuleInterop": true,               // 启用 ES 模块与 CommonJS 模块的互操作性
    "isolatedModules": true,               // 确保每个文件都可以独立编译（适用于 Babel 等工具）
    "resolveJsonModule": true,             // 允许导入 JSON 文件作为模块
    "verbatimModuleSyntax": false,          // 替代已废弃的 importsNotUsedAsValues 和 preserveValueImports

    // ===== 类型检查与声明 =====
    "strict": true,                        // 启用所有严格类型检查选项
    "noImplicitAny": false,                // 允许隐式 any 类型（覆盖 strict 模式的设置）
    "skipLibCheck": true,                  // 跳过声明文件的类型检查（提高编译速度）
    "useDefineForClassFields": true,       // 使用 define 语义定义类字段（符合 ES 标准）
    "noUncheckedIndexedAccess": true,  // 允许直接访问数组索引

    // ===== 类型声明文件 =====
    "typeRoots": [                         // 类型声明文件的根目录
      "node_modules/@types",               // 默认的 @types 包目录
      "src/types"                          // 项目自定义类型目录
    ],
    "types": ["node"],                     // 包含的类型声明：Node.js 类型

    // ===== 路径解析 =====
    "baseUrl": "./",                       // 基础路径：项目根目录
    "paths": {                             // 路径映射配置
      "@": ["src"],                        // @ 映射到 src 目录
      "@/*": ["src/*"]                     // @/* 映射到 src 下的所有子路径
    },

    // ===== JSX 支持 =====
    "jsx": "preserve",                     // JSX 处理方式：保留 JSX 语法（由其他工具处理）

    // ===== 项目构建 =====
    "composite": true,                     // 启用项目引用和增量编译
    "sourceMap": true,                     // 生成源码映射文件（用于调试）
  },

  // ===== 包含的文件 =====
  "include": [
    "env.d.ts",                            // 环境变量类型声明文件
    "src/**/*",                            // src 目录下的所有文件
    "src/**/*.vue",                        // src 目录下的所有 Vue 单文件组件
    "auto-imports.d.ts",                   // 自动导入的类型声明文件
    "vite.config.*",                       // Vite 配置文件
    "vitest.config.*",                     // Vitest 测试配置文件
    "cypress.config.*"                     // Cypress 测试配置文件
  ]
}
