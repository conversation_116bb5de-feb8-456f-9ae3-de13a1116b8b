<template>
  <el-popover
    :title="props.title"
    :width="200"
    :trigger="props.trigger"
  >
    <el-scrollbar view-class="tw-pr-1" wrap-class="tw-max-h-[40vh]">
      <el-checkbox
        v-model="checkAll"
        :indeterminate="isIndeterminate"
        @change="handleSelectAllChange"
        >全选</el-checkbox
      >
      <el-checkbox-group
        v-model="selectColumns"
        @change="handleColumnsChange"
      >
        <el-checkbox v-for="item in columns" :key="item.value" :label="item.value" :disabled="!!item.disabled">{{ item.name }}</el-checkbox>
      </el-checkbox-group>
    </el-scrollbar>
    <template #reference>
      <el-icon color="var(--primary-blue-color)" :size="16"><SvgIcon name="list" color="var(--primary-blue-color)"/></el-icon>
    </template>
  </el-popover>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch, onMounted, onUnmounted, onActivated } from 'vue'
// 组件入参props
const props = withDefaults(defineProps<{
  title?: string,
  columnsSelected: string[],
  columnsAll: {name: string, value: string, disabled?: boolean}[],
  trigger?: string,
}>(), {
  title: '列名列表',
  trigger: 'click',
})
// emit
const emits = defineEmits([
  'update:columnsSelected',
])
// 全部列名
const columns = ref<{name: string, value: string, disabled?: boolean}[]>(props.columnsAll)
// 当前已选择的列名
const selectColumns = ref<string[]>(props.columnsSelected)
// 是否全选
const checkAll = ref(selectColumns.value.length === columns.value.length)
// 是否Indeterminate状态
const isIndeterminate = computed(() => {
  return selectColumns.value.length > 0 && selectColumns.value.length < columns.value.length
})
// 选中列数据变化，处理函数
const handleColumnsChange = () => {
  if (selectColumns.value.length >= columns.value.length) {
    checkAll.value = true
  } else {
    checkAll.value = false
  }
  emits('update:columnsSelected', selectColumns.value)
}
// 点击全选处理函数
const handleSelectAllChange = () => {
  if (selectColumns.value.length >= columns.value.length) {
    selectColumns.value = columns.value?.filter(item => !!item.disabled)?.map(item => item.value) || []
  } else {
    selectColumns.value = columns.value?.map(item => item.value) || []
  }
}
watch(props, () => {
  columns.value = props.columnsAll || []
  selectColumns.value = props.columnsSelected || []
}, {
  deep: true,
  immediate: true
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.input-number-dom :deep(.el-input-group__append) {
  padding: 0 8px;
}

</style>
