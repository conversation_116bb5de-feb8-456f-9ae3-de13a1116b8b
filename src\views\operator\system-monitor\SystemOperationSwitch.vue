<template>
  <HeaderBox title="运行开关" :can-refresh="true" @refresh="init()"/>
  <el-scrollbar class="tw-min-w-[1080px] sm:tw-min-w-full" view-class="tw-p-[16px] tw-bg-[#f2f3f5]">
    <div class="tw-flex tw-w-full tw-mb-[8px] tw-mt-[16px] sm:tw-mb-[4px] sm:tw-mt-[8px] sm:tw-px-[6px]">
      <RadioButtonBox v-model:active="viewType" :list="viewTypeList" @update:active="init()"/>
    </div>
    <!-- engine设置 -->
    <div v-if="viewType===viewTypeList[0]" v-loading="loading1" class="card-box tw-flex-col">
      <div class="tw-flex tw-justify-between tw-w-full tw-mb-[12px]">
        <div class="title-normal tw-mb-[16px]">
          <span>engine设置</span>
          <el-tooltip content="刷新engine设置" placement="right" :show-after="500">
            <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updateEngineData()">
              <SvgIcon name="reset" color="inherit"/>
            </el-icon>
          </el-tooltip>
        </div>
        <div class="tw-flex">
          <el-button  size="small" type="primary" @click="fwBatchVisible=true">防火墙批量操作</el-button>
          <el-button  size="small" type="primary" @click="batchEditThreshold">批量调整阈值</el-button>
          <el-button v-if="tableData1.findIndex(item => item.status === 'stop')>-1" size="small" type="primary" @click="startAllEngine">全部开启</el-button>
          <el-button v-if="tableData1.findIndex(item => item.status === 'start')>-1" size="small" type="danger" @click="stopAllEngine">全部停止</el-button>
        </div>
      </div>
      <el-table
        ref="engineRef"
        :data="tableData1"
        :header-cell-style="tableHeaderStyle"
        stripe
        class="tw-flex-grow"
        row-key="ip"
        @sort-change="handleSortChange"
      >
        <el-table-column width="40" fixed="left" align="left" type="selection"></el-table-column>
        <el-table-column property="ip" label="engine" sortable align="left" min-width="140" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #header="{ column }">
            <div class="tw-flex tw-items-center tw-justify-start">
              <span>{{column.label}}</span>
              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="name" label="名称" sortable align="left" min-width="70" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #header="{ column }">
            <div class="tw-flex tw-items-center tw-justify-start">
              <span>{{column.label}}</span>
              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="status" label="状态" sortable align="center" width="80" :formatter="formatterEmptyData">
          <template #header="{ column }">
            <div class="tw-flex tw-items-center tw-justify-center">
              <span>{{column.label}}</span>
              <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
                <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
                <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
              </div>
            </div>
          </template>
          <template #default="{ row }">
            <el-switch
              v-if="['start', 'stop'].includes(row.status)"
              size="small"
              v-model="row.status"
              inline-prompt
              active-value="start"
              inactive-value="stop"
              active-text="已开启"
              inactive-text="已停止"
              @change="handleEngineSwitch(row)"
            />
            <span v-else>未知状态</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="right" width="120">
          <template #default="{ row }">
            <el-button type="primary" link @click="editThreshold([row.name])">调整阈值</el-button>
            <el-button type="primary" link @click="resetEngine(row)">重置</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- XXL设置 -->
    <div v-if="viewType===viewTypeList[1]" v-loading="loading2" class="card-box tw-flex-col tw-mt-[12px]">
      <div class="tw-flex tw-w-full tw-mb-[12px]">
        <div class="title-normal tw-mb-[16px]">
          <span>XXL设置</span>
          <el-tooltip content="刷新XXL设置" placement="right" :show-after="500">
            <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updateXxlData()">
              <SvgIcon name="reset" color="inherit"/>
            </el-icon>
          </el-tooltip>
        </div>
      </div>
      <el-table
        :data="tableData2"
        :header-cell-style="tableHeaderStyle"
        stripe
        class="tw-flex-grow"
        row-key="id"
      >
        <el-table-column property="executorHandler" label="name" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
        <!-- <el-table-column property="jobDesc" label="desc" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column> -->
        <el-table-column property="triggerStatus" label="status" align="center" width="90" :formatter="formatterEmptyData">
          <template #default="{ row }">
            <el-switch
              v-if="(row.triggerStatus??-1)!==-1"
              v-model="row.triggerStatus"
              inline-prompt
              :active-value="1"
              :inactive-value="0"
              active-text="已开启"
              inactive-text="已停止"
              @change="updateXxlStatus(row)"
            />
            <span v-else>未知状态</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- CAPS设置 -->
    <div v-if="viewType===viewTypeList[1]" v-loading="loading3" class="card-box tw-flex-col tw-mt-[12px]">
      <div class="tw-flex tw-w-full">
        <div class="title-normal">
          <span>CAPS设置</span>
          <!-- <el-tooltip content="刷新CAPS设置" placement="right" :show-after="500">
            <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updateXxlData()">
              <SvgIcon name="reset" color="inherit"/>
            </el-icon>
          </el-tooltip> -->
        </div>
        <div class="tw-flex tw-justify-end tw-w-full">
          <el-button size="small" type="primary" @click="resetCaps">重置CAPS</el-button>
        </div>
      </div>
     
    </div>
    <!-- FS监控 -->
    <FsStatistic v-if="viewType===viewTypeList[2]" v-model:needUpdate="needUpdate4"/>
    <!-- 推送比设置 -->
    <div v-if="viewType===viewTypeList[3]" v-loading="loading2" class="card-box tw-flex-col tw-mt-[12px]">
      <div class="tw-flex tw-w-full tw-mb-[12px]">
        <div class="title-normal tw-mb-[16px]">
          <span>推送比设置</span>
          <el-tooltip content="刷新推送比设置" placement="right" :show-after="500">
            <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updateRadio()">
              <SvgIcon name="reset" color="inherit"/>
            </el-icon>
          </el-tooltip>
        </div>
        <div class="tw-flex tw-items-center">
          <el-button v-if="!editRadioStatus" size="small" type="primary" @click="editRadioStatus=true">修改推送比</el-button>
          <template v-else>
            <el-button size="small" type="primary" @click="saveRadio">保存</el-button>
            <el-button size="small" type="primary" @click="updateRadio">取消</el-button>
          </template>
        </div>
        
      </div>
      <el-table
        :data="tableData3"
        :header-cell-style="tableHeaderStyle"
        stripe
        class="tw-grow"
        row-key="id"
      >
        <el-table-column property="platform" label="平台" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
        <!-- <el-table-column property="jobDesc" label="desc" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column> -->
        <el-table-column property="radio" label="比例" align="center" width="200" :formatter="formatterEmptyData">
          <template #default="{ row }">
            <el-input-number v-if="editRadioStatus" v-model="row.radio" :min="0" :precision="0"></el-input-number>
            <span v-else>{{ row.radio ?? '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </el-scrollbar>
  <EditFireWallDialog v-model:visible="fwBatchVisible" @confirm="updateEngineData()"/>
  <EditThresholdModal v-model:visible="editThresholdVisible" :data="editEngines||[]" @confirm="updateEngineData()"/>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onMounted, onBeforeUnmount, defineAsyncComponent } from 'vue'
import { formatterEmptyData, handleTableSort } from "@/utils/utils"
import { monitorStatisticModel } from '@/api/monitor-statistic'
import HeaderBox from '@/components/HeaderBox.vue'
import { MonitorTotalInfo, XxlMonitorItem} from '@/type/monitor-statistic'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js';
import RadioButtonBox from '@/components/RadioButtonBox.vue'
import EditFireWallDialog from './EditFireWallDialog.vue'
import EditThresholdModal from './EditThresholdModal.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'

const FsStatistic = defineAsyncComponent({loader: () => {return import('./FsStatistic.vue')}})

const viewTypeList = ['engine设置', 'XXL设置', 'FS监控', '推送比设置']
const viewType = ref(viewTypeList[0])

const loading1 = ref(false) // engine loading
const loading2 = ref(false) // xxl loading
const loading3 = ref(false) // xxl loading
const loading4 = ref(false) // 推送比 loading

/** Engine开始 */
type EngineItem = {
  name: string,
  ip: string,
  status: 'start' | 'stop'
}
const fwBatchVisible = ref(false)
const tableData1 = ref<EngineItem[]>([])
const engineRef = ref()
// 初始化Engine列表
const updateEngineData = async () => {
  loading1.value = true
  const [_, res] = await to(monitorStatisticModel.getEngineList()) as [any, Record<string, 'start' | 'stop'>]
  const arr: EngineItem[] = []
  res && Object.keys(res).map(item => {
    const r = item?.split('#')
    arr.push({
      name: r[1] || '',
      ip: r[0] || '',
      status: res[item] || '',
    })
  })
  tableData1.value = arr.sort((a,b) => (+a.ip.split(':')[1]) - (+b.ip.split(':')[1]))
  loading1.value = false
}
// 排序
const handleSortChange = (params: { prop: string, order: string }) => {
  tableData1.value = handleTableSort(tableData1.value || [], params.prop, params.order)
}
// 更新状态操作
const updateEngineStatus = (engineIps: string[], status: boolean) => {
  if (!engineIps || engineIps.length===0) return ElMessage.warning('请至少选择一个engineIps')
  loading1.value = true
  Confirm({
    text: `您确定要【${status ? '开启' : '停止'}】Engine嘛`,
    type: 'warning',
    title: '操作确认',
    confirmText: status ? '开启' : '停止'
  }).then(async () => {
    status ? await monitorStatisticModel.startEngine({engineIps}) : await monitorStatisticModel.stopEngine({engineIps})
    ElMessage.success('操作成功')
  }).catch(err =>{}).finally(() => {
    loading1.value = false
    updateEngineData()
  })
}
// 全部开启按钮，执行函数
const startAllEngine = () => {
  updateEngineStatus(tableData1.value?.map(item => item.ip) || [], true)
}
// 全部停止按钮，执行函数
const stopAllEngine = () => {
  updateEngineStatus(tableData1.value?.map(item => item.ip) || [], false)
}
// 单条地址开关，执行函数
const handleEngineSwitch = (row: EngineItem) => {
  updateEngineStatus([row.ip], row.status === 'start')
}
// 批量修改engine阈值
const editThresholdVisible = ref(false)
const editEngines = ref<string[]>([])
const batchEditThreshold = () => {
  const list = engineRef.value?.getSelectionRows() as EngineItem[]
  if (list && list.length > 0){
    editThreshold(list.map(item => item.name)||[])
  } else {
    ElMessage.warning('请至少选择一个Engine进行操作')
  }
}
const editThreshold = (rows: string[]) => {
  if (!rows ||  rows.length < 1) {
    return ElMessage.warning('请至少选择一个Engine进行操作')
  }
  editEngines.value = rows || null
  editThresholdVisible.value = true
}
// 重置engine
const resetEngine = async (row: EngineItem) => {
  Confirm({
    title: '确认',
    text: `你确定要重置【${row.name}】的FS嘛？`,
    confirmText: '重置',
  }).then(async () => {
    loading1.value = true
    await monitorStatisticModel.resetEngine({
      aiEngineFlags: row.name
    })
    updateEngineData()
    loading1.value = false
    ElMessage.success('重置成功')
  })
}
/** Engine结束 */

/** Xxl开始 */
const tableData2 = ref<XxlMonitorItem[]>([])
const updateXxlData = async () => {
  loading2.value = true
  const [_, res] = await to(monitorStatisticModel.getXxlList()) as [any, XxlMonitorItem[]]
  tableData2.value = res || []
  loading2.value = false
}
// 全部开启按钮，执行函数
const updateXxlStatus = (row: XxlMonitorItem) => {
  loading2.value = true
  Confirm({
    text: `您确定要【${row.triggerStatus === 1 ? '开启' : '停止'}】XXL嘛`,
    type: 'warning',
    title: '操作确认',
    confirmText: row.triggerStatus === 1 ? '开启' : '停止'
  }).then(async () => {
    row.triggerStatus === 1 ? await monitorStatisticModel.startXxl({xxlId: row.id})
    : await monitorStatisticModel.stopXxl({xxlId: row.id})
    ElMessage.success('操作成功')
  }).catch(err =>{}).finally(() => {
    updateXxlData()
    loading2.value = false
  })
}
// 重置caps
const resetCaps = () => {
  loading3.value = true
  Confirm({
    text: `您确定要重置CAPS嘛`,
    type: 'warning',
    title: '操作确认',
  }).then(async () => {
    await monitorStatisticModel.resetCaps()
    ElMessage.success('重置成功')
  }).catch(err =>{}).finally(() => {
    updateXxlData()
    loading3.value = false
  })
}
/** Xxl结束 */

/** 推送比设置 */
const tableData3 = ref<{
  platform: string,
  radio: number,
}[]>([])
const updateRadio = async () => {
  loading4.value = true
  const res = await monitorStatisticModel.getRadioList() as string
  tableData3.value = []
  if (res) {
    tableData3.value = res.split(',').map((item, index) => ({
      platform: `第${index + 1}套`,
      radio: +item,
    }))
  }
  editRadioStatus.value = false
  loading4.value = false
}
const editRadioStatus = ref(false)
const saveRadio = async () => {
  loading4.value = true
  await monitorStatisticModel.saveRadio({
    newRadioParam: tableData3.value.map(item => item.radio||0).join(',')
  })
  ElMessage.success('修改成功')
  updateRadio()
  loading4.value = false
}

/** 推送比结束 */

/** FS统计 */
const needUpdate4 = ref(false)
const init = () => {
  switch(viewType.value) {
    case viewTypeList[0]: {
      const list = engineRef.value?.getSelectionRows() as EngineItem[]
      if (!list || list.length == 0){
        updateEngineData()
      }
      break;
    }
    case viewTypeList[1]: updateXxlData();break;
    case viewTypeList[3]: updateRadio();break;
  } 
}

// 【定时更新】每隔一定事件触发刷新，【进入刷新】包括tab切换和浏览器标签切换，触发刷新
const timer = ref() // time1为顶部每5分钟刷新
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    init()
  }
}
// 添加定时器和监听器
const addLoop = () => {
  timer.value && clearInterval(timer.value)
  document.addEventListener('visibilitychange', handleVisibilityChange)
  timer.value = setInterval(() =>  init(), 15 * 1000)
}
// 移除定时器和监听器
const clearLoop = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  document.removeEventListener('visibilitychange', handleVisibilityChange)
}
onMounted(() => {
  addLoop()
})
onBeforeUnmount(() => {
  clearLoop()
})
init()
</script>

<style scoped lang="postcss" type="text/postcss">
  .el-table {
    font-size: var(--el-font-size-base);
    @media screen and (max-width: 600px) {
      font-size: 11px;
    }
    :deep(.cell) {
      padding: 0 8px;
    }
    :deep(.caret-wrapper) {
      display: none;
    }
  }
  .title-normal {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-black-color-600);
    line-height: 20px;
    display: flex;
    width: 100%;
    align-items: center;
    @media screen and (max-width: 600px) {
      font-size: 12px;
    }
  }
</style>