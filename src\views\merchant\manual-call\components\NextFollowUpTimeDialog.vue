<template>
  <el-dialog
    v-model="dialogVisible"
    class="seat-workbench-dialog next-follow-up-time-dialog"
    width="600"
    :z-index="99"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <template #header>
      <div class="form-dialog-header">
        下次跟进时间
      </div>
    </template>

    <!--弹窗主体-->
    <el-scrollbar class="form-dialog-main">
      <div class="form-dialog-content">
        <el-form
          ref="formRef"
          label-width="120px"
          label-position="right"
          :model="form"
          :rules="rules"
        >
          <el-form-item label="下次跟进时间：" prop="nextFollowUpTime">
            <a-date-picker
              v-model:value="form.nextFollowUpTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="选择下次跟进时间"
              allow-clear
              :locale="antdLocaleConfig"
              :show-now="false"
              :presets="nextFollowUpTimePresets"
              :disabled-date="disabledDate"
              @change="onChangeNextFollowUpTime"
            />
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { antdLocaleConfig } from '@/assets/js/constant'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { reactive, ref, watch } from 'vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { seatWorkbenchClueModel } from '@/api/seat'
import { ClueItem } from '@/type/clue'
import dayjs, { Dayjs } from 'dayjs'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  data: ClueItem,
}>()
const emits = defineEmits([
  'close',
  'update'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(() => props.visible, (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      Object.assign(form, formDefault(), props.data)
    }
  }
)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据
const formDefault = (): ClueItem => {
  return {
    nextFollowUpTime: '',
  }
}
// 表单数据
const form: ClueItem = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  nextFollowUpTime: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('下次跟进时间不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 清除表单的校验结果
  formRef.value?.resetFields()
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 清除表单的校验结果
  formRef.value?.resetFields()
}
/**
 * 提交表单
 */
const submit = async () => {
  // 提交节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  try {
    // 处理参数
    const params = {
      clueId: props.data.id ?? -1,
      followUpLogId: props.data.clueFollowUpLogIds?.at(-1) ?? null,
      nextFollowUpTime: form.nextFollowUpTime ?? ''
    }

    // 请求接口
    await seatWorkbenchClueModel.changeNextFollowUpTime(params)
    ElMessage.success('编辑成功')

    // 提示父组件更新
    emits('update')

    // 关闭弹窗
    closeDialog()
  } catch (e) {
  } finally {
    // 提交节流锁解锁
    throttleConfirm.unlock()
  }
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// 下次跟进时间快捷选择列表
const nextFollowUpTimePresets = ref<{ label: string, value: dayjs.Dayjs }[]>([
  { label: '4小时后', value: dayjs().add(4, 'hour') },
  { label: '1天后', value: dayjs().add(1, 'day') },
  { label: '2天后', value: dayjs().add(2, 'day') },
  { label: '1周后', value: dayjs().add(1, 'week') },
])

/**
 * 禁止选择的日期
 * @param {Dayjs} current 选择的日期
 * @returns {boolean} 是否禁用
 */
const disabledDate = (current: Dayjs): boolean => {
  // 禁止选择现在和过去的时间，只允许选择未来的时间
  return current < dayjs().endOf('d').subtract(1, 'd')
};
/**
 * 修改下次跟进时间
 * @param {Date} val Date对象
 * @param {string} valStr 格式化日期
 */
const onChangeNextFollowUpTime = (val: Date, valStr: string) => {
  if (val && dayjs(val).isBefore(dayjs())) {
    return ElMessage.warning('请选择合适的下次跟进时间!')
  } else {
    form.nextFollowUpTime = valStr
  }
}

// ---------------------------------------- 表单 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
/* 下次跟进时间弹窗 */
.next-follow-up-time-dialog {
  .form-dialog-main {
    padding: 12px 24px;
  }
  .form-dialog-content {
    /* height: 500px; */
    max-height: 70vh;
  }
}
</style>
