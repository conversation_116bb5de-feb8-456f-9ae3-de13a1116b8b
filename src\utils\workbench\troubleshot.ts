import { useSeatInfoStore } from '@/store/seat/seat-info'
import { SeatStatusEnum } from '@/type/seat'
import { checkInAndOutTask } from '@/utils/workbench/related-task'
import { ElMessage } from 'element-plus'

/**
 * 坐席工作台 故障排查
 */
export const workbenchTroubleshoot = {
  // 修复坐席状态
  fixSeatStatus: () => {
    if (!checkSeatStatus([SeatStatusEnum.MANUAL_DIRECT_IDLE, SeatStatusEnum.HUMAN_MACHINE_IDLE])) {
      resetSeatStatus()
    }
  },
}

/**
 * 检查坐席状态
 *
 * 参数传入预期的状态列表，默认是人工直呼和人机协同的空闲状态
 * 如果当前状态不符合预期，则重新签入任务，将坐席状态重置为空闲中
 * @param expectStatusList 预期的状态列表
 * @return boolean 是否符合预期
 */
const checkSeatStatus = (expectStatusList: SeatStatusEnum[] = [SeatStatusEnum.MANUAL_DIRECT_IDLE, SeatStatusEnum.HUMAN_MACHINE_IDLE]): boolean => {
  const seatInfoStore = useSeatInfoStore()
  return expectStatusList.includes(seatInfoStore.seatStatus)
}

/**
 * 重置坐席状态
 */
const resetSeatStatus = () => {
  const seatInfoStore = useSeatInfoStore()
  // 重新签入任务
  checkInAndOutTask(seatInfoStore.seatTaskList).then(() => {
    ElMessage.success('重置坐席状态成功')
  }).catch(() => {
    ElMessage.error('重置坐席状态失败，请尝试手动签入任务')
  })
}
