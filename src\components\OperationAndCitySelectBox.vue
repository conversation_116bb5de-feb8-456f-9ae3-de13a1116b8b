<template>
  <div v-if="props.showOperator" class="item">
    <el-select :model-value="operator" class="tw-w-full" placeholder="运营商" @change="handleOperationChange" clearable>
      <el-option v-for="item in operatorList" :key="item" :label="item" :value="item"/>
    </el-select>
  </div>
  <div class="item">
    <el-select v-model="province" placeholder="省" class="tw-w-full" filterable clearable @change="handleProvinceChange">
      <el-option v-for="item in provinceList || []" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
    </el-select>
  </div>
  <div class="item">
    <el-select :model-value="city" placeholder="市" class="tw-w-full" filterable clearable :filter-method="filterCity" @change="handleCityChange">
      <el-option v-for="item in (cityList||[]).slice(0, cityNum)" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
      <div v-if="cityList && cityList?.length > cityNum" class="tw-py-[2px] tw-z-10 tw-relative">
        <el-button type="primary" link @click="cityNum=cityNum+50">
          加载更多
        </el-button>
      </div>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onUnmounted, } from 'vue'
import { useGlobalStore } from '@/store/globalInfo'
import { ElMessage, } from 'element-plus'
import { OperatorEnum, } from '@/type/common'
import { onBeforeRouteLeave } from 'vue-router';

// 组件入参props
const props = withDefaults(defineProps<{
  operator: string | undefined | null,
  city: string | undefined | null, // 返回的都是code形式
  province: string | undefined | null, // 返回的都是code形式
  showOperator?: boolean, // 是否显示运营商
}>(), {
  showOperator: true,
})
// emit
const emits = defineEmits([
  'update:operator', 'update:city', 'update:province',
])

const operator = ref(props.operator || '')
const city = ref(props.city || '')
const province = ref(props.province || '')

const globalStore = useGlobalStore()

const operatorList = OperatorEnum // 运营商
const provinceList = ref<string[] | null>([]) // 省份列表
const provinceAllMap = ref<{ [key: string]: string[] } | null>({}) // 省份列表对象p形式
const cityList = ref<string[]|null>([]) // 城市列表
const cityNum = ref(20) // 无线滚动，初始显示数量

const filterCity = (val: string = '') => {
  if (!provinceAllMap.value) {
    cityList.value = []
  } else if (!province.value) {
    cityList.value = Object.values(provinceAllMap.value)?.flat().filter(item => !val || item.includes(val)) || []
  } else {
    const keys = Object.keys(provinceAllMap.value)
    const currentProvince = keys.find(item => province.value && item.includes(province.value))
    if (currentProvince) {
      cityList.value = provinceAllMap.value[currentProvince]?.filter(item => !val || item.includes(val)) || []
    } else {
      cityList.value = []
    }
  }
}


const handleOperationChange = (value: string) => {
  emits('update:operator', value)
}
const handleCityChange = (value: string) => {
  emits('update:city', value)
}
const handleProvinceChange = (value: string) => {
  emits('update:province', value)
  filterCity()
}

const init = async () => {
  // 初始化省份城市数据
  if (!globalStore.provinceAllMap || Object.keys(globalStore.provinceAllMap)?.length === 0) {
    await globalStore.getProvinceInfo()
  }
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
}
init()
/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(() => props, () => {
  operator.value = props.operator || ''
  city.value = props.city || ''
  province.value = props.province || ''
}, {deep: true})

onUnmounted(() => {
  provinceAllMap.value = null
  provinceList.value = null
  cityList.value = null
})
onBeforeRouteLeave(() => {
  provinceAllMap.value = null
  provinceList.value = null
  cityList.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.input-number-dom :deep(.el-input-group__append) {
  padding: 0 8px;
}
</style>
