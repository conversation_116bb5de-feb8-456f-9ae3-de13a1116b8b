<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    @close="cancel"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ props.type===1 ? '单个搜索' : '批量搜索' }}</div>
    </template>
    <el-form
      :model="addData"
      :rules="rules"
      label-width="60px"
      ref="addFormRef"
      @submit.native.prevent
    >
      <el-form-item v-if="props.type == 1" label="号码：" prop="phone">
        <el-input
          v-model="addData.phone"
          clearable
          placeholder="请输入号码"
          @keyup.enter="confirm()"
        />
      </el-form-item>
      <el-form-item v-if="props.type == 2" label="号码：" prop="phone">
        <el-input
          v-model="addData.phone"
          type="textarea"
          :autosize="{ minRows: 5, maxRows: 8 }"
          clearable
          @keyup.enter="confirm()"
          placeholder="批量搜索手机号，建议您采用复制粘贴的方式输入，使用“,”号隔开，最多100个号码"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button type="primary" @click="confirm" :icon="Select">搜索</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance, } from 'element-plus'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  type: number; // 1:单个号码；2：多个号码
}>();

const dialogVisible = ref(props.visible)
const rules = {
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
  ],
}

const addData = reactive({phone: ''})
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const addFormRef = ref<FormInstance | null>(null)
const confirm = () => {  
  addFormRef.value && addFormRef.value.validate((valid) => {
    if (valid) {
      const phone = addData.phone.replace(/\s*/g, '')
      emits('confirm', phone)
      cancel()
    }
  })
}

watch(() => props.visible, n => {
  dialogVisible.value = n
  n && (addData.phone = '')
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
:deep(.el-upload-dragger) {
  padding: 16px;
  height: 134px;
  width: 100%;
}
.exceed-dom :deep(.el-upload-dragger){
  border-color: #E54B17;
}
</style>