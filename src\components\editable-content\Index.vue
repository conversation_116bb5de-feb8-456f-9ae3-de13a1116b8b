<template>
  <div class="tw-w-full tw-relative">
    <div v-if="!!props.editable" class="tw-flex tw-justify-between tw-mb-[4px]">
      <div v-if="props.list && props.list.length" size="small" class="tw-flex tw-items-center">
        <el-select v-model="addData.data1" placeholder="请选择变量" style="width:200px">
          <el-option
            v-for="item in props.list"
            :key="item.value"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
        <el-button type="primary" class="tw-ml-[4px]" link size="small" @click="addTag">插入变量</el-button>
      </div>
      <!-- <div>
        <el-button class="tw-ml-[4px]" size="small" link type="primary" @click="addParagraph">新增语句</el-button>
        <el-button v-if="!!props.closeable " class="tw-ml-[4px]" size="small" link type="danger" @click="closeAll">清空全部</el-button>
      </div> -->
    </div>
    <EditorContent :editor="(editor as Editor)"/>
    <div :class="{'character-count': true, 'character-count--warning': editor?.storage.characterCount.characters() >= limitNum}">
      {{ characterCount }} / {{ limitNum }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, onUnmounted, reactive, computed, watch, } from 'vue'
import Placeholder from '@tiptap/extension-placeholder'
import CharacterCount from '@tiptap/extension-character-count'
import StarterKit from '@tiptap/starter-kit';
import { PastePlainText, } from './constant'; 
import { Editor, EditorContent, JSONContent } from '@tiptap/vue-3'
import CustomNode from './CustomNode'; 
import LabelParagraph from './LabelParagraph'
import { ElMessage } from 'element-plus'
import { scriptUnitContent, ContentTypeEnum, CorpusTypeEnum } from '@/type/speech-craft'

const props = defineProps<{
  editable?: boolean,
  content: scriptUnitContent[] | null,
  closeable?: boolean,
  maxLength?: number,
  corpus: {
    corpusType?: CorpusTypeEnum
  },
  needUpdate: boolean,
  list?: {name: string, value: any}[]
}>()
const emits = defineEmits(['update:content', 'update:needUpdate'])

// 限制字数
const limitNum = props.maxLength || 300
const characterCount = computed(() => {
  if (!editor.value) return 0
  const data = editor.value?.getText({
    textSerializers: {
      "customNode": (node) => {
        return node.node?.attrs?.content || ''
      }
    }
  })
  return data?.length || 0 // 鉴于自定义node计数不对，只能转换为纯文本后计算
})

const editor = ref<Editor | null>(null)
const addData = reactive({
  data1: undefined,
})

/** 添加变量 */
const addTag = () => {
  if (!addData.data1) {
    return ElMessage.warning('请选择变量')
  }
  try {
    editor.value?.chain().focus().insertContent(`<CustomNode content="${addData.data1}"></CustomNode>`).run()
  } catch (e) {
    console.log(e);
    ElMessage.error('添加失败')
  }
}
/** 新增语句 */ 
const addParagraph = () => {
  editor.value?.chain().focus('end').insertContent({
    type: 'paragraph',
    attrs: {
      contentId: undefined,
      isPlayed: false,
      deleted: false,
    },
    content: [],
  }).run()
}

// 清空全部
const closeAll = () => {
  editor.value?.commands.clearContent(true)
}
const originContent = ref<JSONContent[]>([])
const initEditableContent = () => {
  originContent.value = (props.content && props.content.length) ? props.content.map(item => {
    return {
      type: 'paragraph',
      attrs: {
        contentId: item.id || undefined,
        isPlayed: item.isPlayed || false,
        corpusType: item.corpusType || props.corpus.corpusType,
      },
      content: item.content ? [
        {
          type: 'text',
          text: item.content || ''
        }
      ] : undefined
    }
  }) : [
    {
      type: 'paragraph',
      attrs: {
        contentId: undefined,
        isPlayed: false,
        corpusType: props.corpus.corpusType,
        deleted: false,
      }
    }
  ]
  destroyEditor()
  editor.value = new Editor({
    extensions: [
      // Document,
      LabelParagraph,
      // 禁用富文本一些格式
      StarterKit.configure({
        history: false,
        bold: false,
        heading: false,
        italic: false,
        strike: false,
        code: false,
        paragraph: false,
        bulletList: false,
        orderedList: false,
        hardBreak: false,
        blockquote: false,
        codeBlock: false,
        dropcursor: false,
        gapcursor: false,
      }),
      CustomNode,
      PastePlainText,
      CharacterCount.configure({
        limit: limitNum,
      }),
      Placeholder.configure({
        placeholder: '请输入，enter键新增语句',
      }),
    ],

    editable: props.editable || false,
    content: {
      type: 'doc',
      content: originContent.value 
    },
    /**
     *  blur 事件处理，获取当前的所有语句数据
     * 
     */
    onBlur({ editor, event }) {
      const data = editor?.getJSON() as JSONContent
      const ids = originContent.value?.map(item => item.attrs?.contentId) || []
      let originArr = originContent.value.filter(v => v.content && v.attrs?.contentId) || []
      let orders = 0
      const res = data.content?.flatMap(item => {
        const content = item?.content?.map(v => v.text).join('') || ''
        if (item.attrs?.contentId && ids.includes(item.attrs?.contentId)) {
          originArr = originArr.filter(v => v.content && v.attrs?.contentId && v.attrs?.contentId !== item.attrs?.contentId)
        }
        if (content || item.attrs?.contentId) {
          orders++
          return [{
            id: item.attrs?.contentId || undefined,
            content: content,
            isPlayed: item.attrs?.isPlayed || false,
            deleted: content ? false : true,
            contentType: ContentTypeEnum['普通文本'],
            corpusType: item.attrs?.corpusType || props.corpus.corpusType,
            orders: orders,
          }]
        } else {
          return []
        }
      })
      
      // 一般来讲此时originArr应该为空，但是为了防止特殊操作就一个语句且删除了又新增清空，应该还原为旧语句id，防止删除并新增了
      if (originArr && originArr.length && res && res.findIndex(item => !item.id) > -1) {
        const index = res?.findIndex(item => !item.id)
        if (index !== undefined && index > -1) {
          const cur = originArr.shift()
          res?.[index] && (res[index].id = cur?.attrs?.contentId)
        }
      }
      emits('update:content', res)
    },
  })
  emits('update:needUpdate', false)
}
watch(() => props.needUpdate, n => {
  n && initEditableContent()
})
onMounted(() => {
  initEditableContent()
})

const destroyEditor = () => {
  if (editor.value && !editor.value.isDestroyed) {
    editor.value.destroy()
    editor.value = null
  }
}

onUnmounted(() => {
  destroyEditor()
})
</script>

<style scoped lang="postcss">
:deep(.tiptap) {
  padding: 12px;
  border-radius: 4px;
  border: 1px solid var(--primary-black-color-200);
  &:focus-visible  {
    outline: 0
  }
  p {
    text-align: justify;
    border: 1px dashed var(--primary-black-color-400);
    border-radius: 2px;
    font-size: 13px;
    line-height: 18px;
    padding: 4px;
    &:not(:first-child) {
      margin-top: 8px;
    }
    .label {
      font-size: 13px;
      color: #313333;
    }
    
  }

  /* Placeholder (on every new line) */
  .is-empty::before {
    font-size: 13px;
    color: var(--el-text-color-placeholder);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }
  .custom-node {
    display: inline-block;
    border-radius: 4px;
    margin: auto 2px;
    padding: 0 2px;
    min-width: 44px;
    height: 22px;
    line-height: 20px;
    font-size: 12px;
    background-color: var(--primary-blue-color);
    color: #fff;
    border: 1px solid #ccc;
    text-align: center;
  }
}
.character-count {
  color: var(--primary-black-color-300);
  font-size: 12px;
  position: absolute;
  right: 8px;
  bottom: -25px;
  &--warning,
  &--warning svg {
    color: var(--primary-red-color);
  }
}
</style>
