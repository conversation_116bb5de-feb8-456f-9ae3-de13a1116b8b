<template>
  <div class="tw-relative tw-p-[12px]">
    <div class="chart-title">
      <span>{{ props.title }}</span>
      <TooltipBox v-if="props.tooltipContent && props.tooltipContent?.length>0" :title="props.title" :num="1" :content="props.tooltipContent" :useSlot="true">
        <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
      </TooltipBox>
      <el-button
        v-if="!!props.canSwitch2Table"
        class="tw-ml-[4px]"
        type="primary"
        link
        @click="handleTypeChange"
      >{{ showType === 0 ? '切换列表' : '切换地图'}}</el-button>
    </div>
    <!-- showType===0 geo地图形式 -->
    <div v-show="showType===0" ref="geoRef" :style="{width: props.width, minHeight: '500px', height: props.height}"></div>
    <!-- showType===1 列表形式 -->
    <el-table
      :data="props.data"
      v-show="showType===1"
      :header-cell-style="tableHeaderStyle"
      :row-style="getRowStyle"
      stripe
      class="tw-grow tw-pt-[42px]"
      row-key="name"
    >
      <el-table-column label="省份" prop="name" align="left" min-width="160" show-overflow-tooltip></el-table-column>
      <el-table-column v-for="item in props.tooltipSort" :prop="'value' + (item + 1)" sortable :key="item" :label="props.legends[item]" align="left" min-width="150" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ 
            testStr(props.legends[item])
              ? formatNumber(row['value' + (item + 1)] * 100, 2) + '%'
              : formatNumber(row['value' + (item + 1)], 2)
          }}
        </template>
      </el-table-column>
    </el-table>
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import china from "@/assets/map/china.json";
import geoCoordMap from "@/assets/map/geoCoordMap"
import { nextTick, onDeactivated, onActivated, onMounted, onUnmounted, shallowRef, watch, ref } from 'vue'
import * as echarts from "echarts";
import { ECharts } from "echarts";
import { formatNumber } from '@/utils/utils'
import { RegionChartItem } from '@/type/task'
import TooltipBox from '@/components/TooltipBox.vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router'
import { testStr } from '@/components/charts/constant'
type EventParams = {
  // 当前点击的图形元素所属的组件名称，
  // 其值如 'series'、'markLine'、'markPoint'、'timeLine' 等。
  componentType: string;
  // 系列类型。值可能为：'line'、'bar'、'pie' 等。当 componentType 为 'series' 时有意义。
  seriesType: string;
  // 系列在传入的 option.series 中的 index。当 componentType 为 'series' 时有意义。
  seriesIndex: number;
  // 系列名称。当 componentType 为 'series' 时有意义。
  seriesName: string;
  // 数据名，类目名
  name: string;
  // 数据在传入的 data 数组中的 index
  dataIndex: number;
  // 传入的原始数据项
  data: RegionChartItem[];
  // sankey、graph 等图表同时含有 nodeData 和 edgeData 两种 data，
  // dataType 的值会是 'node' 或者 'edge'，表示当前点击在 node 还是 edge 上。
  // 其他大部分图表中只有一种 data，dataType 无意义。
  dataType: string;
  // 传入的数据值
  value: number | number[];
  // 数据图形的颜色。当 componentType 为 'series' 时有意义。
  color: string;
};
const emits = defineEmits(['province-change'])
const props = withDefaults(defineProps<{
  data: RegionChartItem[],
  legends?: string[],
  tooltipSort?: number[],
  title?: string,
  width?: string | number,
  height?: string | number,
  visualMapEnabled?: boolean,
  visualMapSingleColor?: boolean,
  tooltipContent?: string[],
  canSwitch2Table?: boolean; // 是否需要切换按钮为列表形式
}>(), {
  title: '地区分布',
  width: '100%',
  height: '800px',
  canSwitch2Table: false,
  tooltipSort: () => [0, 1, 2, 3],
  // 默认图例名
  legends: () => ['接通名单数', '名单接通率', '计划呼叫名单数', '实际呼叫名单数'],
  // 是否显示视觉映射条
  visualMapEnabled: true,
  // 圆圈颜色是否单一
  visualMapSingleColor: false,
})

// 图表DOM
const geoRef = shallowRef<HTMLElement | null>(null)
// 图表实例化对象
let geoChart: ECharts | null = null

const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

const showType = ref(0) // 

// 若图表数据更新，则重新绘制图表
watch(
  () => props.data,
  async () => {
    if (geoRef.value) {
      await initEchart()
      await resize()
    }
  }, {
    immediate: true,
    deep: true
  }
)

/**
 * 组件挂载后
 */
onMounted(() => {
  // ECharts导入地图数据
  echarts.registerMap("china", china as any)

  if (geoRef.value) {
    // 立马绘制图表
    initEchart()
    window.addEventListener('resize', resize)
  }
})
onActivated(() => {
  if (geoRef.value) {
    // 立马绘制图表
    initEchart()
    window.addEventListener('resize', resize)
  }
})
/**
 * 组件离开后
 */
onDeactivated(() => {
  window.removeEventListener('resize', resize)
  geoChart?.off('click')
  geoChart?.off('series')
  geoChart?.clear()
})

/**
 * 组件卸载后
 */
onUnmounted(() => {
  window.removeEventListener('resize', resize)
  geoChart?.off('click')
  geoChart?.off('series')
  geoChart?.dispose()
  geoChart = null
  geoRef.value = null
})
onBeforeRouteLeave(() => {
  window.removeEventListener('resize', resize)
  geoChart?.off('click')
  geoChart?.off('series')
  geoChart?.dispose()
  geoChart = null
  geoRef.value = null
})
/**
 * 重新绘制图表的规格尺寸
 */
const resize = () => {
  geoChart && geoChart.resize()
}

/**
 * 将图表数据转换成地图数据
 * @param data
 */
const convertData = (data: RegionChartItem[]) => {
  const res = [];
  for (let i = 0; i < data.length; i++) {
    const geoCoord = geoCoordMap.find(item => item.name.includes(data[i].name === '未知' ? '台湾省' : data[i].name)) || null
    if (geoCoord) {
      res.push({
        name: data[i].name,
        value: [
          ...(geoCoord.centroid || geoCoord.center || []),
          // 圆圈大小，比如接通数
          data[i].value1 ?? 0,
          // 省份颜色，比如接通率
          data[i].value2 ?? 0,
          // 悬浮展示数据
          data[i].value3 ?? 0,
          // 悬浮展示数据
          data[i].value4 ?? 0,
          data[i].value5 ?? 0,
          data[i].value6 ?? 0,
          data[i].value7 ?? 0,
        ],
        itemStyle: data[i]?.itemStyle ?? {
          areaColor: '#6AA1FF',
          borderColor: '#fff',
        }
      })
      ;
    }
  }
  return res;
};

const getRowStyle = ({row}: {row: RegionChartItem}) => {
  if (row.name === '合计') {
    return {
      fontWeight: 600,
    }
  } else {
    return null
  }
}

/**
 * 绘制图表
 */
const initEchart = async () => {
  // 等数据接收和DOM渲染完毕
  await nextTick()
  // 如果DOM存在并且实例化对象未初始化
  if (geoRef.value && !geoChart) {
    // 初始化图表对象
    geoChart = echarts.init(geoRef.value)
    // @ts-ignore
    geoChart.on('click', 'series', (params: EventParams) => {
      if (params.seriesType === 'effectScatter') {
        emits('province-change', params.name)
      }
    });
  }

  // 获取列表中的最小最大值
  let maxVal = props.data.reduce((previousValue, currentItem) => {
    return currentItem.name !== '合计' ? Math.max(previousValue, currentItem?.value1 || 0) : previousValue
  }, 0)
  const minVal = props.data.reduce((previousValue, currentItem) => {
    return currentItem.name !== '合计' ? Math.min(previousValue, currentItem?.value1 || 0) : previousValue
  }, maxVal)
  // 防止最小最大值都一样
  // min必须严格小于max
  // 即 min < max，等号也不行
  // 不然地图上无法正常显示
  if (minVal >= maxVal) {
    maxVal = minVal + 1
  }
  // 两者差值
  const delta = Math.abs(maxVal - minVal)
  let maxVal2 = props.data.reduce((previousValue, currentItem) => {
    return currentItem.name !== '合计' ? Math.max(previousValue, currentItem?.value2 || 0) : previousValue
  }, 0)
  const minVal2 = props.data.reduce((previousValue, currentItem) => {
    return currentItem.name !== '合计' ? Math.min(previousValue, currentItem?.value2|| 0) : previousValue
  }, maxVal2)
  // 设置图表参数
  const option = {
    // title: {
    //   text: props.title,
    // },
    tooltip: {
      // 数据格式化
      trigger: 'item',
      padding: 8,
      borderWidth: 0,
      position: (point: number[]) => {
        return [point[0] - 150, point[1] + 40];
      },
      borderColor: 'none',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      formatter: (params: {
        name: string,
        seriesType: string,
        seriesIndex: number,
        data: {
          value: number[]
        }
      }) => {
        const strArr = Array.from({length: props.tooltipSort.length}).fill('<div class="tw-text-left tw-truncate">暂无数据</div>')
        let name: string = params.name ?? '未知'
        if (params?.data?.value?.length) {
          for (let i = 0; i < props.tooltipSort.length; i++) {
            const item = props.tooltipSort[i]
            const dataStr = props.legends[item] + '：' + (testStr(props.legends[item])
              ? formatNumber(params.data.value[item + 2] * 100, 2) + '%'
              : formatNumber(params.data.value[item + 2], 2))
            strArr[i] = `<div class="tw-text-left tw-truncate">${dataStr || ''}</div>`
          }
        } else if (params.seriesType === 'map') {
          let data
          if (params.name.slice(0, 2) === '台湾') {
            data = props.data.find(item => ['未知', '台湾'].includes(item.name.slice(0, 2)))
          } else {
            data = props.data.find(item => item.name.slice(0, 2) === params.name.slice(0, 2))
          }
          if (data) {
            name = data.name
            for (let i = 0; i < props.tooltipSort.length; i++) {
              const item = props.tooltipSort[i]
              const dataStr = props.legends[item] + '：' + (testStr(props.legends[item])
              // @ts-ignore
                ? formatNumber(data['value' + (item + 1)] * 100, 2) + '%'
                // @ts-ignore
                : formatNumber(data['value' + (item + 1)], 2))
              strArr[i] = `<div class="tw-text-left tw-truncate">${dataStr || ''}</div>`
            }
          }
        }

        const htmlBefore = `
        <div class="tw-text-white tw-text-p-[13px] tw-flex tw-flex-col tw-leading-[18px] tw-min-w-[300px]">
          <div class="tw-grid tw-grid-cols-2 tw-border-b tw-border-gray-400 tw-pb-[4px]">
            <div class="tw-text-left tw-truncate">省份：${name}</div>
          </div>
          <div class="tw-grid tw-grid-cols-2 tw-gap-x-[16px] tw-mt-[4px]">
        `
        const htmlAfter = `
          </div>
        </div>
        `
        const htmlMiddle = strArr.join('')

        // const htmlMiddle = legendStrList.reduce((previousValue, currentValue, currentIndex) => {
        //   if (props.tooltipShow[currentIndex]) {
        //     return previousValue + currentValue
        //   }
        //   return previousValue
        // }, '')

        return htmlBefore + htmlMiddle + htmlAfter
      }
    },
    // 视觉映射条
    visualMap: {
      show: props.visualMapEnabled ?? true,
      type: 'continuous',
      // min必须严格小于max
      // 即 min < max，等号也不行
      // 不然地图上无法正常显示
      min: minVal2,
      max: maxVal2,
      right: 'right',
      top: 'middle',
      text: ['高', '低'],
      // 取第二组数据，也就是省份散点图
      seriesIndex: 1,
      // 在第二组数据里取第几列
      // 经度 0 维度 1 计划 2 实际 3
      dimension: 3,
      // 颜色范围
      inRange: {
        color: props.visualMapSingleColor ?
          ['#FF7D00'] :
          ['#FFF7E8', '#FFFFCC', '#FFFF99', '#FFCC66', '#FF6600'],
      },
      // 范围标签显示内容
      formatter: (value: number) => {
        return testStr(props.legends[1])
          ? formatNumber((value || 0) * 100, 2) + '%'
          : formatNumber((value || 0), 2);
      },
    },
    geo: {
      map: 'china',
      roam: false, // 不开启缩放和平移
      top: '25%',
      zoom: 1.4, // 视角缩放比例
      label: {
        show: false,
      },
      itemStyle: {
        areaColor: '#6AA1FF',
        borderColor: '#fff',
        // shadowColor: '#d79d3d',
        // shadowBlur: 30,
        // shadowOffsetX: -5,
        // shadowOffsetY: 10,
      },
      emphasis: {
        disabled: true,
        areaColor: '#2a333d',
      }
    },
    series: [
      // 省份地图，显示各省名称
      {
        top: '25%',
        type: 'map',
        map: 'china',
        geoIndex: 1,
        zoom: 1.4,
        label: {
          show: false,
          fontSize: 11,
          color: '#fff'
        },
        emphasis: {
          disabled: true,
          areaColor: '#0f2c70',
          textStyle: {
            color: '#fff',
            fontSize: 11,
          },
        },
        roam: false,
        itemStyle: {
          areaColor: '#6AA1FF',
          borderColor: '#fff',
        },
        data: convertData(props.data),
      },
      // 散点图，显示该省的计划数量和实际数量
      {
        type: 'effectScatter',
        coordinateSystem: 'geo',
        zoom: 2,
        showEffectOn: 'render',
        zlevel: 1,
        symbolSize: function (val: any[]) {
          // 当前值相对整个范围的占比
          const ratio = (val[2] - minVal) / delta
          // 按比例调整标记大小
          return isMobile ? 3 + 10 * ratio : 5 + 35 * ratio
        },
        symbolOffset: [5, 5],
        rippleEffect: {
          period: 5,
          number: 2,
          scale: 2,
          brushType: 'stroke',
        },
        emphasis: {
          scale: true
        },
        label: {
          formatter: '{b}',
          position: 'right',
          offset: [-5, 0],
          color: '#1de9b6',
          show: false
        },
        itemStyle: {
          shadowBlur: 10,
          shadowColor: '#333'
        },
        data: convertData(props.data)
      }
    ]
  };

  // 刷新图表
  if (option && geoChart) {
    geoChart.clear()
    geoChart.setOption(option)
  }
}


const handleTypeChange = () => {
  showType.value = 1 - showType.value
  if (showType.value === 0 && geoRef.value) {
    initEchart()
  }
}
</script>

<style scoped lang="postcss">
.el-table {
  font-size: 13px
}
</style>
