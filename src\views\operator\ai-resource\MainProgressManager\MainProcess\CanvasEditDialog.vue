<template>
  <el-dialog
    v-model="dialogVisible"
    width="480px"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-form
      :model="addData"
      :rules="rules"
      label-width="80px"
      ref="editRef"
      @submit.native.prevent
    >
      <el-form-item label="流程名称：" prop="name">
        <el-input
          v-model="addData.name"
          clearable
          placeholder="请输入流程名称（40字以内）"
          maxlength="40"
          @keyup.enter="confirm"
        />
      </el-form-item>
      <el-form-item label="语境类型：" prop="isOpenContext">
        <el-radio-group v-model="addData.isOpenContext" class="tw-ml-[6px]">
          <el-radio :label="true">开放</el-radio>
          <el-radio :label="false">封闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!!addData.isOpenContext" label="开放范围：" prop="openScopeType">
        <el-radio-group v-model="addData.openScopeType" class="tw-ml-[6px]">
          <el-radio :label="OpenScopeTypeEnum['全部']">全部</el-radio>
          <el-radio :label="OpenScopeTypeEnum['自定义']">自定义</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['自定义']" label="选择分组：" prop="groupOpenScope">
        <SelectBox 
          v-model:selectVal="addData.groupOpenScope"
          :options="groupOptions"
          :canCopy="props.readonly"
          copyName="knowledgeGroupIds"
          name="name"
          val="id"
          placeholder="请选择知识库分组"
          filterable
          class="tw-grow"
          multiple
        >
        </SelectBox>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, onMounted, watch } from 'vue'
import { ScriptBaseInfo, OpenScopeTypeEnum, KnowledgeGroupItem } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { scriptCanvasModel } from '@/api/speech-craft'
import type { FormInstance, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js'
import { useScriptStore } from '@/store/script'
import { CanvasDataOrigin } from './constant'
import Confirm from '@/components/message-box'

const emits = defineEmits(['update:visible', 'confirm'])
const loading = ref(false)
const props = defineProps<{
  visible: boolean;
  readonly?: boolean
  canvasData: ScriptBaseInfo
}>();

const scriptStore = useScriptStore() // 话术信息
const scriptId = scriptStore.id // 话术id

const addData = reactive<ScriptBaseInfo>(new CanvasDataOrigin(scriptId, 1))
const dialogVisible = ref(props.visible)
const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const rules = {
  name: [
    { required: true, message: '请输入流程名称', trigger: 'blur' },
  ],
  isOpenContext: [
    { required: true, message: '请选择语境类型', trigger: 'change' },
  ],
  openScopeType: [
    { required: true, message: '请选择开放范围', trigger: 'change' },
  ],
  groupOpenScope: [
    { required: true, message: '请选择知识库分组开放范围', trigger: 'change' },
  ],
}
const title = computed(() => {
  return addData.id ? '编辑流程' : '新增流程'
})
const confirm = async () => {
  const params: {
    canvasId?: number;
    groupOpenScope?: number[];
    isOpenContext: boolean;
    name: string;
    openScopeType?: OpenScopeTypeEnum;
    weight?: number
  } = {
    name: addData.name!,
    isOpenContext: addData.isOpenContext || false,
    openScopeType: !!addData.isOpenContext ? addData.openScopeType : undefined,
    groupOpenScope: !!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['自定义'] ? addData.groupOpenScope
        :( !!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['全部'] ? groupOptions.value?.map(item => item.id) : undefined),
    canvasId: addData.id,
    weight: addData.weight,
  }
  if (props.canvasData.id && (params.isOpenContext !== props.canvasData?.isOpenContext ||
    params.isOpenContext !== props.canvasData?.isOpenContext || 
    params.openScopeType !== props.canvasData?.openScopeType)
  ) {
    const [err1] = await to(Confirm({
      text: `知识库分组发生变化，请在对应的分支里重新确认优先级配置?`,
      type: 'warning',
      title: '确认'
    })) as [Error | undefined, any]
    if (err1) return
  }
 
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const [err, res] =  await to(addData.id
        ? scriptCanvasModel.updateMasterCanvas(params)
        : scriptCanvasModel.addMasterCanvasName({
          headCorpusId: null,
          isMasterCanvas: true,
          scriptId: scriptId,
          ...params
        })
      )
      loading.value = false
      if (!err) {
        ElMessage.success('操作成功')
        emits('confirm', res)
        cancel()
      }
    }
  })
}

const groupOptions = ref<{
  id: number,
  name: string
}[] | null>([])
const init = async () => {
  loading.value = true
  groupOptions.value = await scriptStore.getKnowledgeGroupOptions()
  loading.value = false
}
watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    init()
    Object.assign(addData, props.canvasData ? JSON.parse(JSON.stringify(props.canvasData)) : new CanvasDataOrigin(scriptId))
  } else {
    Object.assign(addData, new CanvasDataOrigin(scriptId, 1))
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    width: 100%;
    padding: 0 12px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>