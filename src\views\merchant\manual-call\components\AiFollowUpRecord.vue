<template>
  <el-tabs v-model="activeInnerTab" type="border-card" tab-position="top" @tab-change="handleInnerTabChange">
    <el-tab-pane label="今日" name="今日"></el-tab-pane>
    <el-tab-pane label="历史" name="历史"></el-tab-pane>
  </el-tabs>
  <div v-if="currentRecordInfo?.id" class="tw-flex-grow tw-flex-shrink tw-overflow-hidden ai-follow-up-list">
    <div class="tw-flex tw-items-center tw-p-[12px] tw-bg-white tw-flex-grow-0 tw-flex-shrink-0">
      <span class="tw-mr-1 tw-flex-shrink-0">通话录音</span>
      <AudioMode
        v-if="currentRecordInfo && currentRecordInfo.callId && currentRecordInfo.wholeAudioFileUrl"
        v-model:audioStatus="audioStatus"
        v-model:audioVolume="audioVolume"
        :audioUrl="currentRecordInfo.wholeAudioFileUrl || ''"
        :audioName="currentRecordInfo.taskName || '未知任务'"
      >
      </AudioMode>
    </div>
    <div class="tw-flex tw-h-[calc(100%-70px)]">
      <div v-if="isRecordExtend" class="tw-flex-grow-0 tw-flex-shrink-0 tw-h-full tw-w-[265px] tw-bg-white">
        <CallRecordInfoBox
          class="tw-h-full"
          :record-data="currentRecordInfo!"
          :record-type="recordType"
          :readonly="true"
          @updateRecord="updateRecordLeftInfo"
        />
      </div>
      <div class="tw-flex-grow-0 tw-flex-shrink-0 tw-self-center">
        <div class="trapezoid-col" @click="isRecordExtend=!isRecordExtend">
          <el-icon v-if="isRecordExtend" :size="13">
            <ArrowLeftBold />
          </el-icon>
          <el-icon v-else :size="13">
            <ArrowRightBold />
          </el-icon>
        </div>
      </div>
      <div class="tw-flex-grow tw-flex-shrink">
        <CallRecordDialogBoxNew
          v-loading="dialogLoading"
          v-model:needUpdate="needUpdate"
          v-model:clearAudio="clearAudio"
          :callRecordData="currentRecordInfo!"
          :dataList="dialogData"
        />
      </div>
    </div>
  </div>
  <el-empty v-else class="tw-grow"></el-empty>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { computed, defineAsyncComponent, onMounted, reactive, ref } from 'vue'
import { ArrowLeftBold, ArrowRightBold, } from '@element-plus/icons-vue'
import { findValueInEnum } from '@/utils/utils'
import { InfoQueryItem, } from '@/type/speech-craft'
import { ClueFollowItem, ClueFollowLog, ClueItem, } from '@/type/clue'
import { HangupEnum, RecordDialogueData, RecordTypeEnum, TaskCallRecordItem } from '@/type/task'

import { aiOutboundTaskModel, } from '@/api/ai-report'
import { scriptInfoModel } from '@/api/speech-craft'
import to from 'await-to-js';
import { ResponseData } from '@/axios/request/types'
import { useUserStore } from '@/store/user'
import { seatWorkbenchClueModel } from '@/api/seat'

const AudioMode = defineAsyncComponent({ loader: () => import('@/components/AudioMode.vue') })
const CallRecordInfoBox = defineAsyncComponent({ loader: () => import('@/components/record/CallRecordInfoBox.vue') })
const CallRecordDialogBoxNew = defineAsyncComponent({ loader: () => import('@/components/record/CallRecordDialogBoxNew.vue') })

const props = defineProps<{
  clueData: ClueItem,
  tabName: string,
}>()

const userInfo = useUserStore()

/** 模块3-1： 跟进记录-列表 */
const followLogList = ref<ClueFollowItem[] | null>([]) // 跟进记录列表
const activeFollowLog = reactive<ClueFollowLog>({}) // 当前激活的跟进记录，展开其通话记录详情
const currentRecordInfo = ref<TaskCallRecordItem | null>({})

/** 模块3-1： 跟进记录-通话详情 */
const isRecordExtend = ref(true) // 是否展开记录，左侧的详情
// 通话详情右侧
// 播放录音
const audioStatus = ref<'pause' | 'play' | 'none'>('pause') // 左侧通话记录音频播放状态
const audioVolume = ref<number>(70) // 左侧通话记录音频声音
const clearAudio = ref(false) // 对于一些操作，需要是的对话组件的音频清空

const dialogLoading = ref(false) // 针对右侧对话部分的的loading
const needUpdate = ref(false) // 通话记录对话详情是否需要更新

const dialogData = ref<RecordDialogueData[]>([]) // 对话数据列表
const recordType = ref(RecordTypeEnum['AI外呼'])

const updateRecordLeftInfo = async () => {
  if (!props.clueData.id) {
    return ElMessage.warning('获取线索ID失败')
  }
  followLogList.value = await seatWorkbenchClueModel.getFollowById({ clueId: props.clueData.id! }) || []
  const row = followLogList.value.find(item => item.clueFollowUpLog.id === activeFollowLog.id)
  if (row?.clueFollowUpLog?.id) {
    Object.assign(activeFollowLog, row.clueFollowUpLog)
    currentRecordInfo.value = (row.callRecordForHumanMachine && { ...row.callRecordForHumanMachine }) || (row.callRecordForManualDirect && { ...row.callRecordForManualDirect }) || {}
  }
}

/** 模块3-3：ai跟进记录 */
const activeInnerTab = ref('今日')
const handleInnerTabChange = async (tabName: string) => {
  if (!props.clueData.phone) return ElMessage.warning('未获取到线索手机号')
  followUpLoading.value = true
  const [_, res] = await to(aiOutboundTaskModel.findFollowCallRecordList({
    phone: props.clueData.phone,
    account: userInfo.account,
    callStatus: '7',
    calloutStartTime: tabName == '今日' ? dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss') : dayjs().add(-14, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    calloutEndTime: tabName == '今日' ? dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss') : dayjs().add(-1, 'day').endOf('day').format('YYYY-MM-DD HH:mm:ss'),
  })) as [any, ResponseData];
  const arr = (res.data as TaskCallRecordItem[] || []).sort((a, b) => dayjs(a.callOutTime).isAfter(dayjs(b.callOutTime)) ? -1 : 1)
  if (arr && arr?.length > 0) {
    currentRecordInfo.value = arr[0] || null
    updateRecordData()
  } else {
    currentRecordInfo.value = null
    dialogData.value = []
  }
  followUpLoading.value = false
}
// 获取ai跟进记录对话详情
const updateRecordData = async () => {
  dialogLoading.value = true
  const callId = currentRecordInfo.value?.callId || ''
  const recordId = currentRecordInfo.value?.recordId || ''
  if (!callId) {
    dialogData.value = []
    needUpdate.value = true
    dialogLoading.value = false
    return
  }
  const [err1, data1] = await to(aiOutboundTaskModel.getAiDialogueDataList({
    callId: callId,
    recordId: recordId,
  }))
  dialogData.value = data1 || []
  dialogLoading.value = false
  needUpdate.value = true
}

const followUpLoading = ref(false)

onMounted(() => {
  activeInnerTab.value = props.tabName
  handleInnerTabChange(props.tabName)
})
</script>

<style scoped lang="postcss">
.ai-follow-up-list {
  /* 56 模块顶部 39 标签页顶部 2 标签页内容上下边框各1 */
  height: calc(100% - 56px - 39px - 2px);
}
:deep(.el-tabs__content) {
  padding: 0;
}
</style>
