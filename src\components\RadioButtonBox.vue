<template>
  <div class="tw-rounded-[4px] tw-bg-[#fff] tw-flex tw-items-center tw-justify-between tw-h-[38px] sm:tw-h-[32px]">
    <span v-for="item in props.list" :key="item" class="span-btn" :class="{'active-btn': activeType === item}" @click="handleClick(item)">
      <span class="inner-span-btn">{{ item }}</span>
    </span>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch } from 'vue'
const props = defineProps<{
  active: string,
  list: string[],
}>()
const activeType = ref(props.active)
watch(() => props.active, () => {
  activeType.value = props.active
})
const emits = defineEmits([
  'update:active',
])
const handleClick = (val: string) => {
  emits('update:active', val)
}
</script>

<style lang="postcss" type="text/postcss" scoped>
.span-btn {
  padding: 2px;
  background-color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  .inner-span-btn {
    border-radius: 4px;
    display: inline-block;
    padding: 2px 16px;
    font-size: 16px;
    height: 30px;
    line-height: 26px;
    color: var(--primary-black-color-400);
    font-weight: 400;
    text-align: center;
    @media screen and (max-width: 600px) {
      font-size: 14px;
      line-height: 20px;
      height: 24px;
    }
  }
}
.active-btn {
  background-color: #fff;
  .inner-span-btn {
    background-color: var(--el-color-primary);
    color: #fff;
    font-weight: 600;
  }
}
</style>
