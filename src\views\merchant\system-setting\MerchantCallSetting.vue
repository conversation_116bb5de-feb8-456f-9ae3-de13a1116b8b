<template>
  <!--模块标题-->
  <HeaderBox title="系统设置" class="tw-min-w-[600px]" />

  <!--模块主体-->
  <el-scrollbar wrap-class="module-container" class="tw-min-w-[600px]">
    <div class="setting-item">
      <div class="setting-item-label">
        转人工坐席处理时长
      </div>

      <div v-loading="loadingTransfer" class="setting-item-content">
        {{ data.transferManualSeatProcessTime ? data.transferManualSeatProcessTime + ' 秒' : '（未配置）' }}
        <el-button class="tw-ml-[24px]" type="primary" link @click="onClickEdit('transferManualSeatProcessTime')">
          修改
        </el-button>
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-item-label">
        转人工弹窗铃声
      </div>

      <div v-loading="loadingRingtone" class="setting-item-content">
        {{ data.waitingRingtone || '（未配置）' }}
        <el-button class="tw-ml-[24px]" type="primary" link @click="onClickEdit('waitingRingtone')">
          修改
        </el-button>
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-item-label">
        人工话后处理时长
      </div>

      <div v-loading="loadingPost" class="setting-item-content">
        {{ data.postCallProcessTime ? data.postCallProcessTime + ' 秒' : '（未配置）' }}
        <el-button class="tw-ml-[24px]" type="primary" link @click="onClickEdit('postCallProcessTime')">
          修改
        </el-button>
      </div>
    </div>

    <div class="setting-item">
      <div class="setting-item-label">
        意向客户统计范围
      </div>

      <div v-loading="loadingPost" class="setting-item-content">
        {{ data.intentionClass ? data.intentionClass || '-' : '（未配置）' }}
        <el-button class="tw-ml-[24px]" type="primary" link @click="onClickEdit('intentionClass')">
          修改
        </el-button>
      </div>
    </div>
  </el-scrollbar>

  <!--转人工坐席处理时长 弹窗-->
  <el-dialog v-model.trim="dialogVisible.transferManualSeatProcessTime" align-center width="480px" title="转人工坐席处理时长">
    <template #header>
      <div class="setting-dialog-header">
        转人工坐席处理时长
      </div>
    </template>

    <div class="setting-dialog">
      <el-form label-position="left" label-width="50" :model="form" :rules="rules">
        <el-form-item ref="transferRef" prop="transferManualSeatProcessTime" label="时长：">
          <div class="setting-dialog-item-content">
            <InputNumberBox
              v-model="form.transferManualSeatProcessTime"
              placeholder="请填写坐席处理时长"
              :min="transferRange[0]"
              :max="transferRange[1]"
              append="秒"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="setting-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickCancelEdit('transferManualSeatProcessTime')">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="onClickConfirmEdit('transferManualSeatProcessTime')">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!--转人工弹窗铃声 弹窗-->
  <el-dialog v-model="dialogVisible.waitingRingtone" align-center width="480px" title="转人工弹窗铃声">
    <template #header>
      <div class="setting-dialog-header">
        上传提示铃声
      </div>
    </template>

    <div class="setting-dialog">
      <el-form label-position="top" label-width="50" :model="form" :rules="rules">
        <el-upload
          v-show="uploadStatus===uploadStatusEnum.NONE"
          ref="uploadRef"
          drag
          class="upload-box"
          :action="uploadInfo.action()"
          :headers="uploadInfo.headers"
          :method="uploadInfo.method"
          :multiple="uploadInfo.multiple"
          :show-file-list="uploadInfo.showFileList"
          :accept="uploadInfo.accept"
          :limit="uploadInfo.limit"
          :before-upload="uploadInfo.beforeUpload"
          :on-success="uploadInfo.onSuccess"
          :on-error="uploadInfo.onError"
        >
          <div ref="uploadButtonRef" class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon size="24px" color="#969799" class="upload-icon">
              <SvgIcon name="upload2" color="inherit" />
            </el-icon>
            <div class="upload-hint">点击或拖拽文件到此处上传</div>
          </div>
        </el-upload>

        <div
          v-show="uploadStatus!==uploadStatusEnum.NONE"
          class="upload-wrapper"
          :class="{'fail-border':uploadStatus===uploadStatusEnum.FAIL}"
        >
          <div class="upload-result">
            <div class="upload-filename">
              <div class="upload-filename-text">
                {{ form.waitingRingtone || '（未配置）' }}
              </div>
              <el-icon v-show="uploadStatus===uploadStatusEnum.PENDING" class="tw-ml-[8px]" :size="16">
                <img src="@/assets/img/loading.png" class="tw-animate-spin">
              </el-icon>
              <el-icon v-show="uploadStatus===uploadStatusEnum.SUCCESS" class="tw-ml-[8px]" :size="16" color="#13BF77">
                <SvgIcon name="success" color="inherit" />
              </el-icon>
              <el-icon v-show="uploadStatus===uploadStatusEnum.FAIL" class="tw-ml-[8px]" :size="16" color="#E64B17">
                <SvgIcon name="fail" color="inherit" />
              </el-icon>
            </div>

            <div v-show="uploadStatus===uploadStatusEnum.PENDING" class="upload-button-box">
              <el-button link type="primary" @click="onClickUploadCancel">
                取消上传
              </el-button>
            </div>

            <div
              v-show="uploadStatus===uploadStatusEnum.SUCCESS||uploadStatus===uploadStatusEnum.FAIL"
              class="upload-button-box"
            >
              <el-button link type="primary" @click="onClickUploadRetry">
                重新上传
              </el-button>
              <el-button link type="primary" @click="onClickUploadDelete">
                删除
              </el-button>
            </div>
          </div>
        </div>

        <div v-show="uploadStatus===uploadStatusEnum.FAIL" class="upload-fail-reason">
          {{ uploadFailReason }}
        </div>

        <el-form-item ref="ringRef" prop="waitingRingtone" />
      </el-form>
    </div>

    <template #footer>
      <div class="setting-dialog-footer">
        <el-button :icon="CloseBold" :disabled="uploading" @click="onClickCancelEdit('waitingRingtone')">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="onClickConfirmEdit('waitingRingtone')">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!--人工话后处理时长 弹窗-->
  <el-dialog v-model.trim="dialogVisible.postCallProcessTime" align-center width="480px" title="人工话后处理时长">
    <template #header>
      <div class="setting-dialog-header">
        话后处理时长
      </div>
    </template>

    <div class="setting-dialog">
      <el-form label-position="left" label-width="104" :model="form" :rules="rules">
        <el-form-item ref="postRef" prop="postCallProcessTime" label="话后处理时长：">
          <div class="setting-dialog-item-content">
            <InputNumberBox
              v-model="form.postCallProcessTime"
              placeholder="填写话后处理时长"
              :min="postRange[0]"
              :max="postRange[1]"
              append="秒"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="setting-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickCancelEdit('postCallProcessTime')">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="onClickConfirmEdit('postCallProcessTime')">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!--意向客户统计范围 弹窗-->
  <el-dialog v-model.trim="dialogVisible.intentionClass" align-center :close-on-click-modal="false" width="480px" title="人工话后处理时长">
    <template #header>
      <div class="setting-dialog-header">
        意向客户统计范围
      </div>
    </template>

    <div class="setting-dialog">
      <el-form label-position="left" label-width="60" :model="form" :rules="rules">
        <el-form-item ref="intentionRef" prop="intentionClass" label="范围：">
          <div class="setting-dialog-item-content">
            <SelectBox 
              v-model:selectValStr="form.intentionClass!"
              isString
              :options="intentionClassOption"
              name="name"
              val="value"
              placeholder="请选择意向客户统计范围"
              class="tw-w-full"
              multiple
            >
            </SelectBox>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="setting-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickCancelEdit('intentionClass')">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="onClickConfirmEdit('intentionClass')">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { CloseBold, Select } from '@element-plus/icons-vue'
import { defineAsyncComponent, reactive, ref, toRaw } from 'vue'
import { CallSetting } from '@/type/seat'
import { ElMessage, FormRules, UploadFile, UploadRawFile } from 'element-plus'
import { getToken, Throttle } from '@/utils/utils'
import { callSettingModel } from '@/api/seat'
import { IntentionClassEnum } from '@/type/common'
import { enum2Options } from '../../../utils/utils';
import SelectBox from '@/components/SelectBox.vue'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const InputNumberBox = defineAsyncComponent(() => import('@/components/InputNumberBox.vue'))

// 转人工坐席处理时长，正在加载
const loadingTransfer = ref<boolean>(false)
// 转人工坐席处理时长，节流锁
const throttleTransfer = new Throttle(loadingTransfer)

// 转人工弹窗铃声，正在加载
const loadingRingtone = ref<boolean>(false)
// 转人工弹窗铃声，节流锁
const throttleRingtone = new Throttle(loadingRingtone)

// 人工话后处理时长，正在加载
const loadingPost = ref<boolean>(false)
// 人工话后处理时长，节流锁
const throttlePost = new Throttle(loadingPost)

// 意向客户统计范围，正在加载
const loadingIntention = ref<boolean>(false)
// 意向客户统计范围，节流锁
const throttleIntention = new Throttle(loadingIntention)

// 转人工坐席处理时长，范围，闭区间
const transferRange = [1, 600]
// 人工话后处理时长，范围，闭区间
const postRange = [1, 300]

// 当前配置好的数据
const data = reactive<CallSetting>({
  // 转人工坐席处理时长，单位：秒
  transferManualSeatProcessTime: null,
  // 转人工弹窗铃声，音频文件名
  waitingRingtone: null,
  // 转人工弹窗铃声，音频文件成功上传后的远程地址
  waitingRingtoneUrl: null,
  // 人工话后处理时长，单位：秒
  postCallProcessTime: null,
  // 意向客户统计范围
  intentionClass: undefined,
})
// 准备提交给接口的表单数据
const form = reactive<CallSetting>({
  transferManualSeatProcessTime: null,
  waitingRingtone: null,
  waitingRingtoneUrl: null,
  postCallProcessTime: null,
  intentionClass: null,
})

// 上传状态枚举
enum uploadStatusEnum {
  NONE,
  PENDING,
  SUCCESS,
  FAIL,
}

// 意向客户统计范围选项
const intentionClassOption = enum2Options(IntentionClassEnum)

// 正在上传
const uploading = ref(false)
// 上传状态
const uploadStatus = ref<uploadStatusEnum>(uploadStatusEnum.NONE)
// 上传失败原因
const uploadFailReason = ref<string>('')

// 上传组件DOM
const uploadRef = ref()
// 上传按钮DOM
const uploadButtonRef = ref()
// 上传组件参数
const uploadInfo = {
  // 请求 URL
  action: () => {
    let baseURL = location.protocol === 'https:'
      ? import.meta.env.VITE_API_ORIGIN_HTTPS
      : import.meta.env.VITE_API_ORIGIN_HTTP
    return baseURL + 'AiSpeech/callSetting/uploadWaitingRingtone'
  },
  // 设置上传的请求头部
  headers: {
    token: getToken(),
  },
  // 设置上传请求方法
  method: 'post',
  // 是否支持多选文件
  multiple: false,
  // 是否显示已上传文件列表
  showFileList: false,
  // 接受上传的文件类型
  accept: '.wav',
  // 允许上传文件的最大数量
  limit: 1,
  // 文件上传之前的钩子
  beforeUpload: (rawFile: UploadRawFile) => {
    if (!rawFile.name.endsWith('.wav')) {
      ElMessage({
        message: '上传的音频类型必须为wav',
        type: 'error',
      })
      return false
    } else {
      uploadStatus.value = uploadStatusEnum.PENDING
      form.waitingRingtone = rawFile.name ?? null
      form.waitingRingtoneUrl = null
      return true
    }
  },
  // 文件上传成功时的钩子
  onSuccess: (response: any, uploadFile: UploadFile) => {
    form.waitingRingtone = uploadFile.name ?? null
    form.waitingRingtoneUrl = response?.data ?? null
    ElMessage({
      message: `${uploadFile.name} 上传成功`,
      type: 'success',
    })
    setTimeout(() => {
      uploadStatus.value = uploadStatusEnum.SUCCESS
    }, 1000)
  },
  // 文件上传失败时的钩子
  onError: (error: Error, uploadFile: UploadFile) => {
    form.waitingRingtone = data.waitingRingtone ?? null
    uploadFailReason.value = Object.values(error).join('\n')
    ElMessage({
      message: `${uploadFile.name} 上传失败！错误原因：${error}`,
      type: 'error',
    })
    setTimeout(() => {
      uploadStatus.value = uploadStatusEnum.FAIL
    }, 1000)
  },
}
/**
 * 上传铃声组件 清空当前文件队列
 */
const resetUploadFiles = () => {
  uploadRef.value?.clearFiles && uploadRef.value.clearFiles()
  uploadStatus.value = uploadStatusEnum.NONE
  form.waitingRingtone = null
  form.waitingRingtoneUrl = null
}
/**
 * 上传铃声组件 点击取消上传按钮
 */
const onClickUploadCancel = () => {
  uploadRef.value?.abort && uploadRef.value.abort()
  resetUploadFiles()
}
/**
 * 上传铃声组件 点击重新上传按钮
 */
const onClickUploadRetry = () => {
  resetUploadFiles()
  // 帮用户点击一下选择文件按钮
  uploadButtonRef.value?.click && uploadButtonRef.value.click()
}
/**
 * 上传铃声组件 点击删除按钮
 */
const onClickUploadDelete = () => {
  resetUploadFiles()
}
/**
 * 上传铃声组件 更新上传状态
 */
const updateUploadStatus = () => {
  // 如果存在铃声文件，则状态改成上传成功
  if (data.waitingRingtone) {
    uploadStatus.value = uploadStatusEnum.SUCCESS
  } else {
    uploadStatus.value = uploadStatusEnum.NONE
  }
}

// 转人工坐席处理时长，表单项DOM
const transferRef = ref()
// 转人工弹窗铃声，表单项DOM
const ringRef = ref()
// 人工话后处理时长，表单项DOM
const postRef = ref()
// 意图客户范围，表单项DOM
const intentionRef = ref()
// 表单项DOM映射表
const formDomList: { [prop in keyof CallSetting]: any } = {
  transferManualSeatProcessTime: transferRef,
  waitingRingtone: ringRef,
  postCallProcessTime: postRef,
  intentionClass: intentionRef,
}
// 表单校验规则
const rules = reactive<FormRules>({
  transferManualSeatProcessTime: [{
    required: true,
    trigger: ['blur'],
    validator: (rule: any, value: any, callback: any) => {
      if (value === undefined || value === null) {
        callback(new Error('请填写内容'))
      } else {
        if (isNaN(value)) {
          callback(new Error('格式不正确，请输入 1-600 的正整数'))
        } else {
          if (value < transferRange[0] || transferRange[1] < value) {
            callback(new Error('数值范围不正确，请输入 1-600 的正整数'))
          } else {
            callback()
          }
        }
      }
    }
  }],
  waitingRingtone: [{
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('请上传铃声文件'))
      } else {
        callback()
      }
    }
  }],
  postCallProcessTime: [{
    required: true,
    trigger: ['blur'],
    validator: (rule: any, value: any, callback: any) => {
      if (value === undefined || value === null) {
        callback(new Error('请填写内容'))
      } else {
        if (isNaN(value)) {
          callback(new Error('格式不正确，请输入 1-600 的正整数'))
        } else {
          if (value < postRange[0] || postRange[1] < value) {
            callback(new Error('数值范围不正确，请输入 1-600 的正整数'))
          } else {
            callback()
          }
        }
      }
    }
  }],
  intentionClass: [{
    required: true,
    message: '请选择意向客户统计范围',
    trigger: ['blur', 'change'],
  }],
})

/**
 * 更新通话设置
 */
const updateSetting = async (param?: CallSetting) => {
  if (param) {
    // 本地更新
    Object.keys(data).forEach((prop: string) => {
      // @ts-ignore
      data[<keyof CallSetting>prop] = param[<keyof CallSetting>prop] ?? null
    })
  } else {
    // 接口更新
    // 节流锁上锁
    throttleTransfer.lock()
    throttleRingtone.lock()
    throttlePost.lock()
    throttleIntention.lock()
    try {
      const res = <CallSetting>await callSettingModel.findCallSetting()
      Object.assign(data, res)
    } catch (e) {
    } finally {
      // 节流锁解锁
      throttleTransfer.unlock()
      throttleRingtone.unlock()
      throttlePost.unlock()
      throttleIntention.unlock()
    }
  }

  updateUploadStatus()
}
/**
 * 点击编辑
 * @param prop 表单项属性名
 */
const onClickEdit = (prop: keyof CallSetting) => {
  if (form.hasOwnProperty(prop)) {
    // 编辑时，初始值应该是直接读取配置好的数据
    // @ts-ignore
    form[prop] = data[prop]
    // 弹窗铃声，页面上展示一个字段（文件名），表单和接口里有两个字段（文件名和文件URL）
    if (prop === 'waitingRingtone') {
      form.waitingRingtoneUrl = data.waitingRingtoneUrl
      updateUploadStatus()
    }
  }

  // 显示弹窗
  dialogVisible[prop] = true
}
/**
 * 点击取消编辑
 * @param prop 表单项属性名
 */
const onClickCancelEdit = (prop: keyof CallSetting) => {
  // 关闭弹窗
  dialogVisible[prop] = false
  // 清除表单校验结果
  formDomList[prop]?.value?.clearValidate()
}
/**
 * 点击确定编辑
 * @param prop 表单项属性名
 */
const onClickConfirmEdit = (prop: keyof CallSetting) => {
  // 先做表单校验，校验通过了，再请求接口更新
  const dom = formDomList[prop]
  dom.value && dom.value.validate('blur', async (valid: boolean) => {
    if (valid) {
      try {
        // 处理参数
        // 其他的都读取原始数据，只有点击确定的那一项读取表单数据
        const param = JSON.parse(JSON.stringify(toRaw(data)))
        param[prop] = form[prop]
        // 弹窗铃声，页面上展示一个字段（文件名），表单和接口里有两个字段（文件名和文件URL）
        if (prop === 'waitingRingtone') {
          param.waitingRingtoneUrl = form.waitingRingtoneUrl
        }

        const res = <CallSetting>await callSettingModel.saveCallSetting(param)
        await updateSetting(res)

        // 关闭弹窗
        dialogVisible[prop] = false
      } catch (e) {
      }
    }
  })
}

// ---------------------------------------- 弹窗 开始 ----------------------------------------

// 是否显示弹窗
const dialogVisible = reactive<{ [prop: string]: boolean }>({
  transferManualSeatProcessTime: false,
  waitingRingtone: false,
  postCallProcessTime: false,
  intentionClass: false,
})

// ---------------------------------------- 弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

updateSetting()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
:deep(.el-form .el-form-item) {
  margin: 0;
  &:first-child {
    margin: 0;
  }
  /*noinspection CssUnusedSymbol*/
  .el-form-item__label {
    padding: 0;
  }
}
.setting-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  height: auto;
  margin: 0;
  padding: 0;
}
.setting-item-label {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  color: #313233;
  margin-bottom: 8px;
}
.setting-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 4px;
  background: #FFF;
  line-height: 20px;
}
.setting-dialog {
  margin: 0;
  padding: 20px 12px;
}
.setting-dialog-item-content {
  width: 100%;
}
.setting-dialog-header {
  font-size: 16px;
  font-weight: 600;
  color: #313233;
  text-align: left;
}
.setting-dialog-footer {
  padding: 0 8px;
}
.upload-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  height: 96px;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  &.fail-border {
    border-color: #E54B17;
  }
}
.upload-result {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  align-self: flex-start;
  margin: 0 16px;
  text-align: left;
}
.upload-filename {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #313233;
  line-height: 22px;
}
.upload-fail-reason {
  margin-top: 8px;
  font-size: 12px;
  font-weight: 400;
  color: #E54B17;
  line-height: 20px;
  text-align: left;
  word-break: break-all;
}
.el-button.is-link {
  padding: 0;
}
.upload-box {
  width: 100%;
  height: 96px;
}
.upload-icon {
  width: 24px;
  height: 24px;
}
.upload-hint {
  margin-top: 16px;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #313233;
  text-align: center;
}
:deep(.el-upload) {
  width: 100%;
  height: 100%;
}
:deep(.el-upload-dragger) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0;
  border-radius: 4px;
}
</style>
