<template>
  <!--模块标题-->
  <HeaderBox title="坐席监控" />

  <!--模块主体-->
  <div class="module-container tw-min-w-[1080px] tw-text-left">
    <!--标签卡-->
    <TabsBox v-model:active="activeTab" :tabList="tabList" />
    <!--实时状态-->
    <RealTimeStatusList v-if="activeTab==='实时状态'" />
    <!--数据统计-->
    <StatisticsList v-else-if="activeTab==='数据统计'" />
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, ref } from 'vue'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const TabsBox = defineAsyncComponent(() => import('@/components/TabsBox.vue'))
const RealTimeStatusList = defineAsyncComponent(() => import('./RealTimeStatusList.vue'))
const StatisticsList = defineAsyncComponent(() => import('./StatisticsList.vue'))

type TabName = '实时状态' | '数据统计'
const tabList: TabName[] = ['实时状态', '数据统计']
const activeTab = ref<TabName>(tabList[0])
</script>

<style scoped lang="postcss">
</style>
