<template>
  <div class="basic-QA-container">
    <div class="tw-mb-[6px] tw-grid tw-grid-cols-4 tw-items-center tw-gap-x-[8px]">
      <el-input
        v-model.trim="searchForm.branchName"
        placeholder="请输入分支名称"
        maxlength="30"
        clearable
        @keyup.enter="search"
        @clear="search"
      >
      </el-input>
      <el-input
        v-model.trim="searchForm.corpusName"
        placeholder="请输入语料名称"
        maxlength="30"
        clearable
        @keyup.enter="search"
        @clear="search"
      >
      </el-input>
      <el-input
        v-model.trim="searchForm.canvasName"
        placeholder="请输入主动流程名称"
        maxlength="30"
        clearable
        @keyup.enter="search"
        @clear="search"
      >
      </el-input>
      <div class="tw-flex">
        <el-button type="primary" class="tw-ml-1" @click="search()" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
      
    </div>
    <div class="tw-mb-[8px] tw-flex tw-justify-between tw-items-center tw-text-[13px]">
      <div class="tw-flex tw-items-center tw-text-[var(--primary-black-color-300)]">
        <span class="tw-ml-[6px]">已选：</span>
        <span >{{ selectLen || 0 }}</span>
        <span>/</span>
        <span>{{ tableData?.length || 0 }}</span>
        <span class="tw-ml-[12px]">仅支持对同名分支批量编辑</span>
      </div>
      <div>
        <el-button v-if="!isChecked" type="primary" @click="handleBatchOperate()">
          <el-icon :size="16"><SvgIcon name="edit"></SvgIcon></el-icon>
          <span>批量编辑</span>
        </el-button>
      </div>
    </div>
    <el-table
      :data="tableTempData"
      style="width: 100%"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      ref="tableRef"
      row-key="id"
      v-loading="loading"
      stripe
      border
    >
      <el-table-column v-if="!isChecked" width="36" fixed="left" align="left" type="selection"></el-table-column>
      <el-table-column property="branchName" fixed="left" label="分支名称" align="left" width="240" :formatter="formatterEmptyData"> </el-table-column>
      <el-table-column property="matchText" align="left" label="满足条件" min-width="280">
        <template #default="{ row, $index }">
          <div v-if="row.matchText && row.matchText.length" v-for="(item, index) in row.matchText||[]" :key="`${$index}-${index}`">
            <el-divider v-if="index" border-style="dashed">or</el-divider>
            <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
              {{ item2 || '-' }}
            </li>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column property="excludeText" align="left" label="排除条件" min-width="280">
        <template #default="{ row, $index }">
          <div v-if="row.excludeText && row.excludeText.length" v-for="(item, index) in row.excludeText||[]" :key="`${$index}-${index}`">
            <el-divider v-if="index" border-style="dashed">or</el-divider>
            <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
              {{ item2 || '-' }}
            </li>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>

      <el-table-column property="corpusName" align="left" label="所属语料" width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="canvasName" align="left" label="所属流程" min-width="120" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="updateTime" align="center" label="最后更新时间" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template v-slot="{ row }">
          {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="!isChecked" align="right" label="操作" fixed="right" width="64">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total || 0"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  <BatchEditDialog
    v-model:visible="dialogVisible"
    :branchData="branchData || []"
    @confirm="search"
  />
</template>

<script lang="ts" setup>
import { ScriptNormalBranchItem } from '@/type/speech-craft'
import { scriptBranchModel, } from '@/api/speech-craft'
import { CaretTop, CaretBottom, Plus } from '@element-plus/icons-vue'
import { reactive, computed, ref, onDeactivated, onActivated, watch } from 'vue'
import dayjs from 'dayjs'
import { ElMessage, TableInstance } from 'element-plus'
import { tableHeaderStyle } from '@/assets/js/constant'
import { useScriptStore } from '@/store/script'
import { formatterEmptyData } from '@/utils/utils'
import to from 'await-to-js'
import { useUserStore } from "@/store/user";
import { onBeforeRouteLeave } from 'vue-router'
import SvgIcon from '@/components/SvgIcon.vue';
import BatchEditDialog from './BatchEditDialog.vue'
import { translateCorpusRules } from "@/components/corpus/constant";
import PaginationBox from '@/components/PaginationBox.vue'

const loading = ref(false)
const scriptStore = useScriptStore()
const scriptId = scriptStore.id
const isChecked = scriptStore.isChecked
// 用户权限获取
const userStore = useUserStore();

const searchForm = reactive<{
  scriptId: number,
  branchName?: string,
  canvasName?: string,
  corpusName?: string,
}>({
  scriptId: scriptId,
  branchName: '',
  canvasName: '',
  corpusName: '',
})

/** 列表 分页和搜索 开始 */
// 列表-分页变量
const tableRef = ref<null | TableInstance>(null)

// 表格原始数据
const tableData = ref<ScriptNormalBranchItem[] | null>([])
const search = async () => {
  loading.value = true
  const [err, data] = await to(scriptBranchModel.findList(searchForm))
  tableData.value = (data || []).map(item => {
    return {
      ...item,
      matchText: translateCorpusRules(item.semCombineEntity?.satisfySemConditions || []),
      excludeText: translateCorpusRules(item.semCombineEntity?.excludeSemConditions || []),
    }
  }).sort((a, b) => a.branchId - b.branchId)
  total.value = tableData.value?.length || 0
  loading.value = false
}

// 分页
const currentPage = ref(1)
const pageSize = ref(100)
const total = ref(0)
const tableTempData = computed(() => {
  return tableData.value?.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value) || []
})
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
/** 分页和搜索 结束 */

/** 批量操作 */
const branchData = ref<ScriptNormalBranchItem[] | null>(null)
const selectLen = computed(() => tableRef.value?.getSelectionRows()?.length || 0)
const dialogVisible = ref<null | 'single' | 'batch'>(null)
const handleBatchOperate = () => {
  branchData.value = []
  const arr = tableRef.value?.getSelectionRows() || []
  if (!arr?.length) return ElMessage.warning('请选择需要操作的分支')
  let branchName = ''
  arr?.some((item: ScriptNormalBranchItem, index: number) => {
    if (index === 0) {
      branchName = item.branchName
    }
    if(item.branchId && item.branchName === branchName) {
      branchData.value?.push(item)
      return false
    } else {
      ElMessage.warning('请选择相同名称的分支')
      branchName = ''
      return true
    }
  })
  if (!branchName) return
  dialogVisible.value = 'batch'
}
const edit = (row: ScriptNormalBranchItem) => {
  branchData.value = [row]
  dialogVisible.value = 'single'
}

onActivated(() => {
  search()
})
const clearAll = () => {
  tableRef.value?.clearSelection()
  tableData.value = null
  tableRef.value = null
}
onDeactivated(() => {
  clearAll()
})
onBeforeRouteLeave(() =>{
  clearAll()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.basic-QA-container {
  width: 100%;
  padding: 16px 12px 16px;
  position: relative;
  box-sizing: border-box;
  overflow-y: auto;
  height: calc(100vh - 165px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .el-table {
    font-size: var(--el-font-size-base);
    :deep(.cell) {
      padding: 0 8px;
      line-height: 20px;
    }
    .option-checkbox {
      display: block;
      cursor: pointer;
      width: 14px;
      height: 14px;
      border-radius: 2px;
      border-width: 1px;
      position: relative;
      margin-right: 8px;
      .el-icon {
        display: none;
        position: absolute;
        top: 1px;
        left: 0px;
      }
    }
    .selected {
      font-weight: normal;
      &.option-checkbox {
        background-color: #165DFF;
        border-color: #165DFF;
        .el-icon {
          display:inline-block;
        }
        
      }
    }
    &.selected::after {
      display: none;
      content: '';
    }
  }
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
</style>