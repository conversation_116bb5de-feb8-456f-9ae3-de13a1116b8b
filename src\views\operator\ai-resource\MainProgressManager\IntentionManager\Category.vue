<template>
  <div class="category-container">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <span class="info-title">请严格按照话术制作规范设置分类等级，勿随意调整</span>
      <el-button v-if="!loading && !isChecked && tableData && tableData.length==0" type="primary" :icon="Plus" @click="createDefault">
        创建默认标签
      </el-button>
    </div>
    <el-table
      id="label-draggable-table"
      ref="tableRef"
      :data="tableData"
      style="width: 100%"
      class="tw-grow"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column v-if="!isChecked" label=" " align="left" width="80">
        <div class="handle tw-cursor-pointer">
          <el-icon>
            <Switch />
          </el-icon>
        </div>
      </el-table-column>
      <el-table-column property="sequence" align="left" label="优先顺序" width="120"></el-table-column>
      <el-table-column property="intentionType" align="center" label="分类" width="120"></el-table-column>
      <el-table-column align="left" label="分类方案名称" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.intentionName }}
        </template>
      </el-table-column>
      <el-table-column v-if="!isChecked" label="操作" align="right" width="200">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">编辑</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
  </div>
  <el-dialog
    v-model="addDialogVisible"
    width="450px"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">编辑分类</div>
    </template>
    <el-form
      ref="intentionEditRef"
      :model="editData"
      :rules="rules"
      label-width="7em"
      class="tw-mt-2"
    >
      <el-form-item prop="intentionType" label="分类名称：">
        <span class="info-title">{{ editData.intentionType || '-' }}</span>
      </el-form-item>
      <el-form-item prop="intentionName" label="分类方案：">
        <el-input
          v-model="editData.intentionName"
          :placeholder="`请输入分类方案，${nameInputLimit}字以内`"
          clearable
          :maxlength="nameInputLimit"
          show-word-limit
          :style="`width: 100%; max-width: ${typeInputLimit+5}em;`"
          :formatter="(value: string) => value.trim()"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span v-if="!isChecked" class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="save" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { IntentionType, } from '@/type/IntentionType'
import { scriptIntentionModel } from '@/api/speech-craft'
import { CloseBold, Edit, Plus, Select, Switch, } from '@element-plus/icons-vue'
import { onActivated, onDeactivated, reactive, ref, watch } from 'vue'
import { ElMessage, FormInstance } from 'element-plus'
import Confirm from '@/components/message-box'
import Sortable from "sortablejs"
import { useScriptStore } from '@/store/script'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js';
import { trace } from '@/utils/trace'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked

// 输入框最大长度
const typeInputLimit = ref<number>(20)
const nameInputLimit = ref<number>(15)

// 表格区
const tableData = ref<IntentionType[] | null>([])
const intentionEditRef = ref<FormInstance | null>(null)
const rules = {
  intentionName: [
    { required: true, message: '请输入分类方案名称', trigger: 'blur' },
    { min: 1, max: 15, message: '长度在 1 到 15 个字符', trigger: 'blur' },
  ],
}

// 搜索区
const search = async () => {
  loading.value = true
  try {
    const data = await scriptStore.getIntentionLevelOptions(true)
    tableData.value = data || []
  } catch (err) {
    ElMessage.error('获取分类列表数据失败')
  }
  loading.value = false
}

// 编辑
class IntentionTypeOrigin {
  sequence = undefined
  id = undefined
  intentionName = ''
  intentionType = ''
  scriptId = editId
}

const editData = reactive<IntentionType>(new IntentionTypeOrigin())
const addDialogVisible = ref(false)
const edit = (row?: IntentionType) => {
  if (row && row.id) {
    const { sequence, id, intentionName, intentionType } = row
    Object.assign(editData, {
      sequence, id, intentionName, intentionType
    })
  } else {
    Object.assign(editData, new IntentionTypeOrigin())
  }
  intentionEditRef.value?.clearValidate()
  addDialogVisible.value = true
  
}
const cancel = () => {
  editData.id = -1
  addDialogVisible.value = false
}
const createDefault = async () => {
  trace({
    page: `话术编辑-意向分类-创建默认分类`,
    params: { editId },
  })
  const [err] = await to(scriptIntentionModel.createDefaultIntention({
    id: editId
  }))
  !err && ElMessage.success('操作成功')
  search()
}
const save = () => {
  editData.intentionName = editData.intentionName.trim()
  intentionEditRef.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const { id, sequence, intentionName, intentionType, scriptId } = editData
      const params = {
        id: id && id > 0 ? id : undefined,
        sequence: sequence && sequence > 0 ? sequence : undefined,
        intentionName: intentionName,
        intentionType: intentionType,
        scriptId
      }
      trace({
        page: `话术编辑-意向分类-修改意向(${editId})`,
        params: params,
      })
      const [err] = await to(scriptIntentionModel.saveIntention(params))
      if (!err) {
        ElMessage.success('操作成功')
        cancel()
        search()
      }
      loading.value = false
    }
  })
}
// 拖动
const tableRef = ref(null)
const sortable = ref<null | Sortable>(null)
const initDraggableTable = () => {
  sortable.value = Sortable.create(
    document.querySelector('#label-draggable-table .el-table__body tbody') as HTMLElement, {
      animation: 300,
      handle: ".handle",
      onEnd: async (evt) => {
        if (!tableData.value) return
        const newIndex = evt.newIndex as number
        const oldIndex = evt.oldIndex as number
        loading.value = true
        const id2Index = newIndex > oldIndex ? newIndex + 1 : newIndex
        const params = {
          id1: tableData.value[oldIndex].id as number,
          id2: tableData.value[id2Index] ? tableData.value[id2Index].id as number : null,
          scriptId: editId
        }
        trace({
          page: `话术编辑-意向分类-意向分类排序(${editId})`,
          params: params,
        })
        const [err] = await to(scriptIntentionModel.switchIntentionSequence(params))
        !err && ElMessage.success('操作成功')
        tableData.value = []
        search()
        loading.value = false
      },
    })
}

// 操作区
const delAction = async (id: number) => {
  loading.value = true
  const [_, res] = await to(scriptIntentionModel.deleteOneLabel({ id }))
  if (!!res) {
    ElMessage.success('删除成功')
    search()
  }
  loading.value = false
}
const del = (row: IntentionType) => {
  Confirm({ 
    text: `您确定要删除语料【${row.intentionName}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}

// 执行

onActivated(() => {
  search()
  !isChecked && initDraggableTable()
})
onDeactivated(() => {
  sortable.value?.destroy()
  sortable.value = null
  tableData.value = null
  tableRef.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.category-container {
  width: 100%;
  height: calc(100vh - 250px);
  padding: 16px 12px 0;
  position: relative;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  font-size: var(--el-font-size-base);
  .el-table {
    font-size: var(--el-font-size-base);
  }
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
</style>
