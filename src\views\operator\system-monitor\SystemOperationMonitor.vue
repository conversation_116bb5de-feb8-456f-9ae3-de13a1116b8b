<template>
  <HeaderBox title="运行监控" :can-refresh="true" @refresh="init()"/>
  <Statistic v-model:needUpdate="needUpdateMonitorData"/>
  <el-scrollbar v-if="isMobile" class="tw-w-full" view-class="tw-pb-[16px] tw-bg-[#f2f3f5]">
    <div class="tw-flex tw-justify-start tw-items-center tw-p-[8px]">
      <div class="title-normal">
        <span>队列监控</span>
        <el-tooltip content="刷新队列数据统计" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updateQueueData(true)">
            <SvgIcon name="reset" color="inherit"/>
          </el-icon>
        </el-tooltip>
      </div>
      <el-switch
        v-model="swiperEnabled"
        class="tw-mr-auto tw-ml-[24px]"
        size="small"
        inactive-text="只看当前"
        active-text="定时轮播"
        @change="onChangeSwiperEnabled"
      />
      <div class="tw-ml-auto tw-text-[--primary-black-color-400] tw-text-[13px] tw-text-right">更新时间：{{ updateTime ? dayjs(updateTime).format('MM-DD HH:mm:ss') : '暂无数据' }}</div>
    </div>
    <TabsBox v-model:active="activeTab" :tabList="tabList" @update:active="onUpdateTab" />
    <div class="tw-sticky tw-bg-[#f2f3f5] tw-top-[-2px] tw-left-0 tw-w-full tw-grid tw-grid-cols-6 tw-py-[4px] tw-text-[12px] tw-px-[8px]">
      <span class="tw-text-[--primary-black-color-600] tw-font-[600] tw-text-center">Ready</span>
      <span class="tw-text-[--primary-black-color-600] tw-font-[600] tw-text-center">UnAcked</span>
      <span class="tw-text-[--primary-black-color-600] tw-font-[600] tw-text-center">Total</span>
      <span class="tw-text-[--primary-black-color-600] tw-font-[600] tw-text-center">Incoming</span>
      <span class="tw-text-[--primary-black-color-600] tw-font-[600] tw-text-center">Deliver</span>
      <span class="tw-text-[--primary-black-color-600] tw-font-[600] tw-text-center">Ack</span>
    </div>
    <div class="tw-flex tw-flex-col tw-gap-0 tw-bg-white">
      <div v-for="row in tableData" class="tw-text-[12px] tw-border-t-[1px] first:tw-border-t-0">
        <div class="tw-text-justify tw-flex tw-items-center tw-px-[8px] tw-leading-[20px] tw-font-[600] tw-text-[14px] tw-line-clamp-2 tw-w-full tw-break-all">
          <span class="tw-inline-block tw-rounded-full tw-w-[5px] tw-h-[5px] tw-mr-[4px]" :class="stateClassObj[row.state as MQStateEnum]"></span>
          <span class="tw-font-[600] tw-text-[13px]">
            {{ simplifyName(row.queue) }}
          </span>
        </div>
        <div class="tw-grid tw-grid-cols-6 tw-px-[8px]">
          <div class="tw-text-center" :class="getNumberClassObj(row.ready)">{{ formatNumber1(row.ready) }}</div>
          <div class="tw-text-center" :class="getNumberClassObj(row.unAcked)">{{ formatNumber1(row.unAcked) }}</div>
          <div class="tw-text-center" :class="getNumberClassObj(row.total)">{{ formatNumber1(row.total) }}</div>
          <div class="tw-text-center tw-text-[--primary-black-color-400]">{{ row.incoming ? formatNumber(row.incoming, 2) + '/s' : 0 }}</div>
          <div class="tw-text-center tw-text-[--primary-black-color-400]">{{ row.deliverGet ? formatNumber(row.deliverGet, 2) + '/s' : 0 }}</div>
          <div class="tw-text-center tw-text-[--primary-black-color-400]">{{ row.ack ? formatNumber(row.ack, 2) + '/s' : 0 }}</div>
        </div>
      </div>
    </div>
  </el-scrollbar>
  <el-scrollbar v-else class="tw-min-w-[1080px] sm:tw-min-w-full" view-class="tw-p-[16px] tw-bg-[#f2f3f5]">
    <div class="tw-flex tw-justify-start tw-items-center tw-p-[12px]">
      <div class="title-normal">
        <span>队列监控</span>
        <el-tooltip content="刷新队列数据统计" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updateQueueData(true)">
            <SvgIcon name="reset" color="inherit"/>
          </el-icon>
        </el-tooltip>
      </div>
      <el-switch
        v-model="swiperEnabled"
        class="tw-mr-auto tw-ml-[24px]"
        inactive-text="只看当前"
        active-text="定时轮播"
        @change="onChangeSwiperEnabled"
      />
      <div class="tw-ml-auto tw-text-[--primary-black-color-400] tw-text-[13px] tw-text-right">更新时间：{{ updateTime ? dayjs(updateTime).format('MM-DD HH:mm:ss') : '暂无数据' }}</div>
    </div>
    <TabsBox v-model:active="activeTab" :tabList="tabList" @update:active="onUpdateTab" />
    <el-table
      :data="tableData"
      v-loading="loading2"
      :header-cell-style="tableHeaderStyle"
      stripe
      class="tw-flex-grow"
      row-key="id"
    >
      <el-table-column property="queue" label="Name" align="left" fixed="left" min-width="240" show-overflow-tooltip :formatter="formatterEmptyData">
        <template #default="{ row }">
          <!-- <div class="tw-flex tw-items-center tw-h-[20px] tw-truncate"> -->
            <span class="tw-inline-block tw-rounded-full tw-w-[4px] tw-h-[4px] tw-mr-[4px]" :class="stateClassObj[row.state as MQStateEnum]"></span>
            <span class="tw-font-[600] tw-text-[13px]">
              {{ simplifyName(row.queue) }}
            </span>
          <!-- </div> -->
        </template>
      </el-table-column>
      <el-table-column property="ready" label="Ready" sortable align="left" width="100" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <span :class="getNumberClassObj(row.ready)">
            {{ formatNumber1(row.ready) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column property="unAcked" label="UnAcked" sortable align="left" width="100" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <span :class="getNumberClassObj(row.unAcked)">
            {{ formatNumber1(row.unAcked) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column property="total" label="Total" sortable align="left" width="120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <span :class="getNumberClassObj(row.total)">
            {{ formatNumber1(row.total) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column property="incoming" label="incoming" sortable align="left" min-width="120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.incoming ? formatNumber(row.incoming, 2) + '/s' : 0 }}
        </template>
      </el-table-column>
      <el-table-column property="deliverGet" label="deliver/get" sortable align="left" min-width="120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.deliverGet ? formatNumber(row.deliverGet, 2) + '/s' : 0 }}
        </template>
      </el-table-column>
      <el-table-column property="ack" label="ack" sortable align="left" min-width="120" show-overflow-tooltip>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.ack ? formatNumber(row.ack, 2) + '/s' : 0 }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="content" align="center" min-width="80">
        <template #default="{ row }">
          <el-button link @click="goDetail(row)" type="primary">详情</el-button>
        </template>
      </el-table-column> -->
      <template #empty>
        暂无数据
        <!--<el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />-->
      </template>
    </el-table>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, onBeforeUnmount, defineAsyncComponent } from 'vue'
import { CaretTop, CaretBottom, } from '@element-plus/icons-vue'
import { formatNumber, formatNumber1, } from '@/utils/utils'
import { formatterEmptyData } from "@/utils/utils"
import { monitorStatisticModel } from '@/api/monitor-statistic'
import dayjs from 'dayjs'
import HeaderBox from '@/components/HeaderBox.vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { MonitorTotalInfo, MQMonitorItem, MQStateEnum, } from '@/type/monitor-statistic'
import { ElMessage } from 'element-plus'
import to from 'await-to-js';
import { useGlobalStore } from '@/store/globalInfo'
import { useSystemMonitor } from '@/store/system-monitor'
import { storeToRefs } from 'pinia'
import Statistic from '@/views/operator/merchant-data-manager/OperationMonitor/Statistic.vue'

const TabsBox = defineAsyncComponent(() => import('@/components/TabsBox.vue'))

// 是否是小屏幕
const globalInfo = useGlobalStore()
const isMobile = globalInfo.isMobile

// 是否定时轮播
const systemMonitorStore = useSystemMonitor()
const { swiperEnabled } = storeToRefs(systemMonitorStore)

/** 头部数据 开始 */
const monitorData = reactive<MonitorTotalInfo>({})
// 头部数据loading
const needUpdateMonitorData = ref(false)
const updateMonitorData = async () => {
  needUpdateMonitorData.value = true
}
/** 头部数据 结束 */

/** 底部队列监控列表 开始 */
type TabName = 'PT0' | 'PT1' | 'PT2' | 'PT3'
const tabList: TabName[] = ['PT0', 'PT1', 'PT2', 'PT3']
const activeTab = ref<TabName>(tabList[0])
const tableData = ref<MQMonitorItem[]>([]) // 全部数据
const updateTime = ref('')
// 列表轮播定时器
let swiperTimer: number | null = null
/**
 * 列表轮播定时器 处理
 */
const handleSwiperTimer = () => {
  // console.log('handleSwiperTimer')
  clearLoop()

  let currentIndex = tabList.findIndex((item: TabName) => {
    return item === activeTab.value
  })
  if (-1 < currentIndex && currentIndex < tabList.length - 1) {
    currentIndex++
  } else {
    currentIndex = 0
  }
  activeTab.value = tabList[currentIndex]

  // 更新列表数据
  updateQueueData()
  addLoop()
}
/**
 * 列表轮播定时器 开启
 */
const startSwiperTimer = () => {
  stopSwiperTimer()
  // console.log('startSwiperTimer')
  swiperTimer = <number><unknown>setInterval(handleSwiperTimer, 5000)
}
/**
 * 列表轮播定时器 关闭
 */
const stopSwiperTimer = () => {
  // console.log('stopSwiperTimer')
  swiperTimer && clearInterval(swiperTimer)
  swiperTimer = null
}
/**
 * 列表轮播 轮播开关
 * @param val 开关状态
 */
const onChangeSwiperEnabled = (val: boolean) => {
  val ? startSwiperTimer() : stopSwiperTimer()
}
/**
 * 标签页组件 更新
 */
const onUpdateTab = () => {
  updateQueueData()
  if (swiperEnabled.value) {
    startSwiperTimer()
  }
}
/** 搜索和接口数据获取 */
const loading2 = ref(false)
const updateQueueData = async (needTip: boolean = false) => {
  const [_, res] = await to(monitorStatisticModel.findMonitorMQList({
    platform: activeTab.value ?? tabList[0],
  })) as [any, {
    data: MQMonitorItem[],
    updateTime: string
  }]
  tableData.value = res.data || []
  updateTime.value = res.updateTime
  needTip && ElMessage.success('队列数据刷新成功')
}
// const currentQueue = ref<string>('')
// const currentContent = ref<string>('')
// const dialogVisible = ref(false)
// const goDetail = (row: MQMonitorItem) => {
//   currentQueue.value = row.queue || ''
//   currentContent.value = row.content || ''
//   dialogVisible.value = true
// }
const stateClassObj: Record<MQStateEnum, string> = {
  [MQStateEnum['运行中']]: 'tw-bg-[--primary-green-color]',
  [MQStateEnum['空闲中']]: 'tw-bg-[--el-text-color-primary]',
}
const getNumberClassObj = (num?: number) => {
  if (!num) return 'tw-text-[--primary-black-color-400]'
  if (num > 5000) {
    return 'tw-text-[--primary-red-color] tw-font-[600]'
  }
  if (num > 1000) {
    return 'tw-text-[--primary-orange-color] tw-font-[600]'
  }
  return 'tw-text-[--primary-black-color-400]'
}
const simplifyName = (val: string) => {
  let res = ''
  if (val?.split('_')?.length >= 7) {
    if(val?.startsWith('call_') || val?.startsWith('CALL_')) {
      res = val.slice(5)
    } else {
      res = val
    }
    return res
  } else {
    return val
  }
}
/** 底部账号维度列表 结束 */

// 初始化数据
const init = async () => {
  updateMonitorData() // 顶部数据获取
  updateQueueData() // 中间账号维度【图表】获取 - 除导入名单
}

/** 定时轮询 开始 */
// 【定时更新】每隔一定事件触发刷新，【进入刷新】包括tab切换和浏览器标签切换，触发刷新
const timer1 = ref() // time1为顶部每5分钟刷新
const timer2 = ref() // time2为列表每5秒刷新
// 浏览器标签切换，触发刷新
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    init()
  }
}
// 添加定时器和监听器
const addLoop = () => {
  // timer1.value && clearInterval(timer1.value)
  timer2.value && clearInterval(timer2.value)
  document.addEventListener('visibilitychange', handleVisibilityChange)
  // timer1.value = setInterval(() =>  updateMonitorData(), 5 * 60 * 1000)
  timer2.value = setInterval(() =>  updateQueueData(), 5000)
}
// 移除定时器和监听器
const clearLoop = () => {
  // if (timer1.value) {
  //   clearInterval(timer1.value)
  //   timer1.value = null
  // }
  if (timer2.value) {
    clearInterval(timer2.value)
    timer2.value = null
  }
  document.removeEventListener('visibilitychange', handleVisibilityChange)
}
onMounted(() => {
  timer1.value = setInterval(() =>  updateMonitorData(), 5 * 60 * 1000)
  if (swiperEnabled.value) {
    startSwiperTimer()
  } else {
    addLoop()
  }
})
onBeforeUnmount(() => {
  timer1.value && clearInterval(timer1.value)
  stopSwiperTimer()
  clearLoop()
})
init()
</script>

<style scoped lang="postcss" type="text/postcss">
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
  .title-normal {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-black-color-600);
    line-height: 20px;
    display: flex;
    align-items: center;
    @media screen and (max-width: 600px) {
      font-size: 13px;
    }
  }
  .number-normal {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-blue-color);
    line-height: 28px;
    @media screen and (max-width: 600px) {
      font-size: 16px;
    }
  }
  .title-small {
    font-size: 13px;
    color: var(--primary-black-color-400);
    line-height: 20px;
    @media screen and (max-width: 600px) {
      font-size: 11px;
    }
  }
  .number-small {
    font-size: 13px;
    color: var(--primary-black-color-400);
  }
  .content-normal {
    color: var(--primary-black-color-600);
    font-size: 13px;
    line-height: 18px;
    text-align: justify;
  }
  .status-box {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    width: 54px;
  }
</style>
