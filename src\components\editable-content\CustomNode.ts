import { Node } from '@tiptap/core'

export default Node.create({
  name: 'customNode',
  group: 'inline',
  inline: true,
  atom: true,
  leaf: true,
  // 获取Attr，动态添加属性
  addAttributes() {
    return {
      content: {
        default: '123',
      },
    }
  },
  // 定义从 HTML 到节点的解析规则。
  parseHTML() {
    return [
      {
        tag: 'CustomNode',
      },
    ]
  },
  // 定义节点如何渲染为 HTML
  renderHTML({ HTMLAttributes }) {
    return ['CustomNode', HTMLAttributes, 0]
  },
  addNodeView() {
    return ({ editor, node, getPos }) => {
      const dom = document.createElement('div')
      dom.classList.add('custom-node')
      dom.innerText = node.attrs.content || ''
      return {
        dom,
      }
    }
  },
})