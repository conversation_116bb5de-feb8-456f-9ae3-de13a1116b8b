<template>
  <el-form
    v-if="props.type!==FrequencyRestrictionTypeEnum.GLOBAL"
    class="search-box"
    label-position="top"
  >
    <el-form-item v-if="props.type===FrequencyRestrictionTypeEnum.INDUSTRY" label="行业：">
      <!--行业选择-->
      <el-cascader
        v-model="industrySearchVal"
        :options="industryOptions"
        :props="industryProps"
        clearable
        collapse-tags
        collapse-tags-tooltip
        :max-collapse-tags="2"
        :show-all-levels="false"
        placeholder="下拉选择"
        style="width: 300px;"
      />
      <!--查询按钮-->
      <el-button type="primary" link class="tw-ml-[12px]" @click="onClickSearch(industrySearchVal)">
        <el-icon size="--el-font-size-base" color="var(--el-color-primary)">
          <SvgIcon name="search" color="none" />
        </el-icon>
        <span>查询</span>
      </el-button>
    </el-form-item>

    <el-form-item v-else-if="props.type===FrequencyRestrictionTypeEnum.PRODUCT" label="产品：">
      <!--产品选择-->
      <el-select
        v-model="productSearchVal"
        clearable
        filterable
        placeholder="下拉选择"
        style="width: 300px;"
      >
        <el-option
          v-for="productItem in productOptions"
          :key="productItem.id"
          :label="productItem.name"
          :value="productItem.id"
        />
      </el-select>
      <!--查询按钮-->
      <el-button type="primary" link class="tw-ml-[12px]" @click="onClickSearch(productSearchVal)">
        <el-icon size="--el-font-size-base" color="var(--el-color-primary)">
          <SvgIcon name="search" color="none" />
        </el-icon>
        <span>查询</span>
      </el-button>
    </el-form-item>

  </el-form>

  <div v-show="!(props.type===FrequencyRestrictionTypeEnum.GLOBAL&&allList.length>=1)" class="button-box">
    <el-button type="primary" class="tw-ml-auto" @click="onClickAdd">
      <template v-if="props.type===FrequencyRestrictionTypeEnum.GLOBAL">
        新增全局限制
      </template>
      <template v-else-if="props.type===FrequencyRestrictionTypeEnum.INDUSTRY">
        新增行业限制
      </template>
      <template v-else-if="props.type===FrequencyRestrictionTypeEnum.PRODUCT">
        新增产品限制
      </template>
    </el-button>
  </div>

  <el-table
    :header-cell-style="tableHeaderStyle"
    :data="currentList"
  >
    <el-table-column v-if="props.type===FrequencyRestrictionTypeEnum.GLOBAL" align="left" prop="scope" label="范围" width="60" show-overflow-tooltip>
      全局
    </el-table-column>
    <el-table-column v-else-if="props.type===FrequencyRestrictionTypeEnum.INDUSTRY" align="left" prop="industry" label="行业" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:FrequencyRestrictionInfo}">
        {{ computeText(row?.productIndustryId ?? '', FrequencyRestrictionTypeEnum.INDUSTRY) }}
      </template>
    </el-table-column>
    <el-table-column v-else-if="props.type===FrequencyRestrictionTypeEnum.PRODUCT" align="left" prop="product" label="产品" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:FrequencyRestrictionInfo}">
        {{ computeText(row?.productIndustryId ?? '', FrequencyRestrictionTypeEnum.PRODUCT) }}
      </template>
    </el-table-column>

    <el-table-column align="left" prop="callingRestrictions" label="拨打限制" min-width="300" show-overflow-tooltip>
      <template #default="{row}:{row:FrequencyRestrictionInfo}">
        {{ formatFrequencyRestrictionsText(1, row?.callLimit ?? []) }}
      </template>
    </el-table-column>
    <el-table-column align="left" prop="dialingRestrictions" label="拨通限制" min-width="300" show-overflow-tooltip>
      <template #default="{row}:{row:FrequencyRestrictionInfo}">
        {{ formatFrequencyRestrictionsText(2, row?.putThroughLimit ?? []) }}
      </template>
    </el-table-column>

    <!--<el-table-column align="right" fixed="right" label="操作" :width="props.type===FrequencyRestrictionTypeEnum.GLOBAL?60:100">-->
    <el-table-column align="right" fixed="right" label="操作" width="100">
      <template #default="{row}">
        <el-button type="primary" link @click="onClickEdit(row)">
          编辑
        </el-button>
        <el-button type="danger" link @click="onClickDelete(row)">
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>

  <!--分页条-->
  <PaginationBox
    :currentPage="pageNum"
    :pageSize="pageSize"
    :pageSizeList="pageSizeList"
    :total="total"
    @search="updateList"
    @update="updateList"
  />

  <!--编辑频率限制弹窗-->
  <FrequencyRestrictionDialog
    :visible="dialogVisible"
    :isEdit="dialogIsEdit"
    :type="props.type"
    :data="dialogData"
    :list="allList"
    :options="dialogOptions"
    :mapList="dialogMapList"
    @close="onDialogClose"
    @update="onDialogUpdate"
  />
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import { computed, defineAsyncComponent, nextTick, ref, watch } from 'vue'
import { CallLimit, FrequencyRestrictionInfo, FrequencyRestrictionTypeEnum, PutThroughLimit } from '@/type/dataFilter'
import { useGlobalStore } from '@/store/globalInfo'
import Confirm from '@/components/message-box'
import { CascaderProps, ElMessage } from 'element-plus'
import { updateCurrentPageList } from '@/utils/utils'
import { frequencyRestrictionModel } from '@/api/data-filter'
import to from 'await-to-js'
import { IndustryOption, ProductItem } from '@/type/industry'

// 动态引入组件
const PaginationBox = defineAsyncComponent(() => import('@/components/PaginationBox.vue'))
const FrequencyRestrictionDialog = defineAsyncComponent(() => import('@/components/FrequencyRestrictionDialog.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  type: FrequencyRestrictionTypeEnum,
  data: FrequencyRestrictionInfo[],
}>(), {
  type: FrequencyRestrictionTypeEnum.GLOBAL,
  data: () => [],
})
const emits = defineEmits(['update'])

const globalStore = useGlobalStore()

watch(props, () => {
  // console.log('watch props updateAllList')
  updateAllList()
})

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 搜索 开始 ----------------------------------------

// 搜索条件，行业，组件表单值
const industrySearchVal = ref<string>('')
// 搜索条件，行业，级联选择器，数据内容
const industryOptions = ref<any[]>([])
// 搜索条件，行业，级联选择器，配置信息
const industryProps: CascaderProps = {
  multiple: false,
  emitPath: false,
  value: 'id',
  label: 'name',
  children: 'secondaryIndustries',
}
// 搜索条件，行业，名称和ID映射表
const industryMapList = ref<{ id: number, name: string }[]>([])

// 搜索条件，产品，组件表单值
const productSearchVal = ref<string>('')
// 搜索条件，产品，选择框，数据内容
const productOptions = ref<any[]>([])
// 搜索条件，产品，名称和ID映射表
const productMapList = ref<{ id: number, name: string }[]>([])

/**
 * 获取行业列表
 */
const getIndustryList = async () => {
  // console.log('获取行业列表')
  try {
    await globalStore.getAllIndustryList()
    industryOptions.value = globalStore.getIndustryOption
    industryMapList.value = globalStore.getIndustryIdAndNameMapList
  } catch (e) {
    ElMessage.error('无法正确获取行业列表')
  } finally {
  }
}
/**
 * 获取产品列表
 */
const getProductList = async () => {
  // console.log('获取产品列表')
  try {
    await globalStore.updateProductList()
    productOptions.value = globalStore.productList
    productMapList.value = globalStore.getProductIdAndNameMapList
  } catch (e) {
    ElMessage.error('无法正确获取产品列表')
  } finally {
  }
}
/**
 * 点击查询按钮
 * @param {string} val 搜索框内容
 */
const onClickSearch = (val: string) => {
  emits('update', val)
}

// ---------------------------------------- 搜索 结束 ----------------------------------------

// ---------------------------------------- 列表 开始 ----------------------------------------

// 列表 全部
const allList = ref<FrequencyRestrictionInfo[]>([])

// 列表 当前页
const currentList = ref<FrequencyRestrictionInfo[]>([])
// 当前页码
const pageNum = ref(1)
// 每页大小
const pageSize = ref(20)
// 可选页码大小
const pageSizeList = [10, 20, 50, 100]
// 总数
const total = computed(() => {
  return allList.value.length ?? 0
})

/**
 * 列表 更新 全部
 */
const updateAllList = async () => {
  // console.log('table updateAllList')
  allList.value = props.data?.length ? props.data : []
  // console.log('table allList', allList.value.length)
  updateList(pageNum.value, pageSize.value)
}
/**
 * 列表 更新 当前页码
 */
const updateList = (p?: number, s?: number) => {
  if (p || s) {
    // 如果参数指定了页码或/和每页大小，则按参数更新列表
    p && (pageNum.value = p)
    s && (pageSize.value = s)
    currentList.value = updateCurrentPageList(allList.value, pageNum.value, pageSize.value)
  } else {
    // 如果参数都没有指定，则页码和页面大小保持不变，请求接口更新
    // updateAllList()
    emits('update', industrySearchVal.value)
  }
}
/**
 * 删除列表行 请求接口
 * @param {FrequencyRestrictionInfo} row 列表行，限制信息
 */
const deleteRow = async (row: FrequencyRestrictionInfo) => {
  // 处理参数
  const params: FrequencyRestrictionInfo = {
    id: row.id ?? undefined,
    frequentType: row.frequentType + '',
    productIndustryId: row.productIndustryId + '',
  }
  // 请求接口
  const [err, _] = <[any, FrequencyRestrictionInfo]>await to(frequencyRestrictionModel.delete(params)
  )
  if (err) {
    ElMessage.error('删除失败')
    return
  }
  ElMessage.success('删除成功')
  // 更新列表
  emits('update', industrySearchVal.value)
}
/**
 * 列表 点击删除按钮
 * @param {FrequencyRestrictionInfo} row 点击的列表行，限制信息
 */
const onClickDelete = (row: FrequencyRestrictionInfo) => {
  let name = ''
  if (props.type === FrequencyRestrictionTypeEnum.GLOBAL) {
    name = '全局'
  } else if (props.type === FrequencyRestrictionTypeEnum.INDUSTRY) {
    name = computeText(row.productIndustryId ?? '', FrequencyRestrictionTypeEnum.INDUSTRY) ?? ''
  } else if (props.type === FrequencyRestrictionTypeEnum.PRODUCT) {
    name = computeText(row.productIndustryId ?? '', FrequencyRestrictionTypeEnum.PRODUCT) ?? ''
  }

  // 显示删除二次确认
  Confirm({
    text: `确定要删除${props.type}【${name}】吗？`,
    type: 'danger',
    title: '删除确认'
  }).then(() => {
    deleteRow(row)
  }).catch(() => {
  })
}
/**
 * 列表 点击编辑按钮
 * @param {FrequencyRestrictionInfo} row 点击的列表行，限制信息
 */
const onClickEdit = (row: FrequencyRestrictionInfo) => {
  showDialog(row, true)
}
/**
 * 列表 点击新增按钮
 */
const onClickAdd = () => {
  showDialog(dialogDataDefault(), false)
}
/**
 * 格式化频率限制文本
 * @param {number} type 类型，1: 拨打限制, 2: 拨通限制
 * @param {CallLimit[] | PutThroughLimit[]} list 频率限制信息列表
 */
const formatFrequencyRestrictionsText = (type: number = 1, list: CallLimit[] | PutThroughLimit[] = []) => {
  const originalList = list?.length ? list : []
  let resultList: string[] = []

  if (type === 1) {
    resultList = (<CallLimit[]>originalList).map((item: CallLimit): string => {
      return `${item.callLimit ?? ''}次/${item.callLimitTime ?? ''}天`
    })
  } else if (type === 2) {
    resultList = (<PutThroughLimit[]>originalList).map((item: PutThroughLimit) => {
      return `${item.putThroughLimit ?? ''}次/${item.putThroughLimitTime ?? ''}天`
    })
  }

  return resultList.join('、') || '-'
}
const computeText = (id: string, type: FrequencyRestrictionTypeEnum) => {
  let list = industryMapList.value
  if (type === FrequencyRestrictionTypeEnum.PRODUCT) {
    list = productMapList.value
  }
  const result = list.find((item) => {
    return item.id + '' === id
  })
  return result?.name
}

// ---------------------------------------- 列表 结束 ----------------------------------------

// ---------------------------------------- 频率限制弹窗 开始 ----------------------------------------

// 频率限制弹窗 显示
const dialogVisible = ref<boolean>(false)
// 频率限制弹窗 新增编辑
const dialogIsEdit = ref<boolean>(false)
// 频率限制弹窗 表单默认数据
const dialogDataDefault = () => {
  return {
    id: undefined,
    frequentType: '',
    productIndustryId: '',
    productIndustryName: '',
    callLimit: [],
    putThroughLimit: [],
  }
}
// 频率限制弹窗 表单数据
const dialogData = ref<FrequencyRestrictionInfo>(dialogDataDefault())
// 频率限制弹窗 选择框 数据内容
const dialogOptions = ref<IndustryOption[] | ProductItem[]>([])
// 频率限制弹窗 选择框 名称ID映射表
const dialogMapList = ref<{ id: number, name: string }[]>([])

/**
 * 频率限制弹窗 显示
 */
const showDialog = (data: FrequencyRestrictionInfo, isEdit: boolean = false) => {
  // 点击的项目信息传入弹窗组件
  dialogData.value = JSON.parse(JSON.stringify(data))
  dialogIsEdit.value = isEdit

  // 更新选择框数据
  if (props.type === FrequencyRestrictionTypeEnum.INDUSTRY) {
    // 行业
    dialogOptions.value = JSON.parse(JSON.stringify(industryOptions.value))
    // 新增需要剔除已添加，编辑保持全部
    if (!isEdit) {
      dialogOptions.value = (<IndustryOption[]>dialogOptions.value).filter((primaryItem: IndustryOption) => {
        primaryItem.secondaryIndustries = primaryItem.secondaryIndustries.filter((secondaryItem: {
          name: string,
          id: number
        }) => {
          return !allList.value.find((item: FrequencyRestrictionInfo) => {
            return Number(item?.productIndustryId) === secondaryItem?.id
          })
        })
        return !!(primaryItem.secondaryIndustries?.length ?? 0)
      })
    }
    dialogMapList.value = globalStore.getIndustryIdAndNameMapList
  } else if (props.type === FrequencyRestrictionTypeEnum.PRODUCT) {
    // 产品
    dialogOptions.value = JSON.parse(JSON.stringify(productOptions.value))
    // 新增需要剔除已添加，编辑保持全部
    if (!isEdit) {
      dialogOptions.value = globalStore.productList.filter((allItem: ProductItem) => {
        return !allList.value.find((item: FrequencyRestrictionInfo) => {
          return Number(item?.productIndustryId) === allItem?.id
        })
      })
    }
    dialogMapList.value = globalStore.getProductIdAndNameMapList
  } else {
    // 全局
    dialogOptions.value = []
    dialogMapList.value = []
  }

  nextTick().then(() => {
    // 显示弹窗
    dialogVisible.value = true
  })
}
/**
 * 频率限制弹窗 关闭
 */
const onDialogClose = () => {
  dialogVisible.value = false
  dialogOptions.value = []
  dialogMapList.value = []
}
/**
 * 频率限制弹窗 更新
 * @param {FrequencyRestrictionInfo} data 新数据
 */
const onDialogUpdate = (data: FrequencyRestrictionInfo) => {
  // 更新父组件表单数据
  dialogData.value = data
  // 更新父组件列表
  emits('update', industrySearchVal.value)
}

// ---------------------------------------- 频率限制弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// 更新搜索条件
if (props.type === FrequencyRestrictionTypeEnum.INDUSTRY) {
  getIndustryList()
} else if (props.type === FrequencyRestrictionTypeEnum.PRODUCT) {
  getProductList()
}

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
/* 搜索工具条 */
.search-box {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: stretch;
  flex-wrap: nowrap;
  margin-bottom: 16px;
  padding: 16px 16px 0;
}
/* 表单项组件 */
.el-form-item {
  margin-bottom: 0;
}
/* 按钮工具条 */
.button-box {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: stretch;
  flex-wrap: nowrap;
  margin-top: 12px;
  padding: 0 16px 12px;
}
/* 文本框内的标签 */
:deep(.el-cascader__tags .el-tag) {
  margin: 0 0 0 4px;
}
</style>
