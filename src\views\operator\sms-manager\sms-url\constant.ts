import dayjs from 'dayjs'
import { SmsStatisticSearch } from '@/type/sms'
export class UrlOrigin {

  constructor(type?: number) {
    if (type === 0) {
      this.expireTime = dayjs().add(7, 'd').endOf('d').format('YYYY-MM-DD')
      this.expireDays = undefined
    } else if (type === 1) {
      this.expireTime = undefined
      this.expireDays = 3
    } else {
      this.expireTime = undefined
      this.expireDays = undefined
    }
  }
  linkNumber = undefined
  linkName = undefined
  shortUrl = undefined
  originalUrl = undefined
  groupId = undefined
  account = undefined
  domain = undefined
  enableStatus = undefined
  tenantSmsTemplates = undefined
  needRegister = undefined
  expireTime
  expireDays
  paramTypes = undefined
  id = undefined
}

export const shortcuts = [
  {
    text: '今天',
    value: dayjs().endOf('d').format('YYYY-MM-DD'),
  },
  {
    text: '7天',
    value: dayjs().add(7, 'd').endOf('d').format('YYYY-MM-DD'),
  },
  {
    text: '30天',
    value: dayjs().add(30, 'd').endOf('d').format('YYYY-MM-DD'),
  },
]

// 到期时间选择限制只能选进入往后，且不超过31天
export const disabledFn = (date: Date) => {
  return dayjs(date).isBefore(dayjs().startOf('day')) || dayjs(date).isAfter(dayjs().add(31, 'days').endOf('day'))
}

export type ChartItem = {
  name: string;
  xName?: string
  value1: number;
  value2: number;
  value3?: number;
}
export class StatisticSearchOrigin implements SmsStatisticSearch {
  constructor(linkNumber: string) {
    this.linkNumber = linkNumber
  }
  linkNumber
  domain = undefined
  originalUrl = undefined
  date = dayjs().format('YYYY-MM-DD')
  size = 5
}
