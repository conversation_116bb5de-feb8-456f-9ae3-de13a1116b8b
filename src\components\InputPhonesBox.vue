<template>
  <el-input
    v-model="searchPhone"
    placeholder="号码/名单编号"
    @keyup.enter="search()"
    @blur="updatePhone"
    clearable
  >
    <template #suffix>
      <el-button :icon="FullScreen" type="primary" link @click="showPhone"/>
    </template>
  </el-input>
  <el-dialog
    v-model="phoneDialogVisible"
    width="480px"
    :close-on-click-modal="false"
    align-center
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">号码/名单编号</div>
    </template>
    <div class="tw-grid tw-grid-cols-2 tw-gaps-1 tw-p-[12px]">
      <div>
        <el-input
          v-model="tempPhone"
          :autosize="{minRows:15, maxRows:25}"
          type="textarea"
          @blur="formatPhone(tempPhone)"
          placeholder="请输入号码/名单编号"
        />
      </div>
      <div class="tw-ml-[12px]">
        <div class="tw-text-left tw-mb-[6px] tw-text-[14px]">号码/名单编号：</div>
        <el-scrollbar max-height="60vh">
          <li v-for="p in tempPhone?.split(',') || []" v-show="!!p" class="tw-text-left info-title tw-list-decimal tw-leading-[22px] tw-my-[2px] tw-break-all">{{ p }}</li>
        </el-scrollbar>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmPhone">确认</el-button>
        <el-button @click="cancelPhone">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, } from 'vue'
import { FullScreen } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus';

// 组件入参props
const props = withDefaults(defineProps<{
  value?: string
}>(), {})
// emit
const emits = defineEmits([
  'update:value', 'search',
])

/** 号码、名单编号批量输入显示弹窗 开始 */
const phoneDialogVisible = ref(false) // 名单编号弹窗Visible
const searchPhone = ref(props.value || '')
const tempPhone = ref('') // 名单编号弹窗 数据
// 点击icon打开名单编号弹窗
const showPhone = () => {
  phoneDialogVisible.value = true
  tempPhone.value = searchPhone.value
}
// 点击名单编号弹窗确认
const confirmPhone = () => {
  const len = tempPhone.value?.split(',')?.length || 0
  if (len > 500) {
    ElMessage.warning('单次最多支持查询500个号码，已为您自动截断')
    tempPhone.value = tempPhone.value?.split(',').slice(0, 500).join(',')
  }
  searchPhone.value = tempPhone.value
  phoneDialogVisible.value = false
  tempPhone.value = ''
  search()
}
// 点击名单编号弹窗取消
const cancelPhone = () => {
  phoneDialogVisible.value = false
  tempPhone.value = ''
}
// 弹窗-失焦后，格式化名单编号
const formatPhone = (val?: string) => {
  if (!val || !val?.trim())  {
    tempPhone.value = ''
  } else {
    const str = val?.trim()?.replace(/\s+/g, ',') || ''
    tempPhone.value = [...new Set(str.split(',').map(item => item.trim()).filter(item => !!item))].join(',')
  }
}
const search = () => {
  emits('update:value', searchPhone.value)
  emits('search')
}
const updatePhone = () => {
  if (!searchPhone.value || !searchPhone.value?.trim())  {
    searchPhone.value = ''
  } else {
    const str = searchPhone.value?.trim()?.replace(/\s+/g, ',') || ''
    searchPhone.value = [...new Set(str.split(',').map(item => item.trim()).filter(item => !!item))].join(',')
  }
  const len = searchPhone.value?.split(',')?.length || 0
  if (len > 500) {
    ElMessage.warning('单次最多支持查询500个号码，已为您自动截断')
    searchPhone.value = searchPhone.value?.split(',').slice(0, 100).join(',')
  }
  emits('update:value', searchPhone.value)
}
/** 号码、名单编号批量输入显示弹窗 结束 */
/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(props, () => {
  searchPhone.value = props.value || ''
}, {deep: true, immediate: true})
</script>

<style lang="postcss" type="text/postcss" scoped>

.select-dom {
  :deep(.el-select-tags-wrapper) {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: hidden;
    .el-tag {
      padding: 0 2px;
    }
    .el-tag__content {
      display: flex;
      align-items: center;
    }
  }
}
.popper-dom {
  .el-button {
    font-size: 13px;
  }
  .el-select-dropdown__item {
    padding: 8px;
  }
  .el-select-dropdown.is-multiple .el-select-dropdown__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .option-checkbox {
      width: 14px;
      height: 14px;
      border-radius: 2px;
      border-width: 1px;
      position: relative;
      margin-right: 8px;
      .el-icon {
        display: none;
        position: absolute;
        top: 1px;
        left: 0px;
      }
    }
    &.selected {
      font-weight: normal;
      color: #165DFF;
      .option-checkbox {
        background-color: #165DFF;
        border-color: #165DFF;
        .el-icon {
          display:inline-block;
        }
      }
    }
    &.selected::after {
      display: none;
      content: '';
    }
  }
}
</style>
