<template>
  <template v-if="!!currentSemantics.id" >
    <div class="tw-flex tw-justify-between tw-items-center tw-flex-0 tw-pr-[14px] tw-text-[18px] tw-font-bold">
      <span>{{ currentSemantics.semantic }}</span>
      <div v-if="!readonly">
        <el-button v-if="!isEdit" type="primary" @click="isEdit= true">编辑</el-button>
        <el-button v-if="isEdit" type="primary" @click="save">保存</el-button>
        <el-button v-if="isEdit" @click="cancel">取消</el-button>
      </div>
    </div>
    <div class="tw-flex-1 tw-overflow-x-hidden tw-overflow-y-auto tw-w-[100%] tw-mt-[1em] tw-mr-[-14px]">
      <el-scrollbar class="tw-pr-[14px]">
        <div class="tw-p-[1em] tw-bg-white">
          <div class="tw-flex tw-justify-between tw-min-h-[2em]">
            <span class="tw-text-[18px]">核心短语</span>
            <div v-if="!readonly">
              <el-button v-if="!isEdit" @click="copy">复制</el-button>
              <el-button v-else type="primary" link @click="edit()">新增短语</el-button>
            </div>
          </div>
          <div class="tw-flex tw-flex-wrap">
            <el-tag
              v-for="(item,index) in corePhraseList"
              :key="index"
              class="tw-h-auto tw-m-[.5em] tw-cursor-pointer tw-whitespace-break-spaces"
              style="height: auto; padding: 0.4em; line-height: 1.4em; text-align: left; white-space: break-spaces"
              size="large"
              :closable="isEdit"
              @click="isEdit && edit(item)"
              @close="handleClose(item)"
            >
              {{ item.phraseName }}
            </el-tag>
          </div>
        </div>
        <div class="tw-mt-[1em] tw-p-[1em] tw-bg-white">
          <div class="tw-flex tw-justify-between tw-min-h-[2em]">
            <div class="tw-text-[18px]">短语变更内容</div>
            <span>最近变更时间：{{ recentUpdateTime }}</span>
          </div>
          <div>
            <div class="tw-flex tw-items-center tw-flex-wrap tw-min-h-[2em]">
              <span class="tw-shrink-0 tw-mr-1">删除短语</span>
              <el-tag
                v-for="(item,index) in corePhraseDeleteList"
                :key="index"
                effect="light"
                type="info"
                class="tw-m-[.5em]"
                style="height: auto; padding: 0.4em; line-height: 1.4em; text-align: left; white-space: break-spaces"
                size="large"
                :closable="isEdit"
                @close="handleAdd(item)"
              >
                {{ item }}
              </el-tag>
            </div>
            <div class="tw-flex tw-items-center tw-flex-wrap tw-min-h-[2em]">
              <span class="tw-shrink-0 tw-mr-1">新增短语</span>
              <el-tag
                v-for="(item,index) in corePhraseAddList"
                :key="index"
                effect="light"
                type="success"
                class="tw-m-[.5em]"
                style="height: auto; padding: 0.4em; line-height: 1.4em; text-align: left; white-space: break-spaces"
                size="large"
                :closable="isEdit"
                @close="handleClose(item)"
              >
                {{ item }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </template>
  <div
    v-else
    class="tw-bg-white tw-min-h-full tw-flex tw-item-center tw-justify-center"
  >
    <el-empty description="暂无数据" />
  </div>
  <!--增改短语弹窗-->
  <AddCorePhaseDialog
    v-model:visible="addVisible"
    :addData="addData"
    @confirm="confirmAdd"
  ></AddCorePhaseDialog>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, toRaw, watch } from 'vue'
import { AiCorePhrase, AiCorePhraseRecord, AiSemantics, AiSemanticsResponse } from '@/type/core-semantic'
import AddCorePhaseDialog from './AddCorePhaseDialog.vue'
import dayjs from 'dayjs'
import { deduplicateBaseArray, deduplicateObjectArray } from '@/utils/utils'
import Confirm from '@/components/message-box'
const emits = defineEmits([
  'copy',
  'renovate',
  'updateRecord'
])
const semanticsList = ref<AiSemantics[]>([])
// 父组件（语义管理）传来的参数
// 当前语义信息和核心短语列表
const { corePhraseAllData, currentSemantics, readonly } = defineProps<{
  corePhraseAllData: AiSemanticsResponse
  currentSemantics: AiSemantics
  readonly?: boolean
}>()

// 核心短语是否编辑模式
const isEdit = ref(false)

// 用于接口参数
// 核心短语列表编辑后的数据内容
const corePhraseData = ref<AiCorePhraseRecord>({
  // 底部的删除短语记录
  deleteList: [],
  // 底部的新增短语记录
  saveList: []
})

const debugList = () => {
  // console.log('要发给接口的数据列表 删除', corePhraseData.value.deleteList.length, '个', corePhraseData.value.deleteList,
  //   '新增', corePhraseData.value.saveList.length, '个', corePhraseData.value.saveList)
}

// 用于页面展示
// 和父组件props隔离开
let corePhraseList = ref<AiCorePhrase[]>([])
let corePhraseDeleteList = ref<string[]>([])
let corePhraseAddList = ref<string[]>([])

// 监听父组件是否切换语义
// 如果切换，则更新核心短语列表
watch(
  () => corePhraseAllData,
  (val, oldVal) => {
    // console.log('corePhraseAllData', corePhraseAllData)

    // 更新用于页面展示的列表
    corePhraseList.value = []
    corePhraseList.value.push(...(JSON.parse(JSON.stringify(corePhraseAllData.aiCorePhrases)) || []) as AiCorePhrase[])
    corePhraseDeleteList.value = []
    corePhraseDeleteList.value.push(...(JSON.parse(JSON.stringify(corePhraseAllData.deleteAICorePhrases)) || []) as string[])
    corePhraseAddList.value = []
    corePhraseAddList.value.push(...(JSON.parse(JSON.stringify(corePhraseAllData.insertAICorePhrases)) || []) as string[])

    // 接口数据的列表默认为空
    // 只有用户操作增删才修改列表

    // 清空要发给接口的两个记录列表，准备好下次编辑
    corePhraseData.value.saveList = []
    corePhraseData.value.deleteList = []
    // console.log('清空要发给接口的两个记录列表，准备好下次编辑', corePhraseData.value.saveList, corePhraseData.value.deleteList)
  },
  {
    deep: true
  }
)

/**
 * 监听核心语义变化，若在编辑状态且存在变动，则确认是否保存之前核心语义编辑内容。
 */
 watch(() => currentSemantics.id, n => {
  if(isEdit.value && (corePhraseData.value.saveList?.length > 0 || corePhraseData.value.deleteList?.length > 0)) {
    const tempData =  JSON.parse(JSON.stringify(corePhraseData.value))
    Confirm({
      text: `您是否要保存上一条核心语义中的修改`,
      type: 'warning',
      title: '保存'
    }).then(() => {
      // 退出编辑模式
      isEdit.value = false
      // 将修改后的底部增删记录提交给接口
      emits('updateRecord', n, tempData)
    }).catch(() => {})
  }
}, )

watch(
  corePhraseData,
  (val, oldVal) => {
    debugList()
  },
  {
    deep: true
  }
)

/**
 * 获取最近更新时间字符串
 */
const recentUpdateTime = computed(() => {
  return corePhraseAllData.updateTime ? dayjs(corePhraseAllData.updateTime).format('YYYY-MM-DD HH:mm:ss') : ''
})

/**
 * 编辑模式下点击取消按钮
 */
const cancel = () => {
  // 退出编辑模式
  isEdit.value = false
  // 啥改动都不保存
  // 通知语义管理父组件，更新核心短语列表，参数是当前语义ID
  emits('renovate', currentSemantics.id)
}

/**
 * 编辑模式下点击保存按钮
 */
const save = () => {
  // 退出编辑模式
  isEdit.value = false
  // 将修改后的底部增删记录提交给接口
  emits('updateRecord', currentSemantics.id, corePhraseData.value)
}

class addDataOrigin {
  id = undefined
  semanticId = currentSemantics.id as number
  phraseName = ''
}

const addData = reactive<AiCorePhrase>(new addDataOrigin())

const addVisible = ref(false)

/**
 * 确认增改核心短语
 * 数据来自子组件（增改核心短语弹窗）
 * @param addList 需要追加到底部新增记录的数组
 * @param modifiedItemId 单个修改时，需要记住短语ID，先删除旧的，再新增新的，达到修改的目的
 */
const confirmAdd = (addList: AiCorePhrase[], modifiedItemId: number = -1) => {

  // 注意，从新增短语弹窗过来的，是一个string数组
  // 其他方式都是短语对象

  if (modifiedItemId && modifiedItemId > -1) {
    // 如果修改单个
    // 封装成短语对象
    let meta: AiCorePhrase = {
      semanticId: currentSemantics.id || -1,
      phraseName: '',
    }
    // 1. 找到核心短语列表中对应ID的短语数据
    corePhraseList.value.forEach((item: AiCorePhrase) => {
      if (item.id === modifiedItemId) {
        // 2. 把原数据放进待删除列表
        Object.assign(meta, JSON.parse(JSON.stringify(item)))
        // 产品原型里说不需要在删除列表里展示
        // 但是接口数据还是需要的
        // corePhraseDeleteList.value.push(<string>meta.phraseName)
        corePhraseData.value.deleteList.push(JSON.parse(JSON.stringify(meta)))

        // 3. 将修改后的新数据提取出来
        item.phraseName = addList[0]?.phraseName || ''
        Object.assign(meta, JSON.parse(JSON.stringify(item)))
      }
    })
    // 4. 把新数据放进待新增列表
    if (addList[0]) {
      // console.log(addList[0])
      Object.assign(meta, JSON.parse(JSON.stringify(addList[0])))
      corePhraseAddList.value.push(<string>meta.phraseName)
      corePhraseData.value.saveList.push(JSON.parse(JSON.stringify(meta)))
    }
  } else {
    // 如果批量导入
    // 遍历新增短语列表，准备好发送给接口的参数
    toRaw(addList).forEach((item) => {
      // 封装成短语对象
      let meta: AiCorePhrase = {
        semanticId: currentSemantics.id || -1,
        phraseName: '',
      }

      Object.assign(meta, JSON.parse(JSON.stringify(item)))

      // 更新用于页面展示的列表
      // 因为是新增，所以不需要更新页面展示的删除记录列表
      // 在列表末尾追加新元素
      corePhraseList.value.push(meta)
      corePhraseAddList.value.push(<string>meta.phraseName)

      // 更新准备发送给接口的数据
      corePhraseData.value.saveList.push(meta)
    })
  }

  // 数组去重
  deduplicateSomeList('all')
}

/**
 * 点击生效核心短语或者新增记录的叉按钮——删除核心短语
 * @param row 该短语的一些接口参数
 */
const handleClose = (row: AiCorePhrase | string) => {
  // console.log('删除核心短语', row)
  let meta = {
    semanticId: currentSemantics.id,
    phraseName: ''
  }
  // 从生效的核心短语过来的，是一个AiCorePhrase对象
  // 从新增记录过来的，只是一个string基本类型
  if (typeof row === 'string') {
    meta.phraseName = row
  } else {
    Object.assign(meta, row)
  }

  let temp = JSON.parse(JSON.stringify(<AiCorePhrase>meta))

  // 更新三个列表的页面展示
  // 找到短语内容一样的那个元素并移除
  corePhraseList.value = corePhraseList.value.filter(item => (item.phraseName !== temp.phraseName))
  // 找到短语内容一样的那个元素并移除
  corePhraseAddList.value = corePhraseAddList.value.filter(item => (item !== temp.phraseName))
  // 在列表末尾追加新元素
  corePhraseDeleteList.value.push(<string>temp.phraseName)

  // 更新准备发送给接口的数据
  corePhraseData.value.deleteList.push(<AiCorePhrase>temp)
  corePhraseData.value.saveList = corePhraseData.value.saveList.filter(item => (item.phraseName !== temp.phraseName))
  // 这两个数组去重
  deduplicateSomeList('api')
}

/**
 * 点击删除记录的叉按钮——重新添加核心短语
 * @param row 该短语的一些接口参数
 */
const handleAdd = (row: string) => {
  // console.log('添加核心短语', row)
  // 从删除记录过来的，只是一个string基本类型
  // 需要封装成AiCorePhrase对象，以符合接口参数
  let meta = {
    semanticId: currentSemantics.id,
    phraseName: row
  }

  let temp = JSON.parse(JSON.stringify(<AiCorePhrase>meta))

  // 更新三个列表和页面展示
  // 在列表末尾追加新元素
  corePhraseList.value.push(<AiCorePhrase>temp)
  // 找到短语内容一样的那个元素并移除
  corePhraseDeleteList.value = corePhraseDeleteList.value.filter(item => (item !== temp.phraseName))
  // 在列表末尾追加新元素
  corePhraseAddList.value.push(<string>temp.phraseName)

  // 更新准备发送给接口的数据
  corePhraseData.value.deleteList = corePhraseData.value.deleteList.filter(item => (item.phraseName !== temp.phraseName))
  corePhraseData.value.saveList.push(<AiCorePhrase>temp)
  // 这两个数组去重
  deduplicateSomeList('api')
}

/**
 * 新增或者编辑短语
 * @param row 当前短语的参数
 */
const edit = (row?: AiCorePhrase) => {
  addVisible.value = true
  if (row && row.id) {
    // 如果有当前短语参数，编辑短语
    Object.assign(addData, row)
  } else {
    // 如果没有当前短语参数，新增短语
    Object.assign(addData, new addDataOrigin())
  }
}
const copy = async () => {
  emits('copy', corePhraseList.value)
}

/**
 * 所有数组分别去重
 * @param scope 需要去重的数组范围 all 全部数组 page 页面展示的数组 api 接口数据的数组
 */
const deduplicateSomeList = (scope: string = 'all') => {
  if (scope === 'page' || scope === 'all') {
    // 一、用于页面展示的三个数组
    // 1. 核心短语列表
    corePhraseList.value = deduplicateObjectArray(corePhraseList.value, 'phraseName')
    // 2. 删除记录列表
    corePhraseDeleteList.value = deduplicateBaseArray(corePhraseDeleteList.value)
    // 3. 新增记录列表
    corePhraseAddList.value = deduplicateBaseArray(corePhraseAddList.value)
  }
  if (scope === 'api' || scope === 'all') {
    // 二、用于接口数据的两个数组
    // 1. 准备发给接口的删除记录
    // 和原有的删除记录列表不一定内容一致
    const newDeleteList = deduplicateObjectArray(corePhraseData.value.deleteList, 'phraseName')
    corePhraseData.value.deleteList = []
    corePhraseData.value.deleteList.push(...JSON.parse(JSON.stringify(newDeleteList)))
    // 2. 准备发给接口的新增记录
    // 和原有的删除记录列表不一定内容一致
    const newSaveList = deduplicateObjectArray(corePhraseData.value.saveList, 'phraseName')
    corePhraseData.value.saveList = []
    corePhraseData.value.saveList.push(...JSON.parse(JSON.stringify(newSaveList)))
  }
}
</script>

<style scoped lang="postcss">
</style>
