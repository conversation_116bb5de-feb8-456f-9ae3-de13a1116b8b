import { getToken, } from "@/utils/utils";
import type { RouteRecordRaw } from "vue-router";
import router from "@/router";
import { useUserStore } from "@/store/user";
import { useRouteStore } from "@/store/routes";
import { useGlobalStore } from "@/store/globalInfo";
import { AppRouteRecordRawT } from "@/type/route";
import { ElMessage } from "element-plus";
import { cancelAllRequest } from '@/axios'
import { nextTick } from 'vue'
import { versionCheck } from '@/utils/version'
import { aiTeamModel } from '@/api/user'
import {asyncRouter} from '@/router/asyncRoute'
import { RoleResponse } from '@/type/user'
export const filterRoutes = (routes: AppRouteRecordRawT[], permissions: string[]) => {
  const finallyRoutes: AppRouteRecordRawT[] = [];
  routes.forEach((v: AppRouteRecordRawT) => {
    let item = { ...v };
    if (hasPermission(item, permissions)) {
      if (item.children && item.children.length > 0) {
        item.children = filterRoutes(item.children, permissions);
      }
      if (!item.children || item.children.length > 0) {
        finallyRoutes.push(item);
      }
    }
  });
  return finallyRoutes;
};
const hasPermission = (route: AppRouteRecordRawT, permissions: string[]) => {
  if (route.meta) {
    if (!route.meta.id || (route.meta.id as string).includes('common')) {
      return true
    } else {
      return  permissions?.length > 0 ? permissions.some(item => route.meta.id?.includes(item)) : true
    }
  }
  return true
};
const findHomePage = (routes: AppRouteRecordRawT[]): string => {
  if (!routes[0].children) {
    return routes[0].path
  } else {
    return findHomePage(routes[0].children)
  }
}
export const addRoute = (routes: AppRouteRecordRawT[], permissions: string[]) => {
  const routeStore = useRouteStore();
  const userStore = useUserStore();
  const hasRoles = filterRoutes(routes, permissions)
  if (hasRoles && hasRoles.length) {
    if (hasRoles && hasRoles[0] && hasRoles[0].children) {
      const index1 = hasRoles[0].children?.findIndex(item => item.meta.title === '商户端')??-1
      const index2 = hasRoles[0].children?.findIndex(item => item.meta.title === '运营端')??-1
      const r1 = hasRoles[0].children.slice(index1, index2 >= index1 ? index2 : undefined).find(item => item.meta.type??-1 > 0) || undefined
      const r0 = hasRoles[0].children.slice(index2).find(item => item.meta.type??-1 > 0) || undefined
      if (index1 < 0 || !r1) {
        userStore.homePage[1] = '/login'
      } else {
        if (r1.meta.type === 2 || (r1.meta.type === 1 && !r1.children)) {
          userStore.homePage[1] = r1.path
        } else {
          userStore.homePage[1] = r1.children && r1.children.length > 0 ? r1.children.find(item => item.meta.type === 2)?.path || '/login' : '/login'
        }
      }
      if (index2 < 0 || !r0) {
        userStore.homePage[0] = '/login'
      } else {
        if (r0.meta.type === 2 || (r0.meta.type === 1 && !r0.children)) {
          userStore.homePage[0] = r0.path
        } else {
          userStore.homePage[0] = r0.children && r0.children.length > 0 ? r0.children.find(item => item.meta.type === 2)?.path || '/login' : '/login'
        }
      }
    }
    if (userStore.homePage[0] === userStore.homePage[1] && userStore.homePage[0] === '/login') {
      ElMessage.error('角色权限异常')
    } else if(userStore.homePage[0] === '/login') {
      userStore.homePage[0] = userStore.homePage[1]
    } else if(userStore.homePage[1] === '/login') {
      userStore.homePage[1] = userStore.homePage[0]
    }
    routeStore.setAsyncRoutes(hasRoles)
    hasRoles.forEach(v => {
      router.addRoute(v as unknown as RouteRecordRaw);
    });
    routeStore.updateInitStatus(true)
  } else {
    ElMessage({
      message: '获取用户可访问页面出错',
      type: "error",
    });
  }
};
router.onError(err => {
  console.error(err);
  if (JSON.stringify(err).includes('40301') || JSON.stringify(err).includes('401')) {
    window.location.replace('/')
  }
});

// 全局路由前置钩子
router.beforeEach(async (to, from, next) => {
  const globalStore = useGlobalStore()
  globalStore.loadingModule = true
  globalStore.loading = false
  const userStore = useUserStore();
  document.title = userStore.account ? userStore.account + ' | 白泽' : '白泽'
  const name = to.name as string;
  if (!['/login',' /404', '/403', '/'].includes(from.path)) {
    cancelAllRequest()
  }
  if (to.path === "/" || ['/login',' /404', '/403', '/operator/ai-resource/audio-batch-download'].includes(to.path)) {
    next();
  } else {
    const permissions = userStore.permissions ? Object.keys(userStore.permissions) : []
    const hasPermission = permissions?.length > 0 && (!to.meta.id || permissions.some(item => (to.meta.id as string)?.includes(item as string))) || (to.meta.id as string)?.includes('common');
    const hasToken = !!getToken();
    if (hasToken) {
      if (hasPermission) {
        const isNotFound = name ? !router.hasRoute(name) : true;
        isNotFound ? next("/404") : next()
      } else {
        const roleInfo = await aiTeamModel.findOneAdminRoleById({id: userStore.roleId!}) as RoleResponse
        const permissions: {[key: string]: string[]} = roleInfo?.authorityMap || {}
        userStore.permissions = permissions
        addRoute(asyncRouter(), permissions ? Object.keys(permissions) : [])
        next(to.fullPath);
      }
    } else {
      next("/login");
    }
  }
});

// 全局路由后置钩子
router.afterEach(async (to, from) => {
  const globalStore = useGlobalStore()
  await nextTick(() => {
    globalStore.loadingModule = false
  })
  await versionCheck(true)
})
