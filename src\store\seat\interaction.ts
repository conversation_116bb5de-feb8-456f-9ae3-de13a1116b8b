import { defineStore } from "pinia";
import { ref } from "vue";
import { WorkbenchTabEnum } from "@/type/seat";

export const useInteractionStore = defineStore('interfaction', () => {
  /* 人机协同监听后延迟介入 */

  // 人机协同监听后延迟介入的定时器
  // 默认毫秒
  const DELAY_INTERVENE_MS = 2000
  // 延迟介入是否启用
  const delayInterveneEnabled = ref<boolean>(false)
  // 延迟介入定时器
  let delayInterveneTimer: number | null = null

  /**
   * 关闭延迟介入定时器
   */
  const stopDelayInterveneTimer = () => {
    if (typeof delayInterveneTimer === 'number') {
      clearTimeout(delayInterveneTimer)
      delayInterveneTimer = null
    }
    delayInterveneEnabled.value = false
  }
  /**
   * 启动延迟介入定时器
   */
  const startDelayInterveneTimer = () => {
    // 把旧的关掉，重新开一个新的
    stopDelayInterveneTimer()
    delayInterveneTimer = <number><unknown>setTimeout(() => {
      stopDelayInterveneTimer()
    }, DELAY_INTERVENE_MS)
    delayInterveneEnabled.value = true
  }

  /* 坐席工作台通话抽屉右侧标签页保留上次状态 */

  // 标签页枚举值列表，预加载好，以便调用更新方法时直接读取现成的
  const tabEnumValueList: WorkbenchTabEnum[] = Object.values(WorkbenchTabEnum) || []

  // 人工直呼当前标签页
  const manualCallActiveTab = ref<WorkbenchTabEnum>(WorkbenchTabEnum['客户状态记录'])
  /**
   * 更新人工直呼当前标签页
   * @param tabName 新的标签页名称
   */
  const updateManualCallActiveTab = (tabName: WorkbenchTabEnum) => {
    manualCallActiveTab.value = tabEnumValueList.includes(tabName) ? tabName : WorkbenchTabEnum['客户状态记录']
  }

  // 人机协同当前标签页
  const humanMachineActiveTab = ref<WorkbenchTabEnum>(WorkbenchTabEnum['客户状态记录'])
  /**
   * 更新人机协同当前标签页
   * @param tabName 新的标签页名称
   */
  const updateHumanMachineActiveTab = (tabName: WorkbenchTabEnum) => {
    humanMachineActiveTab.value = tabEnumValueList.includes(tabName) ? tabName : WorkbenchTabEnum['客户状态记录']
  }

  return {
    delayInterveneEnabled,
    stopDelayInterveneTimer,
    startDelayInterveneTimer,

    manualCallActiveTab,
    humanMachineActiveTab,
    updateManualCallActiveTab,
    updateHumanMachineActiveTab,
  }
}, {
  persist: [
    { paths: ['manualCallActiveTab', 'humanMachineActiveTab'], storage: localStorage },
  ]
})
