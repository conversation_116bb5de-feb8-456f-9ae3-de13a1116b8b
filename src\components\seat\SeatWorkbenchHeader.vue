<template>
  <!--坐席设置按钮-->
  <el-button v-show="seatSettingButtonVisible" link class="tw-mx-[8px]" @click="onClickSeatSettingButton">
    <el-icon :size="18" color="var(--primary-blue-color)">
      <SvgIcon name="setting1" color="inherit" />
    </el-icon>
  </el-button>

  <!--工作状态和签入任务-->
  <div v-show="seatOnline" class="tw-flex tw-items-center">
    <!--工作状态-->
    <!--只有人机协同模式时显示-->
    <div v-show="seatTaskList.length" class="tw-flex tw-items-center tw-h-full tw-mx-[8px]">
      <el-switch
        v-model="seatBusy"
        :loading="loadingBusy"
        class="tw-flex-none"
        style="--el-switch-on-color: #165DFF; --el-switch-off-color: #C8C9CC"
        size="large"
        inline-prompt
        :active-value="true"
        :inactive-value="false"
        active-text="工作中"
        inactive-text="休息中"
        :before-change="onBeforeChangeBusy"
      />
    </div>

    <div v-show="idleDurationVisible" class="drawer-header-time idle-time">
      <el-icon :size="16" color="#fff" class="tw-mr-[4px]">
        <SvgIcon name="seat" />
      </el-icon>
      <span>空闲时长：</span>
      <span>{{ formatIdleDuration }}</span>
    </div>

    <!--签入任务-->
    <div class="tw-flex tw-items-center tw-h-full tw-mx-[8px]">
      <div class="tw-flex-none tw-text-[13px] tw-text-black">
        <span class="tw-font-bold">签入任务：</span>
        <span>{{ checkedInTaskName }}</span>
      </div>
      <el-button link @click="onClickChangeTask">
        <el-icon :size="18" color="var(--primary-blue-color)">
          <SvgIcon name="edit2" color="inherit" />
        </el-icon>
      </el-button>
    </div>
  </div>

  <!--在线状态-->
  <div class="tw-flex tw-items-center tw-h-full tw-mx-[8px]">
    <div class="tw-flex-none tw-mr-[8px] tw-text-[13px] tw-text-black">
      <span class="tw-font-bold">坐席状态</span>
    </div>
    <el-switch
      v-model="seatOnline"
      :loading="loadingOnline"
      class="tw-flex-none"
      style="--el-switch-on-color: #165DFF; --el-switch-off-color: #C8C9CC"
      size="large"
      inline-prompt
      active-text="在线"
      inactive-text="离线"
      :before-change="onBeforeChangeOnline"
    />
  </div>

  <!--签入任务弹窗-->
  <CheckInTaskDialog
    :visible="dialogTaskVisible"
    :availableTaskList="availableTaskList"
    :checkedTaskList="checkedTaskList"
    @close="onCloseDialogTask "
    @update="onUpdateDialogTask"
  />

  <!--自动签出任务通知 DOM挂载节点-->
  <div ref="checkOutTaskNotificationRef"></div>

  <!--关联任务启动通知 DOM挂载节点-->
  <div ref="linkedTaskStartNotificationRef"></div>

  <!--正在呼叫弹窗-->
  <el-dialog
    v-model="dialogCallingVisible"
    class="seat-workbench-dialog calling-dialog"
    width="650"
    :z-index="2999"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
  >
    <div class="form-dialog-main">
      <div class="form-dialog-content">
        <div v-show="seatPhoneStore.currentClue.name" class="tw-flex-none tw-cursor-pointer" @click="copyText(seatPhoneStore.currentClue.name)">
          {{ seatPhoneStore.currentClue.name }}
        </div>
        <div
          class="tw-mx-[8px] colorful tw-truncate tw-cursor-pointer"
          @click="copyText(seatPhoneStore.currentClue.clueUniqueId ?? '')"
        >
          {{ seatPhoneStore.currentClue.clueUniqueId ?? '' }}
        </div>
        <!--复制按钮-->
        <el-tooltip content="复制" placement="right" :show-after="500">
          <el-icon
            :size="14"
            class="tw-mr-[8px] hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer"
            @click="copyText(seatPhoneStore.currentClue.clueUniqueId ?? '')"
          >
            <SvgIcon name="copy" color="inherit"></SvgIcon>
          </el-icon>
        </el-tooltip>
        <div class="tw-flex-none">正在拨打中，已拨打</div>
        <div class="tw-flex-none colorful">&nbsp;{{ callingSecond }}秒&nbsp;</div>
        <div>...</div>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <div class="tw-flex tw-justify-between tw-items-center">
          <!--人工直呼模式下才支持自动拨打下一个-->
          <div v-show="!seatTaskList.length" class="tw-flex tw-items-center">
            <span class="tw-text-[13px]">
              自动拨打下一个：
            </span>
            <el-switch
              v-model="seatSetting.autoCallNext"
              class="tw-flex-none"
              style="--el-switch-on-color: #165DFF; --el-switch-off-color: #C8C9CC;"
              inline-prompt
              :active-value="true"
              :inactive-value="false"
              active-text="开启"
              inactive-text="关闭"
            />
          </div>

          <el-button v-show="false" link @click="onClickHangup">
            <el-icon :size="30" color="#fff">
              <SvgIcon name="dial-red" />
            </el-icon>
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>

  <!--呼叫失败弹窗-->
  <el-dialog
    v-model="dialogCallFailVisible"
    width="480px"
    class="seat-workbench-dialog call-fail-dialog"
    align-center
    :z-index="2999"
    :show-close="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    @close="onClickCancelCall"
  >
    <div class="form-dialog-main">
      <div class="form-dialog-content">
        <el-icon :size="22" color="#E54B17">
          <SvgIcon name="fail" />
        </el-icon>
        <span class="call-fail-text tw-text-[#165DFF] tw-truncate">
          {{ seatPhoneStore.currentClue.clueUniqueId ?? '' }}
        </span>
        <!--复制按钮-->
        <el-tooltip content="复制" placement="right" :show-after="500">
          <el-icon
            :size="14"
            class="tw-ml-[8px] hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer"
            @click="copyText(seatPhoneStore.currentClue.clueUniqueId ?? '')"
          >
            <SvgIcon name="copy" color="inherit"></SvgIcon>
          </el-icon>
        </el-tooltip>
        <br>
      </div>
      <br>
      <span class="call-fail-text tw-flex-none">
        <span v-show="seatPhoneStore.currentClue.name" class="tw-cursor-pointer" @click="copyText(seatPhoneStore.currentClue.name)">
          {{ seatPhoneStore.currentClue.name }}&nbsp;
        </span>
        <span>
          呼叫失败，原因：
        </span>
        <span class="tw-text-[#E54B17]">
          {{ seatPhoneStore.callFailReason }}
        </span>
      </span>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button link type="info" @click="onClickCancelCall">
          取消
        </el-button>
        <el-button :link="!seatTaskList.length" type="primary" @click="onClickRetryCall">
          重拨
        </el-button>
        <!--人工直呼工作模式下显示-->
        <el-button v-if="!seatTaskList.length" type="primary" @click="onClickCallNext">
          <template v-if="seatSetting.autoCallNext">
            拨打下一个 ({{ seatPhoneStore.callNextCountdownSecond }}s)
          </template>
          <template v-else>
            拨打下一个
          </template>
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!--未上线弹窗-->
  <el-dialog
    v-model="dialogOfflineVisible"
    width="480px"
    class="seat-workbench-dialog not-online-dialog"
    align-center
    :z-index="2999"
    :close-on-click-modal="false"
    @close="onCloseDialogOffline"
  >
    <template #header>
      <div class="form-dialog-header">
        坐席未上线
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-content">
        坐席未上线，请先上线
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="onCloseDialogOffline">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="onConfirmDialogOffline">
          上线
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!--坐席设置弹窗-->
  <SeatSettingDialog
    v-model:visible="seatSettingDialogVisible"
  />
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { seatWorkbenchAccountModel } from '@/api/seat'
import { copyText, formatDuration, Throttle } from '@/utils/utils'
import { SeatLogActionEnum, SeatLogTypeEnum, SeatMember, SeatStatusEnum } from '@/type/seat'
import { TaskManageItem } from '@/type/task'
import { useSeatPhoneStore } from '@/store/seat-phone'
import { useTaskStore } from '@/store/taskInfo'
import { aiOutboundTaskModel } from '@/api/ai-report'
import dayjs from 'dayjs'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js'
import { workbenchTroubleshoot } from '@/utils/workbench/troubleshot'
import { useCallSettingStore } from '@/store/seat/call-setting'
import { useSeatSettingStore } from '@/store/seat/seat-setting'
import { useSeatInfoStore } from '@/store/seat/seat-info'

// 动态引入组件
const CheckInTaskDialog = defineAsyncComponent(() => import('./CheckInTaskDialog.vue'))
const SeatSettingDialog = defineAsyncComponent(() => import('./SeatSettingDialog.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

// 账号
const userStore = useUserStore()
// 坐席通话
const seatPhoneStore = useSeatPhoneStore()
const {
  dialogCallingVisible,
  callingSecond,
  dialogCallFailVisible,
  checkOutTaskNotificationRef,
  linkedTaskStartNotificationRef,
  dialogOfflineVisible,
} = storeToRefs(seatPhoneStore)
const callSettingStore = useCallSettingStore()
const seatSettingStore = useSeatSettingStore()
const { seatSetting } = storeToRefs(seatSettingStore)
const seatInfoStore = useSeatInfoStore()
const {
  currentSeat,
  seatOnline,
  seatBusy,
  seatStatus,
  seatTaskList,
} = storeToRefs(seatInfoStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 顶部工具条 开始 ----------------------------------------

// 正在加载在线状态
const loadingOnline = ref(false)
// 在线状态节流锁
const throttleOnline = new Throttle(loadingOnline)

// 正在加载工作状态
const loadingBusy = ref(false)
// 工作状态节流锁
const throttleBusy = new Throttle(loadingBusy)

// 坐席当前签入任务的文本展示
const checkedInTaskName = computed(() => {
  if (!seatTaskList.value?.length) {
    return '（无）'
  } else {
    const str = seatTaskList.value[0]?.taskName
    if (seatTaskList.value.length > 1) {
      return str + ` 等总共${seatTaskList.value.length}项任务`
    } else {
      return str
    }
  }
})
// 可签入任务列表
const availableTaskList = ref<TaskManageItem[]>([])
// 已签入任务列表
const checkedTaskList = ref<TaskManageItem[]>([])

/**
 * 坐席在线状态切换前
 */
const onBeforeChangeOnline = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击上下线按钮',
  })

  // 节流锁上锁
  if (throttleOnline.check()) {
    return
  }
  throttleOnline.lock()

  return new Promise(async (resolve, reject) => {
    // 切换后状态文本
    const afterChangeOnlineText = seatOnline.value ? '下线' : '上线'

    try {
      if (seatOnline.value) {
        // 下线
        await seatPhoneStore.offline()
      } else {
        // 上线
        await seatPhoneStore.online()
      }
      resolve(true)
    } catch (e: any) {
      ElMessage.error(afterChangeOnlineText + '失败')
      reject()
    } finally {
      // 节流锁解锁
      throttleOnline.unlock()
    }
  })
}
/**
 * 点击签入签出任务按钮
 */
const onClickChangeTask = async () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击签入签出任务按钮',
  })

  try {
    // 更新可签入任务列表
    const availableRes = await seatWorkbenchAccountModel.getAvailableTaskList()
    const tempTaskList = Array.isArray(availableRes) ? availableRes : []
    // 按创建时间倒序排序
    availableTaskList.value = tempTaskList.sort((a: TaskManageItem, b: TaskManageItem) => {
      return dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1
    })

    // 更新已签入任务列表
    const taskIdList: number[] = <number[]>seatTaskList.value.map((task: TaskManageItem) => {
      return task?.id
    }) || []
    const checkedRes = await aiOutboundTaskModel.getTaskListByTaskIds(taskIdList ?? [])
    checkedTaskList.value = Array.isArray(checkedRes) ? checkedRes : []

    // 显示弹窗
    dialogTaskVisible.value = true
  } catch (e) {
    availableTaskList.value = []
    checkedTaskList.value = []
    dialogTaskVisible.value = false
  }
}
/**
 * 坐席休息工作状态切换前
 */
const onBeforeChangeBusy = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击切换工作休息状态按钮',
  })

  // 节流锁上锁
  if (throttleBusy.check()) {
    return
  }
  throttleBusy.lock()

  return new Promise(async (resolve, reject) => {
    // 切换后状态文本
    const afterChangeBusyText = seatBusy.value ? '休息中' : '工作中'

    try {
      if (seatBusy.value) {
        // 切换到休息
        await seatWorkbenchAccountModel.startRest()
        seatInfoStore.updateSeatStatus(SeatStatusEnum.IN_REST)
      } else {
        // 切换到工作
        const callSeatStatus = seatTaskList.value.length
          ? SeatStatusEnum.HUMAN_MACHINE_IDLE
          : SeatStatusEnum.MANUAL_DIRECT_IDLE
        await seatWorkbenchAccountModel.endRest({ callSeatStatus })
        seatInfoStore.updateSeatStatus(callSeatStatus)
      }
      ElMessage.success(currentSeat.value.account + ' 切换到 ' + afterChangeBusyText)
      seatPhoneStore.report({
        action: SeatLogActionEnum['切换工作休息状态'],
        desc: '切换到' + afterChangeBusyText,
      })
      resolve(true)
    } catch (e: any) {
      ElMessage.error(
        currentSeat.value.account
        + ' 无法切换到 '
        + afterChangeBusyText
        + e.data?.message ? ('：' + e.data?.message) : ''
      )
      seatPhoneStore.report({
        type: SeatLogTypeEnum['错误'],
        action: SeatLogActionEnum['切换工作休息状态'],
        desc: '无法切换到' + afterChangeBusyText,
      })
      reject()
    } finally {
      // 节流锁解锁
      throttleBusy.unlock()
      // 更新坐席信息
      await seatPhoneStore.updateSeatInfo()
    }
  })
}

// -------------------- 坐席设置按钮 开始 --------------------

// 坐席设置按钮 显示隐藏
const seatSettingButtonVisible = computed(() => {
  // 坐席在线
  return seatOnline.value
})

/**
 * 坐席设置按钮 点击
 */
const onClickSeatSettingButton = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '点击坐席设置按钮',
  })
  // 显示坐席设置弹窗
  seatSettingDialogVisible.value = true
}

// -------------------- 坐席设置按钮 结束 --------------------

// -------------------- 空闲时长 开始 --------------------

// 空闲时长，是否显示
const idleDurationVisible = computed(() => {
  // 离线和休息中不显示
  return seatStatus.value !== SeatStatusEnum.OFF_LINE
    && seatStatus.value !== SeatStatusEnum.IN_REST
})
// 格式化空闲时长秒数
const formatIdleDuration = computed(() => {
  if (seatPhoneStore.idleSecond === 0) {
    return '0秒'
  }
  return formatDuration(seatPhoneStore.idleSecond)
})

// -------------------- 空闲时长 结束 --------------------

// ---------------------------------------- 顶部工具条 结束 ----------------------------------------

// ---------------------------------------- 签入任务弹窗 开始 ----------------------------------------

// 是否显示签入任务弹窗
const dialogTaskVisible = ref(false)

/**
 * 签入任务弹窗 关闭
 */
const onCloseDialogTask = () => {
  dialogTaskVisible.value = false
}
/**
 * 签入任务弹窗 更新
 */
const onUpdateDialogTask = () => {
  checkedTaskList.value = seatTaskList.value
  // 根据是否签入任务判断是人工直呼空闲还是人机协同空闲
  seatInfoStore.updateSeatStatusByTaskList()
  // 更新坐席工作台的线索模块的统计数据
  seatPhoneStore.needUpdateWorkbenchStatistics = true
}

// ---------------------------------------- 签入任务弹窗 结束 ----------------------------------------

// ---------------------------------------- 呼叫失败弹窗 开始 ----------------------------------------

/**
 * 呼叫失败弹窗 点击取消
 */
const onClickCancelCall = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '呼叫失败弹窗 点击取消按钮',
  })
  seatPhoneStore.onClickCancelCall()
}
/**
 * 呼叫失败弹窗 点击重拨
 */
const onClickRetryCall = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '呼叫失败弹窗 点击重拨按钮',
  })
  seatPhoneStore.onClickRetryCall()
}
/**
 * 呼叫失败弹窗 点击拨打下一个
 */
const onClickCallNext = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '呼叫失败弹窗 点击拨打下一个按钮',
  })
  seatPhoneStore.onClickCallNext()
}

// ---------------------------------------- 呼叫失败弹窗 结束 ----------------------------------------

// ---------------------------------------- 呼叫弹窗 开始 ----------------------------------------

// 呼叫等待音 DOM
const callingAudio = ref<HTMLAudioElement>()

// 当呼叫弹窗显示时，播放或暂停呼叫等待音
watch(dialogCallingVisible, (val) => {
  if (!callingAudio.value) {
    return
  }

  if (val) {
    callingAudio.value.currentTime = 0
    callingAudio.value.play()
  } else {
    callingAudio.value.pause()
    callingAudio.value.currentTime = 0
  }
})

/**
 * 呼叫弹窗 点击挂断按钮
 */
const onClickHangup = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '呼叫弹窗 点击挂断按钮',
  })
  seatPhoneStore.cancelCall()
}

// ---------------------------------------- 呼叫弹窗 结束 ----------------------------------------

// ---------------------------------------- 未上线弹窗 开始 ----------------------------------------

/**
 * 未上线提示弹窗 点击关闭按钮
 */
const onCloseDialogOffline = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '未上线提示弹窗 点击关闭按钮',
  })
  dialogOfflineVisible.value = false
}
/**
 * 未上线提示弹窗 点击上线按钮
 */
const onConfirmDialogOffline = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '未上线提示弹窗 点击上线按钮',
  })
  // 这个传个true表示手动执行更新seatOnline属性
  // 因为这里不像点击开关UI组件，能有回调函数自动去更新seatOnline
  seatPhoneStore.online(true)
}

// ---------------------------------------- 未上线弹窗 结束 ----------------------------------------

// ---------------------------------------- 坐席设置弹窗 开始 ----------------------------------------

// 坐席设置弹窗 是否显示
const seatSettingDialogVisible = ref(false)

// ---------------------------------------- 坐席设置弹窗 结束 ----------------------------------------

// ---------------------------------------- 监测网络环境变化 开始 ----------------------------------------

/**
 * 处理网络环境变化
 */
const handleNetworkChange = () => {
  console.log('navigator.onLine', navigator.onLine)
  if (!navigator.onLine) {
    console.error('网络连接已断开，请检查网络环境并刷新页面重新上线')
    ElMessage({
      message: '网络连接已断开，请检查网络环境并刷新页面重新上线',
      type: 'error',
      duration: 5000,
    })
    // 强制下线
    seatPhoneStore.forceOffline()
    seatPhoneStore.resetWorkbench()
  } else {
    initSeat()
  }
}

// ---------------------------------------- 监测网络环境变化 结束 ----------------------------------------

// ---------------------------------------- 长时间空闲 开始 ----------------------------------------

// 空闲状态检查定时器
let idleCheckTimer: number | null = null
// 空闲状态检查定时器，间隔时间，单位秒
const IDLE_CHECK_SECOND = 60

/**
 * 监听坐席状态，如果是空闲中，启动定时器，定时查询坐席状态；一旦切换到其他状态，关闭定时器
 */
const stopWatchIdle = watch(seatStatus, (val: SeatStatusEnum) => {
  if (val === SeatStatusEnum.HUMAN_MACHINE_IDLE || val === SeatStatusEnum.MANUAL_DIRECT_IDLE) {
    startIdleCheckTimer()
  } else {
    stopIdleCheckTimer()
  }
})
/**
 * 处理空闲状态检查定时器
 */
const handleIdleCheckTimer = async () => {
  // console.log('处理空闲状态检查定时器')
  const [err, res] = <[any, SeatMember]>await to(seatWorkbenchAccountModel.getSeatInfo())
  if (err) {
    console.error('无法获取坐席信息')
  } else if (res) {
    console.log('查询到的坐席状态', res.callSeatStatus)
    if (res.callSeatStatus !== SeatStatusEnum.MANUAL_DIRECT_IDLE
      && res.callSeatStatus !== SeatStatusEnum.HUMAN_MACHINE_IDLE
      && res.callSeatStatus !== SeatStatusEnum.IN_REST) {
      console.warn('当前坐席状态不正确', res.callSeatStatus, seatStatus.value)
      ElMessage({
        type: 'warning',
        message: '当前坐席状态不正确，请刷新页面并重新上线。' + ' ' + res.callSeatStatus + ', ' + seatStatus.value,
        duration: 5000,
      })
    }
  }

  // 重启定时器
  startIdleCheckTimer()
}
/**
 * 启动空闲状态检查定时器
 */
const startIdleCheckTimer = () => {
  stopIdleCheckTimer()
  // console.log('启动空闲状态检查定时器')
  idleCheckTimer = <number><unknown>setTimeout(handleIdleCheckTimer, IDLE_CHECK_SECOND * 1000)
}
/**
 * 关闭空闲状态检查定时器
 */
const stopIdleCheckTimer = () => {
  // console.log('关闭空闲状态检查定时器')
  idleCheckTimer && clearTimeout(idleCheckTimer)
  idleCheckTimer = null
}

// ---------------------------------------- 长时间空闲 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// 监听坐席状态，开启或关闭空闲时长定时器
const stopIdleWatch = watch(() => seatStatus.value, (val: SeatStatusEnum, oldVal: SeatStatusEnum | null | undefined) => {
  if (val === SeatStatusEnum.MANUAL_DIRECT_IDLE || val === SeatStatusEnum.HUMAN_MACHINE_IDLE) {
    if (oldVal !== SeatStatusEnum.MANUAL_DIRECT_IDLE && oldVal !== SeatStatusEnum.HUMAN_MACHINE_IDLE) {
      // 如果不是人工直呼空闲和人机协同空闲切换，而是从其他状态切换到空闲中，可以开启空闲时长定时器
      seatPhoneStore.openIdleTimer()
    }
  } else {
    seatPhoneStore.closeIdleTimer()
    seatPhoneStore.resetIdleTimer()
  }
})
/**
 * 初始化坐席
 */
const initSeat = async () => {
  // 先更新坐席信息
  await seatPhoneStore.updateSeatInfo()
  // 如果坐席信息里记录坐席在线，则先下线再立马上线
  if (seatOnline.value) {
    try {
      // 重置坐席状态为空闲中
      workbenchTroubleshoot.fixSeatStatus()
      setTimeout(async () => {
        await seatWorkbenchAccountModel.offline()
        await seatPhoneStore.online(true, true)
      }, 500)
    } catch (e) {
    }
  }
}
/**
 * 初始化
 */
const init = async () => {
  const taskStore = useTaskStore()
  // 坐席列表
  const callSeatList: SeatMember[] = await taskStore.getCallGroupSeatListOptions(true)
  if (callSeatList?.findIndex(item => item.accountId === userStore.userId) > -1) {
    // 实例化Janus对象，等实例化成功后，回调执行更新当前坐席信息
    seatPhoneStore.createJanus(async () => {
      await initSeat()
    })
    // 更新通话设置
    await callSettingStore.updateCallSetting()
  }

  // 初始化呼叫等待音
  console.log('初始化呼叫等待音')
  callingAudio.value = new Audio()
  callingAudio.value.src = '/calling.wav'
  callingAudio.value.loop = true
  // callingAudio.value.oncanplaythrough = () => {
  //   console.log('***', '呼叫等待音', 'calling.wav', '缓冲完成', '可以循环播放')
  // }

  // 初始化通话提示音
  seatPhoneStore.initDialAudio()
}
/**
 * 重置
 */
const reset = () => {
  // 关闭空闲状态检查
  stopWatchIdle()
  stopIdleCheckTimer()
  // 关闭空闲时长更新
  stopIdleWatch()
  // 关闭心跳检测定时器
  seatPhoneStore.stopHeartbeatTimer()
  // 销毁Janus实例对象
  seatPhoneStore.destroyJanus()
}
onMounted(() => {
  // 监听网络在线状态
  window.addEventListener('online', handleNetworkChange)
  // 监听网络离线
  window.addEventListener('offline', handleNetworkChange)
  // 监听网络信息变化
  // navigator.connection.addEventListener('change', handleNetworkChange)

  init()
  window.addEventListener('beforeunload', reset)
})
onBeforeUnmount(() => {
  // 监听网络在线状态
  window.removeEventListener('online', handleNetworkChange)
  // 监听网络离线
  window.removeEventListener('offline', handleNetworkChange)
  // 监听网络信息变化
  // navigator.connection.removeEventListener('change', handleNetworkChange)

  reset()
  window.removeEventListener('beforeunload', reset)
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.drawer-header-time {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  flex: none;
  &.idle-time {
    padding: 2px 4px;
    border-radius: 4px;
    background-color: #13BF77;
    color: #FFF;
    font-size: 13px;
    line-height: 20px;
  }
}
.calling-dialog {
  .form-dialog-content {
    color: #000;
    font-size: 14px;
    font-weight: 600;
    .colorful {
      color: #165DFF;
    }
    & > * {
      flex: none;
    }
  }
}
.call-fail-dialog {
  .call-fail-text {
    margin-left: 8px;
    font-size: 14px;
    font-weight: 600;
  }
}
</style>
