<template>
  <el-drawer
    v-model="dialogVisible"
    :before-close="cancel"
    size="600px"
    :with-header="false"
  >
    
    <el-scrollbar height="calc(100%-80px)" wrap-class="tw-bg-[#f5f7fa] " view-class="tw-flex tw-flex-col tw-h-full ">
      <div class="tw-px-[16px] tw-py-[12px] tw-text-left tw-bg-white tw-justify-between tw-flex tw-items-center tw-w-full">
        <span class="form-dialog-header ">坐席情况</span>
        <span class="info-title tw-w-[40px]">{{ `${seatData.onlineSeats?.length}/${seatData.totalSeats?.length}` }}</span>
      </div>
      <div class="tw-grid tw-grid-cols-4 tw-gap-[12px] tw-rounded-[4px] tw-m-[16px] tw-py-[12px] tw-px-[16px] tw-bg-white tw-flex-grow">
        <!-- 休息中 -->
        <div>
          <div class="tw-flex tw-items-center tw-mb-[12px]">
            <span class="status-box tw-bg-[var(--primary-red-color)]">休息中</span>
            <span class="tw-ml-[8px]">{{ seatData.inRestSeats?.length }}人</span>
          </div>
          <div>
            <div v-for="item in seatData.inRestSeats" :key="item.id" class="name-box">
              <span class="rectangle-left tw-bg-[var(--primary-red-color)]"></span>
              <span class="tw-truncate">{{ item.account||'' }}</span>
            </div>
          </div>
        </div>
        <!-- 空闲中 -->
        <div>
          <div class="tw-flex tw-items-center tw-mb-[12px]">
            <span class="status-box tw-bg-[var(--primary-orange-color)]">空闲中</span>
            <span class="tw-ml-[8px]">{{ seatData.idleSeats?.length }}人</span>
          </div>
          <div>
            <div v-for="item in seatData.idleSeats" :key="item.id" class="name-box">
              <span class="rectangle-left tw-bg-[var(--primary-orange-color)]"></span>
              <span class="tw-truncate">{{ item.account||'' }}</span>
            </div>
          </div>
        </div>
        <!-- 通话中 -->
        <div>
          <div class="tw-flex tw-items-center tw-mb-[12px]">
            <span class="status-box tw-bg-[var(--primary-blue-color)]">通话中</span>
            <span class="tw-ml-[8px]">{{ seatData.dialingSeats?.length }}人</span>
          </div>
          <div>
            <div v-for="item in seatData.dialingSeats" :key="item.id" class="name-box">
              <span class="rectangle-left tw-bg-[var(--primary-blue-color)]"></span>
              <span class="tw-truncate">{{ item.account||'' }}</span>
            </div>
          </div>
        </div>
        <!-- 话后处理中 -->
        <div>
          <div class="tw-flex tw-items-center tw-mb-[12px]">
            <span class="status-box tw-bg-[var(--primary-green-color)]">话后处理中</span>
            <span class="tw-ml-[8px]">{{ seatData.postingSeats?.length }}人</span>
          </div>
          <div>
            <div v-for="item in seatData.postingSeats" :key="item.id" class="name-box">
              <span class="rectangle-left tw-bg-[var(--primary-green-color)]"></span>
              <span class="tw-truncate">{{ item.account||'' }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref,watch, reactive,} from 'vue'
import { useUserStore } from '@/store/user'
import { SeatMember } from '@/type/seat'
const emits = defineEmits(['update:visible',])
const userInfo  = useUserStore()
const props = defineProps<{
  visible: boolean,
  seatData: {
    dialingSeats?: SeatMember[],
    inRestSeats?: SeatMember[],
    totalSeats?: SeatMember[],
    onlineSeats?: SeatMember[],
    idleSeats?: SeatMember[],
    postingSeats?: SeatMember[],
  }
}>();
const dialogVisible = ref(props.visible)
const seatData = reactive(props.seatData)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    Object.assign(seatData, props.seatData)
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.name-box {
  display: flex;
  width: 129px;
  height: 36px;
  padding: 8px 12px;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: #F0F2F5;
  position: relative;
  line-height: 20px;
  margin-bottom: 12px;
  color: var(--primary-black-color-600);
}
.status-box {
  display: flex;
  height: 24px;
  min-width: 55px;
  padding: 2px 8px;
  align-items: center;
  justify-content: center;
  margin: 0;
  border-radius: 2px;
  color: #fff;
  width: auto;
}
.rectangle-left {
  border-radius: 0px 2px 2px 0px;
  width: 2px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 6px;
}
</style>
