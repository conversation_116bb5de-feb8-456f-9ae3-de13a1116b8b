<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="800px"
    @close="cancel"
    :close-on-click-modal="false"
    align-center
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 180px)'"
      wrap-class="tw-px-[12px] tw-w-full"
      view-class="tw-flex tw-flex-col"
    >
      <el-form
        :model="editData"
        :rules="rules"
        :disabled="readonly"
        label-width="90px"
        class="tw-grow"
        ref="editRef"
      >
        <el-form-item label="意向分类：" prop="intentionLevelId" >
          <el-select v-model="editData.intentionLevelId" clearable :style="'width:100%'"  placeholder="请选择客户意向分类">
            <el-option
              v-for="item in scriptStore.intentionLevelOptions"
              :key="item.id"
              :label="`${item.intentionType} - ${item.intentionName}`"
              :value="item.id+''"
            />
          </el-select>
        </el-form-item>
        <el-form-item label-width="0" prop="excludeConditionList">
          <MultRuleBox
            v-if="dialogVisible"
            v-model:data="editData.excludeConditionList"
            :readonly="readonly"
            :scriptId="editId"
            required
            :options="enum2Options(RuleTypeEnum)"
            title="排除条件"
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer" v-if="!readonly">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, defineAsyncComponent, computed,} from 'vue'
import { FinalIntentionRuleItem, FinalIntentionRuleOrigin, RuleTypeEnum, } from '@/type/IntentionType'
import { ElMessage, } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { scriptIntentionModel, } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js';
import { enum2Options } from '@/utils/utils'
import { checkMultRules }  from '@/components/script-rules/constant'
import MultRuleBox from '@/components/script-rules/MultRuleBox.vue'


const scriptStore = useScriptStore()
const editId = scriptStore.id
const readonly = scriptStore.isChecked
const loading = ref(false)
const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean,
  editData: FinalIntentionRuleItem | null,
}>();
const title = computed(() => (props.editData?.id ? '编辑最终意向' : '新增最终意向'))

const dialogVisible = ref(props.visible)
const editData = reactive<FinalIntentionRuleItem>(props.editData || new FinalIntentionRuleOrigin(editId))

const rules:FormRules = {
  intentionLevelId: [
    { required: true, message: '请选择意向分类', trigger: ['change']},
  ],
  excludeConditionList: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkMultRules(editData.excludeConditionList, true)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
}

const editRef = ref<FormInstance | null>(null)
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      editData.intentionLevelName = scriptStore.intentionLevelOptions?.find(item => item.id == editData.intentionLevelId)?.intentionType
      const [err] = await to(scriptIntentionModel.saveFinalIntentionList({
        id: editData.id || undefined,
        intentionLevelId: editData.intentionLevelId,
        intentionLevelName: editData.intentionLevelName,
        scriptId: editId,
        excludeConditionList: editData.excludeConditionList,
      }))
      loading.value = false
      if (!err) {
        dialogVisible.value = false
        ElMessage.success('操作成功')
        emits('confirm')
      }
    }
  })
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, async n => {
  dialogVisible.value = n
  if (!n) {
    Object.assign(editData, new FinalIntentionRuleOrigin(editId))
    return
  }
  Object.assign(editData, props.editData ? JSON.parse(JSON.stringify(props.editData)) : new FinalIntentionRuleOrigin(editId))
  editRef.value && editRef.value.clearValidate()
})
</script>

<style lang="postcss" type="text/postcss">
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
}
</style>
