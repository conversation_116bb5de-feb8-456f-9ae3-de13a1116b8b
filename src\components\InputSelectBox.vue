<template>
  <el-input
    v-model="val"
    @keyup.enter="search()"
    @blur="handleSelectChange()"
    @clear="search()"
    clearable
    :placeholder="props.placeholder"
  >
    <template #prepend>
      <el-select v-model="searchType" :style="{'width': '96px'}" @change="handleSelectChange">
        <el-option v-for="item in props.selectList" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
    </template>
  </el-input>
</template>

<script lang="ts" setup>
import { ref, watch, } from 'vue'

// 组件入参props
const props = withDefaults(defineProps<{
  value?: string, // 选项列表中代表value的属性
  selectVal?: string,
  placeholder?: string, // 选择框占位符
  selectList: {name: string, value: string}[]
}>(), {
  placeholder: '请输入'
})
// emit
const emits = defineEmits([
  'update:value',
  'update:selectVal',
  'search'
])

const searchType = ref(props.selectList[0].value)
const val = ref('')
const handleSelectChange = () => {
  val.value = val.value?.trim() || ''
  emits('update:value', val.value)
  emits('update:selectVal', searchType.value)
}
// 暴露给父组件
const search = () => {
  handleSelectChange()
  emits('search')
}

/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch([() => props.value, () => props.selectVal], () => {
  val.value = props.value || ''
  searchType.value = props.selectVal || props.selectList[0].value
}, {deep: true, immediate: true})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input {
  width: 100%;
}

</style>
