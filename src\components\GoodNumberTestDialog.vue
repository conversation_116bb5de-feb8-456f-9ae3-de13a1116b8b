<template>
  <el-dialog
    v-model="dialogVisible"
    width="600"
    class="merchant-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        靓号测试
      </div>
    </template>

    <el-scrollbar class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          label-width="90px"
          label-position="right"
          :model="form"
          :rules="rules"
        >
          <el-form-item label="测试号码：" prop="phone">
            <div class="tw-flex tw-flex-row">
              <el-input
                v-model="form.phone"
                maxlength="20"
                show-word-limit
                clearable
                placeholder="请输入正确的手机号码格式"
                style="width: 100%;"
                :disabled="loadingConfirm"
              />
              <el-button type="primary" class="tw-ml-[8px]" :loading="loadingConfirm" @click="onClickTest">
                测试
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="命中规则：">
            <el-table
              v-loading="loadingConfirm"
              :data="ruleList"
              :header-cell-style="tableHeaderStyle"
              max-height="50vh"
            >
              <template #empty>
                暂无数据
              </template>

              <el-table-column label="靓号名称" prop="regexName" show-overflow-tooltip />
              <el-table-column label="靓号规则" prop="phoneRegex" show-overflow-tooltip />
            </el-table>
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="handleCancel">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { CloseBold } from '@element-plus/icons-vue'
import { GoodNumberRestrictionInfo, GoodNumberRestrictionTestParam } from '@/type/dataFilter'
import to from 'await-to-js'
import { goodNumberRestrictionModel } from '@/api/data-filter'
import { SupplierGoodNumberInfo } from '@/type/supplier'
import { tableHeaderStyle } from '@/assets/js/constant'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  // 当前供应商已挂载的靓号限制列表
  list?: SupplierGoodNumberInfo[],
}>()
const emits = defineEmits([
  'close',
  'update'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(() => props.visible, async (val) => {
  dialogVisible.value = val
  // 每次显示弹窗时
  if (val) {
    await nextTick()
    resetForm()
    // 更新表单数据
    Object.assign(form, formDefault())
  }
})

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): GoodNumberRestrictionTestParam => {
  return {
    phone: ''
  }
}
// 表单数据
const form: GoodNumberRestrictionTestParam = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  phone: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value?.length) {
        callback(new Error('号码不能为空'))
      } else if (value.length > 20) {
        callback(new Error('号码长度不超过20个字符'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 重置其他组件数据
  ruleList.value = []
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: GoodNumberRestrictionTestParam = {
    phone: form.phone ?? '',
  }
  // 请求接口
  const [err, res] = <[any, GoodNumberRestrictionInfo[]]>await to(goodNumberRestrictionModel.test(params))
  if (err) {
    ElMessage.error('无法得到靓号测试结果')
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }
  // 更新测试结果
  const tempList = res?.length ? res : []
  if (Array.isArray(props.list)) {
    ruleList.value = tempList.filter((testItem: GoodNumberRestrictionInfo) => {
      return !!props.list!.find((selectedItem: SupplierGoodNumberInfo) => {
        return selectedItem?.lightPhoneId === testItem?.id
      })
    })
  } else {
    ruleList.value = tempList
  }

  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 靓号测试 开始 ----------------------------------------

// 匹配的规则列表
const ruleList = ref<GoodNumberRestrictionInfo[]>([])

/**
 * 点击测试按钮
 */
const onClickTest = () => {
  ruleList.value = []
  handleConfirm()
}

// ---------------------------------------- 靓号测试 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// 如果是每次显示弹窗时都要进行操作，应该放在 dialogVisible 变量的 watch 函数里执行
// 这里是在 vue 组件创建时执行（setup 相当于生命周期的 beforeCreate 和 created），路由不变或者页面不刷新，只会执行一次

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
</style>
