import dayjs from 'dayjs'
import { lineChartModel, lineSupplierModel, lineMerchantModel } from '@/api/line'
import { MonitorTypeEnum, TodayInfo } from '@/type/line'


const colorMap = new Map([
  ['red', {color: '#E54B17', fontWeight: 600}],
  ['yellow', {color: '#FFCC00', fontWeight: 600}],
])

export const timeRangeList = [
  {label: '近5分钟', value: 5},
  {label: '近15分钟', value: 15},
  {label: '近30分钟', value: 30},
  {label: '近60分钟', value: 60},
  {label: '今日', value: 0},
]

// 折线图时间颗粒度
export const intervalList = [
  { name: '5min', val: 5 },
  { name: '10min', val: 10 },
  { name: '15min', val: 15 },
  { name: '30min', val: 30 },
  { name: '60min', val: 60 },
]
export const dateRadioList = [
  { name: '今日', value: dayjs().format('YYYY-MM-DD') },
  { name: '昨日', value: dayjs().add(-1, 'days').format('YYYY-MM-DD') },
  { name: '前日', value: dayjs().add(-2, 'days').format('YYYY-MM-DD') },
  { name: '7天', value: dayjs().add(-6, 'days').format('YYYY-MM-DD') },
  { name: '15天', value: dayjs().add(-14, 'days').format('YYYY-MM-DD') },
]

export const apiChartMap = new Map<string, any[]>([
  [ dayjs().format('YYYY-MM-DD'),
    [
      lineChartModel.getTodayMonitorLineInfo,
      lineChartModel.getTodayMonitorCountryInfo,
      lineChartModel.getTodayMonitorProvinceInfo,
    ]
  ],
  [ dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    [
      lineChartModel.getYesterdayMonitorLineInfo,
      lineChartModel.getYesterdayMonitorCountryInfo,
      lineChartModel.getYesterdayMonitorProvinceInfo
    ]
  ],
  [ dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
    [
      lineChartModel.getBeforeYesterdayMonitorLineInfo,
      lineChartModel.getBeforeYesterdayMonitorCountryInfo,
      lineChartModel.getBeforeYesterdayMonitorProvinceInfo
    ]
  ],
  [ dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
    [
      lineChartModel.getRecentMonitorLineInfo,
      lineChartModel.getRecentMonitorCountryInfo,
      lineChartModel.getRecentMonitorProvinceInfo
    ]
  ],
  [ dayjs().subtract(14, 'day').format('YYYY-MM-DD'),
    [
      lineChartModel.getRecentMonitorLineInfo,
      lineChartModel.getRecentMonitorCountryInfo,
      lineChartModel.getRecentMonitorProvinceInfo
    ]
  ],
])

export const apiMonitorMap = new Map<string, any[]>([
  [ dayjs().format('YYYY-MM-DD'),
    [
      lineSupplierModel.getMonitorList,
      lineMerchantModel.getMonitorList,
      lineSupplierModel.getMonitorDetails,
      lineMerchantModel.getMonitorDetails,
    ]
  ],
  [ dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    [
      lineChartModel.findSupplyLineMonitorListYesterday,
      lineChartModel.findTenantLineMonitorListYesterday,
      lineChartModel.findSupplyLineMonitorDetailListYesterday,
      lineChartModel.findTenantLineMonitorDetailListYesterday
    ]
  ],
  [ dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
    [
      lineChartModel.findSupplyLineMonitorListBeforeYesterday,
      lineChartModel.findTenantLineMonitorListBeforeYesterday,
      lineChartModel.findSupplyLineMonitorDetailListBeforeYesterday,
      lineChartModel.findTenantLineMonitorDetailListBeforeYesterday
    ]
  ],
])

export const apiDurationMap = [
  lineChartModel.findSupplyLineCallDurationDistribution,
  lineChartModel.findTenantLineCallDurationDistribution
]

const monitorMap: Map<string, Map<'red' | 'yellow', {min: number, max: number}>> = new Map([
  ['currentlyConnectedRate', new Map([
    ['red', { min: -Infinity, max: 5 }],
    ['yellow', { min: 5, max: 10 }],
  ])],
  ['currentlySilenceCall', new Map([
    ['red', { min: 25, max: Infinity }],
    ['yellow', { min: 20, max: 25 }],
  ])],
  ['currentlyCallFailed', new Map([
    ['red', { min: 25, max: Infinity }],
    ['yellow', { min: 20, max: 25 }],
  ])],
  ['currentlySilenceHangup', new Map([
    ['red', { min: 8, max: Infinity }],
    ['yellow', { min: 6, max: 8 }],
  ])],
  ['currentlyAssistant', new Map([
    ['red', { min: 8, max: Infinity }],
    ['yellow', { min: 4, max: 8 }],
  ])],
  ['currentlyPromptSound', new Map([
    ['red', { min: 2, max: Infinity }],
    ['yellow', { min: 1, max: 2 }],
  ])],
  ['currentlyOneSecondConnected', new Map([
    ['red', { min: 2, max: Infinity }],
    ['yellow', { min: 1, max: 2 }],
  ])],
])


/**
 * @description: 运行监控列表数据根据指定monitorMap展示颜色
 * @param {string} value 列表数据值
 * @param {string} type 列表的列名
 * @returns {string} 样式
 */
export const getMonitorColor = (value: string, type: string) => {
  if (!value || !value.includes('%')) return ''
  const num = +(value.split('%')[0].trim())
  const indexMap = monitorMap.get(type)
  if (!indexMap) return ''
  if (num <= indexMap.get('red')?.max! && num > indexMap.get('red')?.min!) {
    return colorMap.get('red')
  }
  if (num >= indexMap.get('yellow')?.min! && num <= indexMap.get('yellow')?.max!) {
    return colorMap.get('yellow')
  }
  return ''
}


const columnMap = new Map<string, Record<string, string[]>>([
  ['supply-monitor', {
    total: [
      '供应线路名称', '线路类型', '线路编号', '生效状态', '前缀', '主叫号码', '并发',
      '呼叫数', '接通数', '接通率', '无声通话占比', '送呼失败', '沉默挂机',
      '小助理', '运营商提示音', '秒挂（1s）', '秒挂（2s）', '转人工占比',
      'A类占比', 'B类占比', 'C类占比', 'D类占比',
      '供应商名称', '供应商编号'
    ],
    default: [
      '供应线路名称', '线路类型', '线路编号', '生效状态', '前缀', '主叫号码', '并发',
      '呼叫数', '接通数', '接通率', '无声通话占比', '送呼失败', '沉默挂机',
      '小助理', '运营商提示音', '秒挂（1s）', '秒挂（2s）', '转人工占比',
      'A类占比', 'B类占比', 'C类占比', 'D类占比',
      '供应商名称', '供应商编号'
    ],
    disabled: ['供应线路名称', ],
  }],
  ['tenant-monitor', {
    total: [
      '商户线路名称', '线路类型', '线路编号', '生效状态', '并发',
      '呼叫数', '接通数', '接通率',
      '无声通话占比', '送呼失败', '路由失败',
      '沉默挂机', '小助理', '运营商提示音', '秒挂（1s）', '秒挂（2s）', '转人工占比',
      'A类占比', 'B类占比', 'C类占比', 'D类占比',
      '商户名称', '商户编号', '所属账号',
    ],
    default: [
      '商户线路名称', '线路类型', '线路编号', '生效状态', '并发',
      '呼叫数', '接通数', '接通率',
      '无声通话占比', '送呼失败', '路由失败', '转人工占比',
      '沉默挂机', '小助理', '运营商提示音', '秒挂（1s）', '秒挂（2s）',
      'A类占比', 'B类占比', 'C类占比', 'D类占比',
      '商户名称', '商户编号', '所属账号',
    ],
    disabled: ['商户线路名称', ],
  }],
  ['merchant-tenant-line-monitor', {
    total: [
      '商户线路名称', '线路类型', '线路编号', '生效状态', '并发',
      '呼叫数', '接通数', '接通率',
      '无声通话占比', '转人工占比',
      '沉默挂机', '小助理', '秒挂（1s）', '秒挂（2s）',
      'A类占比', 'B类占比', 'C类占比', 'D类占比',
    ],
    default: [
      '商户线路名称', '线路类型', '线路编号', '生效状态', '并发',
      '呼叫数', '接通数', '接通率',
      '无声通话占比', '转人工占比',
      '沉默挂机', '小助理', '秒挂（1s）', '秒挂（2s）',
      'A类占比', 'B类占比', 'C类占比', 'D类占比',
    ],
    disabled: ['商户线路名称', ],
  }]
])

export const getColumnSettingByName = (name: string) => {
  const obj = columnMap.get(name)
  if (!obj) return {
    totalCols: [], defaultCols: [], disabledCols: [], 
  }
  const totalCols: string[] = [...obj?.total]
  const defaultCols: string[] = [...obj?.default]
  const disabledCols: string[] = [...obj?.disabled]
  return {
    totalCols, defaultCols, disabledCols, 
  }
}

export const monitorStatisticsTypeObj: Record<string, {
  legend: string[],
  isConnect?: boolean,
  name?: string,
  num: keyof Omit<TodayInfo, 'totalCallNum'>,
}> =  {
  [MonitorTypeEnum['外呼接通率']]: {
    legend: ['外呼量', '呼叫接通率', '接通数'],
    name: '外呼接通率',
    isConnect: true,
    num: 'todayConnected',
  },
  
  [MonitorTypeEnum['无声通话占比']]: {
    legend: ['无声通话数', '无声通话占比', '接通数'],
    name: '无声通话占比',
    isConnect: false,
    num: 'todaySilenceCall',
  },
  [MonitorTypeEnum['送呼失败占比']]: {
    legend: ['送呼失败数', '送呼失败占比', '接通数'],
    name: '送呼失败占比',
    isConnect: false,
    num: 'todayCallFailed',
  },
  [MonitorTypeEnum['秒挂（1S）占比']]: {
    legend: ['秒挂（1S）数', '秒挂（1S）占比', '接通数'],
    name: '秒挂（1S）数',
    isConnect: false,
    num: 'todayOneSecondConnected',
  },
  [MonitorTypeEnum['秒挂（2S）占比']]: {
    legend: ['秒挂（2S）数', '秒挂（2S）占比', '接通数'],
    name: '秒挂（2S）占比',
    isConnect: false,
    num: 'todayTwoSecondConnected',
  },
  [MonitorTypeEnum['沉默挂机占比']]: {
    legend: ['沉默挂机数', '沉默挂机占比', '接通数'],
    name: '沉默挂机占比',
    isConnect: false,
    num: 'todaySilenceHangup',
  },
  [MonitorTypeEnum['小助理占比']]: {
    legend: ['小助理通话数', '小助理占比', '接通数'],
    name: '小助理占比',
    isConnect: false,
    num: 'todayAssistant',
  },
  [MonitorTypeEnum['A类占比']]: {
    legend: ['A类通话数', 'A类占比', '接通数'],
    name: 'A类占比',
    isConnect: false,
    num: 'todayClassANum',
  },
  [MonitorTypeEnum['B类占比']]: {
    legend: ['B类通话数', 'B类占比', '接通数'],
    name: 'B类占比',
    isConnect: false,
    num: 'todayClassBNum',
  },
  [MonitorTypeEnum['C类占比']]: {
    legend: ['C类通话数', 'C类占比', '接通数'],
    name: 'C类占比',
    isConnect: false,
    num: 'todayClassCNum',
  },
  [MonitorTypeEnum['D类占比']]: {
    legend: ['D类通话数', 'D类占比', '接通数'],
    name: 'D类占比',
    isConnect: false,
    num: 'todayClassDNum',
  },
  [MonitorTypeEnum['运营商提示音占比']]: {
    legend: ['运营商提示音通话数', '运营商提示音占比', '接通数'],
    name: '运营商提示音占比',
    isConnect: false,
    num: 'todayPromptSound',
  },
  [MonitorTypeEnum['转人工占比']]: {
    legend: ['转人工通话数', '转人工占比', '接通数'],
    name: '转人工占比',
    isConnect: false,
    num: 'todayTransCallSeatNum',
  },
};
