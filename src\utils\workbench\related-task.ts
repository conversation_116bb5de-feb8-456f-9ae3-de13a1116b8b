import { RelatedTaskItem } from '@/type/clue'
import { seatWorkbenchAccountModel } from '@/api/seat'
import { useSeatPhoneStore } from '@/store/seat-phone'
import { SeatLogActionEnum, SeatLogTypeEnum, SeatStatusEnum } from '@/type/seat'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import { useSeatInfoStore } from '@/store/seat/seat-info'
import { storeToRefs } from "pinia"

/**
 * 坐席签入签出任务
 * @param list 已选任务列表
 */
export const checkInAndOutTask = async (list: RelatedTaskItem[] = []): Promise<boolean> => {
  const seatPhoneStore = useSeatPhoneStore()
  const { needUpdateWorkbenchStatistics } = storeToRefs(seatPhoneStore)
  const seatInfoStore = useSeatInfoStore()

  // 提取出有效的任务ID
  const newIdSet = new Set(
    (list?.length ? list : [])
      .map(task => task?.id)
      .filter((id): id is number => typeof id === 'number')
  )
  const newIdList = Array.from(newIdSet)

  // 请求接口
  const params = {
    taskIds: newIdList,
    callSeatStatus: newIdList.length ? SeatStatusEnum.HUMAN_MACHINE_IDLE : SeatStatusEnum.MANUAL_DIRECT_IDLE,
  }
  const [err] = await to(seatWorkbenchAccountModel.checkIn(params))

  // 返回失败结果
  if (err) {
    // 提示失败
    ElMessage.error(`签入签出任务失败：${err.message}`)
    seatPhoneStore.report({
      type: SeatLogTypeEnum['错误'],
      action: SeatLogActionEnum['手动签入签出任务'],
      desc: '手动签入签出任务',
    })
    return false
  }

  // 返回成功结果
  // 提示成功
  ElMessage.success('签入签出任务成功')
  ElMessage.warning('通话中请勿强制刷新、关闭页面')
  seatPhoneStore.report({
    action: SeatLogActionEnum['手动签入签出任务'],
    desc: '手动签入签出任务',
  })
  // 更新签入任务列表
  seatInfoStore.updateSeatTaskList(JSON.parse(JSON.stringify(list)))
  // 更新坐席状态
  seatInfoStore.updateSeatStatus(params.callSeatStatus)
  // 更新通话类型
  seatPhoneStore.updateCallType()
  // 更新坐席统计数据
  needUpdateWorkbenchStatistics.value = true
  return true
}
