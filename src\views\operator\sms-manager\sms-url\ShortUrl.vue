<template>
  <div class="sms-container">
    <div class="search-box">
      <div class="tw-grid tw-grid-cols-6 tw-gap-[8px] tw-border-b-[1px] tw-pb-[8px]">
        <div class="item-col">
          <span class="label">短链ID：</span>
          <el-input
            v-model="searchForm.linkNumber"
            placeholder="请输入短链ID"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div class="item-col">
          <span class="label">短链名称：</span>
          <el-input
            v-model="searchForm.linkName"
            placeholder="请输入短链名称"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div v-show="props.type===0" class="item-col">
          <span class="label">短链：</span>
          <el-input
            v-model="searchForm.shortUrl"
            placeholder="请输入短链"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div class="item-col">
          <span class="label">原始短链：</span>
          <el-input
            v-model="searchForm.originalUrl"
            placeholder="请输入原始短链"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div class="item-col">
          <span class="label">短链域名：</span>
          <el-select v-model="searchForm.domain" class="tw-w-full" placeholder="请选择域名" clearable>
            <el-option v-for="item in domainAllList" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div class="item-col">
          <span class="label">所属账号：</span>
          <el-select
            v-model="searchForm.account"
            clearable
            filterable
            placeholder="选择商户主账号"
            style="width: 100%;"
          >
            <el-option
              v-for="item in masterAccountList"
              :key="item.groupId"
              :label="item.account"
              :value="item.account"
            />
          </el-select>
        </div>
      </div>
      <div class="tw-flex tw-justify-end tw-pt-[8px] tw-items-center tw-h-[32px]">
        <div>
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
      <div v-if="!props.isVolcano" class="tw-mt-[6px]">
        <el-button @click="editUrl()" type="primary" class="tw-float-right">
          新增{{props.type === 0 ? '普通短链' : '千人千链'}}
        </el-button>
      </div>
    </div>

    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      @sort-change="handleSortChange"
      stripe
    >
      <el-table-column property="linkNumber" fixed="left" label="短链ID" align="left" width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="linkName" fixed="left" label="短链名称" align="left" width="240">
        <template #default="{ row }">
          <div class="tw-flex tw-items-center">
            <span>{{ row.linkName || '-' }}</span>
            <div v-if="dayjs(row.expireTime).endOf('day').isBefore(dayjs().endOf('day'))" class="status-box-mini red-status tw-ml-[4px]">已过期</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="props.type === 0" property="shortUrl" label="短链" align="left" min-width="160"></el-table-column>
      <el-table-column property="originalUrl" label="原始链接" show-overflow-tooltip align="left" min-width="240"></el-table-column>
      <el-table-column v-if="props.type === 0" property="domain" label="短链域名" align="left" min-width="160"></el-table-column>
      <el-table-column v-if="props.type === 1" property="domainMap" label="短链域名" show-overflow-tooltip align="left" min-width="160">
        <template #default="{ row }">
          {{ row.domainMap && Object.keys(row.domainMap).length > 0 ?  Object.keys(row.domainMap).join('、') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="account" label="所属主账号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="props.type === 0" property="expireTime" label="过期时间" align="left" min-width="120">
        <template #default="{ row }">
          {{ row.expireTime ?  dayjs(row.expireTime).format('YYYY-MM-DD') : '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="props.type === 1" property="expireDays" label="有效时长" align="left" min-width="120">
        <template #default="{ row }">
          {{ row.expireDays ?  row.expireDays + ' 天' : '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="props.type === 1" property="paramTypes" label="拼接信息" align="left" min-width="240" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.paramTypes && row.paramTypes.length > 0 ?  row.paramTypes.map((item: ShortLinkParamEnum) => findValueInEnum(item, ShortLinkParamEnum)).join('、') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="tenantSmsTemplates" label="关联短信模板" align="center" width="120">
        <template #default="{ row }">
         <span
            v-if="row.tenantSmsTemplates && row.tenantSmsTemplates?.length>0"
            class="tw-text-[var(--el-color-primary)] tw-cursor-pointer"
            @click="showRelated(row.tenantSmsTemplates)"
         >{{ row.tenantSmsTemplates?.length || 0 }}</span>
         <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="createTime" label="创建时间" align="center" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="updateTime" label="更新时间" align="center" width="160" sortable :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="enableStatus" label="短链状态" sortable align="center" fixed="right" width="90" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <el-switch
            :model-value="row.enableStatus"
            inline-prompt
            size="small"
            active-value="ENABLE"
            inactive-value="DISABLE"
            active-text="已启用"
            inactive-text="已冻结"
            @click="changeStatus(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="130" align="right" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="editUrl(row)">编辑</el-button>
          <el-button type="primary" link @click="goDetail(row)">统计</el-button>
          <el-button type="danger" link @click="deleteUrl(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  <EditDialog
    v-model:visible="editVisible"
    :data="currentData"
    :masterAccountList="masterAccountList||[]"
    :type="props.type"
    :isVolcano="props.isVolcano"
    @confirm="search"
  >
  </EditDialog>
  <RelatedListDialog
    title="关联短信模板"
    :list="relatedList||[]"
    v-model:visible="relatedVisible"
  />
  <StatisticsDrawer
    v-model:visible="detailVisible"
    :type="props.type"
    :isVolcano="props.isVolcano"
    :linkNumber="currentData?.linkNumber || ''"
    :linkName="currentData?.linkName"
  />
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { findValueInEnum, formatterEmptyData, handleTableSort } from '@/utils/utils'
import { SmsUrlItem, ShortLinkParamEnum } from '@/type/sms'
import { smsUrlModel } from '@/api/sms'
import { useSmsStore } from '@/store/sms'
import { tableHeaderStyle } from '@/assets/js/constant'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { UrlOrigin } from './constant'
import to from 'await-to-js'
import Confirm from '@/components/message-box'
import EditDialog from './EditDialog.vue'
import RelatedListDialog from '@/components/RelatedListDialog.vue'
import StatisticsDrawer from './StatisticsDrawer.vue'
import { SmsTemplateItem, } from '@/type/merchant'
import { trace } from '@/utils/trace'

const props = defineProps<{
  type: number, // 0: 普通短链，1：千人千链
  isVolcano?: boolean, // 是否火山短链管理，默认false
}>();

const loading = ref(false)

const currentPage = ref(1)
const total = ref(0)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<SmsUrlItem[] | null>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const searchForm = reactive<SmsUrlItem>(new UrlOrigin())
const search = async () => {
  loading.value = true
  const res = await to(props.type === 0 ? smsUrlModel.findNormalList(searchForm) : smsUrlModel.findThousandList(searchForm))
  const res1 = (res[1] || []).filter(item => item.enableStatus === 'ENABLE').sort((a, b) => {
    return dayjs(b.createTime).isBefore(dayjs(a.createTime)) ? -1 : 1
  })
  const res2 = (res[1] || []).filter(item => item.enableStatus === 'DISABLE').sort((a, b) => {
    return dayjs(b.createTime).isBefore(dayjs(a.createTime)) ? -1 : 1
  })
  tableData.value = [...res1, ...res2].filter(item => !props.isVolcano || item.account?.includes('huoshan'))
  total.value = tableData.value?.length || 0
  loading.value = false
}

const clearSearchForm = () => {
  Object.assign(searchForm, new UrlOrigin(props.type))
}

const currentData = ref<SmsUrlItem | null>(null)
const editVisible = ref(false)
const editUrl = (data?: SmsUrlItem) => {
  currentData.value = data || new UrlOrigin(props.type)
  editVisible.value = true
}


const changeStatus = async (row: SmsUrlItem) => {
  if (row.tenantSmsTemplates && row.tenantSmsTemplates?.length > 0) {
    return ElMessage.warning('该短链已关联短信模板, 请先取消关联')
  }
  const [err1] = await to(Confirm({
    text: `您确定要${row.enableStatus !== 'ENABLE' ? '启用' : '冻结'}【${row?.linkName || ''}】?`,
    type: 'warning',
    title: `${row.enableStatus !== 'ENABLE' ? '启用' : '冻结'}确认`,
    confirmText: row.enableStatus !== 'ENABLE' ? '启用' : '冻结',
  }))
  if (err1) return
  loading.value = true
  const params = {
    id: row.id!,
    enableStatus: row.enableStatus !== 'ENABLE' ? 'ENABLE' : 'DISABLE',
  }
  trace({
    page: `${props.isVolcano ? '火山' : ''}短链管理-${props.type === 1 ? '千人千链' : '普通短链'}-${row.enableStatus !== 'ENABLE' ? '启用' : '冻结'}`,
    params
  })
  const [err2] = await to(smsUrlModel[props.type === 1 ? 'switchThousand' : 'switchNormal'](params))
  loading.value = false
  !err2 && ElMessage.success('操作成功')
  search()
}
const deleteUrl = (row: SmsUrlItem) => {
  if (!row.id) {
    return ElMessage.warning('短链ID获取失败')
  }
  if (row.tenantSmsTemplates && row.tenantSmsTemplates?.length > 0) {
    return ElMessage.warning('该短链已关联短信模板, 请先取消关联')
  }
  Confirm({
    text: `您确定要删除【${row?.linkName || ''}】?`,
    type: 'danger',
    title: `删除确认`,
  }).then(async () => {
    trace({
      page: `${props.isVolcano ? '火山' : ''}短链管理-${props.type === 1 ? '千人千链' : '普通短链'}-删除`,
      params: { id: row.id }
    })
    const [err] = await to(smsUrlModel[props.type === 0 ? 'deleteNormal' : 'deleteThousand']({
      id: row.id!,
    }))
    !err && ElMessage.success('删除成功')
    search()
  }).catch(() => {
  })
}
const detailVisible = ref(false)
const goDetail = (row: SmsUrlItem) => {
  detailVisible.value = true
  currentData.value = row || new UrlOrigin(props.type)
}

// 查看关联短信模板
const relatedVisible = ref(false)
const relatedList = ref<string[]>([])
const showRelated = async (list?: SmsTemplateItem[]) => {
  if (!list || list.length === 0) {
    return ElMessage.warning('暂无关联短信模板')
  }
  relatedList.value = list?.flatMap(item => ([item.templateName!])) || []
  relatedVisible.value = true
}

const masterAccountList = ref<{
  account: string,
  groupId: string,
}[] | null>([]) // 主账号列表，全量
const domainAllList = ref<string[]>([]) // 全部可以选择的域名
const init = async () => {
  const [err1, res1] = await to(monitorStatisticModel.getAllMainAccount())
  masterAccountList.value = (res1 || []).filter(item => !!item.account && !!item.groupId && (!props.isVolcano || item.account?.includes('huoshan')))
  const smsInfo = useSmsStore()
  await to(smsInfo.getDomainList())
  await to(smsInfo.getVolcanoDomainList())
  domainAllList.value = !props.isVolcano ? smsInfo.domainAllList : smsInfo.volcanoDomainList
}
watch(() => props.type, () => {
  clearSearchForm()
  search()
})
onMounted(() => {
  init()
  search()
})
onUnmounted(() => {
  masterAccountList.value = null
  tableData.value = null
})

</script>

<style scoped lang="postcss" type="text/postcss">
.sms-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
