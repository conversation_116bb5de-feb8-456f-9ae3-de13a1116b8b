<template>
  <el-empty v-if="isEmpty && props.isAuto && !isEdit"></el-empty>
  <template v-else>
    <div v-if="props.isAuto && !isEmpty" v-loading="loading" class="tw-my-[6px] tw-flex tw-flex-col tw-items-start tw-w-full">
      <div class="tw-flex tw-items-center tw-h-[28px] tw-mb-1">
        <span class="tw-text-left tw-text-[14px] tw-font-semibold tw-text-[--primary-black-color-600] tw-w-[90px]">执行状态</span>
        <el-switch
          v-if="!isEdit "
          :model-value="autoInfo?.status"
          class="tw-ml-1"
          inline-prompt
          active-text="已开启"
          inactive-text="已关闭"
          active-value="1"
          inactive-value="0"
          @click="handleAutoStatus"
        />
        <span v-if="autoInfo?.status=='1'" class="tw-ml-[8px] tw-text-[12px] tw-text-[--primary-black-color-400]">{{ autoInfo?.autoStopTime ? `于${dayjs(autoInfo?.autoStopTime).format('YYYY-MM-DD')}结束` : '永久生效' }}</span>
      </div>
      <div class="tw-text-left tw-text-[14px] tw-font-semibold tw-text-[--primary-black-color-600]">执行规则</div>
    </div>
    <el-scrollbar v-loading="loading" class="tw-w-full tw-grow tw-shrink tw-mr-[-16px]" wrap-class="tw-pr-1">

    <el-form
      :model="editData"
      :disabled="props.isAuto && !isEdit"
      :rules="rules"
      label-width="100px"
      ref="editRef"
    >
      <el-form-item v-if="props.isAuto" label-width="0" prop="startWorkTimes" class="tw-col-span-2">
        <div class="tw-w-full">
          <TimePicker
            v-if="isEdit"
            name="执行时段"
            :startWorkTimeList="startWorkTimeList"
            :endWorkTimeList="endWorkTimeList"
            beginTime="07:00"
            endTime="20:00"
            showShortcut
            showResult
            @update="handleTimeList"
          />
          <div v-else v-if="startWorkTimeList && startWorkTimeList?.length > 0 && endWorkTimeList && endWorkTimeList?.length > 0" class="tw-flex tw-flex-wrap tw-items-center tw-mt-1 tw-text-[13px]">
            <div class="tw-w-[90px] tw-mr-[12px] tw-text-right">选中时间段：</div>
            <el-tag
              v-for="(item, index) in startWorkTimeList"
              :key="item"
              class="tw-mr-[4px] tw-my-[2px]"
            >{{ item + ' - ' +  endWorkTimeList[index]}}</el-tag>
          </div>
        </div>
      </el-form-item>
      <el-form-item v-if="props.isAuto" label="执行间隔：" prop="executeTimeGap">
        <el-select v-model="editData.executeTimeGap" class="tw-grow" placeholder="请选择执行间隔">
          <el-option v-for="item in intervalOption" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="导入来源：">
        <el-select
          v-model="editData.importSource"
          disabled
          placeholder="导入来源"
          class="tw-w-full"
          @change="updateData"
        >
          <el-option label="通话记录" value="0"/>
        </el-select>
      </el-form-item>
      <el-form-item label="通话类型：">
        <el-select
          v-model="editData.outboundType"
          :disabled="true"
          placeholder="通话类型"
          clearable
          class="tw-w-full"
          @change="updateData"
        >
          <el-option v-for="item in taskTypeList" :label="item.name" :value="item.value" :key="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item v-if="!props.isAuto" label="呼出时间：" prop="callOutTimeStart">
        <TimePickerBox
          v-model:start="editData.callOutTimeStart"
          v-model:end="editData.callOutTimeEnd"
          :splitToday="true"
          placeholder="呼出时间"
          class="tw-w-full"
          :maxRange="1000*60*60*7*24"
          format="YYYY-MM-DD HH:mm:ss"
          :clearable="false"
          @change="handleTimeChange()"
        />
      </el-form-item>
      <el-form-item v-if="!props.isAuto" label="外呼任务：" prop="taskIdList">
        <SelectPageBox
          v-model:selectVal="editData.taskIdList"
          :options="taskList||[]"
          name="taskName"
          val="id"
          key="id"
          placeholder="请选择外呼任务"
          :filterable="true"
          class="tw-flex-grow"
          isRemote
          @update:options="updateTaskList"
          @update:selectVal="updateData()"
          :loading="loadingTask"
          :total="taskNum"
          multiple
          canSelectAll
        >
        </SelectPageBox>
      </el-form-item>
      <el-form-item label="包含标签：" prop="containIntentionLabels">
        <InputListBox
          v-model:value="editData.containIntentionLabels"
          placeholder="包含标签"
          :disabled="props.isAuto && !isEdit"
          @update:value="updateData()"
        />
      </el-form-item>
      <el-form-item label="排除标签：" prop="excludeIntentionLabels">
        <div class="tw-w-full tw-flex tw-flex-col tw-items-start">
          <InputListBox
            v-model:value="editData.excludeIntentionLabels"
            placeholder="排除标签"
            :disabled="props.isAuto && !isEdit"
            @update:value="updateData()"
          />
          <el-checkbox v-model="editData.excludeEmptyLabel" true-label="1" false-label="0">排除空标签</el-checkbox>
        </div>
      </el-form-item>
      <el-form-item label="意向分类：" prop="intentionClass">
        <el-select
          v-model="editData.intentionClass"
          class="tw-w-full"
          placeholder="意向分类"
          multiple
          collapse-tags
          :max-collapse-tags="2"
          collapse-tags-tooltip
          clearable
          @change="updateData"
        >
          <el-option v-for="item in IntentionClassEnum" :key="item" :label="item" :value="item"/>
        </el-select>
      </el-form-item>
      <el-form-item prop="callDurationLeft" label="通话时长：">
        <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
          <InputNumberBox v-model:value="editData.minCallDuration" @blur="updateData" :max="editData.maxCallDuration || Number.MAX_VALUE" placeholder="最小" style="width: 47%" append="秒"/>
          <span class="info-title">&nbsp;至&nbsp;</span>
          <InputNumberBox v-model:value="editData.maxCallDuration" @blur="updateData" placeholder="最大" style="width: 47%" append="秒" :min="editData.minCallDuration||0"/>
        </div>
      </el-form-item>
      <el-form-item prop="cycleCountLeft" label="对话轮数：">
        <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
          <InputNumberBox v-model:value="editData.minCycleCount" @blur="updateData" placeholder="最小" :max="editData.maxCycleCount || Number.MAX_VALUE" style="width: 47%" append="轮"/>
          <span class="info-title">&nbsp;至&nbsp;</span>
          <InputNumberBox v-model:value="editData.maxCycleCount" @blur="updateData" placeholder="最大" style="width: 47%" append="轮" :min="editData.minCycleCount||0"/>
        </div>
      </el-form-item>
      <el-form-item  v-if="props.isAuto" label="备注：" prop="comment" class="tw-col-span-2">
        <el-input v-model="editData.comment" type="textarea" :rows="2" placeholder="请输入备注，200字以内" clearable/>
      </el-form-item>
    </el-form>
    </el-scrollbar>
  </template>
  
  <div v-if="props.isAuto" class="tw-shrink-0 tw-w-full tw-bg-white tw-flex tw-justify-end tw-pt-1 tw-border-t-[1px]">
    <template v-if="!isEdit">
      <el-button :disabled="autoInfo?.status=='1'" type="primary" @click="goEdit">{{editData.id ? '编辑' : '新增规则'}}</el-button>
    </template>
    <template v-if="isEdit">
      <el-button type="primary" :loading="loading" @click="save">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </template>
  </div>
  <StartAutoDialog
    v-model:visible="startVisible"
    :data="autoInfo"
    :type="0"
    @confirm="updateAutoInfo"
  />
</template>

<script setup lang="ts">
// type
import { TaskManageItem, TaskTypeEnum } from '@/type/task'
import { IntentionClassEnum } from '@/type/common'
import { CallRecordFilterModal, ClueAutoImportSetting, ClueAutoActionInfo } from '@/type/clue'
// 本地方法
import { pickAttrFromObj } from '@/utils/utils'
import { intervalOption } from './constant'
// 本地组件
import InputNumberBox from '@/components/InputNumberBox.vue'
import InputListBox from '@/components/InputListBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import TimePicker from '@/components/TimePicker.vue'
import SelectPageBox from '@/components/SelectPageBox.vue'
import Confirm from '@/components/message-box'
import StartAutoDialog from './StartAutoDialog.vue'
// api
import { aiOutboundTaskModel } from '@/api/ai-report'
import { clueManagerModel } from '@/api/clue'
// 来自插件、依赖
import { ref, onDeactivated, reactive, watch} from 'vue'
import dayjs from 'dayjs'
import to from 'await-to-js'
import type { FormInstance, } from 'element-plus'
import { ElMessage, } from 'element-plus'
import { onBeforeRouteLeave } from 'vue-router'
import { useTaskStore } from '@/store/taskInfo'

const props = withDefaults(defineProps<{
  isAuto?: boolean // 如果是true则从后端拿自动配置的数据，如果是false表示是从通话记录导入，传空数据进editData
}>(), {
  isAuto: false
});
const emits = defineEmits(['update:data',])
const taskStore = useTaskStore()

const handleAutoStatus = () => {
  if (autoInfo.value?.status != '1') {
    startAutoStatus()
  } else {
    stopAutoStatus()
  }
}
const startVisible = ref(false)
const startAutoStatus = () => {
  startVisible.value = true
}

const stopAutoStatus = () => {
  Confirm({
    text: `确定要【关闭】自动导入线索`,
    type: 'warning',
    title: '操作确认',
    confirmText: '确认',
  }).then(async () => {
    const res = await to(clueManagerModel.switchAutoImport({
      id: autoInfo.value?.id,
      autoStop: autoInfo.value?.autoStop,
      autoStopTime: autoInfo.value?.autoStop == '0' ? undefined : autoInfo.value?.autoStopTime,
      status: '0',
    }))
    if (!res[0]) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
    }
    updateAutoInfo()
  }).catch(() => {})
}

class CallRecordFilterOrigin {
  importSource = '0'
  outboundType = '0'
  excludeEmptyLabel = '0'
  taskIdList = undefined
  callOutTimeStart = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  callOutTimeEnd = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  excludeIntentionLabels = undefined
  containIntentionLabels = undefined
  intentionClass = [IntentionClassEnum['A']]
  minCallDuration = undefined
  maxCallDuration = undefined
  minCycleCount = undefined
  maxCycleCount = undefined
  startWorkTimes = props.isAuto ? '07:00' : undefined
  endWorkTimes = props.isAuto ? '20:00' : undefined
  executeTimeGap = undefined
  comment = undefined
}
// 任务
const taskList = ref<TaskManageItem[]|null>([])
const taskTypeList = [
  {name: 'AI外呼', value: '0'}
]

/** 1.2 名单导入 */
/** 变量 */
const editData = reactive<ClueAutoImportSetting>(new CallRecordFilterOrigin())
const editRef = ref<FormInstance  | null>(null)
/** 弹窗操作 */
const rules = {
  callOutTimeStart: [
    { required: true, message: '请选择任务', trigger: 'change' },
  ],
  executeTimeGap: [{ required: true, message: '请选择执行间隔', trigger: 'change' }],
  startWorkTimes: [
    { required: true, message: '请选择工作时间段', trigger: 'change' },
  ],
}

// 初始化数据，监听器等
const loadingTask = ref(false)
const taskNum = ref(0)
const isEdit = ref(!props.isAuto)
const updateTaskList = async (val: string = '',) => {
  if (props.isAuto) return
  loadingTask.value = true
  const data = await aiOutboundTaskModel.findTaskList({
    startTime: dayjs(editData.callOutTimeStart).format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs(editData.callOutTimeEnd).format('YYYY-MM-DD HH:mm:ss'),
    taskType: TaskTypeEnum['AI外呼'],
    taskName: val ? val : undefined,
    startPage: 0,
    pageNum: 100,
  })
  taskNum.value = data?.total || 0
  taskList.value = data?.data as {id: number, taskName: string}[] || []
  loadingTask.value = false
}

const handleTimeChange = () => {
  editData.taskIdList = []
  updateData()
  updateTaskList()
}
const updateData = () => {
  emits('update:data', editData)
}
updateTaskList()

// 执行时段
const startWorkTimeList = ref<string[]>([])
const endWorkTimeList = ref<string[]>([])
const handleTimeList = (sList: string[], eList: string[]) => {
  startWorkTimeList.value = sList
  endWorkTimeList.value = eList
  editData.startWorkTimes = sList?.join(',')
  editData.endWorkTimes = eList?.join(',')
  editRef.value && editRef.value.clearValidate()
}

// 标签
const containIntentionLabels = ref<string>('')
const excludeIntentionLabels = ref<string>('')

const autoInfo = ref<ClueAutoActionInfo | null>(null)
const isEmpty = ref(false)
const loading = ref(false)
const goEdit = () => {
  if (autoInfo.value?.status == '1') {
    return ElMessage.warning('请先关闭自动导入线索')
  }
  isEdit.value = true
}
const updateAutoInfo = async () => {
  isEdit.value = false
  if (props.isAuto) {
    loading.value = true
    const [err, res] = await to(clueManagerModel.getAutoImportInfo())
    isEmpty.value = !res
    loading.value = false
    if (res) {
      autoInfo.value = pickAttrFromObj(res, 
        ['autoStopTime', 'status', 'autoStop']
      )
      autoInfo.value.id = res.id
      taskStore.autoRuleMap[0] =  res.id
    } else {
      autoInfo.value = null
      taskStore.autoRuleMap[0] = undefined
    }
    Object.assign(editData, res || new CallRecordFilterOrigin())
    containIntentionLabels.value = ''
    excludeIntentionLabels.value = ''
    startWorkTimeList.value = editData.startWorkTimes?.split(',') || []
    endWorkTimeList.value = editData.endWorkTimes?.split(',') || []
  } else {
    Object.assign(editData, new CallRecordFilterOrigin())
    containIntentionLabels.value = ''
    excludeIntentionLabels.value = ''
    startWorkTimeList.value = []
    endWorkTimeList.value = []
    updateData()
  }
}
const cancel = () => {
  updateAutoInfo()
}
const save = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const [err, _] = await to(clueManagerModel.saveAutoImportInfo(editData))
      loading.value = false
      if (!err) {
        ElMessage({
          type: 'success',
          message: '操作成功！'
        })
        cancel()
      }
    }
  })
}

updateAutoInfo()
watch(() => props.isAuto, async n => {
  if (n) {
    isEdit.value = !props.isAuto
    updateAutoInfo()
  } else {
    Object.assign(editData, new CallRecordFilterOrigin())
  }
  
})

const clearAll = () => {
  editRef.value = null
  taskList.value = null
}
onDeactivated(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
const validForm = () => {
  return editRef.value?.validate()
}
defineExpose({
  validForm,
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.status-box-mini {
  width: 42px;
}
.el-button {
  height: 28px;
}
</style>