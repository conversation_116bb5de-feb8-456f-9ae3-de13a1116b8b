
import { OpenScopeTypeEnum } from '@/type/speech-craft'
import { ScriptInfo, ScriptBranch, CanvasCorpus, ScriptCorpusItem } from '@/type/speech-craft'
import { scriptCanvasModel, } from '@/api/speech-craft'
import { Node, } from '@antv/x6'
import { EdgeItem, PortItem } from '@/type/common'
import { filterNodeDom, pasteCorpus, X6ColorEnum, deleteNodes } from '@/components/x6/config'
import to from 'await-to-js'
export class  CanvasDataOrigin {
  constructor(scriptId: number, len?: number) {
    this.scriptId = scriptId
    this.weight = len
  }
  id = undefined
  name =  ''
  scriptId
  isMasterCanvas = true
  headCorpusId = null
  isOpenContext = true
  groupOpenScope = undefined
  openScopeType = OpenScopeTypeEnum['全部']
  weight
}

export class CurCanvasOrigin{
  name = ''
  isMasterCanvas: boolean
  knowledgeGroupId: number | undefined
  id = undefined
  weight = undefined
  scriptId: number
  headCorpusId = null
  canvasCorpusDataMap = {}
  canvasBranchDataMap = {}
  constructor(id: number, groupId?: number) {
    this.scriptId = id
    this.isMasterCanvas = groupId ? false : true
    this.knowledgeGroupId = groupId || undefined
  }
}
export const updateCanvasInfoById = async (id: number, scriptId:number, groupId?: number, statisticObj?: Record<number, string>): Promise<{
  nodeList: Node.Metadata[];
  edgeList: EdgeItem[],
  curCanvas: ScriptInfo,
}> => {
  // 初始化返回值
  const res: {
    nodeList: Node.Metadata[];
    edgeList: EdgeItem[],
    curCanvas: ScriptInfo,
  } = {
    nodeList: [],
    edgeList: [],
    curCanvas: new CurCanvasOrigin(scriptId, groupId),
  }

  // 获取画布信息
  const [err, data] = await to(!groupId ? scriptCanvasModel.findOneMasterCanvas({
    canvasId: id
  }) : scriptCanvasModel.findOneDeepCanvas({ canvasId: id })) as [any,ScriptInfo]
  res.curCanvas = data ? {
    ...data,
    canvasBranchDataMap: data?.canvasBranchDataMap || {},
    canvasCorpusDataMap: data?.canvasCorpusDataMap || {},
  } : new CurCanvasOrigin(scriptId, groupId)
  if (!id || !data) { return res}

  if (!!data?.canvasCorpusDataMap && Object.keys(data.canvasCorpusDataMap).length) {
    // @ts-ignore
    await Promise.all(data.canvasCorpusDataMap && Object.values(data.canvasCorpusDataMap).map((item: CanvasCorpus) => {
      const { corpusId, branches,} = item
      const normalPorts : PortItem[] = []
      const specialPorts : PortItem[] = []
      branches && branches.map((branchItem: ScriptBranch, index) => {
        const { id, color } = branchItem
        const branchId = id as number
        if (branchItem.name && ['沉默','未命中'].includes(branchItem.name)) {
          specialPorts.unshift({
            id: corpusId + 'port' + '--' + branchItem.id,
            name: branchItem.name,
            group: "out",
            attrs: { circle: { stroke: '#999',} },
          })
        } else {
          normalPorts.push({
            id: corpusId + 'port' + '--' + branchItem.id,
            name: branchItem.name || '',
            group: "out",
            attrs: {
              circle: color ? {
                stroke: X6ColorEnum[color as keyof typeof X6ColorEnum] || '#3A7CFF'
              } : {}
            },
          })
        }
        if (data.canvasBranchDataMap && data.canvasBranchDataMap[branchId]) {
          const { nextCorpusId = 1, } = data.canvasBranchDataMap[branchId]
          res.edgeList.push({
            id: corpusId + 'branch' + '--' + branchId,
            shape: 'edge',
            source:  {
              cell: 'corpus--' + corpusId,
              port: corpusId + 'port' + '--' + branchId,
            },
            zIndex: 11,
            target: {
              cell: 'corpus--' + nextCorpusId,
              port: nextCorpusId + 'port' + '--in'
            },
            attrs: {
              line: {
                stroke: color ? X6ColorEnum[color  as keyof typeof X6ColorEnum] || '#8f8f8f' : '#8f8f8f',
                strokeWidth: 1,
              },
            },
          })
        }
      })
      const  ports: PortItem[] = [...normalPorts, ...specialPorts]
      res.nodeList.push(filterNodeDom({...item, percent: statisticObj ? statisticObj[corpusId] || undefined : undefined}, res.curCanvas, ports))
    }))
  }
  return res
}

export const updateCanvasInfoByNewCorpus = (
  node: ScriptCorpusItem,
  nodeList: Node.Metadata[] | null,
  edgeList: EdgeItem[] | null,
  curCanvas: ScriptInfo,
) => {
  const {id: corpusId , branchList,} = node
  nodeList = nodeList?.filter(item => item.data.corpusId !== corpusId) || []
  const normalPorts : PortItem[] = []
  const specialPorts : PortItem[] = []
  const infoBranches: {
    [key: string]: string
  } = {}
  branchList && branchList.map((item: ScriptBranch) => {
    if (item.name && ['沉默','未命中'].includes(item.name)) {
      specialPorts.unshift({
        id: corpusId + 'port' + '--' + item.id,
        name: item.name,
        group: "out",
        attrs: { circle: { stroke: '#999',} },
      })
    } else {
      const colorVal = X6ColorEnum[item.color as keyof typeof X6ColorEnum] || '#3A7CFF'
      normalPorts.push({
        id: corpusId + 'port' + '--' + item.id,
        name: item.name || '',
        group: "out",
        attrs: {
          circle: item.color ? {
            stroke: colorVal
          } : {}
        },
      })
      if (item.color && item.id) {
        infoBranches[item.id] = colorVal
      }
    }
  })
  edgeList && edgeList.map((item, index) => {
    if(infoBranches[item.id.split('--')[1]]) {
      edgeList![index].attrs = {
        line: {
          stroke: infoBranches[item.id.split('--')[1]] || '#8f8f8f',
          strokeWidth: 1,
        },
      }
    }
  })
  // 仅将第一个语句组拼接并更新至画布
  const  ports: PortItem[] = [...normalPorts, ...specialPorts]
  nodeList.push(filterNodeDom({
    ...node,
    branches: branchList || [],
  }, curCanvas, ports))
  edgeList = edgeList?.filter(item => !(item.source.cell == ('corpus--' + corpusId) && !ports.find(v => v.id == item.source.port))) || []
  return {
    nodeList,
    edgeList,
  }
}