<template>
  <FormDrawer
    :id="props.id"
    :visible="dialogVisible"
    :content="content"
    :contentDefault="contentDefault"
    :dialogStyle="dialogStyle"
    :readonly="props.readonly"
    class="submodule-detail"
    @opened="handleOpened"
    @close="closeDialog"
    @finish="handleFinish"
    @modify="handleModify"
  >

    <!--DOM延迟渲染-->
    <div v-if="!showForm" v-loading="!showForm" class="tw-min-h-[20vh]"></div>
    <div v-if="showForm">

      <div class="form-block-title">
        线路归属
      </div>
      <el-form-item label="供应商ID：">
        <span style="width: 190px; word-break: break-all;">{{ props.supplier.supplierNumber }}</span>
      </el-form-item>
      <el-form-item label="供应商名称：">
        <span style="width: 190px; word-break: break-all;">{{ props.supplier.supplierName }}</span>
      </el-form-item>
      <el-form-item label="供应商简称：" style="margin-right: 0;">
        <span style="width: 190px; word-break: break-all;">{{ props.supplier.supplierProfile }}</span>
      </el-form-item>

      <!--分割线-->
      <hr class="tw-my-[8px]">

      <div class="form-block-title">
        基本信息
      </div>
      <el-form-item label="线路编号：">
        <el-input v-model.trim="content.lineNumber" placeholder="无" clearable disabled style="width: 240px;" />
      </el-form-item>
      <el-form-item label="线路类型：" prop="lineType">
        <el-select
          v-model.trim="content.lineType"
          placeholder="无"
          style="width: 240px;"
          disabled
        >
          <el-option
            v-for="lineTypeItem in Object.values(supplierLineTypeList)"
            :key="lineTypeItem.value"
            :value="lineTypeItem.value"
            :label="lineTypeItem.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="线路名称：" prop="lineName">
        <el-input
          v-model.trim="content.lineName"
          placeholder="无"
          clearable
          maxlength="50"
          show-word-limit
          style="width: 240px;"
        />
      </el-form-item>
      <el-form-item label="数据传输：" prop="lineName">
        <el-select v-model.trim="content.isForEncryptionPhones" placeholder="请选择数据传输方式" style="width: 240px;">
          <el-option
            v-for="item in dataEncryptionMethodOption"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <br>
      <el-form-item label="启用状态：" prop="enableStatus">
        <el-select v-model.trim="content.enableStatus" placeholder="无" style="width: 240px;">
          <el-option
            v-for="lineStatusItem in Object.values(supplierLineStatusList)"
            :key="lineStatusItem.name"
            :value="lineStatusItem.val"
            :label="lineStatusItem.text"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="挂起状态：" prop="pending">
        <el-input :model-value="content.pending ? '已挂起' : '未挂起'" placeholder="无" disabled style="width: 240px;" />
      </el-form-item>
      <el-form-item label="备注：">
        <el-input
          v-model.trim="content.notes"
          type="textarea"
          placeholder="无"
          clearable
          maxlength="250"
          show-word-limit
          autosize
          resize="none"
          style="width: 600px;"
        />
      </el-form-item>

      <div class="form-block-title">
        通讯要素
      </div>
      <el-form-item label="主叫号码：" prop="masterCallNumber">
        <el-input
          v-model.trim="content.masterCallNumber"
          placeholder="无"
          clearable
          style="width: 240px;"
        />
      </el-form-item>
      <el-form-item label="前缀：">
        <el-input
          v-model.trim="content.prefix"
          placeholder="无"
          clearable
          style="width: 210px;"
        />
        <!--信息提示图标-->
        <div class="tw-flex tw-justify-center tw-items-center tw-ml-[8px]">
          <el-tooltip content="请确认该供应线路是否采用前缀方式进行外呼" placement="bottom" effect="dark">
            <el-icon size="20">
              <SvgIcon name="warning" />
            </el-icon>
          </el-tooltip>
        </div>
      </el-form-item>
      <el-form-item label="注册IP：" prop="registerIp">
        <el-input
          v-model.trim="content.registerIp"
          placeholder="无"
          clearable
          style="width: 240px;"
        />
      </el-form-item>
      <el-form-item label="注册端口：" prop="registerPort">
        <el-input v-model.trim="content.registerPort" placeholder="请输入注册端口" clearable style="width: 240px;" />
      </el-form-item>

      <!--业务限制-->
      <div class="form-block-title">
        业务限制
      </div>
      <el-form-item label="并发上限：" prop="concurrentLimit">
        <el-input
          v-model.trim="content.concurrentLimit"
          placeholder="无"
          clearable
          style="width: 240px;"
        />
      </el-form-item>

      <!--AI外呼显示速率限制，人工直呼不显示速率限制-->
      <el-form-item v-if="content.lineType===supplierLineTypeList.AI_OUTBOUND_CALL.value" label="速率限制：" prop="caps">
        <el-input-number
          v-model.trim="content.caps"
          placeholder="无"
          clearable
          :controls="false"
          :precision="0"
          :min="1"
          :max="1000"
          style="width: 160px;"
        />
        <span class="tw-ml-[1rem]">次/秒</span>
        <!--信息提示图标-->
        <div class="tw-flex tw-justify-center tw-items-center tw-ml-[8px]">
          <el-tooltip content="CAPS限制：每秒钟该线路外呼的次数上限" placement="bottom" effect="dark">
            <el-icon size="20">
              <SvgIcon name="warning" />
            </el-icon>
          </el-tooltip>
        </div>
      </el-form-item>
      <el-form-item v-else>
        <div style="width: 240px;"></div>
      </el-form-item>

      <el-form-item label="外呼类型：" prop="outboundTypes">
        <el-select
          v-model.trim="content.outboundTypes"
          placeholder="无"
          clearable
          multiple
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="2"
          style="width: 240px;"
        >
          <el-option
            v-for="outboundTypeItem in Object.values(supplierLineOutboundTypeList)"
            :key="outboundTypeItem.name"
            :value="outboundTypeItem.val"
            :label="outboundTypeItem.text"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="适用行业：" prop="secondIndustries">
        <el-cascader
          v-model.trim="content.secondIndustries"
          :options="industryOptions"
          :props="industryProps"
          :show-all-levels="false"
          placeholder="无"
          clearable
          style="width: 240px;"
        />
      </el-form-item>
      <el-form-item label="所属运营商：" prop="serviceProviders">
        <el-select
          v-model.trim="content.serviceProviders"
          placeholder="无"
          multiple
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="2"
          style="width: 240px;"
          @change="handleProvidersSelectValChange"
        >
          <el-option
            v-for="operatorItem in Object.values(supplierOperatorList)"
            :key="operatorItem.name"
            :value="operatorItem.val"
            :label="operatorItem.text"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="接入类型：" prop="lineAccessType">
        <el-select
          v-model.trim="content.lineAccessType"
          placeholder="无"
          style="width: 240px;"
        >
          <el-option
            v-for="lineAccessTypeItem in supplierLineAccessTypeList"
            :key="lineAccessTypeItem.name"
            :value="lineAccessTypeItem.val"
            :label="lineAccessTypeItem.text"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="外显号码：" prop="displayCallNumber">
        <el-input
          v-model.trim="content.displayCallNumber"
          placeholder="无"
          clearable
          style="width: 240px;"
        />
      </el-form-item>

      <!--频率限制-->
      <div class="form-block-title">
        线路频率限制
      </div>
      <el-form-item ref="callingRestrictionsRef" :label-width="100" label="线路拨打限制："  prop="callingRestrictions" class="muti-items">
        <div class="tw-w-[240px] tw-flex tw-flex-col tw-items-left">
          <template v-if="content.callingRestrictions?.length">
            <div
              v-for="(callingRestriction, index) in content.callingRestrictions"
              v-if="content.callingRestrictions && content.callingRestrictions.length > 0"
              :key="callingRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(callingRestriction.split('-')[0]) || undefined"
                placeholder="无"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 90px;"
                @change="(v: string) => updateCallingRestriction(v, callingRestriction.split('-')[1], index)"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(callingRestriction.split('-')[1]) || undefined"
                placeholder="无"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 90px;"
                @change="(v: string) => updateCallingRestriction(callingRestriction.split('-')[0], v, index)"
              />
              <span>小时</span>
            </div>
          </template>
          <template v-else>
            无
          </template>
        </div>
      </el-form-item>
      <el-form-item ref="dialingRestrictionsRef" :label-width="100" label="线路拨通限制：" prop="dialingRestrictions" class="muti-items">
        <div class="tw-w-[240px] tw-flex tw-flex-col tw-items-left">
          <template v-if="content.dialingRestrictions?.length">
            <div
              v-for="(dialingRestriction, index) in content.dialingRestrictions"
              v-if="content.dialingRestrictions && content.dialingRestrictions.length > 0"
              :key="dialingRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(dialingRestriction.split('-')[0]) || undefined"
                placeholder="无"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 90px;"
                @change="(v: string) => updateDialingRestriction(v, dialingRestriction.split('-')[1], index)"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(dialingRestriction.split('-')[1]) || undefined"
                placeholder="无"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 90px;"
                @change="(v: string) => updateDialingRestriction(dialingRestriction.split('-')[0], v, index)"
              />
              <span>小时</span>
            </div>
          </template>
          <template v-else>
            无
          </template>
        </div>
      </el-form-item>

      <!--频率限制-->
      <div class="form-block-title">
        平台频率限制
      </div>
      <el-form-item  label="平台拨打限制：" :label-width="100" prop="callingRestrictionsGlobal" class="muti-items">
        <div class="tw-w-[240px] tw-flex tw-flex-col tw-items-left">
          <template v-if="content.callingRestrictionsGlobal?.length">
            <div
              v-for="(callingRestriction, index) in content.callingRestrictionsGlobal"
              v-if="content.callingRestrictionsGlobal && content.callingRestrictionsGlobal.length > 0"
              :key="callingRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(callingRestriction.split('-')[0]) || undefined"
                placeholder="无"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 90px;"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(callingRestriction.split('-')[1]) || undefined"
                placeholder="无"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 90px;"
              />
              <span>小时</span>
            </div>
          </template>
          <template v-else>
            无
          </template>
        </div>
      </el-form-item>
      <el-form-item label="线路拨通限制：" :label-width="100" prop="dialingRestrictionsGlobal" class="muti-items">
        <div class="tw-w-[240px] tw-flex tw-flex-col tw-items-left">
          <template v-if="content.dialingRestrictionsGlobal?.length">
            <div
              v-for="(dialingRestriction, index) in content.dialingRestrictionsGlobal"
              v-if="content.dialingRestrictionsGlobal && content.dialingRestrictionsGlobal.length > 0"
              :key="dialingRestriction + index"
              class="tw-flex tw-justify-between tw-mt-[8px]"
            >
              <el-input-number
                :model-value="parseInt(dialingRestriction.split('-')[0]) || undefined"
                placeholder="无"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 90px;"
              />
              <span>次</span>
              <el-input-number
                :model-value="parseInt(dialingRestriction.split('-')[1]) || undefined"
                placeholder="无"
                :controls="false"
                :precision="0"
                :min="1"
                clearable
                style="width: 90px;"
              />
              <span>小时</span>
            </div>
          </template>
          <template v-else>
            无
          </template>
        </div>
      </el-form-item>

      <div class="form-block-title">
        时间限制
      </div>
      <el-form-item label="选中时间段：">
        <template v-if="timeSlotStrList.length">
          <TagsBox :tagsArr="timeSlotStrList" tagsName="选中时间段" :wrap="true" />
        </template>
        <template v-else>
          无
        </template>
      </el-form-item>

      <div class="form-block-title">
        线路计费
      </div>
      <el-form-item label="计费周期：" prop="billingCycle" required>
        <div class="tw-flex tw-flex-row tw-justify-start tw-items-center">
          <el-select v-model.trim="content.billingCycle" placeholder="无" style="width: 100px;">
            <el-option
              v-for="lineStatusItem in [60,6]"
              :key="lineStatusItem"
              :value="lineStatusItem"
              :label="lineStatusItem"
            />
          </el-select>
          <span class="tw-ml-[10px]">秒</span>
        </div>
      </el-form-item>
      <el-form-item label="线路单价：" prop="unitPrice" required>
        <div class="tw-flex tw-flex-row tw-justify-start tw-items-center">
          <el-input-number
            v-model.trim="content.unitPrice"
            placeholder="无"
            :precision="3"
            :step="0.001"
            :min="0.000"
            :max="10.000"
            controls-position="right"
            clearable
            style="width: 120px;"
          />
          <span class="tw-ml-[10px]">元</span>
        </div>
      </el-form-item>

      <div class="form-block-title">
        线路网关
      </div>
      <el-form-item label="所属网关：" prop="lineGateways">
        <template v-if="!!content.lineGateways?.length">
          <el-tag v-for="gatewayItem in content.lineGateways" class="tw-m-[4px]">
            {{ gatewayItem?.name ?? '' }}
          </el-tag>
        </template>
        <template v-else>
          无
        </template>
      </el-form-item>

      <!--外呼支持范围-->
      <div class="form-block-title">
        外呼支持范围
      </div>
      <!--外呼范围组件-->
      <CitySettingBox
        :taskRestrictData="taskRestrictData"
        :selectedOperatorList="selectedOperatorList"
        :readonly="props.readonly"
        @update:data="handleCityUpdate"
      />

    </div>

  </FormDrawer>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, onMounted, reactive, ref, watch } from 'vue'
import { SupplierInfo, SupplierLineInfo, supplierLineTypeList } from '@/type/supplier'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import { OperatorEnum, RestrictModal } from '@/type/common'
import FormDrawer from '@/components/FormDrawer.vue'
import {
  dataEncryptionMethodOption,
  supplierLineAccessTypeList,
  supplierLineOutboundTypeList,
  supplierLineStatusList,
  supplierOperatorEnum,
  supplierOperatorList,
  supplierOperatorMap,
  supplierOperatorTabNameMap,
} from '@/assets/js/map-supplier'
import { MerchantLineConstituteParams } from '@/type/merchant'
import {
  convertTimeSlotApiToComponent,
  convertTimeSlotComponentToApi,
  convertTimeSlotComponentToDisplay
} from '@/utils/utils'

// 动态引入组件
const TagsBox = defineAsyncComponent(() => import('@/components/TagsBox.vue'))
const CitySettingBox = defineAsyncComponent({
  loader: () => {
    return import('@/components/CitySettingBox.vue')
  }
})
// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  id: number,
  supplier: SupplierInfo,
  content: SupplierLineInfo,
  readonly: boolean,
}>()
const emits = defineEmits([
  // 关闭弹窗
  'close',
  // 通知父组件更新弹窗状态并更新列表
  'update',
  // 通知父组件修改
  'modify'
])

// 全局变量
const globalStore = useGlobalStore()
const { loading, allIndustryList } = storeToRefs(globalStore)

onMounted(async () => {
  await globalStore.getProvinceInfo()

  updateTimeList()
  await updateAllIndustryList()
})

// 默认开始时间
const defaultStartTime = '00:00'
// 默认结束时间
const defaultEndTime = '24:00'

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 弹窗 开始 ----------------------------------------

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)
// 弹窗样式
const dialogStyle = {
  // 表单项标签宽度
  labelWidth: '90px',
  // 抽屉宽度，纯数字按像素px计算，带百分号按百分比计算，其余情况都会转成number计算
  drawerSize: '98%',
  // 表单宽度，表单本体最大宽度是外呼支持范围组件的910，再加上左右两侧内边距各24，总共910+48=958
  formWidth: '958px',
  // 底部按钮容器宽度
  bottomButtonBoxWidth: '958px',
}

/**
 * 每次显示弹窗时更新内容
 */
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val) {
      // 有ID就使用传入的数据，没ID或者ID不正确就用表单默认值
      props.id > -1
        ? Object.assign(content, JSON.parse(JSON.stringify(props.content)))
        : Object.assign(content, JSON.parse(JSON.stringify(contentDefault)))

      // 更新表单数据
      contentDefault.callLineSupplierId = props.supplier.id!
      contentDefault.callLineSupplierNumber = props.supplier.supplierNumber
      content.callLineSupplierId = props.supplier.id!
      content.callLineSupplierNumber = props.supplier.supplierNumber
      content.callingRestrictions = content.callingRestrictions || []
      content.dialingRestrictions = content.dialingRestrictions || []
      // 创建线路时的一些默认值
      if (props.id === -1) {
        content.serviceProviders = [supplierOperatorList.all.val]
      }

      // 将scope属性转换封装，方便组件使用
      scopeToData()

      // 呼叫时间限制
      updateTimeApiToComponent()
    }
  },
)
/**
 * 弹窗已打开
 */
const handleOpened = () => {
  // 显示表单DOM的操作放进宏任务队列
  setTimeout(() => {
    showForm.value = true
  }, 0)
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  // 关闭弹窗
  emits('close')
  // 清除外呼支持范围
  content.scope = {}
  Object.assign(taskRestrictData, new RestrictModalOrigin())
  // 移除表单DOM的操作放进宏任务队列
  setTimeout(() => {
    showForm.value = false
  }, 0)
}

// ---------------------------------------- 弹窗 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 显示表单DOM
const showForm = ref<boolean>(false)

// 表单默认值
const contentDefault: SupplierLineInfo = {
  // 线路ID
  id: -1,

  // 供应商ID
  callLineSupplierId: props.supplier.id!,
  // 供应商编号
  callLineSupplierNumber: props.supplier.supplierNumber,

  // 线路编号
  lineNumber: '',
  // 线路类型
  lineType: '',
  // 线路名称
  lineName: '',
  // 启用状态
  enableStatus: supplierLineStatusList.enabled.val,
  // 备注
  notes: '',

  // 主叫号码
  masterCallNumber: '',
  // 前缀
  prefix: '',
  // 注册IP
  registerIp: '',
  // 注册端口
  registerPort: '',

  // 并发上限
  concurrentLimit: '',
  // 速率限制
  caps: null,
  // 外呼类型
  outboundTypes: [],
  // 适用行业
  primaryIndustry: '',
  // 所属运营商
  serviceProviders: [supplierOperatorList.all.val],
  // 接入类型
  lineAccessType: supplierLineAccessTypeList.direct.val,
  // 外显号码
  displayCallNumber: '',

  // 线路拨打限制
  callingRestrictions: [],
  // 线路拨通限制
  dialingRestrictions: [],
  // 平台拨打限制
  callingRestrictionsGlobal: [],
  // 平台拨通限制
  dialingRestrictionsGlobal: [],

  // 时间限制
  disableTimeSlots: convertTimeSlotComponentToApi([defaultStartTime], [defaultEndTime]),

  // 计费周期
  billingCycle: 60,
  // 线路单价
  unitPrice: 0.000,

  // 所属网关 表单数据
  lineGatewayIds: [],
  // 所属网关 接口数据
  lineGateways: [],

  // 外呼支持范围
  cityCodeGroups: [],
}
// 表单内容
const content = reactive<SupplierLineInfo>({ ...contentDefault })

/**
 * 表单已提交
 */
const handleFinish = () => {
  // 通知父组件更新弹窗状态并更新列表
  emits('update')
}
/**
 * 表单需要修改
 */
const handleModify = () => {
  // 通知父组件修改
  emits('modify')
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 业务限制 开始 ----------------------------------------

// 适用行业，级联选择器，数据内容
const industryOptions = ref<{ label: string, value: string, children: { label: string, value: string }[] }[]>([])
// 适用行业，级联选择器，配置信息
const industryProps = { multiple: true, emitPath: false }

/**
 * 更新全部二级行业列表
 */
const updateAllIndustryList = async () => {
  await globalStore.getAllIndustryList()
  industryOptions.value = allIndustryList.value.map((primaryItem) => {
    return {
      label: primaryItem?.primaryIndustry ?? '',
      value: primaryItem?.primaryIndustry ?? '',
      children: (primaryItem?.secondaryIndustries ?? []).map((secondItem) => {
        return {
          label: secondItem?.name ?? '',
          value: secondItem?.name ?? '',
        }
      })
    }
  })
}

// ---------------------------------------- 业务限制 结束 ----------------------------------------

// ---------------------------------------- 频率限制 开始 ----------------------------------------

// 拨打限制DOM
const callingRestrictionsRef = ref()
// 拨通限制DOM
const dialingRestrictionsRef = ref()

/**
 * 更新拨打限制
 * @param val1 次数
 * @param val2 小时
 * @param index 更新对应的拨打限制，转换为val1-val2
 */
const updateCallingRestriction = (val1: string, val2: string, index: number) => {
  if (content.callingRestrictions) {
    content.callingRestrictions[index] = `${val1 || ''}-${val2 || ''}`
  }
}
/**
 * 更新拨通限制
 * @param val1 次数
 * @param val2 小时
 * @param index 更新对应的拨通限制，转换为val1-val2
 */
const updateDialingRestriction = (val1: string, val2: string, index: number) => {
  if (content.dialingRestrictions) {
    content.dialingRestrictions[index] = `${val1 || ''}-${val2 || ''}`
  }
}

// ---------------------------------------- 频率限制 结束 ----------------------------------------

// ---------------------------------------- 时间限制 开始 ----------------------------------------

// 允许时间段开始位置 组件数据
const startWorkTimeList = ref<string[]>([defaultStartTime])
// 禁止时间段开始位置 组件数据
const endWorkTimeList = ref<string[]>([defaultEndTime])
// 允许时间段的文本 页面展示
const timeSlotStrList = ref<string[]>([])

/**
 * 选中时间段 接口数据转换成组件数据
 */
const updateTimeApiToComponent = () => {
  const { startWorkTimeList, endWorkTimeList } = convertTimeSlotApiToComponent(content.disableTimeSlots ?? [])
  timeSlotStrList.value = convertTimeSlotComponentToDisplay(startWorkTimeList, endWorkTimeList)
}
/**
 * 选中时间段 组件数据转换成接口数据
 */
const updateTimeList = () => {
  const result = convertTimeSlotApiToComponent(content.disableTimeSlots ?? [])
  startWorkTimeList.value = result.startWorkTimeList
  endWorkTimeList.value = result.endWorkTimeList
}

// ---------------------------------------- 时间限制 结束 ----------------------------------------

// ---------------------------------------- 外呼范围 开始 ----------------------------------------

// 外呼范围组件缓存数据 按运营商和省市分类
class RestrictModalOrigin {
  allRestrictProvince = null
  allRestrictCity = null
  ydRestrictProvince = null
  ydRestrictCity = null
  ltRestrictProvince = null
  ltRestrictCity = null
  dxRestrictCity = null
  dxRestrictProvince = null
  virtualRestrictCity = null
  virtualRestrictProvince = null
  unknownRestrictCity = null
  unknownRestrictProvince = null
}

// 外呼范围子组件引用
const restrictComponentRef = ref()
// 已选中范围
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())
// 运营商，备份的旧值，用于监听变化
let providersOld: string[] = []
const selectedOperatorList = ref<('全部' | OperatorEnum)[]>([])
// 更新选中城市、运营商数据
const handleCityUpdate = (data: RestrictModal, operators: ('全部' | OperatorEnum)[]) => {
  Object.assign(taskRestrictData, data)
  selectedOperatorList.value = operators
}
/**
 * 更新外呼支持范围，接口数据转换成组件数据
 */
const scopeToData = () => {
  // 遍历对象里包含的运营商，存放到组件对应数据里
  (content.cityCodeGroups ?? []).forEach((operator: MerchantLineConstituteParams) => {
    const operatorName = <supplierOperatorEnum>operator.serviceProvider

    // 获得当前运营商的一些配置信息，这个是前端自己维护的
    const operatorInfo = supplierOperatorMap.get(operatorName)
    if (operatorInfo) {
      // 将接口数据传给组件，左边是组件，右边是接口
      // 省
      const provinceList = (operator.cityCodes ?? []).map((item: string) => {
        return item.slice(0, 2) + '0000'
      })
      const provinceCodes = new Set<string>(provinceList)
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.province] = (Array.from(provinceCodes) ?? []).join(',')
      // 市
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.city] = (operator.cityCodes ?? []).join(',')
    }
    // console.log('taskRestrictData', taskRestrictData)
  })
}
/**
 * 更新当前已添加的运营商，组件数据转换成接口数据
 */
const updateBlockOperatorList = (val: string[] = []) => {
  // 更新所属运营商
  let tempServiceProviders: string[] = []
  // 遍历组件里已添加的标签卡，得到已添加运营商列表
  val?.forEach((item: string) => {
    const info = supplierOperatorTabNameMap.get(item)
    if (info) {
      tempServiceProviders.push(info.val)
    }
  })
  // 更新表单数据（接口数据）
  if (!tempServiceProviders.length) {
    // 子组件初始化时，是空值
    // 但是表单校验不允许所属运营商为空，所以临时使用一个值
    // 之后会被子组件替换成正确的
    content.serviceProviders = [supplierOperatorList.all.val]
  } else {
    content.serviceProviders = tempServiceProviders
  }

  // 备份运营商
  providersOld = [...(content.serviceProviders ?? [])]
}
/**
 * 处理运营商内容变化
 */
const handleProvidersSelectValChange = () => {
  // 如果被清空，则使用默认值，因为表单不允许空数组
  if (!(content?.serviceProviders?.length)) {
    const list = restrictComponentRef?.value?.blockOperatorList
    if (list && list.length) {
      updateBlockOperatorList(list)
    } else {
      content.serviceProviders = [supplierOperatorList.all.val]
    }
    // console.log('如果被清空', content.serviceProviders)
    return
  }

  // 和旧值比较，多出的是新增，少掉的是删除
  // console.log('和旧值比较', content.serviceProviders)

  // 两者全集，但是这是没去重的列表
  const allList = [...(content.serviceProviders ?? []), ...providersOld]
  // 新增的
  const addSet = new Set(allList)
  providersOld.forEach((item) => {
    addSet.delete(item)
  })
  // 删除的
  const deleteSet = new Set(allList)
  content.serviceProviders?.forEach((item) => {
    deleteSet.delete(item)
  })

  // console.log('addSet', addSet, 'deleteSet', deleteSet)
  // console.log('和旧值比较 更新后', content.serviceProviders)

  // 更新外呼支持范围
  // 添加运营商
  addSet.forEach((provider: string) => {
    const providerName = <supplierOperatorEnum>provider

    // 根据运营商名称找到该运营商的前端配置信息
    const operatorInfo = supplierOperatorMap.get(providerName)
    if (operatorInfo && restrictComponentRef.value) {
      // 添加标签卡
      restrictComponentRef.value.addTabs(<OperatorEnum>operatorInfo.tabName)
      // console.log('添加标签卡', operatorInfo.tabName)

      // 切换到新标签卡
      // restrictComponentRef.value.activeOperator = operatorInfo.tabName
      // 全选当前供应商的所有省市
      // restrictComponentRef.value.selectAllData()
    }
  })

  // 删除运营商
  deleteSet.forEach((provider: string) => {
    const providerName = <supplierOperatorEnum>provider

    // 根据运营商名称找到该运营商的前端配置信息
    const operatorInfo = supplierOperatorMap.get(providerName)
    if (operatorInfo && restrictComponentRef.value) {
      // 清空当前运营商的所有省市
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.province] = null
      taskRestrictData[<keyof typeof taskRestrictData>operatorInfo.city] = null
      // 移除标签卡
      restrictComponentRef.value.removeTab(<OperatorEnum>operatorInfo.tabName)
      // console.log('移除标签卡', operatorInfo.tabName)
    }
  })

  // 备份运营商
  providersOld = [...(content.serviceProviders ?? [])]
}

// ---------------------------------------- 外呼范围 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
:deep(.el-form--inline .muti-items) {
  vertical-align: baseline;
}
</style>
