<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    class="script-check-dialog"
    align-center
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <template #header>
      <div class="form-dialog-header">
        话术{{ props.status ? '通过' : '驳回' }}
      </div>
    </template>

    <el-scrollbar class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          label-width="80px"
          label-position="top"
        >
          <el-form-item label="审核意见：" :required="!props.status">
            <el-input
              v-model.trim="opinion"
              type="textarea" :autosize="{minRows: 2, maxRows: 6}"
              placeholder="300字以内（驳回时意见必填）"
              maxlength="300"
              show-word-limit
              resize="none"
            />
            <div class="hint">
              <el-icon :size="16" color="#E54B17">
                <WarningFilled />
              </el-icon>
              <div class="hint-text">
                话术通过审核后，会先进入预发布状态，等待音频全部同步完成后状态会变为生效中。
              </div>
            </div>
            <div v-if="smsTriggerChanged" class="hint">
              <el-icon :size="16" color="#E54B17">
                <WarningFilled />
              </el-icon>
              <div class="hint-text tw-font-[600]">
                检测到短信触发点变动，如任务模版或任务上已设置触发短信，请进行对应的修改，否则将影响后续的呼叫
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { CloseBold, Select, WarningFilled } from '@element-plus/icons-vue'
import { scriptTableModel } from '@/api/speech-craft'
import { SpeechCraftInfoItem, SpeechCraftStatusEnum } from '@/type/speech-craft'
import { compareUnorderedLists } from '@/utils/utils'

const props = defineProps<{
  visible: boolean
  status: boolean
  scriptId: number
}>()
const emits = defineEmits([
  'update:visible',
  'submit'
])

// 组件本身用自己的响应式对象
// props是单向的，和父组件传来的隔离开
const dialogVisible = ref<boolean>(props.visible)

// 话术触发点是否变化
const smsTriggerChanged = ref(false)



// 监听父组件props控制显示隐藏状态，同步更新自身的显示隐藏状态
watch(
  () => props.visible,
  async (val) => {
    dialogVisible.value = val
    // 当存在话术ID，且审核通过时提示
    if (val && props.scriptId && props.scriptId > 0) {
      const currentScript = await scriptTableModel.findOneScriptById({
        id: props.scriptId
      })
      if (!currentScript) return (smsTriggerChanged.value = false)
      const scriptList = await scriptTableModel.getScriptTables({
        name: currentScript?.scriptName,
        status: SpeechCraftStatusEnum['生效中']
      }) || []
      if (!scriptList || scriptList.length === 0) return (smsTriggerChanged.value = false)
      smsTriggerChanged.value = !compareUnorderedLists(scriptList[0]?.smsTriggerNames, currentScript?.smsTriggerNames)
    } else {
      smsTriggerChanged.value = false
    }
  }
)

// 审核意见
const opinion = ref<string>('')

/**
 * 弹窗关闭回调
 */
const handleClose = () => {
  // console.log('弹窗关闭回调')
  // 隐藏弹窗
  dialogVisible.value = false

  // 重置输入的内容
  opinion.value = ''

  // 通知父组件
  emits('update:visible', false)
}

/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  // console.log('点击确定按钮')
  let str = opinion.value.trim()

  // 如果是驳回，需要检测意见不为空
  if (!props.status) {
    if (!str) {
      ElMessage({
        message: '话术驳回时，审核意见是必填项',
        type: 'warning'
      })
      return
    }
  }

  // 关闭弹窗
  handleClose()
  // 通知父组件发送给接口
  emits('submit', {
    approved: props.status,
    opinion: str
  })
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  // console.log('点击取消按钮')
  // 关闭弹窗
  handleClose()
}
</script>

<style scoped lang="postcss">
/* 提示容器 */
.hint {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
}
/* 提示文本 */
.hint-text {
  margin-left: 4px;
  color: var(--primary-red-color);
}
</style>
