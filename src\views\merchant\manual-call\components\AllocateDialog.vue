<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="600px"
    align-center
    :close-on-click-modal="false"
    :z-index="15"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ props.type === 0 ? '线索分配' : '线索重新分配' }}</div>
    </template>
    <el-form
      :model="editData"
      class="tw-px-[12px] tw-pt-[16px]"
      :rules="rules"
      label-width="80px"
      ref="editRef"
    >
      <div class="info-title tw-text-left tw-mb-[14px] tw-pl-[16px]">已选择
        <span class="tw-text-[var(--el-color-primary)]">【{{editData.clueIds?.length||0}}条】</span>
        数据。</div>
      <el-form-item label="选择坐席：" prop="callSeatIds">
        <div class="tw-flex tw-w-full tw-flex-col">
          <el-select
            v-model="editData.callSeatIds"
            placeholder="请选择坐席"
            multiple
            collapse-tags
            :max-collapse-tags="2"
          >
            <el-option v-for="item in callSeatList" :key="item.id" :label="`${item.account||''}（${item.name||''}）`" :value="item.id" />
          </el-select>
          <div v-if="!!globalStore.getCopyCallSeatAccounts" class="tw-w-full tw-flex tw-justify-between tw-items-center">
            <div class="tw-flex tw-leading-[22px]">
              <span class="tw-shrink-0">上次选择：</span>
              <span class="tw-text-justify tw-break-all ">{{ globalStore.getCopyCallSeatAccounts }}</span>
            </div>
            <el-button link type="primary" @click="copyCallSeat" class="tw-grow-0">复用</el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="自动回收：" prop="autoRecovery">
        <el-switch
          v-model="editData.autoRecovery"
          class="tw-flex-none"
          inline-prompt
          active-text="开启"
          inactive-text="关闭"
          :active-value="1"
          :inactive-value="0"
          @change="handleAutoRecovered"
        />
      </el-form-item>
      <el-form-item v-if="editData.autoRecoveryTime" label="回收时间：" prop="autoRecoveryTime">
        <a-date-picker
          v-model:value="editData.autoRecoveryTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          show-time
          allowClear
          placeholder="选择回收时间"
          :locale="antdLocaleConfig"
          :presets="nextFollowUpTimePresets"
          @change="handleRecoveryTimeChange"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,watch, reactive, onDeactivated} from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { clueManagerModel } from '@/api/clue'
import { ElMessage, } from 'element-plus'
import { SeatMember } from '@/type/seat'
import dayjs from 'dayjs'
import { useGlobalStore } from '@/store/globalInfo'
import { storeToRefs } from 'pinia'
import { useTaskStore } from '@/store/taskInfo'
import type { FormInstance, } from 'element-plus'
import { antdLocaleConfig } from '@/assets/js/constant'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'
import { trace } from '@/utils/trace'

const emits = defineEmits(['update:visible', 'confirm'])
const userInfo  = useUserStore()
const globalStore = useGlobalStore()
const  {loading} = storeToRefs(globalStore)
const props = defineProps<{
  visible: boolean,
  list: number[],
  type: number, // 0: 分配，1：重新分配
}>();
const editData = reactive<{
    clueIds: number[],
    callSeatIds?: number[],
    autoRecoveryTime?: string ,
    autoRecovery: number
}>({
  clueIds: props.list || [],
  autoRecoveryTime: undefined,
  callSeatIds: [],
  autoRecovery: 0
})
// 下次跟进时间快捷选择列表
const nextFollowUpTimePresets = ref<{ label: string, value: dayjs.Dayjs }[]>([
  { label: '今日', value: dayjs().endOf('d') },
  { label: '明日', value: dayjs().add(1, 'd').endOf('d') },
  { label: '后日', value: dayjs().add(2, 'd').endOf('d') },
  { label: '一周', value: dayjs().add(1, 'w').endOf('d') },
])
const callSeatList = ref<SeatMember[] | null>([])
const dialogVisible = ref(props.visible)
const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const rules = {
  callSeatIds: [
    { required: true, message: '请选择坐席', trigger: 'change' },
  ],
  autoRecoveryTime: [
    { required: true, message: '请选择自动回收时间', trigger: 'change' },
  ],
}

const copyCallSeat = () => {
  editData.callSeatIds = globalStore.copyCallSeatIds || []
}

const confirm = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      if (!editData.autoRecovery) {
        editData.autoRecoveryTime = undefined
      }
      loading.value = true
      globalStore.copyCallSeatIds = editData.callSeatIds || []
      const params = {
        clueIds: editData.clueIds || [],
        callSeatIds: editData.callSeatIds || [],
        autoRecoveryTime: editData.autoRecoveryTime ? dayjs(editData.autoRecoveryTime).format('YYYY-MM-DD HH:mm:ss') : undefined
      }
      const [err] = await to(props.type === 0 ?  clueManagerModel.distributeClues2CallSeat(params) : clueManagerModel.redistributeClues2CallSeat(params))
      trace({
        page: `线索管理-${props.type === 0 ? '分配线索' : '重新分配线索'}`,
        params: params
      })
      if (!err) {
        emits('confirm')
        cancel()
        ElMessage({
          type: 'success',
          message: '线索分配成功！'
        })
      }
      loading.value = false
    }
  })
}
const handleAutoRecovered = (val: number) => {
  if (val) {
    editData.autoRecoveryTime = dayjs().endOf('D').format('YYYY-MM-DD HH:mm:ss')
  } else {
    editData.autoRecoveryTime = undefined
  }
}
const handleRecoveryTimeChange = (val: Date, valStr: string) => {
  if (val && dayjs(val).isBefore(dayjs())) {
    editData.autoRecoveryTime = dayjs().endOf('d').format('YYYY-MM-DD HH:mm:ss')
    return ElMessage.warning('请选择合适的自动回收时间!')
  } else {
    editData.autoRecoveryTime = valStr
  }
}
const taskStore = useTaskStore()
watch(() => props.visible, async n => {
  dialogVisible.value = n
  if (props.visible) {
    
    callSeatList.value = await taskStore.getCallTeamSeatListOptions()
    editData.clueIds = props.list || []
    editData.autoRecoveryTime = undefined
    editData.callSeatIds = []
    editData.autoRecovery = 0
  }
})

onBeforeRouteLeave(() => {
  editRef.value = null
  callSeatList.value = null
})
onDeactivated(() => {
  editRef.value = null
  callSeatList.value = null
})
</script>

<style lang="postcss" type="text/postcss" scoped>
</style>
