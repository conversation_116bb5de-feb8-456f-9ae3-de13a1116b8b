// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    AdvanceRuleDetailsDialog: typeof import('./src/components/record/AdvanceRuleDetailsDialog.vue')['default']
    AiConfirm: typeof import('./src/components/message-box/AiConfirm.vue')['default']
    AntvX6GraphBox: typeof import('./src/components/x6/AntvX6GraphBox.vue')['default']
    AntvX6GraphStatic: typeof import('./src/components/x6/AntvX6GraphStatic.vue')['default']
    AudioMode: typeof import('./src/components/AudioMode.vue')['default']
    BarCategoryChart: typeof import('./src/components/charts/BarCategoryChart.vue')['default']
    BarChart: typeof import('./src/components/charts/BarChart.vue')['default']
    BaseCorpusDrawer: typeof import('./src/components/corpus/BaseCorpusDrawer.vue')['default']
    BranchEditDialog: typeof import('./src/components/corpus/components/BranchEditDialog.vue')['default']
    CallLog: typeof import('./src/components/task/CallLog.vue')['default']
    CallQueue: typeof import('./src/components/task/CallQueue.vue')['default']
    CallRecordDetailsDrawer: typeof import('./src/components/record/CallRecordDetailsDrawer.vue')['default']
    CallRecordDialogBox: typeof import('./src/components/record/CallRecordDialogBox.vue')['default']
    CallRecordDialogBoxNew: typeof import('./src/components/record/CallRecordDialogBoxNew.vue')['default']
    CallRecordInfoBox: typeof import('./src/components/record/CallRecordInfoBox.vue')['default']
    CallTaskDialog: typeof import('./src/components/task/CallTaskDialog.vue')['default']
    CanvasCorpusDrawer: typeof import('./src/components/corpus/CanvasCorpusDrawer.vue')['default']
    CardListRadioBox: typeof import('./src/components/CardListRadioBox.vue')['default']
    CheckInTaskDialog: typeof import('./src/components/seat/CheckInTaskDialog.vue')['default']
    CitySettingBox: typeof import('./src/components/CitySettingBox.vue')['default']
    CollectInterfaceDialog: typeof import('./src/components/CollectInterfaceDialog.vue')['default']
    ColumnSetting: typeof import('./src/components/ColumnSetting.vue')['default']
    ColumnsSelection: typeof import('./src/components/ColumnsSelection.vue')['default']
    CopyBranchDialog: typeof import('./src/components/corpus/components/CopyBranchDialog.vue')['default']
    CorpusBatchEditDialog: typeof import('./src/components/corpus/CorpusBatchEditDialog.vue')['default']
    CorpusContentSettingDrawer: typeof import('./src/components/corpus/CorpusContentSettingDrawer.vue')['default']
    CorpusOtherSettings: typeof import('./src/components/corpus/CorpusOtherSettings.vue')['default']
    CustomVariableFormItem: typeof import('./src/components/sms/CustomVariableFormItem.vue')['default']
    DatePicker: typeof import('./src/components/DatePicker.vue')['default']
    EditTaskDialog: typeof import('./src/components/task/EditTaskDialog.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTimeSelect: typeof import('element-plus/es')['ElTimeSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FormDialog: typeof import('./src/components/FormDialog.vue')['default']
    FormDrawer: typeof import('./src/components/FormDrawer.vue')['default']
    FormRecord: typeof import('./src/components/clue/FormRecord.vue')['default']
    FormRecordDialog: typeof import('./src/components/clue/FormRecordDialog.vue')['default']
    FrequencyRestrictionDialog: typeof import('./src/components/FrequencyRestrictionDialog.vue')['default']
    FunnelChart: typeof import('./src/components/charts/FunnelChart.vue')['default']
    GeoChart: typeof import('./src/components/charts/GeoChart.vue')['default']
    GoodNumberTestDialog: typeof import('./src/components/GoodNumberTestDialog.vue')['default']
    HeaderBox: typeof import('./src/components/HeaderBox.vue')['default']
    ImportOrEditBlacklistPhoneDialog: typeof import('./src/components/blacklist/ImportOrEditBlacklistPhoneDialog.vue')['default']
    Index: typeof import('./src/components/editable-content/Index.vue')['default']
    InputListBox: typeof import('./src/components/InputListBox.vue')['default']
    InputNumberBox: typeof import('./src/components/InputNumberBox.vue')['default']
    InputPhonesBox: typeof import('./src/components/InputPhonesBox.vue')['default']
    InputSelectBox: typeof import('./src/components/InputSelectBox.vue')['default']
    InspectionWorkOrderBox: typeof import('./src/components/record/InspectionWorkOrderBox.vue')['default']
    LineChart: typeof import('./src/components/charts/LineChart.vue')['default']
    LineChart2: typeof import('./src/components/charts/LineChart2.vue')['default']
    LoadCityNameOrAreaCodeDialog: typeof import('./src/components/LoadCityNameOrAreaCodeDialog.vue')['default']
    LoadCorpusDialog: typeof import('./src/components/corpus/components/LoadCorpusDialog.vue')['default']
    MiniAudio: typeof import('./src/components/MiniAudio.vue')['default']
    MixLineBarChart: typeof import('./src/components/charts/MixLineBarChart.vue')['default']
    MultCorpusConditionBox: typeof import('./src/components/corpus/MultCorpusConditionBox.vue')['default']
    MultRuleBox: typeof import('./src/components/script-rules/MultRuleBox.vue')['default']
    MutiTimeRangePickerBox: typeof import('./src/components/MutiTimeRangePickerBox.vue')['default']
    MutiTimeRangeSelectBox: typeof import('./src/components/MutiTimeRangeSelectBox.vue')['default']
    NameList: typeof import('./src/components/task/NameList.vue')['default']
    OperationAndCitySelectBox: typeof import('./src/components/OperationAndCitySelectBox.vue')['default']
    PaginationBox: typeof import('./src/components/PaginationBox.vue')['default']
    PasswordEditDialog: typeof import('./src/components/PasswordEditDialog.vue')['default']
    PieChart: typeof import('./src/components/charts/PieChart.vue')['default']
    PlaintextDialog: typeof import('./src/components/record/PlaintextDialog.vue')['default']
    ProcessDialog: typeof import('./src/components/ProcessDialog.vue')['default']
    RadioButtonBox: typeof import('./src/components/RadioButtonBox.vue')['default']
    RelatedListDialog: typeof import('./src/components/RelatedListDialog.vue')['default']
    RestSeatDialog: typeof import('./src/components/task/RestSeatDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchPhoneDialog: typeof import('./src/components/blacklist/SearchPhoneDialog.vue')['default']
    SearchPhoneList: typeof import('./src/components/blacklist/SearchPhoneList.vue')['default']
    SeatSettingDialog: typeof import('./src/components/seat/SeatSettingDialog.vue')['default']
    SeatWorkbenchHeader: typeof import('./src/components/seat/SeatWorkbenchHeader.vue')['default']
    SelectBox: typeof import('./src/components/SelectBox.vue')['default']
    SelectPageBox: typeof import('./src/components/SelectPageBox.vue')['default']
    SendSmsDialog: typeof import('./src/components/sms/SendSmsDialog.vue')['default']
    SingleConditionItem: typeof import('./src/components/corpus/components/SingleConditionItem.vue')['default']
    SingleRuleItem: typeof import('./src/components/script-rules/SingleRuleItem.vue')['default']
    SingleUploadDialog: typeof import('./src/components/task/SingleUploadDialog.vue')['default']
    SmsPreviewDialog: typeof import('./src/components/sms/SmsPreviewDialog.vue')['default']
    SupplyCall: typeof import('./src/components/task/SupplyCall.vue')['default']
    SupplyLineTable: typeof import('./src/components/SupplyLineTable.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon.vue')['default']
    TabsBox: typeof import('./src/components/TabsBox.vue')['default']
    TagsBox: typeof import('./src/components/TagsBox.vue')['default']
    TaskCacheBox: typeof import('./src/components/task/TaskCacheBox.vue')['default']
    TaskChartBox: typeof import('./src/components/task/TaskChartBox.vue')['default']
    TaskDataCard: typeof import('./src/components/task/TaskDataCard.vue')['default']
    TaskDetails: typeof import('./src/components/task/TaskDetails.vue')['default']
    TaskSmsDialog: typeof import('./src/components/task/TaskSmsDialog.vue')['default']
    TaskTable: typeof import('./src/components/task/TaskTable.vue')['default']
    TaskVariableDialog: typeof import('./src/components/task/TaskVariableDialog.vue')['default']
    TimeLineBox: typeof import('./src/components/TimeLineBox.vue')['default']
    TimePicker: typeof import('./src/components/TimePicker.vue')['default']
    TimePickerBox: typeof import('./src/components/TimePickerBox.vue')['default']
    TimeRangePickerDialog: typeof import('./src/components/TimeRangePickerDialog.vue')['default']
    TooltipBox: typeof import('./src/components/TooltipBox.vue')['default']
    TransferBox: typeof import('./src/components/TransferBox.vue')['default']
  }
  export interface ComponentCustomProperties {
    vInfiniteScroll: typeof import('element-plus/es')['ElInfiniteScroll']
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
