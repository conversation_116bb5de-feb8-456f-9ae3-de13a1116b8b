<template>
  <HeaderBox title="线路报表" :can-refresh="true" @refresh="refreshAction"/>
  <div class="module-container tw-min-w-[1080px]">
    <div class="search-box">
      <div class="tw-grid tw-grid-cols-4 tw-gap-3">
        <div class="item">
          <el-input
            v-model.trim="searchForm.tenantLineName"
            placeholder="线路名称"
            clearable
            @keyup.enter="search()"
          />
        </div>
        <div class="item-btn">
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
      <div class="tw-py-[8px]">
        <div class="tw-float-left">
          <el-radio-group v-model="searchForm.recentMin"  @change="search()">
            <el-radio-button v-for="item in timeRangeList" :label="item.value" :key="item.value">{{ item.label }}</el-radio-button>
          </el-radio-group>
        </div>
        <div class="tw-float-right tw-flex tw-align-stretch tw-h-[32px]">
          <ColumnSetting
            :totalList="totalCols"
            :defaultList="defaultCols"
            :disabledList="disabledCols"
            :name="'merchant-tenant-line-monitor'"
          />
          <el-button :loading="exportLoading" type="primary" @click="handleExport">
            <el-icon :size="16" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>
            <span>导出表格</span>
          </el-button>
        </div>
      </div>
    </div>
    
    <TenantMonitorTable
      :table-data="tableData || []"
      :recentMin="searchForm.recentMin"
      @update:table="search()"
      :line-number="lineCurrentInfo?.tenantLineNumber"
      showPagination
      :loading="loading"
    >
      <template v-slot:operate="{row}">
        <el-button type="primary" link @click="goDetail(row)">详情</el-button>
      </template>
    </TenantMonitorTable>
  </div>
  <el-drawer
    v-model="drawerVisible"
    :size="getDrawerWidth()"
    :with-header="true"
  >
    <template v-slot:header>
      <div class="tw-bg-white tw-h-[50px]  tw-flex tw-items-end tw-pb-[12px]">
        <div class="tw-text-[16px] tw-font-semibold tw-text-left tw-text-[var(--primary-black-color-600)]">
          {{ lineCurrentInfo?.tenantLineName || '' }}
        </div>
        <div class="tw-text-[12px] tw-text-left tw-text-[var(--primary-black-color-600)] tw-ml-[4px]">
          的数据分布
        </div>
        <el-tooltip content="刷新" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5 tw-pb-1" @click="needRefresh=true">
            <SvgIcon name="reset" color="inherit"/>
          </el-icon>
        </el-tooltip>
      </div>
    </template>
    <el-scrollbar wrap-class="tw-p-[12px] tw-bg-[#f2f3f5]">
      <LineChartBox :lineInfo="lineCurrentInfo" :type="1" v-model:refresh="needRefresh"></LineChartBox>
    </el-scrollbar>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref, onUnmounted  } from 'vue'
import LineChartBox from '@/views/operator/line-manager/components/LineChartBox.vue'
import { TenantMonitorInfoItem, MonitorInfoItem, } from '@/type/line'
import { lineMerchantModel } from '@/api/line'
import TenantMonitorTable from '@/views/operator/line-manager/components/TenantMonitorTable.vue'
import { onBeforeRouteLeave } from 'vue-router'
import { timeRangeList, getColumnSettingByName } from '@/views/operator/line-manager/components/constant'
import to from 'await-to-js'
import ColumnSetting from '@/components/ColumnSetting.vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { exportExcel } from '@/utils/export'
import dayjs from 'dayjs'
import { getMerchantLineTypeText } from '@/utils/line'

// 获取路由传参
const userInfo = useUserStore()
const accountType = userInfo.accountType
const loading = ref(false)
const {totalCols, defaultCols, disabledCols} = getColumnSettingByName('merchant-tenant-line-monitor')

// 列表及分页
const tableData = ref<TenantMonitorInfoItem[] | null>([])

// 表格选中线路信息
const lineCurrentInfo = ref<MonitorInfoItem | null>(null)
// 选中、取消选中某条数据，更新底部图表数据
const goDetail = (row: TenantMonitorInfoItem) => {
  if (row) {
    lineCurrentInfo.value = row
    needRefresh.value = true
    drawerVisible.value = true
  }
}

/** 单挑线路统计详情抽屉 */ 
const drawerVisible = ref(false)
const getDrawerWidth = () => {
  return window.innerWidth > 1400 ? '75%' : '1080px'
}

// 搜索
const searchForm = reactive<{
  tenantLineName?: string
  recentMin?: number
}>({tenantLineName: '', recentMin: 5})

// 搜索函数
const search = async () => {
  loading.value = true
  const [_, res] = await to(lineMerchantModel.getMonitorList({
    ...searchForm,
    recentMin: searchForm.recentMin ? searchForm.recentMin : undefined
  })) as [any, TenantMonitorInfoItem[]]
  tableData.value =  (res || []).sort((a, b) => b.currentlyCallNum - a.currentlyCallNum)
  loading.value = false
}

// 导出数据
const exportLoading = ref(false)
const handleExport = () => {
  if (!tableData.value?.length) {
    return ElMessage({
      type: 'warning',
      message: '导出数据为空'
    })
  }
  exportLoading.value = true
  const selectCols = userInfo.colInfo['merchant-tenant-line-monitor'] || []
  const csvData = tableData.value.map(item => {
    return {
      '线路名称': item.tenantLineName,
      '线路类型': item.tenantLineType ? getMerchantLineTypeText(item.tenantLineType) : '',
      '生效状态': item.status === 'ENABLE' ? '启用' : '停用',
      '并发（占用/上限)': item.concurrency,
      '呼叫数': item.currentlyCallNum,
      '接通数': item.currentlyConnectedNum,
      '接通率': item.currentlyConnectedRate,
      '无声通话占比': item.currentlySilenceCall,
      '沉默挂机': item.currentlySilenceHangup,
      '小助理': item.currentlyAssistant,
      '秒挂（1s）': item.currentlyOneSecondConnected,
      '秒挂（2s）': item.currentlyTwoSecondConnected,
      '转人工占比': item.currentlyTransCallSeatNum,
      'A类占比': item.currentlyClassANum,
      'B类占比': item.currentlyClassBNum,
      'C类占比': item.currentlyClassCNum,
      'D类占比': item.currentlyClassDNum,
    }
  })
  const labelName = timeRangeList.find(item => item.value === searchForm.recentMin)?.label || ''
  exportExcel(csvData, `线路报表-${labelName}-${dayjs().format('YYYY-MM-DD')}.xlsx`);
  exportLoading.value = false
}

const needRefresh = ref(true) // 抽屉中的数据统计模块刷新
const refreshAction = () => {
  search()
}

search()

onBeforeRouteLeave(() => {
  tableData.value = null
})
onUnmounted(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.search-box {
  padding-bottom: 0;
}
</style>
