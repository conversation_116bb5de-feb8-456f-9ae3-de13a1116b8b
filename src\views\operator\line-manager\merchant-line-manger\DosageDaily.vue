<template>
  <HeaderBox :title="titleList" />
  <div class="dosage-details-container">
    <div class="tw-w-full tw-bg-white tw-mb-[16px] tw-p-[16px]">
      <div class="tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-gap-3 tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model="searchForm.tenantLineName"
            placeholder="商户线路名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.tenantLineNumber"
            placeholder="商户线路编号"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.account"
            placeholder="所属账号"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.tenantName"
            placeholder="商户名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.tenantNumber"
            placeholder="商户编号"
            clearable
            @keyup.enter="search"
          />
        </div>
      </div>
      <div class="tw-flex tw-justify-end tw-mt-[12px]">
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
    <div class="tw-mb-[8px] tw-flex tw-justify-end tw-items-center">
      <button @click="exportDetails2Excel" class="common-btn"><el-icon size="--el-font-size-base" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>批量导出</button>
      <button @click="exportCurrent2Excel" class="common-btn tw-ml-[8px]"><el-icon size="--el-font-size-base" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>导出数据</button>
    </div>
    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      ref="tableRef"
      :row-key="(row:TenantDosageDailyItem) => row.tenantLineName! + row.tenantLineNumber! + '1'"
      :row-class-name="getTotalRowClass"
      :header-cell-style="tableHeaderStyle"
      @sort-change="handleSortChange"
      stripe
      border
    >
      <el-table-column property="tenantLineName" label="商户线路名称" align="left" min-width="240" :formatter="formatterEmptyData" fixed="left" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.tenantLineName || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="tenantLineType" label="线路类型" align="center" width="80" :formatter="formatterEmptyData" show-overflow-tooltip fixed="left">
        <template #default="{ row }">
          {{ getMerchantLineTypeText(row?.tenantLineType) }}
        </template>
      </el-table-column>
      <el-table-column property="tenantLineNumber" label="线路编号" align="left" min-width="160" :formatter="formatterEmptyData" fixed="left" show-overflow-tooltip></el-table-column>
      <el-table-column property="calculateNumOfSixty" label="计量数（60s)" sortable="custom" align="left" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.calculateNumOfSixty) }}
        </template>
      </el-table-column>
      <el-table-column property="calculateNumOfSix" label="计量数（6s)" sortable="custom" align="left" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.calculateNumOfSix) }}
        </template>
      </el-table-column>
      <el-table-column property="totalCallNum" label="呼叫总量" align="left" sortable="custom" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.totalCallNum) }}
        </template>
      </el-table-column>
      <el-table-column property="totalConnectNum" label="接通总量" align="left" sortable="custom" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.totalConnectNum) }}
        </template>
      </el-table-column>
      <el-table-column property="connectRate" label="接通率" align="left" sortable="custom" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="totalConnectedDuration" label="接通时长" sortable="custom" align="left" min-width="160" :formatter="(row:any, column:any, data:number)=>formatMsDuration(data/1000)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="averageConnectedDuration" label="平均接通时长" sortable="custom" align="left" min-width="120" :formatter="(row:any, column:any, data:number)=>formatMsDuration(data/1000)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="silenceCallProportion" label="无声通话占比" align="left" sortable="custom" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="silenceHangup" sortable="custom" label="沉默挂机" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="assistant" sortable="custom" label="小助理" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="promptSound" sortable="custom" label="运营商提示音" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="oneSecondConnectedProportion" label="秒挂（1s）占比" sortable="custom" align="left" min-width="200" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="twoSecondConnectedProportion" label="秒挂（2s）占比" sortable="custom" align="left" min-width="200" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="callFailedProportion" label="送呼失败" sortable="custom" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="transCallSeatNum" sortable="custom" label="转人工占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classANum" sortable="custom" label="A类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classBNum" sortable="custom" label="B类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classCNum" sortable="custom" label="C类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classDNum" sortable="custom" label="D类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="account" label="所属账号" align="left" min-width="160" :formatter="formatterEmptyData" ></el-table-column>
      <el-table-column property="tenantName" label="商户名称" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="tenantNumber" label="商户编号" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" min-width="60" align="right" fixed="right">
        <template #default="{ row }">
          <el-button v-if="row.tenantLineName && !['合计', '总计'].includes(row.tenantLineName)" type="primary" link @click="goDetail(row)">详情</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, watch, markRaw  } from 'vue'
import { ElMessage, } from 'element-plus'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import PaginationBox from '@/components/PaginationBox.vue'
import { exportExcel, generateExcelBook } from '@/utils/export'
import dayjs from 'dayjs'
import { tableHeaderStyle } from '@/assets/js/constant'
import { formatMsDuration, formatterEmptyData, formatNumber, handleTableSort } from '@/utils/utils'
import { TenantDosageDailyParam, TenantDosageDailyItem, TenantDosageDetailsItem } from '@/type/line'
import { lineMerchantModel } from '@/api/line'
import router from '@/router'
import { useRoute } from 'vue-router'
import HeaderBox from '@/components/HeaderBox.vue'
import { getMerchantLineTypeText, getSupplierLineTypeText } from '@/utils/line'
import JSZip from 'jszip'
import FileSaver from 'file-saver';

const route = useRoute();
const globalStore = useGlobalStore()
const { loading, primaryIndustryList } = storeToRefs(globalStore)
const currentPage = ref(1)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<TenantDosageDailyItem[]>([])
const total = ref(0)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const lineChartId = ref(-1)
const titleList = computed(() => [
  {title: '商户用量统计', name: 'MerchantLineManger',},
  {title: route.query.date as string},
])
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
const getTotalRowClass = ({ row }: {row: TenantDosageDailyItem}) => {
  if ([row.tenantLineName, row.tenantLineNumber].includes('合计') || [row.tenantLineName, row.tenantLineNumber].includes('总计') ) {
    return 'tw-font-[700] tw-bg-[#f7f8fa]'
  } else {
    return undefined
  }
}

const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}

class searchOrigin {
  tenantLineName = undefined
  tenantLineNumber = undefined
  tenantName = undefined
  tenantNumber = undefined
  account = undefined
  date = route.query.date as string
}
const tableRef = ref(null)
const searchForm = reactive<TenantDosageDailyParam>(new searchOrigin())

const clearSearchForm = () => {
  Object.assign(searchForm, new searchOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}
const search = async () => {
  loading.value = true
  tableData.value = await lineMerchantModel.getDailyDosage(searchForm) as TenantDosageDailyItem[] || []
  total.value = tableData.value?.length || 0
  loading.value = false
}

const exportCurrent2Excel = () => {
  if (!tableData.value || tableData.value.length < 1) {
    return ElMessage({
      type: 'warning', message: '暂无数据可导出'
    })
  }
  const data = tableData.value.map(item => {
    const { calculateNumOfSixty, calculateNumOfSix, totalCallNum, totalConnectNum,
      connectRate, totalConnectedDuration, averageConnectedDuration, silenceCallProportion,
      oneSecondConnectedProportion, twoSecondConnectedProportion, callFailedProportion,
      tenantLineName, tenantLineNumber, tenantName, tenantNumber, tenantLineType,
      silenceHangup, assistant,  promptSound, transCallSeatNum, classANum, classBNum, classCNum, classDNum,
    } = item
    return {
      '线路名称': tenantLineName,
      '线路编号': tenantLineNumber,
      '线路类型': tenantLineType ? getMerchantLineTypeText(tenantLineType) : '',
      '供应商名称': tenantName,
      '供应商编号': tenantNumber,
      '计量数（60s）': calculateNumOfSixty,
      '计量数（6s）': calculateNumOfSix,
      '呼叫总量': totalCallNum,
      '接通总量': totalConnectNum,
      '接通率': connectRate,
      '接通时长': totalConnectedDuration,
      '平均接通时长': averageConnectedDuration,
      '无声通话占比': silenceCallProportion,
      '沉默挂机': silenceHangup || 0,
      '小助理': assistant || 0,
      '运营商提示音': promptSound || 0,
      '秒挂（1s）占比': oneSecondConnectedProportion,
      '秒挂（2s）占比': twoSecondConnectedProportion,
      '送呼失败': callFailedProportion,
      '转人工占比': transCallSeatNum || 0,
      'A类占比': classANum || 0,
      'B类占比': classBNum || 0,
      'C类占比': classCNum || 0,
      'D类占比': classDNum || 0,
    }
  })
  exportExcel(data, `商户线路 -【${dayjs(route.query.date as string).format('YYYY-MM-DD')}】用量统计详情.xlsx`);
}

// 导出列表每项的详情数据并压缩zip
const exportDetails2Excel = async () => {
  if (!tableData.value || tableData.value.length < 1) {
    return ElMessage({
      type: 'warning', message: '暂无数据可导出'
    })
  }
  const zip = new JSZip();
  await Promise.all(tableData.value.map(async item => {
    if (item.tenantLineName !== '总计') {
      const arr = (await lineMerchantModel.getDailyDosageDetails({
        tenantLineNumber: item.tenantLineNumber,
        date: route.query.date as string,
      }) || []) as TenantDosageDetailsItem[]
      const data = arr.map(subItem => {
        const { calculateNumOfSix, calculateNumOfSixty, totalCallNum, totalConnectNum, connectRate, totalConnectedDuration,
          averageConnectedDuration, silenceCallProportion, oneSecondConnectedProportion, twoSecondConnectedProportion,
          callFailedProportion, masterCallNumber, supplierName, supplierNumber, supplyLineName, supplyLineNumber, supplyLineType,
          silenceHangup, assistant,  promptSound, classANum, classBNum, classCNum, classDNum, transCallSeatNum,
        } = subItem
        return {
          '线路名称': supplyLineName,
          '线路编号': supplyLineNumber,
          '线路类型': supplyLineType ? getSupplierLineTypeText(supplyLineType) : '',
          '主叫号码': masterCallNumber,
          '计量数（60s）': calculateNumOfSixty,
          '计量数（6s）': calculateNumOfSix,
          '呼叫总量': totalCallNum,
          '接通总量': totalConnectNum,
          '接通率': connectRate,
          '接通时长': totalConnectedDuration,
          '平均接通时长': averageConnectedDuration,
          '无声通话占比': silenceCallProportion,
          '沉默挂机': silenceHangup || 0,
          '小助理': assistant || 0,
          '运营商提示音': promptSound || 0,
          '秒挂（1s）占比': oneSecondConnectedProportion,
          '秒挂（2s）占比': twoSecondConnectedProportion,
          '送呼失败': callFailedProportion,
          '转人工占比': transCallSeatNum || 0,
          'A类占比': classANum || 0,
          'B类占比': classBNum || 0,
          'C类占比': classCNum || 0,
          'D类占比': classDNum || 0,
          '供应商名称': supplierName,
          '供应商编号': supplierNumber,
        }
      })
      const res = generateExcelBook(data)
      zip.file(`【${dayjs(route.query.date as string).format('YYYY-MM-DD')}】商户线路【${item.tenantLineName}】用量统计详情.xlsx`, res)
    }
  }))
  zip.generateAsync({ type: 'blob' })
  .then((content) => {
    FileSaver.saveAs(content, `【${dayjs(route.query.date as string).format('YYYY-MM-DD')}】商户线路用量统计详情.zip`);
  })
  .catch((error) => {
    ElMessage.error('压缩文件生成失败:' + error);
  });
}

const goDetail = (row: any) => {
  router.push({
    name: 'MerchantLineDosageDailyDetails',
    query: {
      tenantLineNumber: row.tenantLineNumber,
      tenantLineName: row.tenantLineName,
      date: route.query.date,
    }
  })
}
watch(() => route.query.date, () => {
  searchForm.date = route.query.date as string
  searchForm.date && search()
}, { deep: true })
search()
</script>

<style scoped lang="postcss" type="text/postcss">
.dosage-details-container {
  width: 100%;
  overflow-x: hidden;
  min-width: 1080px;
  box-sizing: border-box;
  padding: 16px;
  display: flex;
  flex-direction: column;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
}
</style>