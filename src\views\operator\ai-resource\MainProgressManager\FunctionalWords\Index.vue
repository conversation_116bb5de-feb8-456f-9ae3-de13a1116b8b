<template>
    <div class="functional-container">
      <el-tabs v-model="active">
        <el-tab-pane label="重复语料" :name="CorpusTypeEnum['重复语料']"></el-tab-pane>
        <el-tab-pane label="沉默语料" :name="CorpusTypeEnum['沉默语料']"></el-tab-pane>
        <el-tab-pane label="最高优先" :name="CorpusTypeEnum['最高优先']"></el-tab-pane>
        <el-tab-pane label="打断垫句" :name="CorpusTypeEnum['打断垫句']"></el-tab-pane>
        <el-tab-pane label="续播垫句" :name="CorpusTypeEnum['续播垫句']"></el-tab-pane>
        <el-tab-pane label="承接语料" :name="CorpusTypeEnum['承接语料']"></el-tab-pane>
      </el-tabs>
      <keep-alive>
        <Repeat v-if="active===CorpusTypeEnum['重复语料']"/>
        <Silence v-else-if="active===CorpusTypeEnum['沉默语料']"/>
        <HighestPriority v-else-if="active===CorpusTypeEnum['最高优先']"/>
        <PreCorpus v-else :corpusType="active"/>
      </keep-alive>
    </div>
</template>

<script lang="ts" setup>
import { ref, onActivated, defineAsyncComponent } from 'vue'
import { useScriptStore } from '@/store/script'
import { CorpusTypeEnum } from '@/type/speech-craft';

const Repeat = defineAsyncComponent({loader: () => import('./Repeat.vue')})
const Silence = defineAsyncComponent({loader: () => import('./Silence.vue')})
const HighestPriority = defineAsyncComponent({loader: () => import('./HighestPriority.vue')})

const PreCorpus = defineAsyncComponent({loader: () => import('./PreCorpus.vue')})

const active = ref(CorpusTypeEnum['重复语料'])
// 执行区
onActivated(async () => {
  const scriptStore = useScriptStore()
  await scriptStore.getIntentionTagOptions(true)
  await scriptStore.getEventOptions(true)
});

</script>

<style scoped lang="postcss" type="text/postcss">
.functional-container {
  width: 100%;
  height: calc(100vh - 180px);
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  background-color: #fff;
  .el-tabs :deep(.el-tabs__item){
    padding: 10px auto;
    height: 40px;
    line-height: 40px;
    width: 100px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    border: none;
    background-color: #e5e7eb;
  }
  :deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
    line-height: 40px;
    font-size: 16px;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
}
</style>