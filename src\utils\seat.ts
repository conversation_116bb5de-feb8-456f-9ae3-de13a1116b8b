import { seatStatusList } from '@/type/seat'

/**
 * 获取坐席工作状态的展示文本
 * @param status 工作状态
 */
export const getSeatStatusText = (status: string = '') => {
  if (seatStatusList.hasOwnProperty(status)) {
    return seatStatusList[<keyof typeof seatStatusList>status].name
  }
  return '-'
}

/**
 * 获取坐席工作状态的标签颜色样式
 * @param status 工作状态
 */
export const getSeatStatusColorClassName = (status: string = '') => {
  if (seatStatusList.hasOwnProperty(status)) {
    return seatStatusList[<keyof typeof seatStatusList>status].colorClassName
  }
  return '-'
}
