<template>
  <el-drawer
    v-model="visible"
    :size="getDrawerWidth()"
    :with-header="true"
    class="dialog-form"
    @close="close"
  >
    <template v-slot:header>
      <div class="tw-bg-white tw-h-[50px]  tw-flex tw-items-end tw-pb-[12px]">
        <div class="tw-font-semibold tw-text-[15px] tw-text-left tw-text-[var(--primary-black-color-600)]">
          【{{props.currentAccount?.account || ''}}】的巡检详情
        </div>
      </div>
    </template>

    <div class="tw-bg-white tw-w-full tw-flex tw-flex-col tw-h-full">
      <div class="search-box tw-px-[12px]">
        <div class="tw-grid tw-grid-cols-4 tw-gap-[12px]">
          <div class="item">
            <el-input
              v-model.trim="searchForm.recordId"
              maxlength="100"
              clearable
              placeholder="请输入被叫号码"
              @keyup.enter="search()"
            />
          </div>
          <div class="item-btn">
            <el-button type="primary" @click="search()" link>
              <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
              <span>查询</span>
            </el-button>
          </div>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        ref="tableRef"
        row-key="id"
        class="tw-grow tw-shrink"
        :header-cell-style="tableHeaderStyle"
        stripe
      >
        <el-table-column label="号码" property="recordId" fixed="left" align="left" width="160" :formatter="formatterEmptyData">
          <template #default="{ row }">
            <span class="tw-cursor-pointer" @click="copyText(row.recordId)">{{ filterPhone(row.recordId, 8, 6) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="通话类型" align="center" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            {{ findValueInEnum(row.callType, RecordTypeEnum) || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="播放录音" align="center" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="findValueInStatus(row.playRecord) " class="status-box-mini tw-mx-auto" :class="!!row.playRecord ? 'green-status' : 'orange-status'">
              {{ findValueInStatus(row.playRecord) || '-' }}
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column property="inspectionTime" label="巡检时间" align="center" min-width="160" :formatter="formatterEmptyData"></el-table-column>
        <template #empty>
          <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
        </template>
      </el-table>
      <PaginationBox
        :pageSize="searchForm.limit"
        :pageSizeList="pageSizeList"
        :currentPage="searchForm.page"
        :total="total"
        @search="search()"
        @update="updateList"
      >
      </PaginationBox>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, } from 'vue'
import to from 'await-to-js'
import dayjs from 'dayjs'
import { tableHeaderStyle } from '@/assets/js/constant'
import { formatterEmptyData, copyText, findValueInEnum, filterPhone, findValueInStatus } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import { ElMessage } from 'element-plus'
import { RecordTypeEnum } from '@/type/task'
import { InspectionRecordDetailItem, } from '@/type/Inspection'
import { inspectionModel, } from '@/api/Inspection'


const props = defineProps<{
  currentAccount : {
    account: string,
    timeStart: string,
    timeEnd: string
  } | null,
  visible: boolean
}>()
const emits = defineEmits([
  'update:visible',
])

const searchForm = reactive({
  recordId: '',
  page: 1,
  limit: 20,
})

/** 分页 开始 */
const pageSizeList = [20, 50, 100, 200]
const total = ref(0)
const updateList = (p: number, s: number) => {
  searchForm.page = p
  searchForm.limit = s
  search()
}

const loading = ref(false)
const visible = ref(false)
const tableData = ref<InspectionRecordDetailItem[] | null>(null)

const search = async () =>{
  if (!props.currentAccount) return ElMessage.warning('获取账号失败')
  if (loading.value) return ElMessage.warning('请勿重复请求')
  loading.value = true
  const [_, data] = await to(inspectionModel.findInspectionRecordDetails({
    ...searchForm,
    ...props.currentAccount,
  }))
  total.value = data?.total || 0
  tableData.value = data?.data as InspectionRecordDetailItem[] || []
  loading.value = false
}

// 批量导出明文数据
const exportVisible = ref(false)
const getDrawerWidth = () => { return window.innerWidth > 1400 ? '60%' : '800px' }


const close = () => {
  visible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, () => {
  visible.value = props.visible
  exportVisible.value = false
  if (props.visible) {
    search()
  }
})
</script>

<style scoped lang="postcss" type="text/postcss">
</style>