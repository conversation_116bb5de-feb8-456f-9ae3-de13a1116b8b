<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">变量信息</div>
    </template>
    <div class="info-title tw-text-left tw-my-1">请注意，导入号码时，仅变量类型为“自定义”的变量，需导入变量值</div>
    <!--  短信变量 -->
    <el-table
      :data="props.data.variableSms"
      :header-cell-style="tableHeaderStyle"
      class="tw-flex-grow"
      row-key="variableName"
    >
      <el-table-column align="left" prop="variableName" label="变量名称" min-width="120" show-overflow-tooltip />
      <el-table-column align="left" prop="variableComment" label="变量说明" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          {{ variableMap?.get(row.variableName)?.variableComment || findValueInEnum(row.variableName, SystemVariableEnum) ||'-' }}
        </template>
      </el-table-column>
      <el-table-column align="left" prop="variableType" label="变量类型" width="120">
        <template #default="{ row }">
          {{ row.variableType ? '自定义' : '系统内置' }}
        </template>
      </el-table-column>
      <el-table-column align="left" prop="columnType" label="字段类型" width="120">
        <template #default="{ row }">
          {{ findValueInEnum(variableMap?.get(row.variableName)?.columnType, SmsVariableColumnTypeEnum) || '-' }}
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive,} from 'vue'
import { TaskManageItem } from '@/type/task'
import to from 'await-to-js'
import { tableHeaderStyle } from '@/assets/js/constant'
import {
  SmsVariableItem,
  SmsVariableColumnTypeEnum
} from '@/type/merchant'
import { findValueInEnum } from '@/utils/utils'
import { useUserStore } from '@/store/user'
import { merchantSmsVariableModel } from '@/api/merchant'
import { SystemVariableEnum } from '@/type/merchant'


const emits = defineEmits(['update:visible', 'confirm'])
const loading = ref(false)
const props = defineProps<{
  data: TaskManageItem,
  visible: boolean
}>();

const variableMap = ref<Map<string, SmsVariableItem> | null>(null)
const userInfo = useUserStore()

const initData = async () => {
  loading.value = true
  const [err, res] = await to(merchantSmsVariableModel.getSmsVariable({groupId: userInfo.groupId}))
  variableMap.value = new Map([])
  res?.map(item => {
    item.variableName && variableMap.value?.set(item.variableName, item)
  })
  loading.value = false
}

const dialogVisible = ref(props.visible)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, n => {
  dialogVisible.value = props.visible
  n && initData()
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.el-table {
  font-size: 13px;
  .caret-wrapper {
    display: none;
  }
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
