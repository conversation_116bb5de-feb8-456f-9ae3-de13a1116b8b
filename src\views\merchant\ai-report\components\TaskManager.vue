<template>
  <!--模块主体-->
  <div v-if="listType==='card'" class="ai-task-container tw-flex tw-flex-grow">
    <!--左半部分，话术列表区块-->
    <div class="tw-w-[258px] tw-flex-shrink-0 tw-flex tw-flex-col tw-items-start tw-pt-[24px] tw-pb-[8px] tw-bg-white">
      <!--搜索部分-->
      <div class="tw-flex tw-items-center tw-mx-[16px] tw-box-border tw-w-[226px]">
        <el-button v-if="createPermission" type="primary" @click="createTask()" class="tw-flex-grow">创建{{ taskTypeStr }}</el-button>
        <CardListRadioBox v-model:active="listType" class="tw-ml-[8px]" @update:active="handleListTypeChange"/>
      </div>
      <el-input
        v-model="task"
        class="tw-mt-[16px] tw-mx-[16px]"
        style="width:226px"
        @blur="search()"
        @keyup.enter="search()"
        clearable
        :placeholder="searchType"
      >
        <template #prepend>
          <el-select v-model="searchType" class="tw-w-[100px]">
            <el-option v-for="item in taskInputList" :key="item" :label="item" :value="item" />
          </el-select>
        </template>
      </el-input>
      <div class="tw-my-[8px] tw-mx-[16px] tw-w-[226px] tw-flex">
        <TimePickerBox
          v-model:start="searchForm.startTime"
          v-model:end="searchForm.endTime"
          :format="dateFormat"
          type="daterange"
          :maxRange="60*60*24*7*1000"
          :clearable="false"
          separator="-"
          @change="search()"
        />
      </div>
        <!--任务状态切换菜单-->
      <TabsBox
        class="tw-w-full tw-text-[13px] tw-mb-[12px]"
        :active="(searchForm.callStatus as string)"
        :tabList="Object.values(TaskStatusEnum)"
        @update:active="search"
      ></TabsBox>
      <!--任务列表-->
      <el-scrollbar v-loading="loadingLeft" wrap-class="tw-pr-[10px] tw-ml-[16px] tw-mr-[6px] tw-w-full" class="tw-flex-grow tw-w-full">
        <div
          v-for="item in taskFilterList"
          :key="item.id"
          class="task-item"
          :style="filterTaskStyle(item)"
          @click="handleTaskItem(item)"
        >
          <div class="task-item-left">
            <div class="title" >
              <span class="name tw-line-clamp-2">
                <el-icon v-if="!!item.ifLock" :size="13" >
                  <SvgIcon name="lock" color="inherit"/>
                </el-icon>
                {{ item.taskName || '-' }}
              </span>
              <div class="tw-grow-0 tw-shrink-0 tw-flex tw-items-center">
                <el-icon v-if="item.smsTemplateAbnormal == 1 && item.callStatus === '未完成'" class="tw-ml-[4px]" color="#E64B17" :size="20"><SvgIcon name="sms-fill"/></el-icon>
                <span class="status" :class="filterStatusClass(item.callStatus as TaskStatusEnum)">{{ item.callStatus }}</span>
              </div>
            </div>
            <div class="task-content">
              <span class="label">执行话术：</span>
              <span class="value">{{ item.speechCraftName || '-' }}</span>
            </div>
            <div class="task-content">
              <span class="label">创建时间：</span>
              <span class="value">{{ dayjs(item.createTime).format('MM-DD HH:mm:ss') || '-' }}</span>
            </div>
          </div>

          <div v-if="permissions.includes(routeMap[taskTypeStr].permissions['编辑任务'])" class="task-item-right">
            <el-tooltip content="屏蔽规则" placement="right" :show-after="500">
              <span class="tw-cursor-pointer" @click.stop="setTaskBlock(item)"><el-icon :size="16" ><SvgIcon name="forbid" color="#fff"></SvgIcon></el-icon></span>
            </el-tooltip>
            <template v-if="item.callStatus!=='进行中'">
              <el-tooltip content="编辑" placement="right" :show-after="500">
                <span class="tw-cursor-pointer" @click.stop="editTask(item)"><el-icon :size="16" color="#fff"><SvgIcon name="edit" color="#fff"></SvgIcon></el-icon></span>
              </el-tooltip>
              <el-tooltip :content="!!item.ifLock ? '解锁任务' : '锁定任务'" placement="right" :show-after="500">
                <span class="tw-cursor-pointer" @click.stop="lockTask(item)">
                  <el-icon v-if="!!item.ifLock" :size="16" color="#fff"><SvgIcon name="unlock" color="#fff"></SvgIcon></el-icon>
                  <el-icon v-else :size="16" color="#fff"><SvgIcon name="lock" color="#fff"></SvgIcon></el-icon>
                </span>
              </el-tooltip>
              <el-tooltip v-if="item.callStatus=='待执行'" content="删除" placement="right" :show-after="500">
                <span class="tw-cursor-pointer" @click.stop="delTask(item)"><el-icon :size="16" color="#fff"><SvgIcon name="delete" color="#fff"></SvgIcon></el-icon></span>
              </el-tooltip>
            </template>
          </div>
        </div>
        <!--空数据提示-->
        <el-empty v-if="!taskFilterList || taskFilterList.length < 1" class="tw-m-auto"/>
      </el-scrollbar>
      <PaginationBox
        class="tw-border-t-[1px] tw-border-t-[#ebeef5] tw-mt-1"
        :pageSize="pageSize"
        :currentPage="currentPage"
        :total="total"
        :mini="true"
        @search="search"
        @update="updatePage"
      >
      </PaginationBox>
    </div>

    <!--右边部分，主体部分-->
    <TaskDetails
      :currentTask="currentTask"
      v-model:needRefresh="needRefresh"
      @update-task="search"
    />
  </div>
  <TaskManagerByList
    v-if="listType==='list'"
    v-model:type="listType"
    v-model:needRefresh="needRefresh"
    :taskType="props.taskType"
    @create-task="createTask()"
    @show-block="setTaskBlock"
  ></TaskManagerByList>
  
  <SelectTemplateDialog
    v-model:visible="selectTemplateVisible"
    :taskType="props.taskType"
    @close="selectTemplateVisible=false"
    @confirm="confirmTemplate"
  ></SelectTemplateDialog>
  <EditTaskDialog
    v-model:visible="editVisible"
    :dataRow="editTaskItem"
    :type="type"
    @update:visible="closeEditVisible"
    @confirm="confirmEditTask"
  ></EditTaskDialog>
  <CallTaskDialog
    v-model:visible="callVisible"
    :currentTask="editTaskItem"
    @confirm="confirmCallVisible"
  ></CallTaskDialog>
  <el-dialog
    v-model="blockVisible"
    width="960px"
    align-center
    @close="closeEditVisible"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{taskRestrictReadonly ? '查看' : '编辑'}}屏蔽地区</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
    >
    <CitySettingBox
      :taskRestrictData="taskRestrictData"
      :selectedOperatorList="selectedOperatorList"
      :readonly="taskRestrictReadonly"
      loadByCity
      @update:data="handleCityUpdate"
    />
    </el-scrollbar>
    <template v-if="editPermission" #footer>
      <span class="dialog-footer">
        <el-button @click="closeEditVisible" :icon="CloseBold">取消</el-button>
        <el-button v-if="!taskRestrictReadonly" type="primary" @click="confirmBlock" :icon="Select">确认</el-button>
        <el-button v-else type="primary" @click="taskRestrictReadonly=false">
          <el-icon :size="16" class="tw-mr-1"><SvgIcon name="edit" color="#fff"></SvgIcon></el-icon>
          编辑
        </el-button>
      </span>
    </template>
  </el-dialog>
  <TaskCacheBox/>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive, watch, computed, defineAsyncComponent, } from 'vue'
import { ElMessage } from 'element-plus'
import { CloseBold, Select, Message } from '@element-plus/icons-vue'
import { TaskManageModel, TaskManageOrigin, TaskManageItem, TaskStatusEnum, TaskTypeEnum, TemplateBaseItem, TaskManageRates, } from '@/type/task'
import dayjs from 'dayjs'
import { enum2Options, findValueInEnum } from '@/utils/utils'
import CallTaskDialog from '@/components/task/CallTaskDialog.vue'
import { RestrictModal, OperatorEnum, RestrictModalOrigin } from '@/type/common'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import SelectTemplateDialog from './SelectTemplateDialog.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import to from 'await-to-js'
import { aiOutboundTaskModel } from '@/api/ai-report'
import Confirm from '@/components/message-box'
import PaginationBox from '@/components/PaginationBox.vue'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import TabsBox from '@/components/TabsBox.vue'
import CardListRadioBox from '@/components/CardListRadioBox.vue'
import EditTaskDialog from '@/components/task/EditTaskDialog.vue'
import TaskDetails from '@/components/task/TaskDetails.vue'
import SvgIcon from '@/components/SvgIcon.vue';
import { trace, traceApi } from '@/utils/trace';
import TaskCacheBox from '@/components/task/TaskCacheBox.vue'

const props = defineProps<{
  taskType: TaskTypeEnum
}>();
const taskTypeStr = computed(() => {
  return props.taskType === TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务'
})
// 组件性能消耗较大，动态引入
const CitySettingBox = defineAsyncComponent({ loader:() => { return import('@/components/CitySettingBox.vue')}})
const TaskManagerByList = defineAsyncComponent({ loader:() => { return import('./TaskManagerByList.vue')}})


// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap[taskTypeStr.value].id]
const editPermission =  computed(() => permissions.includes(routeMap[taskTypeStr.value].permissions['编辑任务']))
const startPermission =  computed(() => permissions.includes(routeMap[taskTypeStr.value].permissions['启停任务']))
const createPermission =  computed(() => permissions.includes(routeMap[taskTypeStr.value].permissions['创建任务']))

// 正在加载
const globalStore = useGlobalStore()
const loadingLeft = ref(false)
const taskStore = useTaskStore()
const callStatusList = enum2Options(TaskStatusEnum)
// 左侧任务搜索栏
class SearchFormOrigin {
  taskName = ''
  callStatus = TaskStatusEnum['待执行']
  taskType = props.taskType
  speechCraftName = ''
  startTime = dayjs().format(dateFormat)
  endTime = dayjs().format(dateFormat)
}

const searchForm = reactive<TaskManageModel>(new SearchFormOrigin())
const listType = ref<'card'|'list'>('card') // 默认卡片形式
const task = ref('')
const taskInputList = ['任务名称', '话术名称',]
const searchType = ref(taskInputList[0])
// const dateArr = ref([dayjs().subtract(60, 'day'), dayjs(),])
const dateFormat = 'YYYY-MM-DD'
const taskList = ref<TaskManageItem[]>([])
const type = ref<'task' | 'template'>('task')
const currentTask = reactive<TaskManageItem>(new TaskManageOrigin(props.taskType)) // 卡片形式：当前右侧任务；列表形式：当前抽屉任务
const editTaskItem = reactive<TaskManageItem>(new TaskManageOrigin(props.taskType))// 当前编辑/开启/停止的任务

const handleListTypeChange = (val: 'card'|'list') => {
  if (val == 'card') {
    handleTaskItem(taskFilterList.value[0])
  } else {
    Object.assign(currentTask, new TaskManageOrigin(props.taskType))
  }
}

const pageSizeList = [50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search(searchForm.callStatus)
}

const taskFilterList = ref<TaskManageItem[]>([])
const needRefresh = ref<boolean>(false) // 某些操作后，需要更新当前任务数据
const search = async (status?: TaskStatusEnum) => {
  if (listType.value !== 'card') {
    needRefresh.value = true
    return
  }
  loadingLeft.value = true
  switch(searchType.value) {
    case taskInputList[0]: {
      searchForm.speechCraftName = ''
      searchForm.taskName = task.value
      break
    };
    case taskInputList[1]: {
      searchForm.speechCraftName = task.value
      searchForm.taskName = ''
      break
    };
  }
  if (status) {
    searchForm.callStatus = status
  }
  const params: TaskManageModel  = {
    startTime: dayjs(searchForm.startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs(searchForm.endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
    callStatus: searchForm.callStatus === TaskStatusEnum['全部'] ? undefined : searchForm.callStatus,
    taskType: props.taskType??undefined,
    taskName: searchForm.taskName?.trim(),
    speechCraftName: searchForm.speechCraftName?.trim(),
  }
    
  const [err, res] = await to(aiOutboundTaskModel.search(params)) as [any, TaskManageItem[]]
  taskList.value = res?.sort((a, b) => {
    return dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1
  }) || []
  total.value = res?.length || 0
  taskFilterList.value = res?.slice(pageSize.value * (currentPage.value - 1), pageSize.value * currentPage.value)
  // 对于卡片形式，需要找出上一次显示、修改的任务，或者默认当前列表第一个任务；对于列表则需要清空当前任务
  const currentItem = taskFilterList.value.find(item => item.id === currentTask.id)
  if (currentItem && !!currentItem.id) {
    Object.assign(currentTask, currentItem)
  } else {
    Object.assign(currentTask, taskFilterList.value[0] || new TaskManageOrigin(props.taskType))
  }
  if (currentTask && currentTask.id! > 0) {
    needRefresh.value = true
  }
  loadingLeft.value = false
}

// 卡片：左侧任务表格区点击；列表：点击某条任务详情，展示抽屉
const handleTaskItem = async (item: TaskManageItem) => {
  needRefresh.value =true
  Object.assign(currentTask, item)
}

const handleNextTask = (flag: number) => {
  const index = taskList.value.findIndex(v => v.id === currentTask.id)
  if (taskList.value[index + flag]) {
    Object.assign(currentTask, taskList.value[index + flag])
    needRefresh.value =true
  } else {
    ElMessage.warning('已至任务列表最' + (flag > 0 ? '底部' : '顶部'))
  }
}

/** 任务操作开始 */
/** 编辑任务 */
const editVisible = ref(false)
const selectTemplateVisible = ref(false)
// 创建任务-选择任务模板弹窗SelectTaskDialog
const createTask = () => {
  Object.assign(editTaskItem, new TaskManageOrigin(props.taskType))
  selectTemplateVisible.value = true
}
// 创建任务-选择任务模板弹窗SelectTaskDialog-选择任务模板/新增任务模板/自定义创建
const confirmTemplate = (item: TemplateBaseItem | null, addTask: boolean) => {
  Object.assign(editTaskItem, item ? JSON.parse(JSON.stringify(item)) : new TaskManageOrigin(props.taskType))
  editTaskItem.id = undefined
  type.value = addTask ? 'task' : 'template'
  editVisible.value = true
}
// 编辑、屏蔽弹窗的任务数据
const editTask = (item?: TaskManageItem) => {
  Object.assign(editTaskItem, item ? JSON.parse(JSON.stringify(item)) : new TaskManageOrigin(props.taskType))
  type.value = 'task'
  editVisible.value = true
}
/** 屏蔽城市弹窗 */ 
const blockVisible = ref(false)
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())
const selectedOperatorList = ref<('全部' | OperatorEnum)[]>([])
const handleCityUpdate = (data: RestrictModal , operators: ('全部' | OperatorEnum)[]) => {
  Object.assign(taskRestrictData, data)
  selectedOperatorList.value = operators
}
const taskRestrictReadonly = ref(false)
const setTaskBlock = (item?: TaskManageItem, readonly?: boolean) => {
  Object.assign(editTaskItem, item || new TaskManageOrigin(props.taskType))
  const { allRestrictProvince, allRestrictCity, ydRestrictProvince, ydRestrictCity, ltRestrictProvince, ltRestrictCity, dxRestrictCity, dxRestrictProvince, virtualRestrictCity, virtualRestrictProvince, unknownRestrictCity, unknownRestrictProvince } = editTaskItem
  Object.assign(taskRestrictData, {
    allRestrictProvince, allRestrictCity, ydRestrictProvince, ydRestrictCity, ltRestrictProvince, ltRestrictCity, dxRestrictCity, dxRestrictProvince, virtualRestrictCity, virtualRestrictProvince, unknownRestrictCity, unknownRestrictProvince
  })
  selectedOperatorList.value = []
  taskRestrictReadonly.value = readonly || false
  blockVisible.value = true
}
// 屏蔽城市确认
const confirmBlock = async () => {
  const params = {
    id: editTaskItem.id as number,
    ...taskRestrictData
  }
  const err = await traceApi(
    `${findValueInEnum(props.taskType, TaskTypeEnum)}任务-屏蔽地区: 开始`,
    params,
    aiOutboundTaskModel.setRestrictArea
  )
  if (!err) {
    ElMessage.success('操作成功')
    search()
    closeEditVisible()
  }
}
// 编辑确认
const confirmEditTask = async (params: TemplateBaseItem) => {
  let res: [Error | null, any]
  await trace({
    page: `${findValueInEnum(props.taskType, TaskTypeEnum)}任务-${params.id ? '编辑' : '创建'}任务: 开始`,
    params: params,
  })
  if (params.id) {
    res = await to(aiOutboundTaskModel.edit(params))
    currentTask.id = params.id
  } else {
    res = await to(aiOutboundTaskModel.createTask(params))
    Object.assign(currentTask, res[1])
  }
  await trace({
    page: `${findValueInEnum(props.taskType, TaskTypeEnum)}任务-${params.id ? '编辑' : '创建'}任务: 完成`,
    params: res[0] || null,
  })
  if (!res[0]) {
    ElMessage.success(`操作成功`)
    await search()
  }
  closeEditVisible()
}
// 编辑、城市屏蔽，弹窗关闭
const closeEditVisible = () => {
  editVisible.value = false
  blockVisible.value = false
}

/** 任务上锁 */
const lockTask = async (item: TaskManageItem) => {
  if (!item.id) {
    return ElMessage.warning('获取任务id失败')
  }
  const [err1, res1] = await to(Confirm({
    text: `您确定要【${!!item.ifLock ? '解锁' : '锁定'}】任务【${item.taskName}】吗?`,
    type: 'warning',
    title: '操作确认'
  })) as [Error | undefined, any]
  if (err1) return
  const params:{
    taskIds: string,
    taskType: TaskTypeEnum
    groupId: string,
  } = {
    taskIds: item.id + '',
    taskType: item.taskType!,
    groupId: item.groupId || userStore.groupId,
  }
  const err2 = await traceApi(
    `${findValueInEnum(props.taskType, TaskTypeEnum)}任务-${!item.ifLock ? '锁定' : '解锁'}任务`,
    params,
    !item.ifLock ? aiOutboundTaskModel.lockTask : aiOutboundTaskModel.unlockTask
  )
  if (!err2) {
    search()
    ElMessage.success('操作成功')
  }
}

/** 删除任务 */ 
const delTask = async (item: TaskManageItem) => {
  Confirm({
    text: `您确定要删除任务【${item.taskName}】吗?`,
    type: 'danger',
    title: '删除确认'
  }).then(async () => {
    loadingLeft.value = true
    const err = await traceApi(
      `${findValueInEnum(props.taskType, TaskTypeEnum)}任务-删除任务`,
      {
        id: item.id as number
      },
      aiOutboundTaskModel.delete
    )
    if (!err) {
      ElMessage.success('删除成功')
    }
    search()
  }).catch(() => {})
}
/** 开始停止任务 */ 
const callVisible = ref(false)
const confirmCallVisible = () => {
  callVisible.value = false
  search()
}
/** 任务操作结束 */


/** 抽屉 */ 
const drawerVisible = ref(false)
// 详情抽屉宽度，监听resize实现调整
const drawerWidth = ref(window.innerWidth > 1400 ? '75%' : '950px')
const getDrawerWidth = () => {
  drawerWidth.value = window.innerWidth > 1400 ? '75%' : '950px'
}
watch(drawerVisible, () => {
  if (drawerVisible.value) {
    window.addEventListener('resize', getDrawerWidth)
  } else {
    window.removeEventListener('resize', getDrawerWidth)
  }
})

// 任务状态，样式
const filterStatusClass = (val: TaskStatusEnum) => {
  switch(val) {
    case TaskStatusEnum['进行中']: return 'green-status';
    case TaskStatusEnum['未完成']: return 'red-status';
    case TaskStatusEnum['待执行']: return 'blue-status';
    case TaskStatusEnum['已停止']: return 'orange-status';
    default: return 'blue-status'
  }
}
const  colorMap = {
  [TaskStatusEnum['进行中']]: '--primary-green-color',
  [TaskStatusEnum['未完成']]: '--primary-red-color',
  [TaskStatusEnum['待执行']]: '--primary-blue-color',
  [TaskStatusEnum['已停止']]: '--primary-orange-color',
  [TaskStatusEnum['全部']]: '--primary-blue-color',
}
const filterTaskStyle = (item: TaskManageItem) => {
  if (item.id === currentTask.id && item.callStatus) {
    return {
      color: `var(${colorMap[item.callStatus]})`,
      borderColor: `var(${colorMap[item.callStatus]})`,
    }
  }
  return {}
}

const initData = async () => {
  await globalStore.getProvinceInfo()
  await taskStore.getAllScriptListOptions(true)
  await taskStore.getCallTeamListOptions(undefined, true)
}
search(searchForm.callStatus)
initData()
</script>

<style scoped lang="postcss" type="text/postcss">
.ai-task-container {
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  min-width: 1080px;
  height: calc(100% - 60px);
  color: #969799;
  font-size: 13px;
  .el-tag--small {
    padding: 0 4px;
  }
  .normal-btn {
    width: 92px;
    height: 32px;
  }
  .task-item {
    margin-bottom: 10px;
    position: relative;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #C8C9CC;
    overflow: hidden;
    width: 226px;
    min-height: 88px;
    text-align: center;
    overflow: hidden;

    .task-item-left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      position: relative;
      box-sizing: border-box;
      align-items: center;
      padding: 8px;
      width: 100%;
      height: 100%
    }
    .task-item-right {
      width: 20px;
      display: none;
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      border-radius: 0 4px 4px 0;
      background-color: #d6d6d6;
      span {
        width: 20px;
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        &:hover {
          background-color: var(--el-color-primary);
        }
      }
    }
    &:hover {
      .task-item-right {
        display: flex;
      }
    }
  }
  .status {
    width: 41px;
    height: 19px;
    font-weight: 600;
    line-height: 19px;
    border-radius: 2px;
    font-size: 11px;
    flex-shrink: 0;
    margin-left: 6px;
    display: inline-block;
  }
  .name {
    font-size: 14px;
    flex-shrink: 1;
    font-weight: 600;
    text-align: justify;
    word-break: break-all;
  }
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
    width: 100%;
    line-height: 22px;
    margin-bottom: 6px;
  }
  .task-content {
    display: flex;
    align-items: center;
    height: 22px;
    line-height: 22px;
    width: 100%;
    overflow: hidden;
    .label {
      width: 66px;
      flex-grow: 0;
      flex-shrink: 0;
      text-align: justify;
      color: #969799
    }
    .value {
      flex-grow: 1;
      flex-shrink: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: justify;
      color: #969799
    }
  }
  :deep(.el-upload-list) {
    display: none;
  }
  .el-button+.el-button {
    margin-left: 8px;
  }
  :deep(.tab-box) {
    height: 36px;
    .normal-tab {
      width: 46px;
      height: 31px;
      font-size: 13px;
      line-height: 28px;
    }
  }
  .task-card {
    border-radius: 8px;
    padding: 16px 16px 12px;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
    .value {
      color: #646566;
    }
  }
  .task-card-main {
    width: calc(100% - 480px);
    height: 160px;
  }
  .task-card-extend {
    width: 100%;
  }
}
.el-form-item {
  margin-bottom: 14px;
}
.el-form-item__label {
  padding-right: 0;
}
</style>
