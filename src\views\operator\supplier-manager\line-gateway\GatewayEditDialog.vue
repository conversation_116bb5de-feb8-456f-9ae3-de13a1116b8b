<template>
  <el-drawer
    v-model="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="cancel"
    :size="drawerWidth"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600] tw-h-[40px] tw-leading-[40px]">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 110px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
         scroll-to-error
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
      >
        <el-form-item label="网关名称：" prop="name">
          <el-input v-model="editData.name" clearable placeholder="请填写网关名称"></el-input>
        </el-form-item>
        <el-form-item v-if="editData.id&&editData.gatewayNumber" label="网关编号：" prop="gatewayNumber">
          <el-input :model-value="editData.gatewayNumber" disabled></el-input>
        </el-form-item>
        <el-form-item label="并发上限：" prop="concurrentLimit">
          <el-input-number
            v-model="editData.concurrentLimit"
            style="width: 100%"
            :min="1"
            :max="200000"
            :precision="0"
            clearable
            placeholder="请填写并发上限"
            :controls="false"
          ></el-input-number>
        </el-form-item>
        <!-- <el-form-item label="CAPS：" prop="caps">
          <el-input-number
            v-model="editData.caps"
            style="width: 100%"
            :min="1"
            :max="200000"
            :precision="0"
            clearable
            placeholder="请填写频率上限"
            :controls="false"
          ></el-input-number>
        </el-form-item> -->
        <el-form-item label="拨打限制：" prop="callingRestrictions" class="tw-w-[50%]">
          <div v-for="(item, index) in callingRestrictions" class="tw-w-full tw-flex tw-items-center tw-justify-between tw-mb-[6px] last:tw-mb-0">
            <InputNumberBox v-model:value="item.num" placeholder="填写" style="width: 45%" :min="1" append="次" @update:value="handleRestrictionsChange(1)"/>
            <span>&nbsp;/&nbsp;</span>
            <InputNumberBox v-model:value="item.hours" placeholder="填写" style="width: 45%" :min="1" append="小时" @update:value="handleRestrictionsChange(1)"/>
            <template v-if="!props.readonly">
              <el-button  type="danger" class="tw-flex-grow-0 tw-flex-shrink-0 tw-ml-1" @click="delRestriction(index, 1)" link>删除</el-button>
            </template>
          </div>
          <el-button class="tw-flex-grow-0 tw-flex-shrink-0" type="primary" @click="addRestriction(1)" link>新增</el-button>
        </el-form-item>
        <el-form-item label="拨通限制：" prop="dialingRestrictions" class="tw-w-[50%]">
          <div v-for="(item, index) in dialingRestrictions" class="tw-w-full tw-flex tw-items-center tw-justify-between tw-mb-[6px] last:tw-mb-0">
            <InputNumberBox v-model:value="item.num" placeholder="填写" style="width: 45%" :min="1" append="次" @update:value="handleRestrictionsChange(2)"/>
            <span>&nbsp;/&nbsp;</span>
            <InputNumberBox v-model:value="item.hours" placeholder="填写" style="width: 45%" :min="1" append="小时" @update:value="handleRestrictionsChange(2)"/>
            <template v-if="!props.readonly">
              <el-button  type="danger" class="tw-flex-grow-0 tw-flex-shrink-0 tw-ml-1" @click="delRestriction(index, 2)" link>删除</el-button>
            </template>
          </div>
          <el-button class="tw-flex-grow-0 tw-flex-shrink-0" type="primary" @click="addRestriction(2)" link>新增</el-button>
        </el-form-item>
        <el-form-item label="运营备注：" prop="notes">
          <el-input
            v-model.trim="editData.notes"
            type="textarea"
            placeholder="请输入备注，不超过200字"
            clearable
            autosize
            maxlength="200"
            show-word-limit
            style="width: 100%;"
            resize="none"
          />
        </el-form-item>
        <el-form-item label="线路组成：" ref="linesRef" prop="supplyLineNumbers">
          <el-button v-if="!props.readonly" type="primary" @click="addLines" link>添加线路</el-button>
        </el-form-item>
        <!-- 线路组成列表 -->
        <SupplyLineTable v-if="lineSelectedList && lineSelectedList?.length>0" :tableData="lineSelectedList||[]">
          <template v-slot:operate="{ row }">
            <el-button :type="props.readonly ? 'default' : 'danger'" @click="delLine(row)" link>
              删除
            </el-button>
          </template>
        </SupplyLineTable>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">{{editData.id ? '修改' : '新增'}}</el-button>
      </span>
    </template>
  </el-drawer>
  <LineAddDialog
    v-model:visible="lineVisible"
    :closable="false"
    :lineSelectedList="lineSelectedList||[]"
    :lineUnselectedList="lineUnselectedList||[]"
    @confirm="confirmLineAction"
  />
</template>

<script setup lang="ts">
import { ref,computed, watch, reactive, onUnmounted} from 'vue'
import { GatewayItem, LineInfo } from '@/type/gateway'
import { gatewayModel } from '@/api/gateway'
import type { FormInstance, } from 'element-plus'
import { supplierModel } from '@/api/supplier'
import { pickAttrFromObj } from '@/utils/utils'
import LineAddDialog from './LineAddDialog.vue'
import SupplyLineTable from '@/components/SupplyLineTable.vue'
import InputNumberBox from '@/components/InputNumberBox.vue'
import { onBeforeRouteLeave } from 'vue-router'
import to from 'await-to-js'
import { trace } from '@/utils/trace'

const emits = defineEmits(['confirm', 'update:visible'])
const props = defineProps<{
  visible: boolean,
  editData?: GatewayItem,
  readonly?: boolean,
  
}>();
const visible = ref(false)
const drawerWidth = ref(window.innerWidth > 1400 ? '75%' : '950px')
const loading = ref(false)
const allLines = ref<LineInfo[] | null>([])
const editData = reactive<GatewayItem>({
  id: undefined,
  gatewayNumber: undefined,
  deleted: undefined,
  name: '',
  caps: undefined,
  concurrentLimit: undefined,
  supplyLineNumbers: [],
  callingRestrictions: [],
  dialingRestrictions: [],
  notes: ''
})
const title = computed(() => editData.id ? '编辑' + editData.name: '创建网关')

/** 频率限制新增规则 开始 */
const callingRestrictions = ref<{num?: number, hours?: number}[] | null>([]) // 拨打限制
const dialingRestrictions = ref<{num?: number, hours?: number}[] | null>([]) // 拨通限制
// type: 1: 拨打限制, 2: 拨通限制
const addRestriction = (type: number) => {
  if (type === 1) {
    callingRestrictions.value?.push({
      num: undefined, hours: undefined
    })
  }
  if (type === 2) {
    dialingRestrictions.value?.push({
      num: undefined, hours: undefined
    })
  }
  handleRestrictionsChange(type)
}
// 更新对应的拨打限制和拨通限制
const handleRestrictionsChange = (type?: number) => {
  if (!type || type === 1) {
    editData.callingRestrictions = []
    callingRestrictions.value?.map(item => {
      if(item.num && !isNaN(+item.num) && item.hours && !isNaN(+item.hours)) {
        editData.callingRestrictions?.push(`${item.num || ''}-${item.hours || ''}`)
      }
    })
  }
  if (!type || type === 2) {
    editData.dialingRestrictions = []
    dialingRestrictions.value?.map(item => {
      if(item.num && !isNaN(+item.num) && item.hours && !isNaN(+item.hours)) {
        editData.dialingRestrictions?.push(`${item.num || ''}-${item.hours || ''}`)
      }
    })
  }
}
// 频率限制删除规则
const delRestriction = (index: number, type: number) => {
  if (type === 1) {
    if(index >= 0 && callingRestrictions.value && callingRestrictions.value.length > index) {
      callingRestrictions.value.splice(index, 1)
    }
  }
  if (type === 2) {
    if(index >= 0 && dialingRestrictions.value && dialingRestrictions.value.length > index) {
      dialingRestrictions.value.splice(index, 1)
    }
  }
  handleRestrictionsChange(type)
}
/** 频率限制新增规则 结束 */

/** 线路组成 开始 */
const lineSelectedList = ref<LineInfo[] | null>([]) // 网关组成线路 已选
const lineUnselectedList = ref<LineInfo[] | null>([]) // 网关组成线路 可选
const lineVisible = ref(false)
// 线路组件的ref，用于更新表格校验
const linesRef = ref()
// 点击添加路线
const addLines = () => {
  lineVisible.value = true
  linesRef.value.clearValidate()
}
// 添加路线弹窗确认函数，更新网关组成线路 已选和可选
const confirmLineAction = (data: {addLines: string[], delLines: string[] }) => {
  const unselectedList: LineInfo[] = []
  const selectedList: LineInfo[] = []
  lineUnselectedList.value?.map(item => {
    if(item.lineNumber && data.addLines?.includes(item.lineNumber)) {
      selectedList.push(item)
    } else {
      unselectedList.push(item)
    }
  })
  lineSelectedList.value?.map(item => {
    if(item.lineNumber && data.delLines?.includes(item.lineNumber)) {
      unselectedList.push(item)
    } else {
      selectedList.push(item)
    }
  })
  lineUnselectedList.value = unselectedList
  lineSelectedList.value = selectedList
}
// 删除线路组成某线路
const delLine = (row: LineInfo) => {
  if (!lineSelectedList.value) return
  const index = lineSelectedList.value.findIndex(l => l.lineNumber === row.lineNumber)
  const line = lineSelectedList.value.splice(index, 1)
  line[0] && lineUnselectedList.value?.unshift(line[0])
  linesRef.value.clearValidate()
}
/** 线路组成 关闭 */

// 初始化，获取网关组成线路和可选的线路
const init = async () => {
  loading.value = true
  // 更新线路组成
  lineSelectedList.value = []
  lineUnselectedList.value = []
  if (!allLines.value  || allLines.value?.length === 0) {
    const [_, data] = await to(supplierModel.getAllSupplyLines()) as [any, LineInfo[]]
    allLines.value = data?.filter(item => item.lineType === 'AI_OUTBOUND_CALL') || []
  }
  allLines.value?.map(item => {
    if (item.lineNumber) {
      if (editData.supplyLineNumbers?.includes(item.lineNumber)) {
        lineSelectedList.value?.push(item)
      } else {
        lineUnselectedList.value?.push(item)
      }
    }
  })
  // 更新拨打限制/拨通限
  callingRestrictions.value = []
  dialingRestrictions.value = []
  editData.callingRestrictions?.map(item => {
    const [num, hours] = item.split('-')
    callingRestrictions.value?.push({num: +num, hours: +hours})
  })
  editData.dialingRestrictions?.map(item => {
    const [num, hours] = item.split('-')
    dialingRestrictions.value?.push({num: +num, hours: +hours})
  })
  loading.value = false
}

const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  editRef.value && editRef.value.clearValidate()
  emits('update:visible', false)
}
const rules = {
  // 网关名称
  name: [
    { required: true, message: '请填写网关名称', trigger: 'blur' },
    { min: 2, max: 20, message: '网关名称长度必须在2-20个字符', trigger: 'blur' }
  ],
  // 并发上限
  concurrentLimit: [
    { required: true, message: '请填写并发上限', trigger: 'blur' },
  ],
  // 频率上限
  // caps: [
  //   { required: true, message: '请填写频率上限caps', trigger: 'blur' },
  // ],
  // 运行备注
  notes: [
    { max: 200, message: '备注不得超过200个字符', trigger: 'blur' }
  ],
  // 拨打限制
  callingRestrictions: [{
    trigger: ['blur'], validator: (rule: any, value: string[], callback: any) => {
      if (callingRestrictions.value && callingRestrictions.value.length > 0) {
        const hours: number[] = []
        callingRestrictions.value.map(item => {
          if (!item.num || !item.hours) {
            callback(new Error('拨通限制规则存在空值'))
          } else if (isNaN(+item.num) || isNaN(+item.hours)) {
            callback(new Error('拨通限制规则必须为数字'))
          } else {
            hours.includes(item.hours) ? callback(new Error('拨通限制规则存在相同时间段')) : hours.push(item.hours)
          }
        })
      }
      callback()
    }
  }],
  dialingRestrictions: [{
    trigger: ['blur'], validator: (rule: any, value: string[], callback: any) => {
      if (dialingRestrictions.value && dialingRestrictions.value.length > 0) {
        const hours: number[] = []
        dialingRestrictions.value.map(item => {
          if (!item.num || !item.hours) {
            callback(new Error('拨打限制规则存在空值'))
          } else if (isNaN(+item.num) || isNaN(+item.hours)) {
            callback(new Error('拨打限制规则必须为数字'))
          } else {
            hours.includes(item.hours) ? callback(new Error('拨打限制规则存在相同时间段')) : hours.push(item.hours)
          }
        })
      }
      callback()
    }
  }],
}

const confirm = async () => {  
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      handleRestrictionsChange()
      editData.supplyLineNumbers = (lineSelectedList.value||[]).map(item => item.lineNumber!)
      const params = pickAttrFromObj(editData, [
        'name', 'concurrentLimit', 'callingRestrictions', 'dialingRestrictions', 'notes', 'supplyLineNumbers', //'caps', 
      ])
      if (editData.id) {
        params.id = editData.id
        params.gatewayNumber = editData.gatewayNumber
      }
      loading.value = true
      trace({ page: `供应线路网关-${editData.id ? '编辑' : '创建'}网关`, params })
      const { id } = editData.id ? await gatewayModel.editGateway(params) as GatewayItem : await gatewayModel.addGateway(params) as GatewayItem
      emits('confirm', id)
      cancel()
      loading.value = false
    }
  })
}

/** watch */ 
// 监听visible
watch(() => props.visible, n => {
  visible.value = n
  if(n) {
    Object.assign(editData, props.editData || {
      id: undefined,
      name: '',
      concurrentLimit: undefined,
      callingRestrictions: [],
      dialingRestrictions: [],
      remark: ''
    })
    init()
  }
})

const clearAllData = () => {
  editRef.value = null
  linesRef.value = null
  lineSelectedList.value = null
  lineUnselectedList.value = null
  callingRestrictions.value = null
  dialingRestrictions.value = null
  allLines.value = null
}
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
    .el-form-item__content {
      align-items: flex-start;
      justify-content: flex-start;
    }
    &:first-child {
      margin-top: 14px;
    }
    &.muti-items {
      margin-top: 0;
      width: 320px;
      &:last-child {
        margin-left: 20px;
      }
    }
  }
  .el-table {
    font-size: var(--el-font-size-base);
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
</style>
