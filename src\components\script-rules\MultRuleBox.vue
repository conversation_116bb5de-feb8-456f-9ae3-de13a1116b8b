<template>
  <!-- 满足条件，必填 -->
  <div class="tw-w-full">
    <div class="tw-flex tw-items-center tw-justify-between tw-mb-[8px] info-title-deep ">
      <span class="tw-text-right tw-w-[90px] tw-font-[600]"><span v-if="props.required" class="tw-text-[#E54B17] tw-text-[11px]">* </span>{{ props.title }}：</span>
      <el-button link type="primary" @click="addOrRules()" class="tw-float-right">+ or条件</el-button>
    </div>
    <template v-for="(list, listIndex) in multRules">
      <el-divider v-if="listIndex && !list.ifDelete" border-style="dashed">or</el-divider>
      <div
        v-if="!list.ifDelete"
        :key="`match${list.conditionUniqueId || 0}-${multRules?.length || 0}-${listIndex}`"
        class="tw-flex tw-mb-[8px]"
      >
        <div
          class="tw-px-[6px] tw-py-[12px] tw-rounded-[4px]
            tw-border-[2px] tw-border-dashed
            tw-flex tw-flex-col tw-w-[calc(100%-40px)] tw-grow tw-gap-y-[8px]"
        >
          <template v-for="(item, index) in list.advancedRuleConditionDTOList">
            <div
              v-if="!item.ifDelete"
              :key="`match${list.conditionUniqueId || 0}-${list.advancedRuleConditionDTOList?.length || 0}-${listIndex}-${item.id || 0}-${index}`"
              class="tw-flex tw-w-full tw-items-center"
            >
              <SingleRuleItem
                class="tw-w-[calc(100%-30px)]"
                :options="props.options || []"
                typeName="conditionType"
                idName="conditionUniqueId"
                :data="item"
                @update:data="newData => updateRuleData(newData, listIndex, index)"
                :readonly="props.readonly"
              />
              <el-button v-if="!item.ifDelete" class="tw-grow-0 tw-shrink-0 tw-ml-[6px]" link type="danger" @click="deleteAndRule(listIndex, index)">
                <el-icon :size="14">
                  <SvgIcon name="delete" ></SvgIcon>
                </el-icon>
              </el-button>
            </div>
          </template>
          <div class="tw-w-full tw-flex tw-justify-between">
            <el-button link type="primary" @click="addAndRule(listIndex)">+ and条件</el-button>
          </div>
        
        </div>
        <el-button v-if="!list.ifDelete && (!props.required || exsitMatchLen > 1)" class="tw-grow-0 tw-shrink-0 tw-ml-[4px]" link type="danger" @click="deleteOrRule(listIndex)">
          <el-icon :size="13">
            <SvgIcon name="delete" ></SvgIcon>
          </el-icon>
          删除
        </el-button>
      </div>
    </template>
    <div v-if="!multRules || multRules.length === 0" class="info-title tw-text-left tw-ml-[44px]">
      （未设置）
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed,} from 'vue'
import { RuleItem, MutiRuleItem, } from '@/type/IntentionType'
import { ElMessage, } from 'element-plus'
import SingleRuleItem from '@/components/script-rules/SingleRuleItem.vue'
import SvgIcon from '@/components/SvgIcon.vue';

const emits = defineEmits(['update:data',])
const props = defineProps<{
  required?: boolean, // 是否必须存在一条规则
  title: string,
  options: {
    name: string,
    value: any
  }[],
  readonly: boolean,
  scriptId: number,
  data: MutiRuleItem[],
}>();

const multRules = ref<MutiRuleItem[]>(JSON.parse(JSON.stringify(props.data)))

// 更新规则
const updateRuleData = (data: RuleItem, listIndex: number, index: number) => {
  multRules.value[listIndex].advancedRuleConditionDTOList[index] = {
    ...data,
    scriptId: props.scriptId
  }
  emits('update:data', multRules.value)
}

// 新增or
const addOrRules = () => {
  const newMultRule: MutiRuleItem = {
    ifDelete: false,
    advancedRuleConditionDTOList: [{
      conditionType: props.options[0].value,
      ifDelete: false,
    }]
  }
  if (!multRules.value || multRules.value.length === 0) {
    multRules.value = [newMultRule]
    return
  }
  multRules.value.push(newMultRule)
  emits('update:data', multRules.value)
}

// 新增and
const addAndRule = (index: number) => {
  const newRule: RuleItem = {
    conditionType: props.options[0].value,
    conditionUniqueId: multRules.value[index].conditionUniqueId || undefined,
    ifDelete: false,
    scriptId: props.scriptId || undefined,
  }
  if (!multRules.value[index].advancedRuleConditionDTOList || multRules.value[index].advancedRuleConditionDTOList.length === 0) {
    multRules.value[index].advancedRuleConditionDTOList = [newRule]
    return
  }
  multRules.value[index].advancedRuleConditionDTOList.push(newRule)
  emits('update:data', multRules.value)
}

const exsitMatchLen = computed(() => multRules.value?.filter(item => !item.ifDelete)?.length || 0) // 已有的满足条件长度
// 删除or
const deleteOrRule = (index: number) => {
  // 满足条件至少有1个，排除条件不限制
  if (props.required && exsitMatchLen.value <= 1) return ElMessage.warning('请至少保留一个' + props.title)
  if (multRules.value[index].conditionUniqueId) {
    multRules.value[index].ifDelete = true
  } else {
    multRules.value.splice(index, 1)
  }
  emits('update:data', multRules.value)
}

// 删除and
const deleteAndRule = (index1: number, index2: number) => {
  const exsitLen = multRules.value[index1].advancedRuleConditionDTOList.filter(item => !item.ifDelete)?.length || 0
  if (exsitLen <= 1) return ElMessage.warning('请至少保留一个规则')
  if (multRules.value[index1].advancedRuleConditionDTOList[index2].id) {
    multRules.value[index1].advancedRuleConditionDTOList[index2].ifDelete = true
  } else {
    multRules.value[index1].advancedRuleConditionDTOList.splice(index2, 1)
  }
  emits('update:data', multRules.value)
}

const init = () => {
  if (!!props.required && (!multRules.value || multRules.value.length === 0)) {
    addOrRules()
    emits('update:data', multRules.value)
  }
}

watch(() => props.data, () => {
  multRules.value = JSON.parse(JSON.stringify(props.data))
  init()
}, {
  deep: true,
  immediate: true
})
</script>

<style lang="postcss" type="text/postcss">
</style>