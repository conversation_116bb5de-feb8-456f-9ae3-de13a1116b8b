<template>
  <HeaderBox title="成员管理" />
  <div class="account-container">
    <div class="tw-flex tw-justify-end tw-mb-[12px]">
      <el-button type="primary" class="tw-w-[68px] tw-h-[32px]" @click="edit()">新建账号</el-button>
      <el-button v-if="isNotProd" class="tw-w-[68px] tw-h-[32px]" @click="export2Excel()">导出账号</el-button>
      <el-button class="tw-w-[68px] tw-h-[32px]" @click="handleUpload()">导入账号</el-button>
      <input type="file" ref="fileRef" @change="handleFileChange" accept=".xls,.xlsx" class="batch-upload tw-hidden"/>
    </div>
    <div class="tw-w-full tw-p-[16px] tw-bg-white tw-mb-[16px]">
      <div class="tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-gap-4 tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.name"
            placeholder="请输入用户姓名"
            clearable
            @keyup.enter="search"
          >
          </el-input>
        </div>
        <div class="item">
          <el-select v-model="searchForm.roleId" placeholder="选择角色" clearable>
            <el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id" />
          </el-select>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.account"
            clearable
            placeholder="请输入用户账号"
            @keyup.enter="search"
          >
          </el-input>
        </div>
        <div class="item tw-col-span-2">
          <TimePickerBox
            v-model:start="searchForm.startTime"
            v-model:end="searchForm.endTime"
            placeholder="最后登录时间"
            separator="-"
          />
        </div>
      </div>
      <div class="tw-flex tw-justify-end tw-mt-[12px]">
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
    <el-table
      :data="tableTempData"
      v-loading="loading"
      :header-cell-style="tableHeaderStyle"
      stripe
      :max-height="maxTableHeight"
      row-key="id"
    >
      <el-table-column property="account" label="账号" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="name" label="姓名" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="gender" label="性别" align="left" width="80">
        <template #default="{ row }">
          {{ row.gender === 'FEMALE' ? '女' : '男'}}
        </template>
      </el-table-column>
      <el-table-column property="phone" label="联系方式" align="left" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ filterPhone(row.phone, 3, 4) || '-'}}
        </template>
      </el-table-column>
      <el-table-column property="roleId" label="角色" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #default="{ row }">
          {{ roleList.find(item => item.id == row.roleId)?.roleName || '' }}
        </template>
      </el-table-column>
      <el-table-column property="department" label="部门" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="latestLoginTime" label="最后登录时间" sortable align="center" min-width="163">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="createTime" label="创建时间" sortable align="center" width="160">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="240" align="right" fixed="right">
        <template #default="{ row }">
          <div class="table-btn-box">
            <el-button type="primary" link @click="editPassWord(row)">修改密码</el-button>
            <el-button type="primary" link @click="edit(row)">编辑信息</el-button>
            <el-button type="primary" link @click="editFreezeStatus(row)">{{row.accountEnable ? '冻结' : '解冻'}}</el-button>
            <el-button type="danger" link @click="del(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="tableData.length || 0"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
      
    <AccountEditDialog
      v-model:visible="editVisible"
      :rowData="editData"
      :roleList="roleList"
      @confirm="search"
    >
    </AccountEditDialog>
    <PasswordEditDialog
      v-model:editPassWordVisible="editPassWordVisible"
      :passwordData="passwordData"
      :isSelf="false"
      @confirm="confirmPassword"
    >
    </PasswordEditDialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, } from 'vue'
import AccountEditDialog from './AccountEditDialog.vue'
import PasswordEditDialog from '@/components/PasswordEditDialog.vue'
import PaginationBox from '@/components/PaginationBox.vue'
import { ElMessage,  } from 'element-plus'
import dayjs from 'dayjs'
import { AccountItem, RoleResponse } from '@/type/user'
import { aiTeamModel, merchantUserModel } from '@/api/user'
import { storeToRefs } from 'pinia'
import { tableHeaderStyle } from '@/assets/js/constant'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { filterPhone, formatterEmptyData } from '@/utils/utils'
import { passwordReg, } from '@/utils/constant'

import { useGlobalStore } from '@/store/globalInfo'
import { useUserStore } from '@/store/user'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import { generateExcelByAoa, readXlsx } from '@/utils/export'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const isNotProd = !(import.meta.env.MODE?.includes('production'))
const userStore = useUserStore()
const editVisible = ref(false)
const editPassWordVisible = ref(false)
class AccountOrigin {
  id = undefined
  account = ''
  name = ''
  gender = 'MALE'
  phone = ''
  department = undefined
  roleId = undefined
  password = undefined
  email = undefined
  address = undefined
  createTime = undefined
  latestLoginTime = undefined
  accountType = 1
  tenantId = userStore.tenantId
  accountEnable = true
}
const editData = reactive<AccountItem>(new AccountOrigin())
const passwordData = reactive<{
    id: number,
    account: string,
    newPassword: string
}>({
  id: -1,
  account: '',
  newPassword: ''
})
const currentPage = ref(1)
const pageSize = ref(20)
const tableData = ref<AccountItem[]>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const tableTempData = computed(() => {
  return tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
class searchOrigin {
  name = ''
  account = ''
  roleId = null
  startTime = undefined
  endTime = undefined
}
const maxTableHeight = ref<number>(document.body.clientHeight - 300)
window.onresize = () => {
  return (() => {
    maxTableHeight.value = document.body.clientHeight -300
  })()
}

const searchForm = reactive(new searchOrigin())
const edit = (row?: AccountItem) => {
  editVisible.value = true
  Object.assign(editData, row || new AccountOrigin())
}
const editPassWord = (row: AccountItem) => {
  editPassWordVisible.value = true
  passwordData.id = row.id as number
  passwordData.account = row.account
  passwordData.newPassword = ''
}
const editFreezeStatus =  (row: AccountItem) => {
  Confirm({ 
    text: `您确定要【${row.accountEnable ? '冻结' : '解冻'}】账号【${row.account}】吗?`,
    type: 'warning',
    title: `${row.accountEnable ? '冻结' : '解冻'}账号确认`
  }).then(async () => {
    const params = {
      account: row.account,
      accountEnable: !row.accountEnable,
      accountType: 1
    }
    await aiTeamModel.editUserFreezeStatus(params)
    ElMessage({
      type: 'success',
      message: '操作成功'
    })
    search()
  }).catch(() => {})
}
const confirmPassword = async (params: {
  id: number,
  newPassword: string
}) => {
  await aiTeamModel.changeAccountPassword(params)
  ElMessage({
    type: 'success',
    message: '操作成功'
  })
  editPassWordVisible.value = false
  search()
}
const clearSearchForm = () => {
  Object.assign(searchForm, new searchOrigin())
}
const search = async () => {
  loading.value = true
  const data = await aiTeamModel.getMerchantAccountList(searchForm) as AccountItem[] || []
  tableData.value = data.sort((a,b) => dayjs(a.createTime).isAfter(dayjs(b.createTime)) ? -1 : 1)
  loading.value = false
}
const roleList = ref<RoleResponse[]>([])
const init = async () => {
  roleList.value = await aiTeamModel.searchRoleList() as RoleResponse[]
}
init()
search()
// 操作区
const del = async (row: AccountItem) => {
  Confirm({ 
    text: `您确定要【删除】账号【${row.account}】吗?`,
    type: 'danger',
    title: `删除确认`
  }).then(async () => {
    const params = {
      id: row.id as number
    }
    await aiTeamModel.deleteAccount(params)
    ElMessage({
      type: 'success',
      message: '账号删除成功'
    })
    search()
  }).catch(() => {})
}

const export2Excel = () => {
  if (!tableData.value || tableData.value.length === 0) {
    return ElMessage({
      type: 'warning',
      message: '导出数据为空'
    })
  }
  loading.value = true
  const arr = tableData.value?.map(item => {
    const roleName =roleList.value?.find(v => v.id == item.roleId)?.roleName || ''
    return [
      item.account, roleName, '', item.name
    ]
  }) || []
  generateExcelByAoa([
      ['账号', '角色', '初始密码', '姓名'],
      ...arr
    ],
    userStore.account + '账号.xlsx'
  );
  loading.value = false
}
const handleUpload = () => {
  // @ts-ignore
  document.querySelector('.batch-upload')!.click()
}
const fileRef = ref(null)
// 导入文件：批量导入标签
const handleFileChange = async (e: Event) => {
  const {data: xlsData} = await readXlsx(e) as { data: Record<string, any>[] }
  if (!xlsData || xlsData?.length === 0 || userStore.accountType !== 1) {
     // @ts-ignore
     fileRef.value.value = null
    return ElMessage.warning('文档上传失败，请检查')
  }
  const errMsg: string[] = []
  const data = xlsData?.map((roleItem, index) => {
    if (!roleItem['账号']) {
      errMsg.push(`账号不能为空`)
    }

    let roleId: undefined | number = undefined
    if (!roleItem['角色']) {
      errMsg.push(`角色不能为空`)
    } else {
      const role = roleList.value?.find(item => item.roleName == roleItem['角色'])
      if (!role) {
        errMsg.push(`角色不存在`)
      } else {
        roleId = role.id
      }
    }

    if (!roleItem['初始密码']) {
      errMsg.push(`初始密码不能为空`)
    } else if (!(roleItem['初始密码'] + '').match(passwordReg)) {
      errMsg.push(`初始密码必须为包含数字和字母，6~16位`)
    }

    if (!roleItem['姓名']) {
      errMsg.push(`姓名不能为空`)
    }

    return {
      account: roleItem['账号'] + '',
      roleId: roleId,
      password: roleItem['初始密码'] + '',
      password2: roleItem['初始密码'] + '',
      name: roleItem['姓名'] + '',
      accountType: 1,
      tenantId: userStore.tenantId,
      accountEnable: true,
      gender: 'MALE',
    }
  })
  if (errMsg.length > 0) {
     // @ts-ignore
    fileRef.value.value = null
    return ElMessage.warning(errMsg.join('；'))
  }
  loading.value = true
  for (const params of data) {
    await aiTeamModel.addAccount(params)
  }
  ElMessage.success('导入成功')
  search()
  loading.value = false
  // @ts-ignore
  fileRef.value.value = null
}

</script>

<style scoped lang="postcss" type="text/postcss">
.account-container {
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
  min-width: 1080px;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
    :deep(.caret-wrapper) {
      display: none;
    }
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
}
</style>