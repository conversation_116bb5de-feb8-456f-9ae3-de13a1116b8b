<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    @close="cancel"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">批量上传</div>
    </template>
    <el-form
      :model="addData"
      :rules="phoneRules"
      class="tw-px-[12px]"
      label-width="80px"
      ref="addFormRef"
    >
      <el-form-item label="上传文件：" prop="uploadFiles">
        <div class="tw-w-full">
          <el-upload
            v-model:file-list="addData.uploadFiles"
            class="tw-flex-grow"
            :class="(addData.uploadFiles && addData.uploadFiles?.length>0 && addData.uploadFiles[0]?.size || 0) / 1024 / 1024 > 80 ? 'exceed-dom' : ''"
            drag
            ref="uploadRef"
            :action="actionUrl"
            :headers="headerInfo"
            :limit="1"
            :show-file-list="false"
            :on-exceed="exceedFileNum"
            :http-request="uploadFileAction"
            accept=".csv,.xls,.xlsx"
            :auto-upload="false"
          >
            <template #trigger>
              <div v-if="addData.uploadFiles && addData.uploadFiles?.length > 0" class="tw-leading-[22px] tw-flex tw-flex-col tw-items-start tw-justify-center tw-h-full">
                <div class="tw-text-[14px] tw-text-[var(--primary-black-color-600)] tw-flex tw-items-center">
                  <el-icon :size="16" color="var(--primary-black-color-500)" class="tw-mr-[4px]"><SvgIcon name="file" color="inherit" /></el-icon>
                  <span>{{ addData.uploadFiles![0].name }}</span>
                  <el-icon v-if="(addData.uploadFiles![0]!.size || 0)/1024/1024<= 80" class="tw-ml-[8px]" :size="16" color="#13BF77">
                    <SvgIcon name="success" color="inherit" />
                  </el-icon>
                  <el-icon v-else class="tw-ml-[8px]" :size="16" color="#E64B17">
                    <SvgIcon name="fail" color="inherit" />
                  </el-icon>
                </div>
                <!-- <div class="info-title">文件大小：{{ translateSize(addData.uploadFiles[0].size) }}</div> -->
                <div class="tw-mt-[16px]">
                  <el-button link type="primary" @click="addData.uploadFiles=[]">重新上传</el-button>
                  <el-button link type="primary" @click.stop="addData.uploadFiles=[]">删除</el-button>
                </div>
              </div>
              <div v-else>
                <el-icon :size="24" color="#969799" class="upload-icon">
                  <SvgIcon name="upload2" color="inherit" />
                </el-icon>
                <p class="tw-text-[14px] tw-mt-[12px] tw-leading-[22px]">拖拽或上传文件至此</p>
                <p class="info-title">每次仅支持10000个号码，支持csv、xlsx</p>
              </div>
            </template>
          </el-upload>
          <el-button class="tw-float-right tw-mt-[4px]" type="primary" link @click="downloadTemplate">下载模版</el-button>
        </div>
      </el-form-item>
      <el-form-item  label="有效时间：">
        <el-radio-group v-model="expirationType">
          <el-radio v-if="props.groupType=='white'" :label="0">永久</el-radio>
          <el-radio v-else :label="2">固定时长</el-radio>
          <el-radio :label="1">自定义</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="expirationType===1"
        label="到期时间："
        prop="expireDate"
      >
        <el-date-picker
          v-model="addData.expireDate"
          style="width: 100%"
          format="YYYY-MM-DD"
          :shortcuts="shortcuts"
          placeholder="请选择到期时间"
          :disabled-date="disabledFn"
          type="date"
          :clearable="props.groupType==='black'"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { useUserStore } from '@/store/user'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { WhiteBlackListImportItem, } from '@/type/dataFilter'
import type { UploadRawFile, UploadUserFile, FormInstance, UploadRequestOptions } from 'element-plus'
import { getToken, } from '@/utils/utils'
import { exportFileByBlob } from '@/utils/export'
import to from 'await-to-js'
import { clueManagerModel } from '@/api/clue'
import { whitelistModel, blacklistModel } from '@/api/data-filter'
import { shortcuts, phoneRules, WhiteListImportItemOrigin } from './constant'
import dayjs from 'dayjs'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  groupId: number;
  groupType: string; // 'black' | 'white'
}>();
const loading = ref(false)
const userStore = useUserStore()
const dialogVisible = ref(props.visible)
const addData = reactive<WhiteBlackListImportItem>(new WhiteListImportItemOrigin())
const expirationType = ref(0)
const addFormRef = ref<FormInstance | null>(null)

// 文件上传
const headerInfo = { token: getToken(), }
const uploadRef = ref()
const actionUrl = computed(() => {
  return import.meta.env.VITE_BASE_URL + `AiSpeech/clue/importClueByExcel?groupId=${userStore.groupId}`
})
watch(() => addData.uploadFiles, () => {
  addFormRef.value && addFormRef.value.validate()
})
const exceedFileNum = () => {
  ElMessage.warning('仅支持上传一个文件！')
}
// 批量上传接口调用和处理
const uploadFileAction = async (opt: UploadRequestOptions) => {
  loading.value = true
  const data = new FormData()
  data.append('file', opt.file)
  const [err, res] = await to(props.groupType === 'white' ? whitelistModel.import(data, {
    expireDate: dayjs(expirationType.value === 0 ? '2400-01-01' : addData.expireDate).format('YYYY-MM-DD')
  }) : blacklistModel.import(data, {
    id: props.groupId,
    expireDate: expirationType.value === 2 ? '' : (addData.expireDate ? dayjs(addData.expireDate).format('YYYY-MM-DD') : ''),
  }))
  loading.value = false
  addData.uploadFiles = []
  if (!err) {
    ElMessage({
      message: `${opt.file.name || ''}上传成功`,
      type: 'success',
    })
    emits('confirm', res)
    cancel()
  }
}
const downloadTemplate = async () => {
  const res = await clueManagerModel.downloadTemplate() as BlobPart
  exportFileByBlob(res, '模板.xlsx', 'xls')
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = () => {  
  addFormRef.value && addFormRef.value.validate((valid) => {
    if (valid) {
      uploadRef.value?.submit()
    }
  })
}

// 到期时间选择限制只能选进入往后
const disabledFn = (date: Date) => {
  return dayjs(date).isBefore(dayjs().endOf('day'))
}

watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    addFormRef.value?.clearValidate()
    Object.assign(addData, new WhiteListImportItemOrigin())
    expirationType.value = props.groupType === 'white' ? 0 : 2
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
:deep(.el-upload-dragger) {
  padding: 16px;
  height: 134px;
  width: 100%;
}
.exceed-dom :deep(.el-upload-dragger){
  border-color: #E54B17;
}
</style>