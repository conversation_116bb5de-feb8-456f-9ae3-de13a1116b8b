<template>
  <HeaderBox title="黑名单限制" />
  <div class="module-container">
    <TabsBox v-model:active="activeTab" :tabList="tabList"></TabsBox>
    <BlacklistTable :type="activeTab"/>
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, computed, ref, watch, } from 'vue'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
import { useUserStore } from "@/store/user";
import { useGlobalStore } from '@/store/globalInfo'

const BlacklistTable = defineAsyncComponent({loader: () => import('./BlacklistTable.vue')})

// 用户权限获取
const userStore = useUserStore();

// 路由
const tabList:('全局限制'|'行业限制'|'产品限制')[]  = ['全局限制', '行业限制', '产品限制']
const activeTab = ref<'全局限制'|'行业限制'|'产品限制'>(tabList[0])
const globalStore = useGlobalStore()
globalStore.updateProductList(true)
</script>

<style scoped lang="postcss" type="text/postcss">
.module-container {
  min-width: 1080px;
}
</style>
