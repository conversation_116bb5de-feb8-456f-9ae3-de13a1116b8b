import { RecordTypeEnum } from "./task";
import dayjs from "dayjs";

// 巡检记录
export interface InspectionRecordItem {
  account: string;
  accountType: number;
  checkCallTimes: number;
  checkPlayTimes: number;
  createTime: string;
  id: number;
  name: string;
  roleName: string;
  updateTime: string;
}

// 巡检记录详情-列表记录
export interface InspectionRecordDetailItem {
  account: string;
  callType: RecordTypeEnum;
  recordId: string;
  playRecord: boolean;
  inspectionTime: string;
  updateTime: string;
}

// 巡检记录详情-查询条件
export interface InspecteParams {
  account: string;
  callType: RecordTypeEnum;
  recordId: string;
  roleName: string
}

export enum InspecteTypeEnum {
  '分类问题' = 'CLASSIFICATION_ISSUE',
  '标签问题' = 'LABEL_ISSUE',
  '语义问题' = 'SEMANTIC_ISSUE',
  '高级规则' = 'ADVANCED_RULE_ISSUE',
  '语料问题' = 'CORPUS_ISSUE',
  '录音问题' = 'RECORDING_ISSUE',
  '坐席问题' = 'AGENT_ISSUE',
  '通话问题' = 'CALL_ISSUE',
  '系统问题' = 'SYSTEM_ISSUE',
  '其他问题' = 'OTHER_ISSUE',
}
export enum InspecteStatusEnum {
  '未处理' = 'UN_PROCESSED',
  '无需处理' = 'NO_REQUIRED_PROCESS',
  '处理中' = 'PROCESSING',
  '延期处理' = 'DEFERRED_PROCESSING',
  '处理完成' = 'PROCESSED',
}

// 巡检工单-创建内容
export interface InspecteOrderBaseItem {
  id?: number;
  callRecordId?: string;
  type: InspecteTypeEnum; // 问题类型
  description: string; // 问题描述
  principal?: string; // 负责人
}
// 巡检工单-全量内容
export interface InspecteOrderItem extends InspecteOrderBaseItem {
  id: number;
  callTime?: string; // 呼出时间
  phone: string; // 被叫号码
  phoneType: RecordTypeEnum; // 通话类型
  commitUser: string; // 提交人
  createTime?: string; // 提交时间
  
  handleTime?: string; // 处理时间
  handleUser: string; // 处理人
  comment: string; // 处理备注
  status: InspecteStatusEnum; // 最后工单状态
}

// 
export interface InspecteOrderSearchParams {
  callTimeEnd: string;
  callTimeStart: string;
  comment: string;
  commitTimeEnd: string;
  commitTimeStart: string;
  commitUser: string;
  description: string;
  handleUser: string;
  limit: number;
  page: number;
  phone: string;
  phoneType: string;
  principal: string; // 负责人
  status: InspecteStatusEnum;
  type: InspecteTypeEnum;
  workOrderId: number;
}
export class InspecteOrderSearchParamsOrigin implements Partial<InspecteOrderSearchParams> {
  callTimeEnd = undefined
  callTimeStart = undefined
  comment = undefined
  commitTimeEnd =  dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  commitTimeStart = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  commitUser = undefined
  description = undefined
  handleUser = undefined
  limit = 20
  page = 1
  phone = undefined
  phoneType = undefined
  principal = undefined
  status = undefined
  type = undefined
  workOrderId = undefined
}

export class InspecteOrderItemOrigin implements Partial<InspecteOrderItem> {
  id = undefined
  type = undefined
  description = undefined
  principal = undefined
  commitUser = undefined
  handleUser = undefined
  phone = undefined
  phoneType = undefined
  status = undefined
  comment = undefined
}
