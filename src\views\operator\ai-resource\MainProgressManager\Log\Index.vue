<template>
  <!--模块容器-->
  <div class="log-container">
    <!--模块标题-->
    <h5 class="tw-text-left tw-text-[20px] tw-font-semibold tw-leading-[50px]">操作日志</h5>
    <!--模块主体-->
    <!--操作日志表格-->
    <el-table
      :data="tableData || []"
      style="width: 100%"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column property="userAccount" label="操作账号" align="left" min-width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="operateType" label="操作类型" align="center" width="120" show-overflow-tooltip :formatter="formatterEmptyData">
        <template #default="{ row }">
          <div v-if="row.operateType" class="status-box" :class="filterTypeClass(row.operateType)">
            {{ row.operateType }}
          </div>
        </template>
      </el-table-column>
      <el-table-column property="operateContent" align="left" label="操作内容" min-width="240" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="opinion" align="left" label="备注" width="200" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="createTime" align="center" label="操作时间" min-width="160" show-overflow-tooltip :formatter="formatterEmptyData">
        <template v-slot="scope:{row:{createTime:any}}">
          {{ dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') ?? '-' }}
        </template>
      </el-table-column>
      <!--空数据提示-->
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onActivated, onDeactivated } from 'vue'
import { ElMessage } from 'element-plus'
import { useScriptStore } from '@/store/script'
import { scriptCheckModel } from '@/api/speech-craft'
import { ScriptOperationLog } from '@/type/speech-craft'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import dayjs from 'dayjs'
import { formatterEmptyData } from '@/utils/utils'
import { tableHeaderStyle } from '@/assets/js/constant'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const maxTableHeight = ref<number>(document.body.clientHeight - 270)
watch(() => document.body.clientHeight, n => {
  maxTableHeight.value = n - 270
})
const scriptStore = useScriptStore()
const editId = scriptStore.scriptStringId || -1
// 表格区
const tableData = ref<ScriptOperationLog[] | null>([])
// 搜索区
const search = async () => {
  loading.value = true
  try {
    // 请求接口
    tableData.value = <ScriptOperationLog[]>await scriptCheckModel.getOperationLogList({
      id: editId || 0
    })
  } catch (err) {
    ElMessage({
      message: '获取操作日志失败',
      type: 'error',
    })
  }
  loading.value = false
}
// 表格操作类型样式
const filterTypeClass = (type: string) => {
  switch(type) {
    case '审核': return 'orange-status';
    case '提交': return 'blue-status';
    case '新建': return 'green-status';
    case '复制': return 'green-status';
    default: return 'blue-status';
  }
}
// 操作
onActivated(() => {
  search()
})
onDeactivated(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss">
.log-container {
  box-sizing: border-box;
  position: relative;
  height: calc(100vh - 180px);
  overflow-y: auto;
  width: 100%;
  padding: 20px 10px 20px 20px;
  background-color: #fff;
  font-size: 15px;
  display: flex;
  flex-direction: column;
  .el-table {
    font-size: var(--el-font-size-base);
  }
  h5 {
    background-color: inherit;
  }
  .el-row, .el-col {
    align-items: center;
    flex-wrap: nowrap;
    text-align: center;
  }
  :deep(.el-row) {
    padding: 5px;
  }
  .el-input {
    width: 90%;
  }
  .title {
    border-top: 2px solid #eee;
    border-bottom: 2px solid #eee;
    font-size: 17px;
    line-height: 50px;
    text-align: justify;
  }
  .border {
    border-bottom: 1px solid #eee;
  }
  .tips {
    flex-wrap: nowrap;
    font-size: 13px;
    line-height: 30px;
    color: #bbb;
    text-align: justify;
  }
  .label {
    font-size: 14px;
    line-height: 36px;
  }
  .value {
    display: flex;
    justify-content: flex-start;
    span {
      margin-left: 10px;
    }
  }
}
</style>
