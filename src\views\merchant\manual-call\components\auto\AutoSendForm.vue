<template>
  <el-empty v-if="isEmpty && !isEdit" class="tw-m-auto"></el-empty>
  <div v-else v-loading="loading" class="tw-flex tw-flex-col tw-items-start tw-w-full">
    <div class="tw-my-[6px] tw-flex tw-flex-col tw-items-start tw-w-full">
      <div v-if="editData.id" class="tw-flex tw-items-center tw-h-[28px] tw-mb-1">
        <span class="tw-text-left tw-text-[14px] tw-font-semibold tw-text-[--primary-black-color-600] tw-w-[90px]">执行状态</span>
        <el-switch
          :disabled="isEdit"
          :model-value="autoInfo?.status"
          class="tw-ml-1"
          inline-prompt
          active-text="已开启"
          inactive-text="已关闭"
          active-value="1"
          inactive-value="0"
          @click="handleAutoStatus"
        />
        <span v-if="autoInfo?.status=='1'" class="tw-ml-[8px] tw-text-[12px] tw-text-[--primary-black-color-400]">{{ autoInfo?.autoStopTime ? `于${dayjs(autoInfo?.autoStopTime).format('YYYY-MM-DD')}结束` : '永久生效' }}</span>
      </div>
      <div class="tw-text-left tw-text-[14px] tw-font-semibold tw-text-[--primary-black-color-600]">执行规则</div>
    </div>
    <el-form
      :model="editData"
      :disabled="!isEdit"
      class="tw-w-full tw-grow"
      :rules="rules"
      label-width="100px"
      ref="editRef"
    >
      <template v-if="props.type===3">
        <el-form-item label="执行时间：" prop="workTimes">
          <div v-if="isEdit" class="tw-w-full tw-flex tw-flex-col tw-items-start"><el-button link type="primary" @click="addTime()">添加时间</el-button>
          <div v-for="(time, index) in timeArr" :key="index">
            <el-time-select
              v-model="timeArr[index]"
              class="tw-my-[2px]"
              start="07:00"
              step="00:30"
              end="20:00"
              :editable="false"
              placeholder="请选择执行时间"
              @change="handleTimeChange"
            />
            <el-button link type="danger" class="tw-ml-1" @click="delTime(index)">删除</el-button>
          </div>
        </div>
          <div v-else>{{ editData.workTimes || '-' }}</div>
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item label-width="0" prop="startWorkTimeList" class="tw-col-span-2">
          <div class="tw-w-full">
            <TimePicker
              v-if="isEdit"
              name="执行时段"
              :startWorkTimeList="startWorkTimeList"
              :endWorkTimeList="endWorkTimeList"
              beginTime="07:00"
              endTime="20:00"
              showShortcut
              showResult
              @update="handleTimeList"
            />
            <div v-else v-if="startWorkTimeList && startWorkTimeList?.length > 0 && endWorkTimeList && endWorkTimeList?.length > 0" class="tw-flex tw-flex-wrap tw-items-center tw-mt-1 tw-text-[13px]">
              <div class="tw-w-[90px] tw-text-right tw-mr-[12px]">选中时间段：</div>
              <el-tag
                v-for="(item, index) in startWorkTimeList"
                :key="item"
                class="tw-mr-[4px] tw-my-[2px]"
              >{{ item + ' - ' +  endWorkTimeList[index]}}</el-tag>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="执行间隔：" prop="executeTimeGap">
          <el-select v-model="editData.executeTimeGap" class="tw-grow" placeholder="请选择执行间隔">
            <el-option v-for="item in intervalOption" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
      </template>
      <template v-if="props.type===1">
        <el-form-item label="下发类型：" prop="distributeType">
          <el-select v-model="editData.distributeType" class="tw-grow" placeholder="请选择下发类型">
            <el-option v-for="item in beSendTypeOption" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分配模式：" prop="allocationType">
          <el-select v-model="editData.allocationType" class="tw-grow" placeholder="请选择分配模式">
            <el-option v-for="item in allocateTypeOption" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择坐席组：" prop="callTeamIds">
          <SelectBox
            v-model:selectVal="editData.callTeamIds"
            :options="callTeamList||[]"
            name="callTeamName"
            val="id"
            canSelectAll
            placeholder="请选择坐席组"
            filterable
            class="tw-grow"
            multiple
          >
          </SelectBox>
        </el-form-item>
      </template>
      <template v-if="props.type===2">
        <el-form-item label="分配模式：" prop="allocationType">
          <el-select v-model="editData.allocationType" class="tw-grow" placeholder="请选择分配模式">
            <el-option v-for="item in [allocateTypeOption[0]]" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择坐席：" prop="callSeatIds" class="tw-col-span-2">
          <SelectBox
            v-model:selectVal="editData.callSeatIds"
            :options="callSeatList||[]"
            name="account"
            val="id"
            placeholder="请选择坐席"
            filterable
            class="tw-flex-grow"
            multiple
          >
            <template v-slot:option-tips="{ option }">
              <span class="tw-text-gray-400">{{ option.name || '' }}</span>
            </template>
          </SelectBox>
        </el-form-item>
      </template>
      <template v-if="props.type===3">
        <el-form-item label="归档类型：" prop="clueStatusList">
          <el-checkbox-group v-model="editData.clueStatusList" @change="handleClueStatusListChange">
            <el-checkbox v-for="item in clueStatusOption" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="editData.clueStatusList?.includes(ClueStatusEnum['已分配'])" label="归档排除：">
          <div class="tw-flex tw-items-center tw-w-full">
            <!-- <span class="tw-w-[90px] tw-text-[13px]">已分配：</span> -->
            <el-checkbox v-model="editData.excludeStart" true-label="1" false-label="0">排除星标</el-checkbox>
            <el-checkbox v-model="editData.excludeNotCalled" true-label="1" false-label="0">排除待首呼</el-checkbox>
          </div>
        </el-form-item>
      </template>
      <el-form-item label="备注：" prop="comment" class="tw-col-span-2">
        <el-input v-model="editData.comment" type="textarea" :rows="2" placeholder="请输入备注，200字以内" clearable/>
      </el-form-item>
    </el-form>
  </div>
  <div class="tw-w-full tw-flex tw-justify-end tw-pt-1 tw-border-t-[1px]">
    <template v-if="!isEdit">
      <el-button :disabled="autoInfo?.status=='1'" type="primary" @click="goEdit">{{editData.id ? '编辑' : '新增规则'}}</el-button>
    </template>
    <template v-if="isEdit">
      <el-button type="primary" :loading="loading" @click="save">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </template>
  </div>

  <StartAutoDialog
    v-model:visible="startVisible"
    :data="autoInfo"
    :type="props.type"
    @confirm="updateAutoInfo"
  />
</template>

<script setup lang="ts">
import { ref, watch, reactive, onDeactivated, onActivated, onMounted } from 'vue'
import { ClueAutoActionInfo, AllocateTypeEnum, BeSendTypeEnum, ClueAutoOrigin, ClueAutoSettingInfo, ClueStatusEnum } from '@/type/clue'
import Confirm from '@/components/message-box'
import { ElMessage, } from 'element-plus'
import { useTaskStore } from '@/store/taskInfo'
import { SeatTeam, SeatMember } from '@/type/seat'
import type { FormInstance, } from 'element-plus'
import dayjs from 'dayjs'
import SelectBox from '@/components/SelectBox.vue'
import StartAutoDialog from './StartAutoDialog.vue'
import TimePicker from '@/components/TimePicker.vue'
import { enum2Options, pickAttrFromObj } from '@/utils/utils'
import { intervalOption } from './constant'
import { clueManagerModel } from '@/api/clue'
import { to } from 'await-to-js'
import { useUserStore } from '@/store/user'

const props = defineProps<{
  type: number, // 1：自动下发；2：自动分配, 3: 自动归档
}>();

const loading = ref(false)
const userInfo  = useUserStore()
const taskStore  = useTaskStore()
const editData = reactive<ClueAutoSettingInfo>(new ClueAutoOrigin(props.type))
const autoInfo = ref<ClueAutoActionInfo | null>(null)
const isEdit = ref(false)
const editRef = ref<FormInstance  | null>(null)
const rules = {
  executeTimeGap: [{ required: true, message: '请选择执行间隔', trigger: 'change' }],
  allocationType: [{ required: true, message: '请选择分配模式', trigger: 'change' }],
  distributeType: [{ required: true, message: '请选择下发类型', trigger: 'change' }],
  startWorkTimes: [
    { required: true, message: '请选择工作时间段', trigger: 'change' },
  ],
  callSeatIds: [{ required: true, message: '请选择坐席', trigger: 'change' }],
  callTeamIds: [{ required: true, message: '请选择坐席组', trigger: 'change' }],
  clueStatusList: [{ required: true, message: '请选择归档类型', trigger: 'change' }],
  workTimes: [
    { required: true, message: '请选择执行时间', trigger: 'change' },
    { validator: (rule: any, value: any, callback: any) => {
      const arr = [...new Set([...(timeArr.value ||[])])]
      if (arr.length  < timeArr.value.length) {
        return new Error('时间段不能重复')
      }
      if (timeArr.value.length > 3) {
        return new Error('最多添加3个时间段')
      }
      return callback()
    }, trigger: 'change' },
  ],
}

const isEmpty = ref(false)
const updateAutoInfo = async () => {
  loading.value = true
  isEdit.value = false
  let res: [Error|null, ClueAutoActionInfo|null|undefined] = [null, null]
  if (props.type === 1) {
    res = await to(clueManagerModel.getAutoSendInfo())
  }
  if (props.type === 2) {
    res = await to(clueManagerModel.getAutoAllocateInfo({
      groupId: userInfo.groupId,
    }))
  }
  if (props.type === 3) {
    res = await to(clueManagerModel.getAutoArchiveInfo())
  }
  loading.value = false
  isEmpty.value = !res[1]
  if (res[1]) {
    autoInfo.value = pickAttrFromObj(res[1], 
      ['autoStopTime', 'status', 'autoStop', 'id']
    )
    autoInfo.value.id = res[1].id
    taskStore.autoRuleMap[props.type] =  res[1]?.id
  } else {
    autoInfo.value = null
    taskStore.autoRuleMap[props.type] = undefined
  }
  Object.assign(editData, res[1] || new ClueAutoOrigin(props.type))
  startWorkTimeList.value = editData.startWorkTimes?.split(',') || []
  endWorkTimeList.value = editData.endWorkTimes?.split(',') || []
  timeArr.value = editData.workTimes?.split(',') || []
}

// 执行时段
const startWorkTimeList = ref<string[]>([])
const endWorkTimeList = ref<string[]>([])
const handleTimeList = (sList: string[], eList: string[]) => {
  startWorkTimeList.value = sList
  endWorkTimeList.value = eList
  editData.startWorkTimes = sList?.join(',')
  editData.endWorkTimes = eList?.join(',')
  editRef.value && editRef.value.clearValidate()
}

// 执行时间，归档
const timeArr = ref<string[]>([])
const handleTimeChange = () => {
  editData.workTimes = timeArr.value?.join(',') || ''
}
const addTime = () => {
  if (timeArr.value?.length >= 3) {
    return ElMessage.warning('最多添加3个时间段')
  }
  timeArr.value?.push('18:00')
  handleTimeChange()
  editRef.value && editRef.value.clearValidate()
}
const delTime = (index: number) => {
  if (timeArr.value?.length <= 1) {
    return ElMessage.warning('至少保留1个时间段')
  }
  timeArr.value?.splice(index, 1)
  handleTimeChange()
  editRef.value && editRef.value.clearValidate()
}

/** 不包含已分配，清空归档排除 */
const handleClueStatusListChange = () => {
  if (!editData.clueStatusList?.includes(ClueStatusEnum['已分配'])) {
    editData.excludeNotCalled = '0'
    editData.excludeStart = '0'
  }
}

// 进入编辑模式
const goEdit = () => {
  if (autoInfo.value?.status == '1') {
    return ElMessage.warning('请先关闭自动下发线索')
  }
  isEdit.value = true
}

/** 开关执行自动操作 */ 
const handleAutoStatus = () => {
  if (isEdit.value) {
    return ElMessage.warning('请先保存执行规则')
  }
  if (autoInfo.value?.status != '1') {
    startAutoStatus()
  } else {
    stopAutoStatus()
  }
}
const startVisible = ref(false)
const startAutoStatus = () => {
  startVisible.value = true
}
// 关闭自动操作
const stopAutoStatus = () => {
  const obj: Record<number, Function> = {
    1: clueManagerModel.switchAutoSend,
    2: clueManagerModel.switchAutoAllocate,
    3: clueManagerModel.switchAutoArchive,
  }
  const nameObj: Record<number, string> = {
    1: '自动下发线索',
    2: '自动分配线索',
    3: '自动归档线索',
  }
  Confirm({
    text: `确定要【关闭】${nameObj[props.type]}`,
    type: 'warning',
    title: '操作确认',
    confirmText: '确认',
  }).then(async () => {
    
    const params = {
      id: autoInfo.value?.id,
      autoStop: autoInfo.value?.autoStop,
      autoStopTime: autoInfo.value?.autoStop == '0' ? undefined : autoInfo.value?.autoStopTime,
      status: '0',
    }
    const [err1, res1] = await to(obj[props.type](params))
    if (!err1) {
      ElMessage.success('操作成功')
      updateAutoInfo()
    }
  }).catch(() => {})
}

const cancel = () => {
  updateAutoInfo()
}
const save = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const actionObj: Record<number, keyof typeof clueManagerModel> = {
        1: 'saveAutoSendInfo',
        2: 'saveAutoAllocateInfo',
        3: 'saveAutoArchiveInfo',
      }
      // @ts-ignore
      const [err, res] = await to(clueManagerModel[actionObj[props.type]](editData))
      loading.value = false
      if (!err) {
        ElMessage({
          type: 'success',
          message: '操作成功！'
        })
      }
      updateAutoInfo()
    }
  })
}

const callTeamList = ref<SeatTeam[]>([]) // 坐席组列表
const callSeatList = ref<SeatMember[]>([]) // 坐席组列表
const allocateTypeOption = enum2Options(AllocateTypeEnum)
const beSendTypeOption = enum2Options(BeSendTypeEnum)
const clueStatusOption = enum2Options(ClueStatusEnum).filter(item => item.name !== '已归档')
const init = async () => {
  // 获取当前groupId 的坐席组
  if (props.type === 1) {
    callTeamList.value = await taskStore.getCallTeamListOptions()
  }
  if (props.type === 2) {
    callSeatList.value = await taskStore.getCallTeamSeatListOptions()
  }
  updateAutoInfo()
}
init()
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-button {
  height: 28px;
}
.status-box-mini {
  width: 42px;
}
</style>
