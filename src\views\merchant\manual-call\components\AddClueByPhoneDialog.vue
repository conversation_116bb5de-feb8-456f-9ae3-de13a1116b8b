<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    align-center
    class="dialog-form"
    @close="cancel()"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">任务导入</div>
    </template>
    <el-scrollbar
      v-if="stepNo===0"
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-p-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="100px"
        ref="editRef"
      >
        <!-- 选择名单导入线索 -->
        <div class="tw-text-left tw-font-[600]">筛选方案</div>
        <el-form-item label="外呼任务：" prop="taskId">
          <el-select
            v-model="editData.taskId"
            placeholder="请选择外呼任务"
            @change="handleTaskChange"
            filterable
            :filter-method="(val: string) => updateTaskList(val)"
            :multiple=false
            clearable
            class="tw-w-full"
          >
            <el-option v-for="item in taskList" :key="item.id" :label="item.taskName" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="筛选类型：">
          <el-select
            v-model="filterType"
            placeholder="筛选类型"
            clearable
            class="tw-w-full"
          >
            <el-option label="名单管理" :value="1"/>
            <el-option label="通话记录" :value="2"/>
          </el-select>
        </el-form-item>
        <div class="tw-text-left tw-font-[600]">筛选项</div>
        <el-form-item v-if="filterType===2" label="标签：" prop="label">
          <el-input
            v-model="editData.label"
            placeholder="请输入标签"
          >
          </el-input>
        </el-form-item>
            
        <el-form-item label="号码/名单：" prop="phone">
          <el-input
            v-model="editData.phone"
            placeholder="请输入号码/名单编号"
          >
          </el-input>
        </el-form-item>
        <el-form-item v-if="filterType===2" label="呼出时间：" prop="calloutStartTime">
          <TimePickerBox
            v-model:start="editData.callOutTimeStart"
            v-model:end="editData.callOutTimeEnd"
            :splitToday="true"
            placeholder="呼出时间"
            class="tw-w-full"
            format="YYYY-MM-DD HH:mm:ss"
            :clearable="false"
          />
        </el-form-item>
        <template v-else>
          <el-form-item label="加入时间：" prop="addStartTime">
            <TimePickerBox
              v-model:start="editData.addTimeStart"
              v-model:end="editData.addTimeEnd"
              placeholder="加入时间"
              :splitToday="true"
              format="YYYY-MM-DD HH:mm:ss"
              class="tw-w-full"
              :clearable="false"
            />
          </el-form-item>
          <el-form-item label="最后外呼时间：" prop="lastCallStartTime">
            <TimePickerBox
              v-model:start="editData.lastCallTimeStart"
              v-model:end="editData.lastCallTimeEnd"
              placeholder="最后外呼"
              format="YYYY-MM-DD HH:mm:ss"
              class="tw-w-full"
              :clearable="true"
            />
          </el-form-item>
        </template>
        <template v-if="filterType===1">
          <el-form-item prop="putThroughNumLeft" label="接通次数：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.putThroughNumLeft" placeholder="最小" :max="editData.putThroughNumRight || Number.MAX_VALUE" style="width: 47%" append="次"/>
              <span class="info-title">&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.putThroughNumRight" placeholder="最大" style="width: 47%" append="次" :min="editData.putThroughNumLeft||0"/>
            </div>
          </el-form-item>
          <el-form-item prop="calledNumLeft" label="拨打轮数：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.calledNumLeft" placeholder="最小" :max="editData.calledNumRight || Number.MAX_VALUE" style="width: 47%" append="轮"/>
              <span class="info-title">&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.calledNumRight" placeholder="最大" style="width: 47%" append="轮" :min="editData.calledNumLeft||0"/>
            </div>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item prop="callDurationLeft" label="通话时长：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.minCallDuration" :max="editData.maxCallDuration || Number.MAX_VALUE" placeholder="最小" style="width: 47%" append="秒"/>
              <span class="info-title">&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.maxCallDuration" placeholder="最大" style="width: 47%" append="秒" :min="editData.minCallDuration||0"/>
            </div>
          </el-form-item>
          <el-form-item prop="cycleCountLeft" label="对话轮数：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.minCycleCount" placeholder="最小" :max="editData.maxCycleCount || Number.MAX_VALUE" style="width: 47%" append="轮"/>
              <span class="info-title">&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.maxCycleCount" placeholder="最大" style="width: 47%" append="轮" :min="editData.minCycleCount||0"/>
            </div>
          </el-form-item>
          <el-form-item prop="sayCountLeft" label="客户说话：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.minSayCount" placeholder="最小" :max="editData.maxSayCount" style="width: 47%" append="次"/>
              <span class="info-title">&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.maxSayCount" placeholder="最大" style="width: 47%" append="次" :min="editData.minSayCount"/>
            </div>
          </el-form-item>
        </template>
        <el-form-item prop="statusList" label="呼叫状态：">
          <el-select
            v-model="editData.statusList"
            placeholder="呼叫状态"
            clearable multiple collapse-tags
            collapse-tags-tooltip
            class="tw-w-full"
            :max-collapse-tags="3"
          >
            <el-option v-for="item in callStatusOptions" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="范围选择：">
          <div class="tw-flex tw-justify-between tw-w-full">
            <el-select v-model="editData.operator" placeholder="运营商" clearable class="tw-w-[145px]">
              <el-option v-for="item in operatorList" :key="item" :label="item" :value="item"/>
            </el-select>
            <el-select v-model="province" placeholder="省" clearable class="tw-w-[145px]" @change="handleProvinceChange">
              <el-option v-for="item in provinceList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item"/>
            </el-select>
            <el-select v-model="editData.cityCode" placeholder="市" clearable class="tw-w-[145px]">
              <el-option v-for="item in cityList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
            </el-select>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <el-scrollbar v-if="stepNo!==0"
      :max-height="'calc(100vh - 200px)'"
      view-class="tw-flex tw-flex-col tw-p-[16px] tw-leading-[30px] tw-text-justify"
    >
      <div class="tw-font-[600]">筛选结果</div>
      <div>共有【{{ filterType === 2 ? '通话记录' : '名单管理' }}】的{{ total }}条数据满足筛选条件</div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="stepNo===0">
          <el-button @click="cancel()" :icon="CloseBold">取消</el-button>
          <el-button :loading="loading" type="primary" @click="filterAction" :icon="Select">筛选</el-button>
        </template>
        <template v-if="stepNo===1">
          <el-button @click="cancel(true)" :icon="CloseBold">重新筛选</el-button>
          <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">上传</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// type
import { TaskManageItem, CallStatusEnum2 } from '@/type/task'
import { OperatorEnum,  } from '@/type/common'
import { TaskCallFilterModal, TaskCallFilterOrigin, } from '@/type/clue'
// 本地方法
import { enum2Options, pickAttrFromObj } from '@/utils/utils'
import { trace } from '@/utils/trace'
// 本地组件
import InputNumberBox from '@/components/InputNumberBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
// api
import { clueManagerModel, } from '@/api/clue'
import { aiOutboundTaskModel } from '@/api/ai-report'
// 来自插件、依赖
import { ref, onDeactivated, reactive, watch} from 'vue'
import dayjs from 'dayjs'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import { useUserStore } from '@/store/user'
import type { FormInstance, } from 'element-plus'
import { CloseBold, Select, } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'

const props = defineProps<{
  visible: boolean,
}>();
const emits = defineEmits(['update:visible', 'confirm'])


const globalStore = useGlobalStore()
const  {loading} = storeToRefs(globalStore)
const taskStore = useTaskStore()
const userStore = useUserStore()
const dialogVisible = ref(false)
const callStatusOptions = enum2Options(CallStatusEnum2)
// 任务、省份、运营商
const taskList = ref<TaskManageItem[]|null>([])
const provinceList = ref<string[]|null>([])
const operatorList = ['全部', ...Object.values(OperatorEnum)]
const provinceAllMap = ref<{ [key: string]: string[] }|null>({})
const province = ref<string | undefined>(undefined)
const cityList = ref<string[]|null>([])
const handleProvinceChange = () => {
  editData.provinceCode = province.value?.split(',')[1]
  editData.cityCode = undefined
  cityList.value = province.value && provinceAllMap.value ? (provinceAllMap.value[province.value] || []) : (Object.values(provinceAllMap.value||[]).flat() || [])
}

/** 1.2 名单导入 */
const filterType = ref(1) // 筛选类型，1、名单， 2、通话记录
const stepNo = ref(0) // 导入类型，===2，名单导入，时，分为2步骤
/** 变量 */
const editData = reactive<TaskCallFilterModal>(new TaskCallFilterOrigin())
const editRef = ref<FormInstance  | null>(null)
/** 1.2.1 stepNo === 0 */
const handleTaskChange = async() => {
  if (editData.taskId) {
    const [_ , res] = await to(aiOutboundTaskModel.findTaskByIds(editData.taskId))
    if (!res || !res[0]) {
      return ElMessage.warning('获取任务信息失败')
    }
    const row = res[0]
    editData.addTimeStart = dayjs(row?.createTime||undefined).startOf('day').format('YYYY-MM-DD HH:mm:ss')
    editData.addTimeEnd = dayjs(row?.createTime||undefined).isBefore(dayjs().startOf('d')) ? dayjs().add(-1, 'd').endOf('day').format('YYYY-MM-DD HH:mm:ss') : dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
    editData.lastCallTimeStart = dayjs(row?.createTime||undefined).startOf('day').format('YYYY-MM-DD HH:mm:ss')
    editData.lastCallTimeEnd = dayjs(row?.createTime||undefined).isBefore(dayjs().startOf('d')) ? dayjs().add(-1, 'd').endOf('day').format('YYYY-MM-DD HH:mm:ss') : dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
    editData.callOutTimeStart = dayjs(row?.createTime||undefined).startOf('day').format('YYYY-MM-DD HH:mm:ss')
    editData.callOutTimeEnd = dayjs(row?.createTime||undefined).isBefore(dayjs().startOf('d')) ? dayjs().add(-1, 'd').endOf('day').format('YYYY-MM-DD HH:mm:ss') : dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  }
}
const total = ref(0)
const tempId = ref('')
/** 弹窗操作 */
const rules = {
  taskId: [
    { required: true, message: '请选择任务', trigger: 'change' },
  ]
}

/**
 * 取消
 * @param isGoBack true: 重新筛选 || false：关闭弹窗
 */
const cancel = async (isGoBack: boolean = false) => {
  if (tempId.value) {
    const params = {
      tempId: tempId.value,
      groupId: userStore.groupId,
      isIntoClueDB: false,
    }
    await filterType.value == 1 ?  clueManagerModel.addPhoneRecordsToClue(params) : clueManagerModel.addCallRecordsToClue(params)
  }
  tempId.value = ''
  if (isGoBack) {
    stepNo.value = 0
  } else {
    dialogVisible.value = false
    emits('update:visible', false)
  }
}
// 筛选操作
const filterAction = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = pickAttrFromObj(editData, filterType.value === 2 ? [
        'taskId', 'phone', 'label', 'callOutTimeStart', 'callOutTimeEnd', 'minCallDuration', 'maxCallDuration',
        'minCycleCount', 'maxCycleCount', 'minSayCount', 'maxSayCount', 'operator', 'provinceCode', 'cityCode'
      ] : [
        'taskId', 'phone', 'operator', 'provinceCode', 'cityCode', 'calledNumLeft', 'calledNumRight', 'putThroughNumLeft', 'putThroughNumRight', 'statusList',
        'addTimeStart', 'addTimeEnd', 'lastCallTimeStart', 'lastCallTimeEnd'
      ])
      const arr = editData.statusList?.join(',')?.split(',')?.flatMap(item => item ? [parseInt(item)] : []) || []
      params.statusList = (arr && arr.length) > 0 ? arr : undefined

      await trace({ page: `线索管理-${filterType.value == 1 ? '导入任务名单' : '导入任务通话记录'}-筛选数据`, params: params })
      const [err, res] = await to(filterType.value == 1 ?  clueManagerModel.getBatchPhoneRecordsNum(params) : clueManagerModel.getBatchCallRecordsNum(params))
      loading.value = false
      if (err) return
      total.value = res?.size || 0
      tempId.value = res?.tempId || ''
      stepNo.value = 1
    }
  })
}
/** 1.2.2 stepNo === 1 */
// 筛选完成后，提交呼叫名单
const confirm = async () => {
  if (total.value <= 0) {
    return ElMessage({
      type: 'warning',
      message: '暂无数据添加！'
    })
  }
  loading.value = true
  const params = {
    tempId: tempId.value,
    groupId: userStore.groupId,
    isIntoClueDB: true,
  }
  if (filterType.value == 1) {
    await to(clueManagerModel.addPhoneRecordsToClue(params))
    trace({
      page: '线索管理-导入任务名单-导入',
      params,
    })
  } else {
    await to(clueManagerModel.addCallRecordsToClue(params))
    trace({
      page: '线索管理-导入任务通话记录-导入',
      params,
    })
  }
  ElMessage.warning('已导入，请稍后到人工外外呼-线索管理查看导入数据！')
  loading.value = false
  cancel()
}

// 初始化数据，监听器等
const initData = async () => {
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
  cityList.value = province.value ? (provinceAllMap.value[province.value] || []) : (Object.values(provinceAllMap.value).flat() || [])
  updateTaskList()
}
const updateTaskList = async (val: string = '',) => {
  const data = await aiOutboundTaskModel.findTaskList({
    startTime: dayjs().add(-1, 'month').format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    taskName: val ? val : undefined,
    startPage: 0,
    pageNum: 100,
  })
  taskList.value = data?.data as {id: number, taskName: string}[] || []
}
watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    initData()
    filterType.value = 1
    stepNo.value = 0
    Object.assign(editData, new TaskCallFilterOrigin())
    editRef.value?.clearValidate()
  }
})

const clearAll = () => {
  editRef.value = null
  taskList.value = null
  provinceList.value = null
  provinceAllMap.value = null
  cityList.value = null
}
onDeactivated(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.normal-progress {
  :deep(.el-progress-bar__inner) {
    border-radius: 2px;
    background: linear-gradient(90deg, #0167FF 0%, #019FFF 100%);
  }
}
:deep(.el-step.is-simple .el-step__title) {
  font-size: var(--el-font-size-base);
}
</style>