export enum CorpusConditionEnum {
  '本句语义命中' = 'HIT_SEM',
  '历史语义命中' = 'HISTORY_SEM',
  '本句短语命中' = 'EXTRA_WORD',
}

export interface CorpusConditionSubItem {
  id?: number,
  semantic?: string,
  word?: string,
}
export interface CorpusConditionItem {
  semListPacks: {
    semListPackType: CorpusConditionEnum,
    singlePhraseList: CorpusConditionSubItem[],
    minNum?: number,
    maxNum?: number
  }[]
}
