<template>
  <el-dialog
    v-model="dialogVisible"
    class="seat-monitor-dialog"
    width="480"
    align-center
    @close="onClose"
  >
    <template #header>
      <div class="form-dialog-header">
        详细任务
      </div>
    </template>

    <div class="form-dialog-main">
      <el-input
        v-model.trim="form.taskName"
        placeholder="搜索任务名称"
        clearable
        :suffix-icon="Search"
        @input="updateFilterList"
      />

      <el-scrollbar max-height="60vh">
        <div v-for="task in currentList" :key="task.id" class="task-item">
          {{ task?.taskName ?? '' }}
        </div>
      </el-scrollbar>

      <PaginationBox
        class="tw-grow-0 tw-shrink-0"
        :currentPage="pageNum"
        :pageSize="pageSize"
        :pageSizeList="pageSizeList"
        :total="tableSize"
        @search="updateAllList"
        @update="updateCurrentList"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, reactive, ref, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { TaskManageItem } from '@/type/task'
import { updateCurrentPageList } from '@/utils/utils'

// 动态引入组件
const PaginationBox = defineAsyncComponent(() => import('@/components/PaginationBox.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  data: TaskManageItem[],
}>()
const emits = defineEmits([
  'close',
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(() => props.visible, (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      // 清除表单的校验结果
      formRef.value?.resetFields()
      // 更新表单
      updateForm()
      // 更新任务列表
      updateAllList()
    }
  }
)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 弹窗 开始 ----------------------------------------

/**
 * 处理弹窗组件的关闭事件
 */
const onClose = () => {
  // console.log('处理弹窗组件的关闭事件')
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

interface FormType {
  taskName: string
}

// 表单DOM
const formRef = ref()
// 表单默认数据
const formDefault = (): FormType => {
  return {
    // 任务名称
    taskName: '',
  }
}
// 表单数据
const form: FormType = reactive(formDefault())
/**
 * 更新表单
 */
const updateForm = () => {
  resetForm()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 清除表单的校验结果
  formRef.value?.resetFields()
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 清除表单的校验结果
  formRef.value?.resetFields()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 任务列表 开始 ----------------------------------------

// 任务列表 全部
const allList = ref<TaskManageItem[]>([])
// 任务列表 筛选
const filterList = ref<TaskManageItem[]>([])
// 任务列表 当前页
const currentList = ref<TaskManageItem[]>([])

// 任务列表 当前页码 从1开始
const pageNum = ref<number>(1)
// 任务列表 每页大小
const pageSize = ref<number>(20)
// 任务列表 每页大小的可选数值
const pageSizeList: number[] = [20, 50, 100]
// 任务列表 列表总长度
const tableSize = computed(() => {
  return filterList.value.length ?? 0
})

/**
 * 更新任务列表 全部
 */
const updateAllList = async () => {
  // 更新列表
  allList.value = props.data

  // 更新搜索结果
  updateFilterList()
}
/**
 * 更新任务列表 筛选
 */
const updateFilterList = () => {
  // 按搜索条件筛选列表
  filterList.value = allList.value
  if (form.taskName) {
    filterList.value = allList.value.filter((task: TaskManageItem) => {
      return task.taskName?.includes(form.taskName)
    })
  }

  updateCurrentList(pageNum.value, pageSize.value)
}
/**
 * 更新任务列表 当前页
 */
const updateCurrentList = (p?: number, s?: number) => {
  if (p || s) {
    // 如果参数指定了页码或/和每页大小，则按参数更新列表
    p && (pageNum.value = p)
    s && (pageSize.value = s)
    currentList.value = updateCurrentPageList(filterList.value, pageNum.value, pageSize.value)
  } else {
    // 如果参数都没有指定，则页码和页面大小保持不变，更新全部列表
    updateAllList()
  }
}

// ---------------------------------------- 任务列表 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.task-item {
  margin: 8px 0;
  color: #313233;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  text-align: left;
}
/* 分页条 */
:deep(.pagination-dox) {
  /* 左右内边距 */
  padding: 8px 0;
}
</style>
