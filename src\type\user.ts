export interface UserLoginForm {
  account: string,
  password: string,
  code?: string,
}
export interface UserLoginResponse {
  id: number,
  userId: number | null,
  roleName: string,
  roleId: number,
  groupId: string,
  account: string,
  accountType: number, // 0: 运营端，1商户端
  tenantId?: number,
  token: string,
  expireTime?: string,
}
export interface UserInfo {
  permissions: Array<string>,
}
export interface TokenInfo {
  token: string,
  expire?: number
}
export interface AccountBaseInfo {
  accountType: number,
  tenantId?: number | null,
}
export interface AccountItem extends AccountBaseInfo {
  id?: number,
  account: string,
  roleId?: number,
  name: string,
  gender?:  string,
  phone?: string,
  department?: string,
  // role?: string,
  password?: string,
  password2?: string,
  email?: string,
  createTime?: string,
  latestLoginTime?: string,
  taskTimeRange?: string,
  address?: string,
  accountEnable?: boolean
}
export interface RoleResponse extends AccountBaseInfo{
  id?: number
  roleName: string
  note: string
  authorityMap?: {[key: string]: string[]}
  authorityCodes?: string[]
  createTime?: string
  updateTime?: string
}
export interface RolePermissionResponse {
  id: number
  menus: object
  permission: object
  children?:  RolePermissionResponse[]
}
export interface TeamSearchModal {
  name: string
  status: string
  role: string
}
export interface TeamInfoItem {
  id: number
  name: string
  account: string
  team: string
  status: string
  role: string
  createTime: string
}
