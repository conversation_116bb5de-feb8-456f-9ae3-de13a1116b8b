<template>
  <!-- 满足条件，必填 -->
  <div class="tw-w-full">
    <div class="tw-flex tw-items-center tw-justify-between tw-mb-[8px] info-title-deep ">
      <span class="tw-text-right tw-w-[90px] tw-font-[600]"><span v-if="props.required" class="tw-text-[#E54B17] tw-text-[11px]">* </span>{{ props.title }}：</span>
      <el-button link type="primary" @click="addOrRules()" class="tw-float-right">+ or条件</el-button>
    </div>
    <template v-for="(list, listIndex) in multRules" :key="`match-${multRules?.length}-${listIndex}`">
      <el-divider v-if="listIndex" border-style="dashed" class="tw-text-[12px]">or</el-divider>
      <div class="tw-flex tw-mb-[8px]">
        <div
          class="tw-px-[6px] tw-py-[12px] tw-rounded-[4px]
            tw-border-[2px] tw-border-dashed
            tw-flex tw-flex-col tw-w-[calc(100%-40px)] tw-grow tw-gap-y-[8px]"
        >
          <template v-for="(item, index) in list.semListPacks" :key="`match-${multRules?.length}-${listIndex}-${list.semListPacks?.length}-${index}`">
            <div class="tw-flex tw-w-full tw-items-center">
              <SingleConditionItem
                :data="item"
                :readonly="props.readonly"
                @update:data="newData => updateRuleData(newData, listIndex, index)"
              />
              <el-button class="tw-grow-0 tw-shrink-0 tw-ml-[6px]" link type="danger" @click="deleteAndRule(listIndex, index)">
                <el-icon :size="14">
                  <SvgIcon name="delete" ></SvgIcon>
                </el-icon>
              </el-button>
            </div>
          </template>
          <div class="tw-w-full tw-flex tw-justify-between">
            <el-button link type="primary" @click="addAndRule(listIndex)">+ and条件</el-button>
          </div>
        
        </div>
        <el-button v-if="!props.required ||  multRules?.length> 1" class="tw-grow-0 tw-shrink-0 tw-ml-[4px]" link type="danger" @click="deleteOrRule(listIndex)">
          <el-icon :size="13">
            <SvgIcon name="delete" ></SvgIcon>
          </el-icon>
          删除
        </el-button>
      </div>
    </template>
    <div v-if="!multRules || multRules.length === 0" class="info-title tw-text-left tw-ml-[44px]">
      （未设置）
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch,} from 'vue'
import { CorpusConditionEnum, CorpusConditionItem, CorpusConditionSubItem } from '@/type/corpus'
import { ElMessage, } from 'element-plus'
import SingleConditionItem from './components/SingleConditionItem.vue'
import SvgIcon from '@/components/SvgIcon.vue';

const emits = defineEmits(['update:data',])
// 务必使用v-if重新渲染页面数据；
const props = defineProps<{
  required?: boolean, // 是否必须存在一条规则
  title: string,
  readonly: boolean,
  data: CorpusConditionItem[] | null | undefined,
}>();

const multRules = ref<CorpusConditionItem[]>(JSON.parse(JSON.stringify(props.data)))

// 更新规则
const updateRuleData = (data: {
  semListPackType: CorpusConditionEnum,
  singlePhraseList: CorpusConditionSubItem[],
  minNum?: number,
  maxNum?: number
}, listIndex: number, index: number) => {
  multRules.value[listIndex].semListPacks[index] = data
  emits('update:data', multRules.value)
}

// 新增or
const addOrRules = () => {
  const newMultRule: CorpusConditionItem = {
    semListPacks: [{
      semListPackType: CorpusConditionEnum['本句语义命中'],
      singlePhraseList: [],
    }]
  }
  if (!multRules.value || multRules.value.length === 0) {
    multRules.value = [newMultRule]
    return
  }
  multRules.value.push(newMultRule)
  emits('update:data', multRules.value)
}

// 新增and
const addAndRule = (index: number) => {
  const newRule: {
    semListPackType: CorpusConditionEnum,
    singlePhraseList: CorpusConditionSubItem[],
    minNum?: number,
    maxNum?: number
  } = {
    semListPackType: CorpusConditionEnum['本句语义命中'],
    singlePhraseList: [],
    minNum: undefined,
    maxNum: undefined
  }
  if (!multRules.value[index].semListPacks?.length) {
    multRules.value[index].semListPacks = [newRule]
    return
  }
  multRules.value[index].semListPacks.push(newRule)
  emits('update:data', multRules.value)
}

// 删除or
const deleteOrRule = (index: number) => {
  // 满足条件至少有1个，排除条件不限制
  if (props.required && multRules.value?.length <= 1) return ElMessage.warning('请至少保留一个' + props.title)
  multRules.value.splice(index, 1)
  emits('update:data', multRules.value)
}

// 删除and
const deleteAndRule = (index1: number, index2: number) => {
  const exsitLen = multRules.value[index1].semListPacks?.length || 0
  if (exsitLen <= 1) return ElMessage.warning('请至少保留一个规则')
  multRules.value[index1].semListPacks.splice(index2, 1)
  emits('update:data', multRules.value)
}

const init = () => {
  if (!!props.required && (!multRules.value || multRules.value.length === 0)) {
    addOrRules()
    emits('update:data', multRules.value)
  }
}

watch(() => props.data, () => {
  multRules.value = props.data ? JSON.parse(JSON.stringify(props.data)) : null
  init()
}, {
  deep: true,
  immediate: true
})
</script>

<style lang="postcss" type="text/postcss">
</style>
