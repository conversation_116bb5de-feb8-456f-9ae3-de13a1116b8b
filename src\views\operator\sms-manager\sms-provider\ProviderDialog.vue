<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    class="sms-dialog"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        {{ props.isEdit ? '编辑' : '创建' }}供应商
      </div>
    </template>

    <el-scrollbar class="form-dialog-main" max-height="90vh">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="90px"
        >
          <div class="form-section">
            <div class="form-section-header">
              基本信息
            </div>
            <el-form-item label="供应商名称：" prop="supplierName">
              <el-input
                v-model.trim="form.supplierName"
                placeholder="请输入"
                clearable
                maxlength="40"
                show-word-limit
                class="tw-w-full"
              />
            </el-form-item>
            <el-form-item label="供应商简称：" prop="supplierProfile">
              <el-input
                v-model.trim="form.supplierProfile"
                placeholder="请输入"
                clearable
                maxlength="15"
                show-word-limit
                class="tw-w-full"
              />
            </el-form-item>
            <el-form-item label="公司地址：" prop="supplierAddress">
              <el-input
                v-model.trim="form.supplierAddress"
                placeholder="请输入"
                clearable
                maxlength="250"
                show-word-limit
                class="tw-w-full"
              />
            </el-form-item>
          </div>

          <div class="form-section">
            <div class="form-section-header">
              联系人信息
            </div>
            <el-form-item label="联系人：" prop="contactName">
              <el-input
                v-model.trim="form.contactName"
                placeholder="请输入"
                clearable
                maxlength="40"
                show-word-limit
                class="tw-w-full"
              />
            </el-form-item>
            <el-form-item label="联系电话：" prop="phoneNumber">
              <el-input
                v-model.trim="form.phoneNumber"
                placeholder="请输入"
                clearable
                maxlength="40"
                show-word-limit
                class="tw-w-full"
              />
            </el-form-item>
            <el-form-item label="邮箱：" prop="email">
              <el-input
                v-model.trim="form.email"
                placeholder="请输入"
                clearable
                maxlength="40"
                show-word-limit
                class="tw-w-full"
              />
            </el-form-item>
            <el-form-item label="职务：" prop="duty">
              <el-input
                v-model.trim="form.duty"
                placeholder="请输入"
                clearable
                maxlength="15"
                show-word-limit
                class="tw-w-full"
              />
            </el-form-item>
            <el-form-item label="联系地址：" prop="contactAddress">
              <el-input
                v-model.trim="form.contactAddress"
                placeholder="请输入"
                clearable
                maxlength="150"
                show-word-limit
                class="tw-w-full"
              />
            </el-form-item>
          </div>

          <div class="form-section">
            <div class="form-section-header">
              合作状态
            </div>
            <el-form-item label="合作状态：" prop="cooperationStatus">
              <el-select
                v-model.trim="form.cooperationStatus"
                placeholder="请选择"
                class="tw-w-full"
              >
                <el-option
                  v-for="smsProviderStatusItem in Object.entries(SmsProviderStatusEnum)"
                  :key="smsProviderStatusItem[1]"
                  :value="smsProviderStatusItem[1]"
                  :label="smsProviderStatusItem[0]"
                />
              </el-select>
            </el-form-item>
          </div>

          <div class="form-section">
            <div class="form-section-header">
              对接管理
            </div>
            <el-form-item label="对接协议：" prop="smsProtocol">
              <el-select
                v-model.trim="form.smsProtocol"
                placeholder="请选择对接协议"
                class="tw-w-full"
              >
                <el-option
                  v-for="SmsProtocolItem in Object.entries(SmsProtocolEnum)"
                  :key="SmsProtocolItem[1]"
                  :value="SmsProtocolItem[1]"
                  :label="SmsProtocolItem[0]"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="配置信息：" prop="configInfo">
              <el-input
                v-model.trim="form.configInfo"
                placeholder="请输入"
                clearable
                maxlength="150"
                show-word-limit
                class="tw-w-full"
              />
            </el-form-item>
          </div>

          <div class="form-section">
            <div class="form-section-header">
              备注
            </div>
            <el-form-item label="备注：" prop="notes">
              <el-input
                v-model.trim="form.notes"
                type="textarea"
                placeholder="请输入"
                clearable
                maxlength="250"
                show-word-limit
                autosize
                resize="none"
                class="tw-w-full"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :disabled="loadingConfirm" :icon="CloseBold" @click="onClickCancel">
          取消
        </el-button>
        <el-button :loading="loadingConfirm" type="primary" :icon="Select" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch, } from 'vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js'
import { SmsProtocolEnum, SmsProviderItem, SmsProviderParams, SmsProviderStatusEnum } from '@/type/sms'
import { smsProviderModel } from '@/api/sms'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  data: SmsProviderItem,
  isEdit: boolean,
  allList: SmsProviderItem[],
}>(), {
  visible: false,
  data: () => ({}),
  isEdit: false,
  allList: () => ([]),
})
const emits = defineEmits([
  'update:visible',
  'confirm',
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): SmsProviderParams => ({
  id: undefined,

  supplierNumber: '',
  supplierName: '',
  supplierProfile: '',
  supplierAddress: '',

  contactName: '',
  phoneNumber: '',
  email: '',
  duty: '',
  contactAddress: '',

  cooperationStatus: SmsProviderStatusEnum['启用'],

  smsProtocol: SmsProtocolEnum['CMPP'],
  configInfo: '',

  notes: '',
})
// 表单数据
const form: SmsProviderParams = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  supplierName: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('供应商名称不能为空'))
      } else {
        callback()
      }
    }
  },
  supplierProfile: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('供应商简称不能为空'))
      } else {
        callback()
      }
    }
  },
  phoneNumber: [{
    required: false,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        // 可以为空
        callback()
      } else {
        if (/^\d{5,20}$/.test(value)) {
          callback()
        } else {
          callback(new Error('联系电话格式不正确，请输入5-20位数字'))
        }
      }
    }
  }],
  email: [{
    required: false,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        // 可以为空
        callback()
      } else {
        // a@b.c
        // 这5个基本元素都不能少
        // a b c 可以是大小英文字母、中文、数字、下划线、连字符
        // .c 可以重复匹配
        if (/^[A-Za-z0-9\u4e00-\u9fa5_-]+@[a-zA-Z0-9\u4e00-\u9fa5_-]+(\.[a-zA-Z0-9\u4e00-\u9fa5_-]+)+$/.test(value)) {
          callback()
        } else {
          callback(new Error('邮箱格式不正确，类似*******************的格式'))
        }
      }
    }
  }],
  cooperationStatus: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!Object.values(SmsProviderStatusEnum).includes(value)) {
        callback(new Error('合作状态不正确'))
      } else {
        callback()
      }
    }
  },
  smsProtocol: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('对接协议不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
        duration: 3000,
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: SmsProviderParams = {
    id: (typeof form.id === 'number') ? form.id : undefined,

    supplierNumber: form.supplierNumber ?? undefined,
    supplierName: form.supplierName ?? undefined,
    supplierProfile: form.supplierProfile ?? undefined,
    supplierAddress: form.supplierAddress ?? undefined,

    contactName: form.contactName ?? undefined,
    phoneNumber: form.phoneNumber ?? undefined,
    email: form.email ?? undefined,
    duty: form.duty ?? undefined,
    contactAddress: form.contactAddress ?? undefined,

    cooperationStatus: form.cooperationStatus || SmsProviderStatusEnum['停用'],

    smsProtocol: Object.values(SmsProtocolEnum).includes(<SmsProtocolEnum>form.smsProtocol)
      ? form.smsProtocol
      : SmsProtocolEnum['CMPP'],
    configInfo: form.configInfo ?? undefined,

    notes: form.notes ?? undefined,
  }

  // 请求接口
  let error
  if (props.isEdit) {
    // 编辑
    const [err, _] = await to(smsProviderModel.editProvider(params))
    error = err
  } else {
    // 新建
    const [err, _] = await to(smsProviderModel.addProvider(params))
    error = err
  }

  // 返回失败结果
  if (error) {
    ElMessage({
      message: '保存失败',
      type: 'error',
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }

  // 返回成功结果
  ElMessage({
    message: '保存成功',
    type: 'success',
  })
  // 关闭弹窗
  closeDialog()
  emits('confirm')
  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 更新表单
 */
const updateForm = () => {
  Object.assign(form, JSON.parse(JSON.stringify(props.data)))
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    updateForm()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
</style>
