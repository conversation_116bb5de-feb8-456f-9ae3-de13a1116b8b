<template>
  <!--模块标题-->
  <HeaderBox title="统计报表" :can-refresh="true" @refresh="search"/>
  <!--模块主体-->
  <el-scrollbar class="module-container tw-min-w-[1080px]">
    <!-- 运营端选择账号 -->
    <div v-if="accountType===0" class="tw-mr-[12px] tw-mb-[8px] tw-w-full tw-flex tw-items-center tw-shrink-0">
      <span class="tw-text-[16px] tw-font-[600] tw-shrink-0">主账号：</span>
      <div v-if="!isSelected" class="tw-flex tw-items-center">
      <SelectBox
        v-model:selectVal="globalStore.masterAccountGroupIds"
        :options="masterAccountList||[]"
        class="tw-w-[282px]"
        name="account"
        val="groupId"
        placeholder="选择主账号列表"
        filterable
        multiple
      >
      </SelectBox>
      <el-button v-if="!isSelected" link type="primary" @click="reselect(true)" class="tw-ml-[12px]">
        <el-icon size="--el-font-size-base">
          <SvgIcon name="filter" color="var(--el-color-primary)" />
        </el-icon>
        <span>筛选</span>
      </el-button>
      </div>
      <el-scrollbar v-if="isSelected" class="tw-w-full" view-class="tw-flex tw-text-left tw-gap-1 tw-items-center">
        <span
          v-for="(item, index) in filteredMasterAccountList||[]"
          :key="item.groupId"
          class="account-box"
          :class="searchForm.groupId === item.groupId ? 'active' : ''"
          @click="handleClickAccount(item.groupId)"
        >{{ (index+1) + '. ' + item.account }}</span>
        <el-button v-if="isSelected" link type="primary" @click="reselect(false)">重新筛选</el-button>
      </el-scrollbar>
    </div>

    <template v-if="searchForm.groupId">
      <!--实时概览：卡片(头部)-->
      <div class="tw-flex tw-items-center tw-w-full tw-mb-[8px]">
        <span class="tw-text-[16px] tw-font-[600] tw-text-[#313233]">实时概览</span>
        <span class="info-title tw-ml-[6px]">今日数据</span>
        <el-tooltip content="刷新实时概览" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updateStatistics">
            <SvgIcon name="reset" color="inherit" />
          </el-icon>
        </el-tooltip>
      </div>
      <div class="merchant-report-container">
        <div v-loading="loadingStatistics" class="tw-flex tw-w-full">
          <div class="tw-grid tw-grid-cols-5 tw-gap-[12px] tw-grow tw-mr-[12px]">
            <!--任务执行情况-->
            <div class="tw-h-[148px] card-box tw-flex-col">
              <div class="tw-flex tw-grow tw-justify-between tw-w-full">
                <div>
                  <div class="title-normal">
                    <span>任务执行情况</span>
                    <el-tooltip :show-after="500">
                      <template #content>
                        <div class="tw-text-left">
                          <h5>今日任务执行情况</h5>
                          <div>可执行：任务状态为“待执行+未完成”的任务数</div>
                          <div>执行中：任务状态为“执行中”的任务数</div>
                          <div>执行率： 执行过（执行中+未完成+已停止）的任务/全部任务</div>
                        </div>
                      </template>
                      <el-icon :size="12" class="tw-cursor-pointer tw-ml-[4px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
                    </el-tooltip>
                  </div>
                  <div class="title-small tw-mt-[8px]">执行中</div>
                  <div class="info-data-content tw-mt-[8px]">{{ formatNumber1(statistics?.executingTasksNum) }}</div>
                </div>
                <div class="tw-grow-0 tw-shrink-0 tw-w-[80px] tw-h-[80px]">
                  <el-progress type="circle" color="var(--el-color-primary)" :percentage="statistics?.executingRates" stroke-linecap="square" :width="80" :stroke-width="12">
                    <span class="tw-text-[16px] tw-font-[600]">
                      {{ formatNumberPercent(statistics?.executingRates, 1) }}
                    </span>
                  </el-progress>
                </div>
              </div>
              <div class="tw-flex tw-w-full tw-justify-between tw-border-t-[1px] tw-pt-[8px] tw-mt-[8px]">
                <div class="title-small">可执行</div>
                <div class="info-data-content">{{ formatNumber1(statistics?.waitingTasksNum) }}</div>
              </div>
            </div>

            <!--名单执行进度-->
            <div class="tw-h-[148px] card-box tw-flex-col">
              <div class="tw-flex tw-grow tw-justify-between tw-w-full">
                <div>
                  <div class="title-normal">
                    <span class="tw-mr-[4px]">名单执行进度</span>
                    <el-tooltip :show-after="500">
                      <template #content>
                        <div class="tw-text-left">
                          <h5>今日名单执行进度</h5>
                          <div>已触达：各任务外呼次数>=1的名单数之和</div>
                          <div>计划：各任务名单总量之和</div>
                          <div>触达率： 已触达/计划</div>
                        </div>
                      </template>
                      <el-icon :size="12" class="tw-cursor-pointer tw-ml-[4px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
                    </el-tooltip>
                  </div>
                  <div class="title-small tw-mt-[8px]">已触达</div>
                  <div class="info-data-content tw-mt-[8px]">
                    {{ (!isMasterAccount && (formatNumber1(statistics?.planedNum||0) < formatNumber1(statistics?.calledNum||0))) ? statistics?.planedNum : statistics?.calledNum }}
                  </div>
                </div>
                <div class="tw-grow-0 tw-shrink-0 tw-w-[80px] tw-h-[80px]">
                  <el-progress type="circle" color="var(--el-color-primary)" :percentage="statistics?.calledRates" stroke-linecap="square" :width="80" :stroke-width="12">
                    <span class="tw-text-[16px] tw-font-[600]">
                      {{ formatNumberPercent(statistics?.calledRates, 1, isMasterAccount) }}
                    </span>
                  </el-progress>
                </div>
              </div>
              
              <div class="tw-flex tw-w-full tw-justify-between tw-border-t-[1px] tw-pt-[8px] tw-mt-[8px]">
                <!-- 触达率 -->
                <div class="title-small">计划</div>
                <div class="info-data-content">{{ formatNumber1(statistics?.planedNum) }}</div>
              </div>
            </div>

            <!--外呼执行进度-->
            <div class="tw-h-[148px] card-box tw-flex-col">
              <div class="tw-flex tw-grow tw-justify-between tw-w-full">
                <div>
                  <div class="title-normal">
                    <span class="tw-mr-[4px]">外呼执行进度</span>
                    <el-tooltip :show-after="500">
                      <template #content>
                        <div class="tw-text-left">
                          <h5>今日外呼执行进度</h5>
                          <div>已完成：各任务中当前“通话记录”的号码个数之和</div>
                          <div>计划：各任务中当前“通话记录+呼叫队列+补呼队列”的号码个数之和，不包含后续产生的补呼</div>
                          <div>实时完成率： 已完成/计划</div>
                        </div>
                      </template>
                      <el-icon :size="12" class="tw-cursor-pointer tw-ml-[4px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
                    </el-tooltip>
                  </div>
                  <div class="title-small tw-mt-[8px]">己完成</div>
                  <div class="info-data-content tw-mt-[8px]">{{ formatNumber1(statistics?.outboundCalledNum) }}</div>
                </div>
                <div class="tw-grow-0 tw-shrink-0 tw-w-[80px] tw-h-[80px]">
                  <el-progress type="circle" color="var(--el-color-primary)" :percentage="statistics?.outboundCalledRates" stroke-linecap="square" :width="80" :stroke-width="12">
                    <span class="tw-text-[16px] tw-font-[600]">
                      {{ formatNumberPercent(statistics?.outboundCalledRates, 1) }}
                    </span>
                  </el-progress>
                </div>
              </div>
              
              <div class="tw-flex tw-w-full tw-justify-between tw-border-t-[1px] tw-pt-[8px] tw-mt-[8px]">
                <!-- 触达率 -->
                <div class="title-small">计划</div>
                <div class="info-data-content">{{ formatNumber1(statistics?.outboundTotalNum) }}</div>
              </div>
            </div>

            <!--外呼执行进度-->
            <div class="tw-h-[148px] card-box tw-flex-col">
              <div class="tw-flex tw-grow tw-justify-between tw-w-full">
                <div>
                  <div class="title-normal">
                    <span class="tw-mr-[4px]">短信成功率</span>
                    <el-tooltip :show-after="500">
                      <template #content>
                        <div class="tw-text-left">
                          <h5>短信成功率</h5>
                          <div>发送成功：账号内短信状态为“发送成功”的数量之和</div>
                          <div>触发：账号内触发短信的数量之和</div>
                          <div>成功率：发送成功/触发</div>
                        </div>
                      </template>
                      <el-icon :size="12" class="tw-cursor-pointer tw-ml-[4px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
                    </el-tooltip>
                  </div>
                  <div class="title-small tw-mt-[8px]">发送成功</div>
                  <div class="info-data-content tw-mt-[8px]">{{ formatNumber1(statistics?.successSmsNum) }}</div>
                </div>
                <div class="tw-grow-0 tw-shrink-0 tw-w-[80px] tw-h-[80px]">
                  <el-progress type="circle" color="var(--el-color-primary)" :percentage="statistics?.successSmsRate" stroke-linecap="square" :width="80" :stroke-width="12">
                    <span class="tw-text-[16px] tw-font-[600]">
                      {{ formatNumberPercent(statistics?.successSmsRate, 1) }}
                    </span>
                  </el-progress>
                </div>
              </div>
              
              <div class="tw-flex tw-w-full tw-justify-between tw-border-t-[1px] tw-pt-[8px] tw-mt-[8px]">
                <!-- 触达率 -->
                <div class="title-small">触发</div>
                <div class="info-data-content">{{ formatNumber1(statistics?.triggerSmsNum) }}</div>
              </div>
            </div>

            <!-- 首呼、补呼、平均通时、意向客户等 -->
            <div class="tw-grid tw-grid-cols-2 tw-gap-[12px] tw-shrink-0">
              <div class="info-data-box-inner">
                  <span class="info-title">
                    <span>首呼剩余</span>
                    <el-tooltip :show-after="500">
                      <template #content>
                        <div class="tw-text-left"><div>今日首呼剩余：各任务“呼叫队列”名单数之和</div></div>
                      </template>
                      <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
                    </el-tooltip>
                  </span>
                  <span class="info-data-content">
                    {{ formatNumber1(statistics?.firstCallRemainNum) }}
                  </span>
              </div>

              <div class="info-data-box-inner">
                <span class="info-title">
                  <span>补呼剩余</span>
                  <el-tooltip :show-after="500">
                    <template #content>
                      <div class="tw-text-left"><div>今日补呼剩余：各任务“补呼队列”名单数之和</div></div>
                    </template>
                    <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
                  </el-tooltip>
                </span>
                <span class="info-data-content">
                  {{ formatNumber1(statistics?.recalledRemainNum) }}
                </span>
              </div>

              <div class="info-data-box-inner">
                <span class="info-title">
                  <span>平均通时(s)</span>
                  <el-tooltip :show-after="500">
                    <template #content>
                      <div class="tw-text-left"><div>今日平均通时（s）：各任务通话记录的通话时长之和/各任务接通次数之和</div></div>
                    </template>
                    <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
                  </el-tooltip>
                </span>
                <span class="info-data-content">
                  {{ formatNumber((statistics?.averageCallDurations || 0) / 1000, 1) }}
                </span>
              </div>

              <div class="info-data-box-inner ">
                <span class="info-title">
                  <span>意向客户数</span>
                  <el-tooltip :show-after="500">
                    <template #content>
                      <div class="tw-text-left"><div>今日意向客户数：意向分类为{{taskStore.intentionClassScope}}的名单数之和</div></div>
                    </template>
                    <el-icon :size="12" class="tw-cursor-pointer tw-ml-[2px]" color="var(--primary-black-color-400)"><SvgIcon name="warning" /></el-icon>
                  </el-tooltip>
                </span>
                <span class="info-data-content">
                  {{ formatNumber1(statistics?.intentionNums) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!--数据统计-->
      <div class="tw-flex tw-flex-col">
        <div class="tw-flex tw-justify-between tw-w-full tw-mb-[8px]">
          <span class="info-title-deep-large tw-flex tw-items-center">
            数据统计
            <el-tooltip content="刷新数据统计" placement="right" :show-after="500">
              <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updateCharts">
                <SvgIcon name="reset" color="inherit" />
              </el-icon>
            </el-tooltip>
          </span>
        </div>

        <!-- 数据统计-条件筛选部分 -->
        <div class="search-box">
          <div class="tw-grid tw-grid-cols-5 tw-gap-[8px] tw-border-b-[1px] tw-pb-[8px]">
            <div class="item">
              <el-select v-model="searchForm.aiOutboundTaskType" placeholder="任务类型" class="tw-w-full" clearable @change="handleDateAndTaskTypeChange">
                <el-option v-for="item in taskTypeList" :key="item.value" :label="item.name" :value="item.value"/>
              </el-select>
            </div>
            <div class="item">
              <el-select v-model="searchForm.taskStatusList" multiple collapse-tags placeholder="任务状态" class="tw-w-full" clearable>
                <el-option v-for="item in taskStatusOptionList" :key="item" :label="item" :value="item"/>
              </el-select>
            </div>
            <div class="item">
              <SelectBox
                v-model:selectVal="searchForm.scriptStringIdList"
                :options="speechCraftList||[]"
                name="scriptName"
                val="scriptStringId"
                key="scriptStringId"
                placeholder="外呼话术"
                filterable
                class="tw-grow"
                multiple
                canSelectAll
              >
              </SelectBox>
            </div>
            <div class="item">
              <SelectPageBox
                v-model:selectVal="searchForm.taskIdList"
                :options="taskList||[]"
                name="taskName"
                val="id"
                key="id"
                placeholder="外呼任务"
                :filterable="true"
                class="tw-grow"
                isRemote
                @update:options="updateTaskList"
                :loading="loadingTask"
                :total="taskNum"
                multiple
                canSelectAll
              >
              </SelectPageBox>
            </div>
            <div class="item">
              <!--日期选择器-->
              <el-date-picker
                v-model="searchForm.queryDate"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择日期"
                style="width: 100%"
                :clearable="false"
                :shortcuts="shortcuts"
                :disabledDate="disabledDate"
                @change="handleDateAndTaskTypeChange"
              />
            </div>
            <template v-if="isExpand">
              <OperationAndCitySelectBox
                v-model:operator="searchForm.operator"
                v-model:province="searchForm.provinceCode"
                v-model:city="searchForm.cityCode"
              />
            </template>
          </div>
          <div class="tw-flex tw-justify-end tw-pt-[8px] tw-items-center tw-h-[28px]">
            <div>
              <el-button type="primary" @click="clearSearchForm" link>
                <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
                <span>重置</span>
              </el-button>
              <el-button type="primary" @click="updateCharts()" link>
                <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
                <span>查询</span>
              </el-button>
              <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>
                收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon>
              </el-button>
              <el-button type="primary" v-else @click="isExpand=true" link>
                展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
        <!-- 数据统计-具体内容 -->
        
        <!-- 数据统计-标签部分，数据概览 | 数据详情 -->
        <div class="tw-flex tw-w-full tw-my-[8px]" >
          <RadioButtonBox v-model:active="viewType" :list="viewTypeList" @update:active="updateActiveViewType"/>
        </div>

        <!-- 外呼统计-数据概览 | 数据详情具体内容 -->
        <keep-alive>
          <CallStatistics v-if="viewType==='数据概览'" v-model:needUpdate="needUpdate[0]" :filterData="searchForm"></CallStatistics>
          <StatisticChartBox v-else-if="['数据详情',].includes(viewType)" :filterData="searchForm" v-model:refresh="needUpdate[1]"></StatisticChartBox>
          <div v-else><el-empty></el-empty></div>
        </keep-alive>
      </div>
    </template>
    <div v-else><el-empty></el-empty></div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, ref, onUnmounted, computed } from 'vue'
import { formatNumber, formatNumber1, formatNumberPercent, Throttle, enum2Options } from '@/utils/utils'
import { AccountStatisticsSearchParams, TaskStatusEnum, TaskTypeEnum, AccountStatistics } from '@/type/task'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { useTaskStore } from '@/store/taskInfo'
import { useUserStore } from '@/store/user'
import dayjs from 'dayjs'
import { statisticsModel } from '@/api/ai-report'
import { SpeechCraftInfoItem } from '@/type/speech-craft'
import RadioButtonBox from '@/components/RadioButtonBox.vue'
import { useGlobalStore } from '@/store/globalInfo'
import { ArrowUp, ArrowDown, } from '@element-plus/icons-vue'
import { onBeforeRouteLeave } from 'vue-router'
import { SearchRecordFormOrigin, disabledDate, shortcuts } from './constant'
import SelectPageBox from '@/components/SelectPageBox.vue'
import StatisticChartBox from './StatisticChartBox.vue';
import SelectBox from '@/components/SelectBox.vue';
import to from 'await-to-js'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { scriptTableModel, } from '@/api/speech-craft'
import { ElMessage } from 'element-plus'
import OperationAndCitySelectBox from '@/components/OperationAndCitySelectBox.vue'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const CallStatistics = defineAsyncComponent(() => import('./CallStatistics.vue'))

const taskStore = useTaskStore()
const userInfo = useUserStore()
const globalStore = useGlobalStore()
const isMasterAccount = userInfo.isMasterAccount
const accountType = userInfo.accountType

// 全部筛选条件
const searchForm = reactive<AccountStatisticsSearchParams>(new SearchRecordFormOrigin())

/**------------------------------------------运营端选择账号---------------------------------------- */
// 运营端：选择全部主账号groupId
const masterAccountList = ref<{
  account: string,
  groupId: string,
}[]>([]) // 主账号列表，全量

// 初筛后的主账号列表（account、groupId），和缓存groupId列表对应globalStore.masterAccountGroupIds
const filteredMasterAccountList = computed(() =>  masterAccountList.value?.filter(item => globalStore.masterAccountGroupIds?.includes(item.groupId)))

const isSelected = ref(false) // 主账号是否完成初筛状态，false：筛选框，多选； true: 完成初筛状态，平铺选项，进行单选
// 通过按钮切换是否完成初筛状态
const reselect = (val: boolean) => {
  isSelected.value = val
  if (val) {
    handleClickAccount(globalStore.masterAccountGroupIds[0])
  }
}

// 进入该页面，读取缓存，并选择第一个选项进行搜索
const getMasterAccountList = async () => {
  const [err, res] = await to(monitorStatisticModel.getAllMainAccount())
  masterAccountList.value = (res || []).filter(item => !!item.account && !!item.groupId)
  globalStore.masterAccountGroupIds = globalStore.masterAccountGroupIds?.length > 0 ? globalStore.masterAccountGroupIds : [masterAccountList.value[0]?.groupId]
  if (globalStore.masterAccountGroupIds?.length > 0) {
    isSelected.value = true
    handleClickAccount(globalStore.masterAccountGroupIds[0])
  }
  // handleGroupIdChange()
}

// 点击切换主账号
const handleClickAccount = async (val?: string) => {
  searchForm.groupId = val
  if (!searchForm.groupId) return
  await updateGroupIdInfo()
  search()
}
/**------------------------------------------运营端选择账号 结束---------------------------------------- */

/** ---------------------------------------- 实时概览:卡片(头部) 开始---------------------------------------- */
// 正在加载实时概览
const loadingStatistics = ref(false)
// 实时概览节流锁
const throttleStatistics = new Throttle(loadingStatistics)
// 实时概览
const statistics = ref<AccountStatistics | null>(null)
// 更新实时概览
const updateStatistics = async () => {
  if (accountType === 0 && !searchForm.groupId) return
  // 节流锁上锁
  if (throttleStatistics.check()) return
  throttleStatistics.lock()
  const [err, res] = await to(accountType === 0 ? statisticsModel.getTaskStatisticsByGroupId({
    queryDate: dayjs().format('YYYY-MM-DD'),
    groupId: searchForm.groupId!,
  }) : statisticsModel.getTaskStatistics({
    queryDate: dayjs().format('YYYY-MM-DD')
  }))
  statistics.value = res || null
  throttleStatistics.unlock()
}

/** ---------------------------------------- 实时概览 结束 ---------------------------------------- */

/** ---------------------------------------- 数据统计-条件筛选部分 开始---------------------------------------- */
// 报表日期
const taskStatusOptionList = Object.values(TaskStatusEnum).filter(item => item !== '全部')
const taskTypeList = enum2Options(TaskTypeEnum)
const speechCraftList = ref<SpeechCraftInfoItem[] | null>([])
const taskList = ref<{id: number, taskName: string}[] | null>([])
const isExpand = ref(false)

const clearSearchForm = () => {
  Object.assign(searchForm, new SearchRecordFormOrigin())
  searchForm.groupId = accountType === 0 ? globalStore.masterAccountGroupIds[0] : userInfo.groupId
}
// 切换日期，更新任务列表
const handleDateAndTaskTypeChange = () => {
  searchForm.taskIdList = []
  updateTaskList()
}
/**---------------------------------------数据统计-条件筛选部分 结束----------------------------------------*/

/**---------------------------------------数据统计-标签 开始----------------------------------------*/
const viewTypeList = ref(['数据概览', '数据详情', ])
const viewType = ref(viewTypeList.value[0])
const needUpdate = ref([false, false])
const updateCharts = () => {
  // 更新图表数据
  needUpdate.value = [true, true]
}
// 切换tab
const updateActiveViewType = () => {
  if (viewType.value !== '数据详情') {
    updateCharts()
  }
}


/**---------------------------------------数据统计-标签 结束----------------------------------------*/

// 刷新全部数据
const search = () => {
  // 更新实时概览
  updateStatistics()
  // 更新数据统计
  updateCharts()
}


const taskNum = ref(0)
const loadingTask = ref(false)
const updateTaskList = async (val: string = '', startPage: number = 0, pageSize: number = 50) => {
  loadingTask.value = true
  const params = {
    startTime: dayjs(searchForm.queryDate).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs(searchForm.queryDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
    taskName: val ? val : undefined,
    taskType: searchForm.aiOutboundTaskType,
    startPage: startPage,
    pageNum: pageSize,
  }
  const [_, data] = await to(accountType === 0
    ? aiOutboundTaskModel.findTaskListByGroupId({
      ...params,
      groupId: searchForm.groupId!,
    }) 
    : aiOutboundTaskModel.findTaskList(params)
  )
  taskNum.value = data?.total || 0
  taskList.value = data?.data as {id: number, taskName: string}[] || []
  loadingTask.value = false
}
// 更新全部涉及账号的信息
const updateGroupIdInfo = async () => {
  if (!searchForm.groupId) return ElMessage.warning('请先选择账号')
  // 获取意向客户设置
  await to(taskStore.getIntentionClassScope(searchForm.groupId, accountType))
  // 初始化任务列表
  updateTaskList()
  // 获取话术列表
  const [_, data1] = await to(scriptTableModel.findScriptByGroupId({
    groupId: searchForm.groupId,
  })) as [any, SpeechCraftInfoItem[]]
  speechCraftList.value = data1 || []
  searchForm.taskIdList = undefined
  searchForm.scriptStringIdList = undefined
}

// 初始化数据
const init = async () => {
  if (accountType === 0) {
    await getMasterAccountList()
  } else {
    searchForm.groupId = userInfo.groupId
    if (!searchForm.groupId) return ElMessage.warning('请先选择账号')
    updateGroupIdInfo()
    // 查询统计数据
    search()
  }
}

init()

const clearAllData = () => {
  Object.assign(searchForm, new SearchRecordFormOrigin())
  taskList.value = null
  speechCraftList.value = null
}

onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})

</script>

<style scoped lang="postcss">
.merchant-report-container {
  box-sizing: border-box;
  overflow-y: auto;
  width: 100%;
  min-width: 1000px;
  padding-bottom: 12px;
  .info-data-box-inner {
    background-color: #fff;
    justify-content: center;
    padding: 0 8px;
  }
  .info-title {
    display: flex;
    align-items: center;
  }
  .title-normal {
    font-size: 15px;
    font-weight: 600;
    color: var(--primary-black-color-600);
    line-height: 20px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.title-small {
  font-size: 13px;
  color: var(--primary-black-color-400);
  line-height: 20px;
  text-align: left;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}
.account-box {
  border-radius: 4px;
  border: 1px solid var(--primary-black-color-300);
  padding: 5px 5px;
  line-height: 20px;
  height: 32px;
  font-size: 13px;
  color: var(--primary-black-color-400);
  cursor: pointer;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 120px;
  flex-shrink: 0;
  background-color: white;
  &.active {
    border-color: var(--el-color-primary);
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
</style>
