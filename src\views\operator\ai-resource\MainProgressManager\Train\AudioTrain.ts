import { ref } from 'vue'
import { scriptTrainAudioModel } from '@/api/speech-craft'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useScriptStore } from '@/store/script'
import { useScriptTrainStore } from '@/store/script-train'
import { IdleAccount, ScriptTrainStatusEnum, ScriptTrainTypeEnum } from '@/type/speech-craft'
import janusUtil, { sipCallList } from '@/utils/janus'
import {
  ConstructorOptions,
  JSEP,
  OfferParams,
  PluginCreateAnswerParam,
  PluginHandle,
  PluginOptions,
  SipEventMessage
} from '@/type/janus'
import { iceServers, janusServer } from '@/assets/js/constant'

// ---------------------------------------- 通用 开始 ----------------------------------------

const scriptStore = useScriptStore()
const scriptTrainStore = useScriptTrainStore()

// 是否注册FS
const registered = ref(false)
// 是否通话中
const busy = ref(false)
// 对方音频流缓存列表
const remoteAudioDomList = ref<{ [prop: string | number]: HTMLAudioElement }>({})

/**
 * 重置语音训练所有数据
 */
export const resetAudioTrainData = () => {
  // 重置FS账号
  resetFsAccount()
  registered.value = false
  busy.value = false
  remoteAudioDomList.value = {}
}

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 开关 开始 ----------------------------------------

/**
 * 结束语音训练
 */
export const endAudioTrain = () => {
  try {
    if (busy.value) {
      // 通知Janus服务器挂断电话
      sipCallList[instanceId]?.send({
        message: {
          request: "hangup"
        }
      })
      console.log('***', '发送动作请求', '挂断')
      // 用上return，则等服务器真正挂断后，浏览器收到挂断hangup消息，才注销账号和销毁Janus实例
      // 不用return，则不管服务器是否挂断，浏览器立马注销账号和销毁Janus实例
      // return
    }

    // 通知接口结束训练
    scriptTrainAudioModel.stopAudioTrain({
      account: fsAccount.value.account,
      id: fsAccount.value.id,
    }).then(() => {
    }).catch(() => {
    })

    // 注销Janus实例
    destroyJanus()
    // 重置状态
    scriptTrainStore.resetStatus()
    // 重置语音训练所有数据
    resetAudioTrainData()
    // 停止检查话术定时器
    scriptTrainStore.closeCheckingTimer()
    // 停止训练计时
    scriptTrainStore.closeTrainTimer()
    // 更新训练历史
    scriptTrainStore.needUpdate.history = true
    // 关闭定时查询对话详情
    scriptTrainStore.needUpdate.dialogRepeat = false
    // 更新对话详情
    scriptTrainStore.needUpdate.dialog = true
    // 更新通话记录
    scriptTrainStore.needUpdate.record = true

    ElMessage.warning('已结束语音训练')
  } catch (e) {
  }
}
/**
 * 检查语音训练前置条件
 */
const checkAudioCondition = () => {
  return new Promise(async (resolve, reject) => {
    try {
      // 训练状态改为检查媒体
      scriptTrainStore.status = ScriptTrainStatusEnum.CHECK_MEDIA
      // 检测音频输入设备（麦克风）
      // 尝试申请麦克风权限
      const stream: MediaStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: false
      })
      console.log('有麦克风权限，可以开始训练')
      // 检测完立马关闭麦克风，不需要占用过多时间
      try {
        // 模拟异常
        stream.getTracks().forEach((track: MediaStreamTrack) => {
          track.stop()
        })
      } catch (e) {
        console.warn('测试麦克风后关闭失败')
      }
      resolve(true)
    } catch (e) {
      console.error('没有麦克风权限')
      ElMessageBox.alert('请检查浏览器麦克风权限', '无法接通电话', {
        confirmButtonText: '好',
      }).then(() => {
      }).catch(() => {
      })
      reject(false)
    }
  })
}
/**
 * 开始语音训练
 * 点击开始训练后，应该优先进行耗时较短的检查，比如麦克风权限、Janus服务、FS账号，
 * 而话术检查放在最后，等话术检查也通过后，开始注册FS账号并调用开始训练账号，等待AI来电
 */
export const startAudioTrain = async () => {
  // 重置状态
  scriptTrainStore.resetStatus()
  // 训练类型改为语音训练
  scriptTrainStore.trainType = ScriptTrainTypeEnum.AUDIO

  try {
    // 检查麦克风
    await checkAudioCondition()

    // 获取FS账号
    await getFsAccount()
    // 检查FS账号
    checkFsAccount()

    // 开始检查话术
    await scriptTrainStore.launchCheckScript(createJanus, endAudioTrain)
  } catch (e) {
    // 结束训练
    endAudioTrain()
  }
}

// ---------------------------------------- 开关 结束 ----------------------------------------

// ---------------------------------------- 事件 开始 ----------------------------------------

/**
 * 提交事件
 */
export const submitAudioTrainEvent = async () => {
  if (!scriptTrainStore.eventSelectedList.length) {
    ElMessage({
      message: '没有选择任何事件',
      type: 'warning'
    })
    return
  }

  try {
    await scriptTrainAudioModel.submitAudioTrainEvent({
      phone: fsAccount.value.account,
      stage: scriptTrainStore.eventSelectedList.join(',') ?? ''
    })
    ElMessage({
      message: '事件提交成功',
      type: 'success'
    })
  } catch (e) {
    ElMessage({
      message: '事件提交失败',
      type: 'error'
    })
  }
}

// ---------------------------------------- 事件 结束 ----------------------------------------

// ---------------------------------------- FS账号 开始 ----------------------------------------

// FS账号，默认值
const fsAccountDefault = () => {
  return {
    id: null,
    account: null,
    password: null,
    ip: null
  }
}
// FS账号
const fsAccount = ref<IdleAccount>(fsAccountDefault())

/**
 * 重置FS账号
 */
export const resetFsAccount = () => {
  fsAccount.value = fsAccountDefault()
}
/**
 * 获取空闲FS账号
 */
export const getFsAccount = async () => {
  try {
    // 请求接口
    const res = <IdleAccount>await scriptTrainAudioModel.getIdleFsAccount()
    // 仅用于调试
    // res = {
    //   id: 3,
    //   account: '1004',
    //   password: '1234',
    //   // ip: '**************',
    //   ip: '***************',
    // }

    // 更新账号信息
    fsAccount.value = { ...res }
  } catch (e) {
    ElMessage({
      message: '无法获取用于训练的电话账号',
      type: 'error'
    })
    // 结束训练
    endAudioTrain()
  }
}
/**
 * 检查账号
 */
export const checkFsAccount = () => {
  // 训练状态改为检查账号
  scriptTrainStore.status = ScriptTrainStatusEnum.CHECK_ACCOUNT

  const check = !!(fsAccount.value.account
    && fsAccount.value.password
    && fsAccount.value.ip)
  console.log('账号信息检查', check)

  if (!check) {
    ElMessageBox.alert('FS账号不正确：' + fsAccount.value.account + '@' + fsAccount.value.ip, '无法开始语音训练', {
        confirmButtonText: '关闭',
        type: 'warning',
      }
    ).then(() => {
    }).catch(() => {
    })
  }
  return check
}

// ---------------------------------------- FS账号 结束 ----------------------------------------

// ---------------------------------------- Janus服务 开始 ----------------------------------------

// Janus实例ID
const instanceId = 'train'

// 接打电话操作，这是一个方法，RTCPeerConnection里的offer或者answer方法
let sipCallAction: Function | null | undefined = null
let sipCallActionParams: OfferParams | PluginCreateAnswerParam = {}

/**
 * 处理Janus SIP事件
 * @param {SipEventMessage} msg 消息内容
 * @param {JSEP} jsepData JSEP
 */
const handleJanusSipEvent = (msg: SipEventMessage, jsepData: JSEP) => {
  // 根据事件名找到对应的事件处理
  const eventHandler: { [eventName: string]: Function } = {
    registering: () => {
      console.log('***', '账号正在注册')
      registered.value = false
    },
    registration_failed: () => {
      console.log('***', '账号注册失败')
      registered.value = false
      ElMessage.error('账号注册失败')
      // 停止训练
      endAudioTrain()
    },
    registered: async () => {
      console.log('***', '账号注册成功', msg?.result?.username)
      registered.value = true
      console.log('**********', '语音训练', '开始训练', 'scriptLongId', scriptStore.id, 'scriptStore.scriptStringId', scriptStore.scriptStringId, 'phone', fsAccount.value.account)
      // 调用开始训练接口
      try {
        const res = <string | null>await scriptTrainAudioModel.startAudioTrain({
          phone: fsAccount.value.account,
          scriptLongId: scriptStore.id,
        })

        scriptTrainStore.recordId = res ?? ''
      } catch (e: any) {
        // 停止训练
        endAudioTrain()
        // 提示错误
        let errStr = e?.message ?? '开始训练接口发生错误'
        if (e?.data?.message) {
          errStr = '开始训练接口发生错误：' + e?.data?.message
        }
        ElMessageBox.alert(errStr, '无法开始语音训练', {
            confirmButtonText: '确定',
          },
        ).then(() => {
        }).catch(() => {
        })
      }
    },
    unregistered: () => {
      console.log('***', '账号已注销', msg?.result?.username)
      // 更新注册状态
      registered.value = false
      // 销毁Janus实例
      destroyJanus()
    },
    incomingcall: () => {
      console.log('***', '来电', msg?.result, 'JSEP', JSON.parse(JSON.stringify(jsepData)))

      let tracks = []
      tracks.push({ type: 'audio', capture: true, recv: true })

      sipCallAction = jsepData ? sipCallList[instanceId]?.createAnswer : sipCallList[instanceId]?.createOffer
      sipCallActionParams = {
        jsep: jsepData,
        tracks: [
          { type: 'audio', capture: true, recv: true }
        ],
        success: function (data: JSEP) {
          console.log('***', '接听 sipCallAction 执行成功', 'SDP', data.type)
          // 发送接听指令
          sipCallList[instanceId]?.send({
            message: {
              request: "accept",
              autoaccept_reinvites: false
            },
            jsep: data,
          })
        },
        error: function (error: string) {
          console.error('***', '接听 sipCallAction 执行失败', error)
          ElMessageBox.alert(`错误原因：${error}`, '无法接通电话', {
            confirmButtonText: '关闭',
          }).then(() => {
          }).catch(() => {
          })
          // 结束训练
          endAudioTrain()
        }
      }

      if (sipCallAction) {
        console.log('----------', '尝试接听')
        try {
          sipCallAction(sipCallActionParams)
        } catch (e) {
        }
      }
    },
    accepted: () => {
      console.log('***', '接听')
      // 更新训练状态
      scriptTrainStore.status = ScriptTrainStatusEnum.IN_CALL
      console.log('----------', '接通电话')
      ElMessage({
        message: '电话已接通',
        type: 'success',
      })
      busy.value = true
      // 启动训练时长定时器
      scriptTrainStore.openTrainTimer()
      // 开始查询对话详情
      scriptTrainStore.needUpdate.dialog = true
      // 开启定时查询对话详情
      scriptTrainStore.needUpdate.dialogRepeat = true
    },
    progress: () => {
      console.log('***', '处理早期媒体')
      // 早期媒体，电话拨打中但还没接通时
      sipCallList[instanceId]?.handleRemoteJsep({
        jsep: jsepData,
        success: function (data: JSEP) {
          console.log('***', '处理早期媒体成功', data)
          busy.value = true
        },
        error: function (error: string) {
          console.error('***', '处理早期媒体失败', error)
          busy.value = false
        }
      })
    },
    hangup: () => {
      console.log('***', '挂断电话')
      // 停止播放音频
      Object.values(remoteAudioDomList.value).forEach((audio) => {
        if (audio) {
          audio.pause()
          audio.srcObject = null
          audio.load()
          console.info('***', '停止播放对方音频流')
        }
      })
      busy.value = false
      ElMessage.warning('电话已挂断')
      // 停止训练
      endAudioTrain()
    },
  }

  return (eventName: string) => {
    eventHandler[eventName] && eventHandler[eventName]()
  }
}
/**
 * 初始化Janus
 */
const createJanus = async () => {
  // 初始化Janus并创建实例对象
  janusUtil.init(instanceId, <ConstructorOptions>{
    server: janusServer,
    iceServers,
    success: function () {
      // 创建成功后，加载SIP插件
      janusUtil.attach(instanceId, <PluginOptions>{
        plugin: "janus.plugin.sip",
        opaqueId: "sip-" + janusUtil.randomString(12),
        success: function (handle: PluginHandle) {
          // SIP插件加载完成
          sipCallList[instanceId] = handle
          register()
        },
        error: function () {
          ElMessage({
            message: 'Janus SIP 插件加载失败',
            type: 'error'
          })
          // 结束训练
          endAudioTrain()
        },
        onmessage: async function (msg: SipEventMessage, jsepData: JSEP) {
          // 事件名
          const event: string = msg?.result?.event ?? ''
          console.log('###', 'Janus 事件', event)

          // 处理事件
          handleJanusSipEvent(msg, jsepData)(event)
        },
        onlocaltrack: function (track: MediaStreamTrack, on: boolean) {
          console.log('***', '本地媒体流是否激活', on, '本地媒体流是否静音', track.muted, '本地媒体流', track)
        },
        onremotetrack: function (track: MediaStreamTrack, mid: string, on: boolean) {
          console.log('***', '远程媒体流是否激活', on, '远程媒体流是否静音', track.muted, '远程媒体流', track)
          // 如果媒体流是音频、没有静音、激活状态
          if (track?.kind === 'audio' && !track.muted && on) {
            // 将音频流添加到本地缓存列表
            remoteAudioDomList.value[mid] = new Audio()
            remoteAudioDomList.value[mid].srcObject = new MediaStream([track])

            // 播放音频，播放前需要音频元素重新加载源
            // 音频播放是异步的，所以需要Promise
            remoteAudioDomList.value[mid].load()
            remoteAudioDomList.value[mid].play().then(() => {
              console.log('***', '开始播放对方音频流', '媒体ID', mid, '对方音频流缓存列表 remoteAudioDomList', remoteAudioDomList.value)
            }).catch((err) => {
              console.error('***', '无法播放对方音频流', err)
            })
          }
        },
      })
    },
    error: function () {
      ElMessage({
        message: 'Janus实例发生错误',
        type: 'error'
      })
      // 结束训练
      endAudioTrain()
    },
  })
}
/**
 * 销毁Janus实例
 */
const destroyJanus = () => {
  if (registered.value) {
    unregister()
  } else {
    janusUtil.destroy(instanceId)
  }
}
/**
 * 注册账号
 */
const register = () => {
  sipCallList[instanceId]?.send({
    message: {
      // 事件名，注册FS账号，必填
      request: 'register',
      // 强制使用TCP，选填，默认false不强制
      force_tcp: true,
      // SIP服务器地址，格式 sip:**************[:5060]，必填
      proxy: `sip:${fsAccount.value.ip}`,
      // SIP账号，格式 sip:1016@**************[:5060]，必填
      username: `sip:${fsAccount.value.account}@${fsAccount.value.ip}`,
      // SIP用户名，如果指定此字段，则覆盖上面的SIP账号字段，选填，默认空字符串
      authuser: fsAccount.value.account,
      // SIP密码，必填
      secret: fsAccount.value.password,
      // 展示的昵称，选填，默认空字符串
      display_name: fsAccount.value.account,
    }
  })
  console.log('@@@', '注册账号信息', JSON.parse(JSON.stringify(fsAccount.value)))
}
/**
 * 取消注册账号
 */
const unregister = () => {
  sipCallList[instanceId]?.send({
    message: {
      request: 'unregister',
    },
  })
  console.log('***', '发送动作请求', '注销')
}

// ---------------------------------------- Janus服务 结束 ----------------------------------------
