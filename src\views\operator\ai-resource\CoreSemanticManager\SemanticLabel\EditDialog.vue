<template>
  <el-dialog
    v-model="dialogVisible"
    width="450px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-form
      ref="addFormRef"
      :model="addData"
      :rules="rules"
      label-width="90px"
    >
      <el-form-item label="语义标签：" prop="semanticLabel">
        <el-input
          v-model="addData.semanticLabel"
          type="text"
          clearable
          maxlength="10"
          show-word-limit
          placeholder="请输入语义标签名称，10汉字以内"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :icon="CloseBold" @click="cancel">取消</el-button>
        <el-button
          :disabled="addData.semanticLabel === props.addData.semanticLabel || !addData.semanticLabel.length"
          :loading="loading"
          type="primary"
          :icon="Select"
          @click="confirm"
        >
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { SemanticsLabelItem } from '@/type/core-semantic'
import type { FormInstance } from 'element-plus'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { scriptCoreSemanticModel } from '@/api/speech-craft'
import to from 'await-to-js'
import { pickAttrFromObj } from '@/utils/utils'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean
  addData: SemanticsLabelItem
}>()
const dialogVisible = ref(props.visible)
const title = computed(() => (props.addData && props.addData.id ? '编辑语义标签' : '新增语义标签'))
const loading = ref(false)
const addData = reactive<SemanticsLabelItem>(props.addData)
const addFormRef = ref<FormInstance | null>(null)
const rules = {
  semanticLabel: [
    { required: true, message: '请输入语义标签名称', trigger: 'blur' },
    { min: 1, max: 30, message: '语义标签名称，10汉字以内', trigger: 'blur' },
  ],
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = async () => {
  if (addData.semanticLabel === props.addData.semanticLabel) {
    return ElMessage({
      message: '请输入不同的语义标签名称',
      type: 'warning',
    })
  }
  addFormRef.value && addFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = pickAttrFromObj(addData, [
        'id', 'semanticLabel', 'secondIndustryId'
      ])
      const [err, res] = await to(scriptCoreSemanticModel.saveSemanticLabel(addData.id ? addData : params)) as [any, SemanticsLabelItem]
      loading.value = false

      if (!err) {
        ElMessage({
          message: '操作成功',
          type: 'success',
        })
        cancel()
        addData.semanticLabel = ''
        emits('confirm', res)
      }
    }
  })
}

watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    Object.assign(addData, props.addData)
    addFormRef.value && addFormRef.value.clearValidate()
  }
})
</script>

<style lang="postcss">
.el-input-number .el-input__inner {
  text-align: left;
}
</style>
