<template>
  <HeaderBox title="产品管理" />
  <el-scrollbar class="module-container tw-min-w-[1080px]" view-class="tw-h-full tw-flex tw-flex-col">
    <div class="tw-flex tw-justify-end tw-mb-[12px]"><el-button type="primary" :icon="Plus" @click="edit()">新增产品</el-button></div>
    <div class="search-box tw-w-full tw-p-[16px] tw-bg-white tw-mb-[16px]">
      <div class="tw-grid tw-grid-cols-4 tw-pb-[12px] tw-gap-x-[12px]">

        <div class="item-col">
          <span class="label">产品名称：</span>
          <el-input
            v-model="searchForm.name"
            placeholder="请输入"
            clearable
            @keyup.enter="search"
          >
          </el-input>
        </div>
        <div class="item-btn">
          <el-button type="primary" @click="search" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>

    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column property="id" label="ID" align="left" min-width="64" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="productName" label="产品名称" align="left" min-width="280" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="industrySecondFieldName" label="行业" align="left" min-width="120">
        <template #default="{ row }">
          {{ row.industrySecondFieldName }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="right" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">编辑</el-button>
          <el-button type="danger" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="tableData.length || 0"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </el-scrollbar>
  <EditDialog
    v-model:visible="editVisible"
    :rowData="editData"
    @confirm="search"
  >
  </EditDialog>
  <ConfirmDialog
    v-model:visible="relateProductVisible"
    :list="relateProduct"
  />
</template>

<script lang="ts" setup>
import { reactive, computed, ref, } from 'vue'
import EditDialog from './EditDialog.vue'
import { ElMessage, } from 'element-plus'
import { formatterEmptyData } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import { Plus } from '@element-plus/icons-vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { ProductItem, ProductItemOrigin } from '@/type/industry'
import { productModel } from '@/api/industry'
import { merchantModel } from '@/api/merchant'
import { MerchantInfo, } from '@/type/merchant'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { useGlobalStore } from '@/store/globalInfo'
import to from 'await-to-js'
import ConfirmDialog from './ConfirmDialog.vue'
import dayjs from 'dayjs'

const globalStore = useGlobalStore()
const loading = ref(false)


const tableData = ref<ProductItem[]>([])
class searchOrigin {
  name = ''
}
const searchForm = reactive(new searchOrigin())
const search = async () => {
  loading.value = true
  const [err, data] = await to(productModel.getList(searchForm))
  tableData.value = (data || []).sort((a,b) => dayjs(a.createTime).isAfter(dayjs(b.createTime)) ? -1 : 1)
  loading.value = false
}
// 分页
const currentPage = ref(1)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const tableTempData = computed(() => {
  return tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})




// 编辑产品
const editVisible = ref(false)
const editData = reactive<ProductItem>(new ProductItemOrigin())
const edit = (row?: ProductItem) => {
  editVisible.value = true
  Object.assign(editData, row || new ProductItemOrigin())
}

// 删除产品
const relateProductVisible = ref(false)
const relateProduct = ref<Record<string, string[]>>({})
const del = async (row: ProductItem) => {
  Confirm({ 
    text: `您确定要删除产品【${row.productName}】吗?`,
    type: 'danger',
    title: `删除确认`
  }).then(async () => {
    const [err, res] = await to(productModel.delete({id: row.id!}))
    if (res && res.length) {
      const merchants = await merchantModel.getMerchantList() as MerchantInfo[] || []
      const masterAccountList = await monitorStatisticModel.getAllMainAccount() || []
      relateProductVisible.value = true
      relateProduct.value  = {}
      res.forEach(item => {
        const accountName = masterAccountList.find(account => account.groupId === item.groupId)?.account || item.groupId || ''
        const tenantName = merchants.find(t => t.id == item.tenantId)?.tenantName || '未知商户'
        relateProduct.value[tenantName] ?  relateProduct.value[tenantName].push(accountName) : (relateProduct.value[tenantName] = [accountName])
      })
    } else {
      ElMessage.success('操作成功')
    }
    search()
  }).catch(() => {})
}

// 初始化页面
const init = async () => {
  await globalStore.getAllIndustryList()
}
init()
search()
</script>

<style scoped lang="postcss" type="text/postcss">
</style>