import { ElMessage } from "element-plus";
import { useUserStore } from "@/store/user"
import { useScriptStore } from '@/store/script'
import router from '@/router'
export const errorHandler = (code: number, err: any, message?: string): void => {
  let msg: string;
  const userStore = useUserStore();
  
  switch (+code) {
    case 400:
      msg = "客户端请求的语法错误，服务器无法理解";
      break;
    case 401:
      msg = "登录鉴权过期,请重新登录";
      // 回到登录页
      setTimeout(() => {
        userStore.logout()
        router.push("/login")
      }, 100)
      break;
    case 403:
      msg = "暂无权限！";
      // 回到登录页
      setTimeout(() => {
        userStore.logout()
        router.push("/login")
      }, 100)
      break;
    case 404:
      msg = `请求地址出错:${err?.response?.config?.url || ''}`;
      break;
    case 405:
      msg = "请求方式被禁止";
      break;
    case 408:
      msg = "请求超时";
      break;
    case 500:
      msg = "服务器内部错误，无法完成请求";
      break;
    case 501:
      msg = "服务器不支持请求的功能，无法完成请求";
      break;
    case 502:
      msg =
        "作为网关或者代理工作的服务器尝试执行请求时，从远程服务器接收到了一个无效的响应";
      break;
    case 503:
      msg =
        "由于超载或系统维护，服务器暂时的无法处理客户端的请求。延时的长度可包含在服务器的Retry";
      break;
    case 504:
      msg = "充当网关或代理的服务器，未及时从远端服务器获取请求";
      break;
    case 505:
      msg = "服务器不支持请求的HTTP协议的版本";
      break;
    case 666:
      msg = "话术已提交，请在新版本上进行编辑"
      // 用于话术编辑时，后端返回不可编辑，跳转回话术首页
      setTimeout(() => {
        const scriptStore = useScriptStore()
        scriptStore.clearScriptData()
        router.push({name: 'SpeechCraftManager'})
      }, 100)
      break;
    default:
      msg = `请求出错:${err?.message || ''}`;
  }
  const url: string | null | undefined = err?.config?.url
  if (url?.includes('traceLog/addTraceInfo') || url?.includes('/remote/logs/seat_workbench_web_log')) {
    // 埋点日志和坐席日志，自身的请求出现报错时，不需要在页面上提示
    return
  }
  msg = message || msg || '';
  ElMessage({
    message: msg.length > 50 ? msg.slice(0, 50) + '...' : msg,
    duration: 3000,
    type: "error",
  });
};
