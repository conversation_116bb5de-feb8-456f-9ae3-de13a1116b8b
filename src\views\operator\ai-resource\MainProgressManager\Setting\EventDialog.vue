<template>
  <el-dialog
    v-model="dialogVisible"
    align-center
    width="720px"
    @close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{addData.id ? '编辑':'新建'}}事件触发</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
    >
      <el-form
        :model="addData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
        :disabled="isChecked"
      >
        <div class="tw-w-[80px] tw-font-[600] tw-text-[var(--primary-black-color-600)] tw-text-left tw-mt-[16px] tw-mb-[4px]">基础配置</div>
        <el-form-item label="事件类型：" prop="eventName">
          <el-select
              v-model="addData.eventName" 
              clearable
              :disabled="!!addData.id"
              placeholder="请选择要使用的事件" 
              style="width:100%"
            >
              <el-option
                v-for="item in eventTypeList"
                :key="item.id"
                :label="item.eventTriggerType"
                :value="item.eventTriggerType"
              />
            </el-select>
        </el-form-item>
        <el-form-item label="备注：" prop="note">
          <el-input v-model="addData.note" clearable placeholder="请输入备注" maxlength="60"/>
        </el-form-item>
        <el-form-item v-if="addData.eventName" label-width="0px"  prop="eventValuemap">
          <div class="tw-flex tw-items-start tw-w-full">
            <span class="tw-w-[80px] tw-font-[600] tw-text-[var(--primary-black-color-600)] tw-text-left tw-my-[4px]">事件字段值：</span>
          </div>
          <el-row
            v-for="(item, index) in eventValues"
            class="tw-flex tw-justify-around tw-items-center tw-w-full tw-mb-1"
            :key="index"
          >
            <el-col :span="8" class="tw-flex">
              <span class="tw-w-[80px] tw-text-right tw-shrink-0">字段值：</span>
              <el-select
                v-model="item.valueId" 
                clearable 
                placeholder="请选择要使用的事件"
                :disabled="activeIndex!=index && !!item.valueId"
                style="width:142px"
              >
                <el-option
                  v-for="val in (activeIndex!=index && item.valueId ? eventValuesList : eventEnabledValuesList)"
                  :key="val.id"
                  :label="val.name"
                  :value="val.id"
                />
              </el-select>
            </el-col>
            <el-col :span="7" class="tw-flex tw-justify-center tw-items-center">
              <span class="tw-w-[60px] tw-text-right tw-shrink-0">释义：</span>
              <el-input
                v-model="item.explanation"
                clearable
                placeholder="请输入释义"
                maxlength="60"
                style="width:150px"
              />
            </el-col>
            <el-col :span="7" class="tw-flex tw-justify-center tw-items-center">
              <span class="tw-w-[60px] tw-text-right tw-shrink-0">备注：</span>
              <el-input v-model="item.note" clearable placeholder="请输入备注" maxlength="60"  style="width:150px"/>
            </el-col>
            <el-col :span="2">
              <el-button :disabled="!(eventValues && eventValues.length > 1)" link :type="(eventValues && eventValues.length > 1) ? 'primary': 'default'" @click="delEventValue(index)">
                删除
              </el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-button link type="primary" @click="addEventValue" style="height: 32px"><el-icon><SvgIcon name="add1"></SvgIcon></el-icon>新增事件字段</el-button>
          </el-row>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer" v-if="!isChecked">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">添加</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, } from 'vue'
import { EventItem, EventValueItem } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { pickAttrFromObj } from '@/utils/utils'
import type { FormInstance, } from 'element-plus'
import { scriptEventModel } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { QuestionFilled, CloseBold, Select } from '@element-plus/icons-vue'
import { trace } from '@/utils/trace'
const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked
const loading = ref(false)
const emits = defineEmits(['close', 'update',])
const props = defineProps<{
  visible: boolean;
  eventData: EventItem;
}>();
const activeIndex = ref(0)
const dialogVisible = ref(props.visible)
const addData = reactive<EventItem>(props.eventData)
const editRef = ref<FormInstance  | null>(null)
const eventValues = ref<EventValueItem[]>([])
const addEventValue = () => {
  if (eventValues.value) {
    if (eventValues.value.length >= eventValuesList.value.length) {
      return ElMessage({
        type: 'warning',
        message: '已经达到事件值上限',
      })
    }
    if (eventValues.value!.at(-1) && (!eventValues.value!.at(-1)!.valueId || !eventValues.value!.at(-1)!.explanation)) {
      return ElMessage({
        type: 'warning',
        message: '请填写事件字段值和释义',
      })
    }
    eventValues.value.push({ valueId: undefined, name: '', note: '', explanation: '', eventName: addData.eventName! })
    activeIndex.value = eventValues.value.length - 1
  }else {
    eventValues.value = [{ valueId: undefined, name: '', note: '', explanation: '', eventName: addData.eventName! }]
    activeIndex.value = 0
  }
}
const delArr = ref<number[]>([])
const delEventValue = async (index: number) => {
  const valueId = eventValues.value[index].valueId
  addData.id && valueId && delArr.value.push(valueId)
  if (activeIndex.value > index) {
    activeIndex.value--
  } else if (activeIndex.value == index) {
    activeIndex.value = -1
  }
  eventValues.value ? eventValues.value.splice(index, 1) : (
    eventValues.value = [{ valueId: undefined, name: '', note: '', explanation: '', eventName: addData.eventName! }]
  )
}
watch(eventValues, () => {
  addData.eventValuemap = {}
  eventValues.value ? eventValues.value.map(item => {
    item.name = eventValuesList.value.find(v => v.id === item.valueId)?.name || ''
    if (addData.eventValuemap && item.valueId) {
      addData.eventValuemap[item.valueId] = item
    }
  }) : (addData.eventValuemap = undefined)
}, {
  deep: true
})
const checkValues = (rule: any, value: any, callback: any) => {
  if (!eventValues.value || eventValues.value.length < 1) {
    return callback(new Error('请输入事件字段值'))
  }
  const obj: {[key: string]: number} = {}
  eventValues.value && eventValues.value.map(item => {
    if (!item.valueId || !item.name) {
      return callback(new Error('请输入事件字段值'))
    }
    if (!item.explanation) {
      return callback(new Error('请输入事件字段释义'))
    }
    if (obj[item.valueId]) {
      return callback(new Error('事件字段不可重复'))
    } else {
      obj[item.valueId] = 1
    }
  })
  return callback()
}
const rules = {
  eventName: [
    { required: true, message: '请输入事件名', trigger: 'change' },
  ],
  eventNote: [
    { required: true, message: '请输入事件备注', trigger: 'blur' },
  ],
  eventValuemap: [
    { validator: checkValues, trigger: ['change', 'blur']},
  ],
}
// 底部确认、取消
const confirm = async () => {  
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        if (addData.id && delArr.value.length > 0) {
          await trace({ page: `话术编辑-事件触发设置-编辑事件-删除事件值(${editId})`, params: delArr.value })
          await Promise.all(
            delArr.value.map(async item => {
              await scriptEventModel.deleteEventValueId({ triggerKeyId: addData.id!, triggerValueId: item })
            })
          )
        }
        const params: EventItem = pickAttrFromObj(addData, 
          ['id', 'scriptLongId', 'eventName', 'note', 'eventValuemap']
        )
        trace({ page: `话术编辑-事件触发设置-${addData.id ? '编辑' : '新增'}事件(${editId})`, params: params })
        const data = await scriptEventModel.saveEvent(params) as EventItem
        ElMessage({
          message: '操作成功',
          type: 'success',
        })
        dialogVisible.value =false
        loading.value = false
        emits('update', data)
      } catch(err) {
        ElMessage({
          message: '操作失败',
          type: 'error',
        })
        loading.value = false
      }
    }
  })
}
const cancel = () => {
  dialogVisible.value = false
  emits('close')
}
const eventTypeList = ref<{
  id: number, eventTriggerType: string
}[]>([])
const eventValuesList = ref<{
  id: number, name: string
}[]>([])
const eventEnabledValuesList = computed(() => {
  return eventValuesList.value?.filter(v => eventValues.value.findIndex(vv => vv.valueId === v.id) === -1) || []
})
const init = async () => {
  eventTypeList.value = await scriptEventModel.getEventTypeList() as {id: number, eventTriggerType: string}[] || []
}
watch(() => addData.eventName, async n => {
  if (n) {
    eventValuesList.value = await scriptEventModel.getEventValuesList({
      eventTriggerType: n as string
    }) as { id: number, name: string }[] || []
    eventValues.value = [{ valueId: undefined, name: '', note: '', explanation: '', eventName: addData.eventName! }]
  } else {
    eventValuesList.value = []
  }
}, {deep: true})
watch(() => props.visible, async n => {
  if (n) {
    init()
    Object.assign(addData, props.eventData || {
      id: undefined,
      scriptLongId: editId,
      eventName: undefined,
      eventNote: '',
      eventValuemap: undefined
    })
    eventValuesList.value = addData.eventName && (await scriptEventModel.getEventValuesList({
      eventTriggerType: addData.eventName
    }) as { id: number, name: string }[]) || []
    eventValues.value = addData.eventValuemap ? Object.values(addData.eventValuemap) || [] : []
    activeIndex.value = addData.id ? -1 : 0
    delArr.value = []
  }
}, {
  immediate: true
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.text {
  font-size: 13px;
  line-height: 24px;
  margin-right: 10px;
  text-align: left;
}

.el-dialog .el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 16px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
}
</style>