import { Extension } from '@tiptap/core';
import { Plugin, } from 'prosemirror-state';

export const PastePlainText = Extension.create({
  name: 'pastePlainText',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        props: {
          // 处理粘贴的 HTML，移除不需要的属性
          transformPastedHTML(html) {
            // 使用 DOMParser 解析粘贴的 HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            // 获取所有元素
            const elements = doc.body.querySelectorAll('*');
            // 移除的属性
            elements.forEach((el) => {
              el.removeAttribute('contentId');
              el.removeAttribute('corpusType');
              el.removeAttribute('isPlayed');
              el.removeAttribute('style');
              el.removeAttribute('class');
            });

            // 返回处理后的 HTML
            return doc.body.innerHTML;
          },
          // 粘贴纯文本
          // handlePaste(view, event, slice) {
          //   // 获取剪贴板中的纯文本内容
          //   const text = event.clipboardData.getData('text/plain');
          //   console.log('text', event.clipboardData);
            
          //   if (text) {
          //     // 阻止默认的粘贴行为
          //     event.preventDefault();

          //     // 获取编辑器状态
          //     const { state, dispatch } = view;
          //     const { selection } = state;
          //     const { from, to } = selection;

          //     // 创建插入纯文本的事务
          //     const transaction = state.tr.insertText(text, from, to);

          //     // 分派事务
          //     dispatch(transaction);

          //     return true;
          //   }
          //   return false;
          // },
        },
      }),
    ];
  },
});
