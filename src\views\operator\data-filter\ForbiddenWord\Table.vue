<template>
  <!--搜索条-->
  <div class="tw-flex tw-m-0 tw-pt-[16px] tw-px-[16px] tw-bg-white">
    <div>
      <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">筛选{{ props.tab }}：</div>
      <el-input
        v-model.trim="searchForm.forbiddenWord"
        :placeholder="'请输入'+props.tab"
        clearable
        style="width: 300px;"
        @keyup.enter="onClickSearch"
      />
    </div>

    <div class="tw-flex tw-items-end tw-ml-[12px] tw-pb-[8px]">
      <el-button type="primary" link @click="onClickReset">
        <el-icon size="--el-font-size-base">
          <SvgIcon name="reset" color="var(--el-color-primary)" />
        </el-icon>
        <span>重置</span>
      </el-button>
      <el-button type="primary" link @click="onClickSearch">
        <el-icon size="--el-font-size-base">
          <SvgIcon name="search" color="var(--el-color-primary)" />
        </el-icon>
        <span>查询</span>
      </el-button>
    </div>
  </div>

  <!--按钮组-->
  <div class="tw-flex tw-justify-end tw-m-0 tw-px-[16px] tw-py-[12px] tw-bg-white">
    <el-button type="primary" @click="onClickAddBatch">
      <el-icon size="16">
        <SvgIcon name="upload" />
      </el-icon>
      <span>批量添加</span>
    </el-button>

    <el-button type="primary" @click="onClickAdd">
      <el-icon size="16">
        <SvgIcon name="add1" />
      </el-icon>
      <span>添加</span>
    </el-button>
  </div>

  <!--列表-->
  <el-table
    v-loading="loadingForbiddenWord"
    :header-cell-style="tableHeaderStyle"
    :data="currentList"
  >
    <el-table-column align="left" prop="forbiddenWord" label="违禁词" min-width="100" show-overflow-tooltip />
    <el-table-column align="center" prop="isRegex" label="类型" min-width="80">
      <template v-slot="{row}">
        {{ row && row.isRegex ? '正则表达式' : '普通文本' }}
      </template>
    </el-table-column>
    <el-table-column align="left" prop="remarks" label="备注" min-width="100" show-overflow-tooltip />
    <el-table-column align="left" prop="updateBy" label="更新人" min-width="100" show-overflow-tooltip />
    <el-table-column align="center" prop="createTime" label="创建时间" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:ForbiddenWordItem}">
        {{ formatTime(null, null, row?.createTime ?? '') }}
      </template>
    </el-table-column>
    <el-table-column align="center" prop="updateTime" label="更新时间" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:ForbiddenWordItem}">
        {{ formatTime(null, null, row?.updateTime ?? '') }}
      </template>
    </el-table-column>
    <el-table-column align="right" fixed="right" label="操作" width="100">
      <template #default="{row}">
        <el-button type="danger" link @click="onClickDelete(row)">
          删除
        </el-button>
        <el-button type="primary" link @click="onClickEdit(row)">
          编辑
        </el-button>
      </template>
    </el-table-column>
  </el-table>

  <!--分页条-->
  <PaginationBox
    :currentPage="pageNum"
    :pageSize="pageSize"
    :pageSizeList="pageSizeList"
    :total="total"
    @search="updateAllList"
    @update="updateList"
  />

  <!--编辑弹窗-->
  <EditDialog
    v-model:visible="editDialogVisible"
    :data="editDialogData"
    :isEdit="editDialogIsEdit"
    :allList="allList"
    :tab="props.tab"
    @confirm="onEditDialogConfirm"
  />

  <!--批量添加弹窗-->
  <AddBatchDialog
    v-model:visible="addBatchDialogVisible"
    :tab="props.tab"
    @confirm="onAddBatchDialogConfirm"
  />
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import { ForbiddenWordItem, ForbiddenWordParam, ForbiddenWordTabEnum } from '@/type/dataFilter'
import { formatTime, Throttle, updateCurrentPageList } from '@/utils/utils'
import PaginationBox from '@/components/PaginationBox.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import to from 'await-to-js'
import { forbiddenWordUrlModel, forbiddenWordVariableModel } from '@/api/data-filter'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import EditDialog from '@/views/operator/data-filter/ForbiddenWord/EditDialog.vue'
import AddBatchDialog from '@/views/operator/data-filter/ForbiddenWord/AddBatchDialog.vue'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  tab: ForbiddenWordTabEnum,
}>(), {
  tab: ForbiddenWordTabEnum.VARIABLE,
})

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 搜索条 开始 ----------------------------------------

// 搜索条件 默认值
const searchFormDefault = () => ({
  forbiddenWord: '',
})
// 搜索条件 表单值
const searchForm: { [prop: string]: any } = reactive(searchFormDefault())

/**
 * 搜索条，重置表单
 */
const resetSearchForm = () => {
  // 表单数据恢复默认值
  Object.assign(searchForm, searchFormDefault())
}
/**
 * 搜索条，点击重置按钮
 */
const onClickReset = () => {
  resetSearchForm()
}
/**
 * 搜索条，点击查询按钮
 */
const onClickSearch = () => {
  updateAllList()
}

// ---------------------------------------- 搜索条 结束 ----------------------------------------

// ---------------------------------------- 按钮组 开始 ----------------------------------------

/**
 * 点击批量添加按钮
 */
const onClickAddBatch = () => {
  addBatchDialogVisible.value = true
}
/**
 * 点击添加按钮
 */
const onClickAdd = () => {
  editDialogData.value = {}
  editDialogIsEdit.value = false
  editDialogVisible.value = true
}

// ---------------------------------------- 按钮组 结束 ----------------------------------------

// ---------------------------------------- 列表 开始 ----------------------------------------

// 列表，正在加载
const loadingForbiddenWord = ref<boolean>(false)
// 列表，加载节流锁
const throttleForbiddenWord = new Throttle(loadingForbiddenWord)

// 列表，全部，接口数据
const allList = ref<ForbiddenWordItem[]>([])

// 列表，筛选，全部的子集
const filterList = ref<any[]>([])

// 列表，当前页，筛选的子集
const currentList = ref<ForbiddenWordItem[]>([])
// 列表，当前页，页码
const pageNum = ref(1)
// 列表，当前页，每页大小
const pageSize = ref(20)
// 列表，当前页，每页大小可选数值
const pageSizeList: number[] = [10, 20, 50, 100]

// 列表，表格展示总数
const total = computed(() => {
  return filterList.value.length ?? 0
})

/**
 * 更新全部列表
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleForbiddenWord.check()) {
    return
  }
  throttleForbiddenWord.lock()

  // 处理参数
  const params: ForbiddenWordParam = {
    word: searchForm.forbiddenWord ?? '',
  }

  // 请求接口
  let err: any = null, res: ForbiddenWordItem[] = []
  if (props.tab === ForbiddenWordTabEnum.VARIABLE) {
    // 变量违禁词
    const [e, r] = <[any, ForbiddenWordItem[]]>await to(forbiddenWordVariableModel.getList(params))
    err = e
    res = r
  } else if (props.tab === ForbiddenWordTabEnum.URL) {
    // 短链违禁词
    const [e, r] = <[any, ForbiddenWordItem[]]>await to(forbiddenWordUrlModel.getList(params))
    err = e
    res = r
  }

  // 返回失败结果
  if (err) {
    ElMessage.error('无法获取违禁词列表')
    allList.value = []
    // 节流锁解锁
    throttleForbiddenWord.unlock()
    return
  }

  // 返回成功结果
  // 更新全部列表
  allList.value = res?.length ? res : []
  // 更新筛选列表
  updateFilterList()
  // 更新当前页列表
  updateList(pageNum.value, pageSize.value)

  // 节流锁解锁
  throttleForbiddenWord.unlock()
}
/**
 * 更新筛选列表
 */
const updateFilterList = () => {
  filterList.value = allList.value.filter((item: ForbiddenWordItem) => {
    return item.forbiddenWord?.includes(searchForm.forbiddenWord)
  })
}
/**
 * 更新当前页列表
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateList = (p?: number, s?: number) => {
  // console.log('updateList', p, s)
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined, null, 0等逻辑假值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    pageNum.value = p || 1
    pageSize.value = s || 20
  }
  // 更新当前页列表
  currentList.value = updateCurrentPageList(filterList.value, pageNum.value, pageSize.value)
}
/**
 * 删除列表行 请求接口
 * @param {FrequencyRestrictionInfo} row 列表行，限制信息
 */
const deleteRow = async (row: ForbiddenWordItem) => {
  // 处理参数
  if (typeof row?.id !== 'number') {
    ElMessage({
      message: '当前违禁词的ID不正确',
      type: 'warning',
    })
    return
  }
  const params: ForbiddenWordParam = {
    id: row.id ?? undefined,
  }
  // 请求接口
  let err: any = null
  if (props.tab === ForbiddenWordTabEnum.VARIABLE) {
    // 变量违禁词
    const [e, _] = <[any, ForbiddenWordItem]>await to(forbiddenWordVariableModel.delete(params))
    err = e
  } else if (props.tab === ForbiddenWordTabEnum.URL) {
    // 短链违禁词
    const [e, _] = <[any, ForbiddenWordItem]>await to(forbiddenWordUrlModel.delete(params))
    err = e
  }

  if (err) {
    ElMessage({
      message: `${props.tab}【${row?.forbiddenWord}】删除失败`,
      type: 'error',
    })
    return
  }
  ElMessage({
    message: `${props.tab}【${row?.forbiddenWord}】删除成功`,
    type: 'success',
  })
  // 更新全部列表
  await updateAllList()
}
/**
 * 点击列表行删除按钮
 * @param row 点击的列表行
 */
const onClickDelete = (row: ForbiddenWordItem) => {
  // 显示删除确认
  Confirm({
    text: `确定要删除${props.tab}【${row?.forbiddenWord ?? ''}】吗？`,
    type: 'danger',
    title: '删除确认'
  }).then(() => {
    // 删除
    deleteRow(row)
  }).catch(() => {
    // 取消
  })
}
/**
 * 点击列表行编辑按钮
 * @param row 点击的列表行
 */
const onClickEdit = (row: ForbiddenWordItem) => {
  editDialogData.value = row
  editDialogIsEdit.value = true
  editDialogVisible.value = true
}

// ---------------------------------------- 列表 结束 ----------------------------------------

// ---------------------------------------- 编辑弹窗 开始 ----------------------------------------

// 编辑弹窗，是否显示
const editDialogVisible = ref<boolean>(false)
// 编辑弹窗，表单数据
const editDialogData = ref<ForbiddenWordItem>({})
// 编辑弹窗，是否是编辑模式，是——编辑，否——新建
const editDialogIsEdit = ref<boolean>(false)

/**
 * 编辑弹窗，提交表单
 */
const onEditDialogConfirm = () => {
  updateAllList()
}

// ---------------------------------------- 编辑弹窗 结束 ----------------------------------------

// ---------------------------------------- 批量添加弹窗 开始 ----------------------------------------

// 批量添加弹窗，是否显示
const addBatchDialogVisible = ref<boolean>(false)

/**
 * 批量添加弹窗，提交表单
 */
const onAddBatchDialogConfirm = () => {
  updateAllList()
}

// ---------------------------------------- 批量添加弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.tab, async () => {
  await nextTick()
  resetSearchForm()
  await updateAllList()
}, { immediate: true })

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
