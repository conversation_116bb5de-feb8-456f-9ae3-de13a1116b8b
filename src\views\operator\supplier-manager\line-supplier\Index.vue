<template>
  <!--模块标题-->
  <HeaderBox title="线路供应商" />

  <!--模块主体-->
  <div v-loading="loading" class="module-main module-main-scroll tw-min-w-[1080px]">

    <!--左半部分-->
    <div v-loading="loadingSupplierAllList" class="aside-list-box">
      <!--创建供应商按钮-->
      <el-button
        class="tw-w-[226px] tw-mx-[16px] tw-box-border"
        size="default"
        type="primary"
        @click="clickCreateSupplierButton"
      >
        创建供应商
      </el-button>

      <!--搜索框-->
      <el-input
        v-model.trim="supplierSearchVal"
        class="tw-mt-[16px] tw-mx-[16px] tw-mb-[12px]"
        style="width:226px"
        placeholder="搜索线路供应商"
        clearable
        @input="handleSupplierSearchChange"
        @clear="handleSupplierSearchClear"
      >
        <template #suffix>
          <el-icon :size="16">
            <SvgIcon name="search" />
          </el-icon>
        </template>
      </el-input>
      <!--供应商状态切换菜单-->
      <TabsBox
        class="supplier-tab"
        :active="activeTabIndex"
        :tabList="Object.values(supplierStatusList).map(item => item.text)"
        @update:active="handleSelectTab"
      />

      <!--符合搜索条件的列表-->
      <el-scrollbar ref="supplierListRef" class="tw-px-[16px]">
        <!--列表里的单项-->
        <div
          v-for="supplierItem in supplierCurrentList"
          :key="supplierItem.id??Math.random()"
          ref="supplierItemRef"
          class="aside-normal-item"
          :class="[{'aside-active-item':supplierItem.id===currentSupplierItem.id}]"
        >
          <!--内层容器-->
          <div class="aside-normal-item-inner" @click="clickSupplierItem(supplierItem)">
            <!--行容器-->
            <div class="tw-flex tw-items-center">
              <!--编号-->
              <div class="tw-flex-1 tw-text-left tw-break-words tw-truncate tw-text-[var(--primary-black-color-400)]">
                {{ supplierItem.supplierNumber }}
              </div>
              <!--联系人姓名-->
              <div class="tw-grow-0 tw-shrink-0 tw-w-[60px] tw-text-right tw-break-words tw-truncate tw-text-[var(--primary-black-color-500)]">
                {{ supplierItem.contactName }}
              </div>
            </div>

            <!--供应商名称-->
            <div class="tw-text-left tw-text-[14px] tw-font-bold tw-break-words">
              {{ supplierItem.supplierName }}
            </div>

            <!--日期时间-->
            <div class="tw-text-[13px] tw-break-words tw-text-[var(--primary-black-color-400)]">
              创建时间：{{ dayjs(supplierItem.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>

          <!--按钮-->
          <div class="aside-item-btn-box">
            <el-tooltip content="编辑供应商" placement="right" :show-after="500">
              <span class="tw-cursor-pointer" @click="clickEditSupplier(supplierItem)">
                <el-icon :size="16" color="#fff">
                  <SvgIcon name="edit" color="#fff" />
                </el-icon>
              </span>
            </el-tooltip>
          </div>
        </div>
        <!--空数据提示-->
        <div v-show="!supplierFilterList.length" class="tw-bg-white">
          <el-empty />
        </div>
      </el-scrollbar>
      <PaginationBox
        class="tw-border-t-[1px] tw-border-t-[#ebeef5] tw-mt-1"
        :pageSize="supplierPageSize"
        :currentPage="supplierPageNum"
        :total="supplierPageTotal"
        :mini="true"
        @search="updateSupplierAllList(currentSupplierItem)"
        @update="updateSupplierCurrentList"
      />
    </div>

    <!--右半部分-->
    <el-scrollbar v-loading="loadingSupplierAllList" class="tw-grow" wrap-class="tw-p-[16px]">

      <!--空数据提示-->
      <div v-show="!supplierFilterList.length" class="tw-bg-white tw-w-full">
        <el-empty />
      </div>

      <div v-show="supplierFilterList.length" class="tw-flex tw-flex-col tw-w-full tw-h-full">

        <!--供应商信息模块-->
        <div v-loading="loadingSupplierAllList">
          <!--空数据-->
          <template v-if="currentSupplierItem.id===undefined||currentSupplierItem.id===null">
            <div class="card-box tw-flex-col">
              <el-empty />
            </div>
          </template>

          <template v-else>
            <!--标题和内容-->
            <div class="card-box tw-flex-col">
              <!--标题-->
              <div class="tw-flex tw-flex-row tw-items-center tw-w-full">
                <p class="tw-text-[14px] tw-font-bold tw-text-[#313233]">
                  {{ currentSupplierItem.supplierName }}
                </p>
                <p class="tw-ml-[8px] tw-text-[13px] tw-font-normal tw-text-[#626366]">
                  简称：{{ currentSupplierItem.supplierProfile }}
                </p>
                <p class="tw-ml-auto tw-text-[13px] tw-font-normal tw-text-[#626366]">
                  <span class="info-title">编号：</span>
                  <span class="info-content-dark">{{ currentSupplierItem.supplierNumber }}</span>
                </p>
              </div>

              <!--基础信息-->
              <div class="tw-flex tw-flex-row tw-justify-start tw-flex-wrap tw-w-full tw-mt-[8px]">
                <p class="info-item">
                  <span class="info-title">联系人：</span>
                  <span class="info-content-dark">{{ currentSupplierItem.contactName }}</span>
                </p>
                <p class="info-item">
                  <span class="info-title">联系电话：</span>
                  <span class="info-content-dark">{{ currentSupplierItem.phoneNumber }}</span>
                </p>
                <p class="info-item">
                  <span class="info-title">邮箱：</span>
                  <span class="info-content-dark">{{ currentSupplierItem.email }}</span>
                </p>
                <p class="info-item">
                  <span class="info-title">联系地址：</span>
                  <span class="info-content-dark">{{ currentSupplierItem.contactAddress }}</span>
                </p>
              </div>

              <!--更多信息-->
              <div v-show="isInfoExpand" class="tw-w-full tw-text-left">
                <p class="info-item tw-flex tw-mt-[8px]">
                  <span class="info-title">业务范围：</span>
                  <span class="info-content-dark">{{ supplierBusinessScopeText }}</span>
                </p>
                <p class="info-item tw-flex tw-mt-[8px]">
                  <span class="info-title">运营备注：</span>
                  <span v-if="(currentSupplierItem.notes ?? '') === ''" class="info-content">暂未填写</span>
                  <span v-else class="info-content-dark">{{ currentSupplierItem.notes }}</span>
                </p>
              </div>
            </div>

            <!--展开收起箭头-->
            <div class="tw-flex tw-justify-center">
              <div class="trapezoid" @click="isInfoExpand=!isInfoExpand">
                <el-icon v-if="isInfoExpand" :size="13">
                  <ArrowUpBold />
                </el-icon>
                <el-icon v-else :size="13">
                  <ArrowDownBold />
                </el-icon>
              </div>
            </div>
          </template>
        </div>

        <!--列表模块-->
        <div
          v-loading="loadingTab"
          class="tw-overflow-hidden tw-flex tw-flex-col tw-mt-[16px] tw-bg-white tw-rounded-[4px]"
        >
          <!--标签卡切换-->
          <TabsBox
            class="list-tab"
            :active="listTabName"
            :tabList="Object.values(LIST_TAB_NAME)"
            @update:active="handleListTabChange"
          />

          <!--线路管理标签卡-->
          <div v-if="listTabName===LIST_TAB_NAME.LINE" class="tab-container">
            <div class="tab-header">
              <!--输入框容器-->
              <el-form
                class="tw-grid tw-grid-cols-4 tw-gap-[8px]"
                label-position="top"
              >
                <el-form-item label="线路名称">
                  <el-input
                    v-model.trim="lineNameSearchVal"
                    placeholder="请填写"
                    clearable
                    style="width: 100%;"
                  />
                </el-form-item>

                <el-form-item label="线路编号">
                  <el-input
                    v-model.trim="lineNumberSearchVal"
                    placeholder="请填写"
                    clearable
                    style="width: 100%;"
                  />
                </el-form-item>

                <el-form-item label="线路状态">
                  <el-select
                    v-model.trim="lineStatusSearchVal"
                    placeholder="全部"
                    clearable
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="lineStatusItem in Object.values(supplierLineStatusList)"
                      :key="lineStatusItem.name"
                      :value="lineStatusItem.val"
                      :label="lineStatusItem.text"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="支持运营商">
                  <el-select
                    v-model.trim="lineOperatorSearchVal"
                    placeholder="全部"
                    clearable
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="1"
                    style="width: 100%;"
                  >
                    <!--不需要显示“全部”-->
                    <el-option
                      v-for="operatorItem in Object.values(supplierOperatorList)"
                      v-show="operatorItem.name!==supplierOperatorList.all.name"
                      :key="operatorItem.name"
                      :value="operatorItem.val"
                      :label="operatorItem.text"
                    />
                  </el-select>
                </el-form-item>

                <template v-if="isExpand">
                  <el-form-item label="适用行业">
                    <el-cascader
                      v-model="lineIndustrySearchVal"
                      :options="industryOptions"
                      :props="industryProps"
                      :show-all-levels="false"
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="全部"
                      clearable
                      style="width: 100%;"
                    />
                  </el-form-item>

                  <el-form-item label="外显号码">
                    <el-input
                      v-model.trim="lineDisplayNoSearchVal"
                      placeholder="请填写"
                      clearable
                      style="width: 100%;"
                    />
                  </el-form-item>

                  <el-form-item label="主叫号码">
                    <el-input
                      v-model.trim="lineMasterNoSearchVal"
                      placeholder="请填写"
                      clearable
                      style="width: 100%;"
                    />
                  </el-form-item>
                </template>
              </el-form>

              <!--分割线-->
              <hr class="tw-my-[12px]">

              <!--按钮容器-->
              <div class="tw-flex tw-justify-end">
                <el-button type="primary" link @click="clickLineResetFilter">
                  <el-icon size="--el-font-size-base">
                    <SvgIcon name="reset" color="var(--el-color-primary)" />
                  </el-icon>
                  <span>重置</span>
                </el-button>
                <el-button type="primary" link @click="clickLineSearch">
                  <el-icon size="--el-font-size-base">
                    <SvgIcon name="search" color="var(--el-color-primary)" />
                  </el-icon>
                  <span>搜索</span>
                </el-button>
                <el-button v-if="isExpand" type="primary" link @click="isExpand=false">
                  <span>收起</span>
                  <el-icon size="--el-font-size-base">
                    <ArrowUp />
                  </el-icon>
                </el-button>
                <el-button v-else type="primary" link @click="isExpand=true">
                  <span>展开</span>
                  <el-icon size="--el-font-size-base">
                    <ArrowDown />
                  </el-icon>
                </el-button>
              </div>

              <!--创建线路按钮-->
              <div class="tw-flex tw-justify-end tw-mt-[12px]">
                <el-dropdown placement="top" trigger="click">
                  <el-button type="primary" :icon="Plus">
                    新增供应线路
                  </el-button>
                  <!--下拉菜单-->
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="onClickCreateLine">新建线路</el-dropdown-item>
                      <el-dropdown-item @click="onClickCopyLine">复制线路</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <!--表格-->
            <el-table
              stripe
              border
              size="small"
              :data="lineList"
              :header-cell-style="tableHeaderStyle"
              class="tw-mt-[12px]"
            >
              <template #empty>
                暂无数据
              </template>

              <el-table-column align="left" fixed="left" prop="lineNumber" label="线路编号" min-width="150" show-overflow-tooltip />
              <el-table-column align="center" fixed="left" prop="lineType" label="线路类型" width="80" show-overflow-tooltip>
                <template #default="scope:{row:{lineType:string}}">
                  {{ getSupplierLineTypeText(scope.row?.lineType) }}
                </template>
              </el-table-column>
              <el-table-column align="left" fixed="left" prop="lineName" label="线路名称" min-width="150" show-overflow-tooltip />
              <el-table-column align="left" prop="displayCallNumber" label="外显号码" width="100" show-overflow-tooltip />
              <el-table-column align="center" prop="enableStatus" label="启用状态" width="100" show-overflow-tooltip>
                <template v-slot="scope:{row:{enableStatus:any}}">
                  <div class="status-box" :class="scope.row.enableStatus === supplierLineStatusList.enabled.val ? 'green-status' : 'orange-status'">
                    {{ supplierLineStatusMap.get(scope.row.enableStatus)?.text ?? '' }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="left" prop="lineGateways" label="线路网关" width="100" show-overflow-tooltip>
                <template v-slot="{row}">
                  {{ getGatewayName(row) }}
                </template>
              </el-table-column>
              <el-table-column align="left" prop="concurrentLimit" label="并发限制" width="80" show-overflow-tooltip />
              <el-table-column align="left" prop="masterCallNumber" label="主叫号码" width="110" show-overflow-tooltip />
              <el-table-column align="left" prop="prefix" label="前缀" width="100" show-overflow-tooltip>
                <template v-slot="{row}:{row:SupplierLineInfo}">
                  {{ row?.prefix || '-' }}
                </template>
              </el-table-column>
              <el-table-column align="left" prop="registerIp" label="注册IP" width="120"/>
              <el-table-column align="left" prop="registerPort" label="注册端口" width="80"/>
              <el-table-column align="left" prop="cityCodeGroups" label="支持范围" width="150" show-overflow-tooltip>
                <template v-slot="{row}">
                  <div class="tw-cursor-pointer tw-truncate tw-text-[--el-color-primary]" @click="onClickScope(row)">
                    {{ getSupplierLineScope(row) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="left" prop="isForEncryptionPhones" label="数据传输" min-width="120" show-overflow-tooltip>
                <template v-slot="{row}">
                  {{ row.isForEncryptionPhones ? '加密' : '普通' }}
                </template>
              </el-table-column>
              <el-table-column align="left" prop="secondIndustries" label="适用行业" width="100" show-overflow-tooltip />
              <el-table-column align="center" prop="lineAccessType" label="接入类型" width="80" show-overflow-tooltip>
                <template v-slot="scope:{row:{lineAccessType:any}}">
                  {{ supplierLineAccessTypeMap.get(scope.row.lineAccessType)?.text ?? '' }}
                </template>
              </el-table-column>
              <el-table-column align="left" prop="billingCycle" label="计费周期" width="80" show-overflow-tooltip />
              <el-table-column align="left" prop="unitPrice" label="线路单价(元)" width="60" show-overflow-tooltip />
              <el-table-column align="left" prop="tenantLines" label="商户线路" width="100" show-overflow-tooltip>
                <template v-slot="{row}">
                  {{ row?.tenantLines?.map((item: MerchantLineInfo) => item?.lineName ?? '').join('，') }}
                </template>
              </el-table-column>
              <el-table-column align="center" fixed="right" label="挂起状态" width="70">
                <template v-slot="scope">
                  <el-switch
                    v-if="scope.row.enableStatus === supplierLineStatusList.enabled.val"
                    v-model="scope.row.pending"
                    inline-prompt
                    active-text="开"
                    inactive-text="关"
                    @click="changePending(scope.row)"
                  />
                  <div v-else class="tw-text-center">-</div>
                </template>
              </el-table-column>
              <el-table-column align="right" fixed="right" label="操作" width="140">
                <template v-slot="scope">
                  <el-button class="tw-ml-0" type="primary" link @click="clickEditLine(scope.row, false)">
                    编辑
                  </el-button>
                  <el-button type="primary" link @click="clickEditLine(scope.row)">
                    详情
                  </el-button>
                  <el-button type="primary" link @click="onConfirmDialogCopyLine(scope.row)">
                    复制
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!--分页条-->
            <PaginationBox
              v-if="lineAllList?.length"
              :pageSize="linePageSize"
              :currentPage="linePageNum"
              :total="lineTableSize"
              @search="updateLineAllList"
              @update="updatePage"
            />
          </div>

          <!--黑名单管理标签卡-->
          <BlackGroup
            v-else-if="listTabName===LIST_TAB_NAME.BLACKLIST"
            :needUpdate="needUpdateTab.BLACKLIST"
            @update="onTabUpdated(LIST_TAB_NAME.BLACKLIST)"
          />

          <!--靓号限制管理标签卡-->
          <GoodNumber
            v-else-if="listTabName===LIST_TAB_NAME.GOOD_NUMBER"
            :needUpdate="needUpdateTab.GOOD_NUMBER"
            @update="onTabUpdated(LIST_TAB_NAME.GOOD_NUMBER)"
          />
        </div>

      </div>

    </el-scrollbar>

  </div>

  <!--线路供应商弹窗-->
  <DialogSupplier
    :id="editingSupplierId"
    :content="editingSupplierItem"
    :visible="dialogSupplierVisible"
    @close="closeDialogSupplier"
    @update="handleDialogSupplierUpdate"
  />

  <!--供应线路抽屉-->
  <SupplierLineDrawer
    :id="editingLineId"
    :content="editingLineItem"
    :supplier="currentSupplierItem"
    :visible="dialogLineVisible"
    :readonly="true"
    @close="closeDialogLine"
    @update="updateLineAllList"
    @modify="handleModifyLine"
  />

  <!--支持范围弹窗-->
  <DialogScope
    :visible="dialogScopeVisible"
    :data="dialogScopeCityCodes"
    @close="onCloseDialogScope"
  />

  <!--复制供应线路弹窗-->
  <DialogCopyLine
    :visible="dialogCopyLineVisible"
    @close="onCloseDialogCopyLine"
    @confirm="onConfirmDialogCopyLine"
  />
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { computed, defineAsyncComponent, nextTick, onActivated, onMounted, ref } from 'vue'
import { ArrowDown, ArrowDownBold, ArrowUp, ArrowUpBold, Plus } from '@element-plus/icons-vue'
import { SearchLineParams, SupplierInfo, SupplierLineInfo } from '@/type/supplier'
import { MerchantLineInfo } from '@/type/merchant'
import { supplierModel } from '@/api/supplier'
import { getListFirstItem, searchInList, searchModeEnum, Throttle, updateCurrentPageList } from '@/utils/utils'
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'
import {
  supplierLineAccessTypeMap,
  supplierLineStatusEnum,
  supplierLineStatusList,
  supplierLineStatusMap,
  supplierOperatorEnum,
  supplierOperatorList,
  supplierOperatorMap,
  supplierStatusList
} from '@/assets/js/map-supplier'
import { ElMessage, ElMessageBox } from 'element-plus'
import router from '@/router'
import { useSupplierStore } from '@/store/supplier'
import { GatewayItem } from '@/type/gateway'
import { getSupplierLineTypeText } from '@/utils/line'
import Confirm from '@/components/message-box'
import { tableHeaderStyle } from '@/assets/js/constant'
import { trace } from '@/utils/trace'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const TabsBox = defineAsyncComponent(() => import('@/components/TabsBox.vue'))
const PaginationBox = defineAsyncComponent(() => import('@/components/PaginationBox.vue'))
const DialogSupplier = defineAsyncComponent(() => import('./DialogSupplier.vue'))
const SupplierLineDrawer = defineAsyncComponent(() => import('./SupplierLineDrawer.vue'))
const DialogScope = defineAsyncComponent(() => import('./line/DialogScope.vue'))
const DialogCopyLine = defineAsyncComponent(() => import('./line/DialogCopyLine.vue'))
const BlackGroup = defineAsyncComponent(() => import('./black-group/Index.vue'))
const GoodNumber = defineAsyncComponent(() => import('./good-number/Index.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

// 路由引入
const route = useRoute()

// 全局变量
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const supplierStore = useSupplierStore()

// 收起选项
const isExpand = ref(false)

/**
 * 更新主体信息
 * 主体信息是指右侧主体区块的所有内容
 * 当前供应商的线路
 */
const updateMainInfo = () => {
  // -------------------- 重置右侧所有数据 开始 --------------------

  lineAllList.value = []
  lineList.value = []

  // 重置搜索条件
  resetLineFilter()

  // -------------------- 重置右侧所有数据 结束 --------------------

  // -------------------- 更新右侧所有数据 开始 --------------------

  // 更新列表模块
  updateListTab()

  // -------------------- 更新右侧所有数据 结束 --------------------
}

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 供应商列表 开始 ----------------------------------------

// 当前激活的标签卡索引
const activeTabIndex = ref<string>(supplierStatusList.enabled.text)

// 供应商列表DOM
const supplierListRef = ref()
// 供应商单项DOM
const supplierItemRef = ref()

// 供应商列表，全部，接口数据
const supplierAllList = ref<SupplierInfo[]>([])
// 供应商列表，全部，正在加载
const loadingSupplierAllList = ref<boolean>(false)
// 供应商列表，全部，加载节流锁
const throttleSupplierAllList = new Throttle(loadingSupplierAllList)

// 供应商列表，启用，是全部的子集，接口数据和页面展示的中间缓存
const supplierEffectiveList = ref<SupplierInfo[]>([])
// 供应商列表，禁用，是全部的子集，接口数据和页面展示的中间缓存
const supplierExpiredList = ref<SupplierInfo[]>([])

// 供应商列表，搜索结果，两种状态列表之一的子集
const supplierFilterList = ref<SupplierInfo[]>([])
// 供应商列表，总数
const supplierPageTotal = computed(() => {
  return supplierFilterList.value.length ?? 0
})
// 供应商搜索框文本
const supplierSearchVal = ref<string>('')

// 供应商列表，当前页，搜索结果的子集
const supplierCurrentList = ref<SupplierInfo[]>([])
// 供应商列表，当前页，页码
const supplierPageNum = ref(1)
// 供应商列表，当前页，每页大小
const supplierPageSize = ref(200)

// 当前选中供应商信息
const currentSupplierItem = ref<SupplierInfo>({ id: -1 })

// 正在编辑的供应商信息
const editingSupplierItem = ref<SupplierInfo>({ id: -1 })
// 正在编辑的供应商ID，新建默认为-1
const editingSupplierId = ref<number>(-1)

// 显示供应商弹窗
const dialogSupplierVisible = ref<boolean>(false)

/**
 * 更新全部供应商列表
 * @param {SupplierInfo} current 当前选中信息
 */
const updateSupplierAllList = async (current?: SupplierInfo) => {
  // 节流锁上锁
  if (throttleSupplierAllList.check()) {
    return
  }
  throttleSupplierAllList.lock()

  try {
    // 切换供应商状态标签卡
    if (current && typeof current.id === 'number' && current.cooperationStatus) {
      const activeTab = Object.values(supplierStatusList).find(item => item.val === current.cooperationStatus)
      activeTabIndex.value = activeTab?.text || activeTabIndex.value
    }
    // 处理参数
    const status = Object.values(supplierStatusList).find(item => item.text === activeTabIndex.value)?.val
    // 请求接口
    supplierAllList.value = <SupplierInfo[]>await supplierModel.getSupplierList({
      status: status,
    })
    // 按当前标签卡选择的状态筛选列表
    filterSupplierListByStatus()
    // 搜索筛选
    searchSupplier()
    // 更新供应商列表
    updateSupplierCurrentList(supplierPageNum.value, supplierPageSize.value)
    // 找到当前供应商在列表的位置
    findCurrentSupplierPosition(current)
    // 更新右侧模块
    updateMainInfo()
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleSupplierAllList.unlock()
  }
}
/**
 * 按状态筛选供应商列表
 */
const filterSupplierListByStatus = () => {
  // 遍历一次，在遍历的过程中，按状态分别push到对应列表里
  // 时间复杂度O(n)

  // 先清空两个列表
  supplierEffectiveList.value = []
  supplierExpiredList.value = []
  // 再遍历全部数据
  supplierAllList.value.forEach((item) => {
    if (item?.cooperationStatus === supplierStatusList.disabled.val) {
      // 停用
      supplierExpiredList.value.push(item)
    } else {
      // 启用
      supplierEffectiveList.value.push(item)
    }
  })

  // 将这两个列表按创建时间倒序排序
  supplierEffectiveList.value = supplierEffectiveList.value.sort((a, b) => {
    return dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1
  })
  supplierExpiredList.value = supplierExpiredList.value.sort((a, b) => {
    return dayjs(a.createTime).isAfter(b.createTime) ? -1 : 1
  })
}
/**
 * 搜索供应商
 */
const searchSupplier = () => {
  // 根据标签卡确定需要从哪个列表里搜索
  if (activeTabIndex.value === supplierStatusList.disabled.text) {
    supplierFilterList.value = searchInList(supplierExpiredList.value, 'supplierName', supplierSearchVal.value, searchModeEnum.FUZZY)
  } else {
    supplierFilterList.value = searchInList(supplierEffectiveList.value, 'supplierName', supplierSearchVal.value, searchModeEnum.FUZZY)
  }
}
/**
 * 供应商列表搜索框文本变化时
 */
const handleSupplierSearchChange = () => {
  // 搜索供应商
  searchSupplier()
  // 页码从1开始
  supplierPageNum.value = 1
  // 更新当前页码列表
  updateSupplierCurrentList()
}
/**
 * 供应商列表搜索框文本清空时
 */
const handleSupplierSearchClear = () => {
  // 搜索供应商
  searchSupplier()
  // 页码从1开始
  supplierPageNum.value = 1
  // 更新当前页码列表
  updateSupplierCurrentList()
}
/**
 * 切换标签卡
 * @param index {string} 选中菜单项的索引名
 */
const handleSelectTab = (index: string) => {
  // 如果标签卡索引相同则不执行后续动作
  if (index === activeTabIndex.value) {
    return
  }
  // 如果节流锁生效，不响应
  if (throttleSupplierAllList.check()) {
    return
  }
  // 切换标签卡
  activeTabIndex.value = index
  // 更新供应商全部列表
  updateSupplierAllList()
}
/**
 * 找到当前供应商在列表的位置
 * @param {SupplierInfo} current 当前选中供应商信息
 */
const findCurrentSupplierPosition = (current?: SupplierInfo) => {
  // 尝试在当前列表找到选中的
  const itemIndex = current?.id && supplierFilterList.value.findIndex((item: SupplierInfo) => {
    return item.id === current?.id
  }) || -1

  if (!current || itemIndex === -1) {
    // 列表里找不到当前索引

    // 如果列表有数据，将列表第一个项目信息展示出来
    // 如果没有，就默认id为-1的空数据
    const resultItem = <SupplierInfo>getListFirstItem(supplierFilterList.value, { id: -1 })
    currentSupplierItem.value = JSON.parse(JSON.stringify(resultItem))
  } else {
    // 更新当前选中供应商信息
    if (itemIndex > -1) {
      currentSupplierItem.value = supplierFilterList.value[itemIndex]
    } else {
      currentSupplierItem.value = { id: -1 }
    }
  }

  // 列表滚动到当前选中的位置
  scrollSupplierList()
}
/**
 * 供应商列表滚动到当前供应商的位置
 */
const scrollSupplierList = async () => {
  // 等DOM更新后再继续操作，防止滚动列表的高度不正确
  await nextTick()
  // 滚动到当前选中的位置
  if (supplierListRef.value) {
    const currentSupplierDom = supplierItemRef.value?.find((item: HTMLElement) => item.classList.contains('aside-active-item'))
    // 减去的数值是边距
    const currentSupplierOffsetTop = Math.max((currentSupplierDom?.offsetTop ?? 0) - 8, 0)
    supplierListRef.value.setScrollTop(currentSupplierOffsetTop)
  }
}
/**
 * 更新供应商列表，当前页
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateSupplierCurrentList = (p?: number, s?: number) => {
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined, null, 0等逻辑假值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    supplierPageNum.value = p!
    supplierPageSize.value = s!
  }
  // 更新当前页码
  supplierCurrentList.value = updateCurrentPageList(supplierFilterList.value, supplierPageNum.value, supplierPageSize.value)
}
/**
 * 点击供应商单项
 * @param {SupplierInfo} current
 */
const clickSupplierItem = (current: SupplierInfo) => {
  // 点击当前供应商可以刷新

  // 更新当前供应商信息
  currentSupplierItem.value = current
  // 更新主体信息
  updateMainInfo()
}
/**
 * 点击创建供应商按钮
 */
const clickCreateSupplierButton = () => {
  // 重置正在编辑的供应商信息
  editingSupplierItem.value = { id: -1 }
  editingSupplierId.value = -1
  // 显示弹窗
  dialogSupplierVisible.value = true
}

// ---------------------------------------- 供应商列表 结束 ----------------------------------------

// ---------------------------------------- 供应商信息 开始 ----------------------------------------

// 折叠面板，供应商更多信息
const isInfoExpand = ref(false)
// 供应商业务范围展示文本
const supplierBusinessScopeText = computed(() => {
  // 列表不存在或为空
  if (!lineAllList.value?.length) {
    return ''
  }

  // 遍历线路列表，将行业提取出来组成数组并用集合去重
  let list: string[] = []
  lineAllList.value?.forEach((item: SupplierLineInfo) => {
    // 如果线路有行业信息，就追加到数组里
    if (item.secondIndustries?.length) {
      list.push(...item.secondIndustries)
    }
  })
  // 数组去重
  const set = new Set(list)

  // 将集合迭代成列表并拼接成字符串，用于页面展示
  return Array.from(set).join(',')
})

/**
 * 点击编辑供应商按钮
 */
const clickEditSupplier = (current: SupplierInfo) => {
  // 将当前供应商信息传给正在编辑的供应商信息
  editingSupplierItem.value = { ...current }
  editingSupplierId.value = current.id ?? -1
  // 显示弹窗
  dialogSupplierVisible.value = true
}
/**
 * 关闭编辑供应商弹窗
 */
const closeDialogSupplier = () => {
  // 关闭弹窗
  dialogSupplierVisible.value = false
}
/**
 * 供应商弹窗通知父组件更新
 * @param {SupplierInfo} supplier 当前编辑供应商信息
 */
const handleDialogSupplierUpdate = (supplier: SupplierInfo) => {
  // 更新后，当前编辑的供应商会被选中（不管原来编辑的是否是选中的）
  updateSupplierAllList(supplier)
}

// ---------------------------------------- 供应商信息 结束 ----------------------------------------

// ---------------------------------------- 标签卡 通用 开始 ----------------------------------------

// 标签卡全部枚举
enum LIST_TAB_NAME {
  LINE = '线路',
  BLACKLIST = '黑名单',
  GOOD_NUMBER = '靓号限制',
}

// 当前激活的列表标签卡名称
const listTabName = ref<string>(LIST_TAB_NAME.LINE)

// 正在切换标签卡
const loadingTab = computed(() => {
  // 当前激活的标签卡里面列表的加载状态
  switch (listTabName.value) {
    case LIST_TAB_NAME.LINE:
      return loadingLineList.value
    case LIST_TAB_NAME.BLACKLIST:
      return false
    case LIST_TAB_NAME.GOOD_NUMBER:
      return false
    default:
      return false
  }
})

// 需要更新标签卡
const needUpdateTab = ref({
  LINE: false,
  BLACKLIST: false,
  GOOD_NUMBER: false,
})

/**
 * 切换列表标签卡
 */
const handleListTabChange = (val: string) => {
  listTabName.value = val
  updateListTab()
}
/**
 * 更新列表标签卡
 */
const updateListTab = () => {
  // 更新当前选中的供应商信息
  supplierStore.currentSupplier = JSON.parse(JSON.stringify(currentSupplierItem.value))

  // 根据标签卡名称更新相关列表
  switch (listTabName.value) {
    case LIST_TAB_NAME.LINE:
      updateLineAllList()
      break
    case LIST_TAB_NAME.BLACKLIST:
      needUpdateTab.value.BLACKLIST = true
      break
    case LIST_TAB_NAME.GOOD_NUMBER:
      needUpdateTab.value.GOOD_NUMBER = true
      break
    default:
      break
  }
}
/**
 * 标签卡组件已完成更新
 * @param {LIST_TAB_NAME} val 标签卡名称
 */
const onTabUpdated = (val: LIST_TAB_NAME) => {
  switch (val) {
    case LIST_TAB_NAME.LINE:
      needUpdateTab.value.LINE = false
      break
    case LIST_TAB_NAME.BLACKLIST:
      needUpdateTab.value.BLACKLIST = false
      break
    case LIST_TAB_NAME.GOOD_NUMBER:
      needUpdateTab.value.GOOD_NUMBER = false
      break
    default:
      break
  }
}

// ---------------------------------------- 标签卡 通用 结束 ----------------------------------------

// ---------------------------------------- 线路列表 开始 ----------------------------------------

// 正在加载线路列表
const loadingLineList = ref<boolean>(false)
// 线路列表加载节流锁
const throttleLine = new Throttle(loadingLineList)

// 线路列表，全部，接口数据
const lineAllList = ref<SupplierLineInfo[]>()
// 线路列表，当前页，页面展示
const lineList = ref<SupplierLineInfo[]>()
// 线路列表，当前页码，从1开始
const linePageNum = ref<number>(1)
// 线路列表，每页大小
const linePageSize = ref<number>(20)
// 线路列表，列表总长度
const lineTableSize = ref<number>(0)

// 搜索条件，线路名称
const lineNameSearchVal = ref<string>('')
// 搜索条件，线路编号
const lineNumberSearchVal = ref<string>('')
// 搜索条件，线路状态
const lineStatusSearchVal = ref<string>('')
// 搜索条件，支持运营商
const lineOperatorSearchVal = ref<string[]>([])
// 搜索条件，适用行业
const lineIndustrySearchVal = ref<string[]>([])
// 搜索条件，适用行业，级联选择器，数据内容
const industryOptions = ref<{
  label: string, value: string, children: { label: string, value: string }[]
}[]>([])
// 搜索条件，适用行业，级联选择器，配置信息
const industryProps = { multiple: true, emitPath: false }
// 搜索条件，外显号码
const lineDisplayNoSearchVal = ref<string>('')
// 搜索条件，主叫号码
const lineMasterNoSearchVal = ref<string>('')

// 正在编辑的线路信息
const editingLineItem = ref<SupplierLineInfo>({ id: -1 })
// 正在编辑的线路ID
const editingLineId = ref<number>(-1)

// 显示供应线路抽屉
const dialogLineVisible = ref<boolean>(false)
// 供应线路抽屉是否只读
const supplierLineDrawerReadonly = ref<boolean>(false)

// 是否显示支持范围弹窗
const dialogScopeVisible = ref<boolean>(false)
// 支持范围弹窗 数据
const dialogScopeCityCodes = ref<SupplierLineInfo[]>([])

/**
 * 更新全部二级行业列表
 */
const updateAllIndustryList = async () => {
  const globalStore = useGlobalStore()
  const { allIndustryList } = storeToRefs(globalStore)
  await globalStore.getAllIndustryList()
  industryOptions.value = allIndustryList.value.map((primaryItem) => {
    return {
      label: primaryItem?.primaryIndustry ?? '',
      value: primaryItem?.primaryIndustry ?? '',
      children: (primaryItem?.secondaryIndustries ?? []).map((secondItem) => {
        return {
          label: secondItem?.name ?? '',
          value: secondItem?.name ?? '',
        }
      })
    }
  })
}
/**
 * 更新线路列表，全部，接口数据
 * @param {number} pageNum 指定更新后跳转的页码，参数为空默认回到第1页
 * @param {Function} callback 成功更新列表后的回调函数
 */
const updateLineAllList = async (pageNum: number = 1, callback?: Function) => {
  // 节流锁上锁
  if (throttleLine.check()) {
    return
  }
  throttleLine.lock()

  try {
    // 处理参数
    const params: SearchLineParams = {
      callLineSupplierId: currentSupplierItem.value.id ?? -1
      // 参数不传表示为空
    }
    // 有参数才传
    params.supplyLineName = lineNameSearchVal.value !== '' ? lineNameSearchVal.value : undefined
    params.supplyLineNumber = lineNumberSearchVal.value !== '' ? lineNumberSearchVal.value : undefined
    params.enableStatus = lineStatusSearchVal.value !== '' ? lineStatusSearchVal.value : undefined
    params.serviceProviders = lineOperatorSearchVal.value.length ? lineOperatorSearchVal.value : undefined
    params.secondIndustries = lineIndustrySearchVal.value.length ? lineIndustrySearchVal.value : undefined
    params.displayCallNumber = lineDisplayNoSearchVal.value !== '' ? lineDisplayNoSearchVal.value : undefined
    params.masterCallNumber = lineMasterNoSearchVal.value !== '' ? lineMasterNoSearchVal.value : undefined

    // 请求接口
    const res = <SupplierLineInfo[]>await supplierModel.getLineList(params)

    // 列表需要双重排序，先按启用状态排序，再按更新时间倒序排序
    lineAllList.value = (Array.isArray(res) ? res : []).sort((a, b) => {
      if (a.enableStatus !== b.enableStatus) {
        // 启用状态的排在前面
        return a.enableStatus === supplierLineStatusEnum.ENABLED ? -1 : 1
      }
      // 更新时间新的排在前面
      return dayjs(a.updateTime).isAfter(b.updateTime) ? -1 : 1
    })

    // 更新列表总长度
    lineTableSize.value = lineAllList.value.length
    // 重置页码
    linePageNum.value = pageNum
    // 更新当前页码的列表数据
    updateLineList()

    if (callback) {
      const current = lineAllList.value.find((item: SupplierLineInfo) => {
        return item?.id === supplierStore?.editingSupplierLine?.id
      })
      if (current) {
        callback(current)
      }
    }
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleLine.unlock()
  }
}
/**
 * 更新线路列表，当前页，页面展示
 */
const updateLineList = () => {
  lineList.value = updateCurrentPageList(lineAllList.value, linePageNum.value, linePageSize.value)
}
/**
 * 修改线路列表当前页码和页面大小
 */
const updatePage = (p: number, s: number) => {
  linePageNum.value = p
  linePageSize.value = s
  updateLineAllList(linePageNum.value)
}
/**
 * 重置所有搜索条件
 */
const resetLineFilter = () => {
  lineNameSearchVal.value = ''
  lineNumberSearchVal.value = ''
  lineStatusSearchVal.value = ''
  lineOperatorSearchVal.value = []
  lineIndustrySearchVal.value = []
  lineDisplayNoSearchVal.value = ''
  lineMasterNoSearchVal.value = ''
}
/**
 * 点击搜索条件的清除按钮
 */
const clickLineResetFilter = () => {
  resetLineFilter()
}
/**
 * 点击搜索按钮
 */
const clickLineSearch = () => {
  // 执行搜索
  updateLineAllList()
}
/**
 * 点击新建线路按钮
 */
const onClickCreateLine = () => {
  // 缓存数据
  supplierStore.currentSupplier = currentSupplierItem.value
  // 初始化供应商线路
  supplierStore.editingSupplierLine = { id: -1 }
  supplierStore.readonly = false

  // 切换路由到编辑线路子页面
  router.push({ name: 'SupplierLineDetail' })
}
/**
 * 点击复制线路按钮
 */
const onClickCopyLine = () => {
  // 缓存数据
  supplierStore.currentSupplier = currentSupplierItem.value
  // 显示弹窗
  dialogCopyLineVisible.value = true
}
/**
 * 点击某个线路的编辑按钮
 * @param item {SupplierLineInfo} 当前行数据
 * @param readonly {boolean} 是否只读
 */
const clickEditLine = (item: SupplierLineInfo, readonly: boolean = true) => {
  if (loadingLineList.value) {
    return
  }

  // 缓存当前供应线路信息 详情抽屉
  editingLineItem.value = { id: -1, ...item }
  editingLineId.value = item.id ?? -1
  supplierLineDrawerReadonly.value = readonly

  // 缓存当前供应线路信息 编辑子页面
  const supplierStore = useSupplierStore()
  supplierStore.editingSupplierLine = item
  supplierStore.currentSupplier = currentSupplierItem.value
  supplierStore.readonly = readonly

  updateLineAllList(linePageNum.value, (newItem: SupplierLineInfo) => {
    // 点击编辑按钮显示可编辑的子页面
    // 点击详情按钮显示只读的抽屉
    if (readonly) {
      // 更新当前线路信息
      editingLineItem.value = { id: -1, ...newItem }
      editingLineId.value = item.id ?? -1
      // 显示弹窗
      dialogLineVisible.value = true
    } else {
      // 更新当前线路信息
      supplierStore.editingSupplierLine = newItem
      supplierStore.currentSupplier = currentSupplierItem.value
      // 切换路由到编辑线路子页面
      router.push({ name: 'SupplierLineDetail' })
    }
  })
}
/**
 * 点击线路挂起开关
 */
const changePending = (row: SupplierLineInfo) => {
  let notes = ''
  if (row?.notes) {
    let regex = /【(.+?)】/g;
    let options = row?.notes.match(regex)
    notes = options?.join('') || ''
  }
  Confirm({
    text: `<p>您确定要${row.pending ? '挂起' : '取消挂起'}【${row?.lineName || ''}】?</p>` + (
      notes ? `<p style="margin-top:6px;color:#E54B17;font-weight:600;">备注：${notes || ''}</p>` : ''
    ),
    type: 'warning',
    title: `${row.pending ? '挂起' : '取消挂起'}确认`,
    confirmText: `${row.pending ? '挂起' : '确认'}`
  }).then(async () => {
    try {
      const params = {
        supplyLineNumber: row.lineNumber!,
        pendingStatus: row.pending!,
      }
      trace({ page: `线路运营-${row.pending ? '挂起' : '取消挂起'}`, params })
      await supplierModel.switchLine(params)
      ElMessage({
        type: 'success',
        message: '操作成功',
      })
    } catch (err) {
      ElMessage({
        type: 'error',
        message: '操作失败',
      })
    }
  }).catch(() => {
  }).finally(() => {
    updateLineAllList()
  })
}
/**
 * 关闭编辑线路弹窗
 */
const closeDialogLine = () => {
  dialogLineVisible.value = false
  supplierLineDrawerReadonly.value = false
}
/**
 * 查看线路抽屉需要进行修改内容
 */
const handleModifyLine = () => {
  // 关闭抽屉
  closeDialogLine()
  // 相当于点击当前线路的点击编辑按钮
  clickEditLine(editingLineItem.value, false)
}
/**
 * 获取表格线路网关名称
 * 返回线路网关列表的第一项名称，如果找不到或者不存在就返回连字符'-'
 */
const getGatewayName = (row: SupplierLineInfo) => {
  const list = row?.lineGateways
  if (Array.isArray(list) && list.length) {
    const nameList = list.map((item: GatewayItem) => {
      return item?.name ?? ''
    })
    return nameList.join(', ')
  }
  return '-'
}
/**
 * 展示当前供应商线路的运营商外呼范围
 * @param row 当前行，供应商线路信息
 */
const getSupplierLineScope = (row: SupplierLineInfo) => {
  // 当前线路的配置的按运营商归类的城市代码列表
  const list = row?.cityCodeGroups ?? []
  // 展示结果的文本列表
  const res: string[] = []
  // 遍历运营商列表
  list.map(item => {
    // 去重记录配置的省份
    const provinceCodes = new Set<string>([])
    item.cityCodes?.map(city => {
      provinceCodes.add(city.slice(0, 2) + '0000')
    })
    // 同时读取该省份里配置里多少城市
    res.push(
      `${supplierOperatorMap.get(<supplierOperatorEnum>item.serviceProvider)?.text || ''}:
      ${provinceCodes?.size || 0}省${item.cityCodes?.length || 0}市`
    )
  })
  // 拼接整理结果并返回展示
  return res.join(';')
}
/**
 * 点击查看支持地区按钮
 * @param row 当前行，供应商线路信息
 */
const onClickScope = (row: SupplierLineInfo) => {
  dialogScopeCityCodes.value = row.cityCodeGroups ?? []
  dialogScopeVisible.value = true
}
/**
 * 关闭支持范围弹窗
 */
const onCloseDialogScope = () => {
  dialogScopeVisible.value = false
}

// ---------------------------------------- 线路列表 结束 ----------------------------------------

// ---------------------------------------- 复制供应商线路弹窗 开始 ----------------------------------------

// 显示复制线路弹窗
const dialogCopyLineVisible = ref<boolean>(false)
/**
 * 复制线路弹窗的关闭事件回调
 */
const onCloseDialogCopyLine = () => {
  dialogCopyLineVisible.value = false
}
/**
 * 复制线路弹窗的确认事件回调
 * @param item 复制的新的线路信息
 */
const onConfirmDialogCopyLine = (item: SupplierLineInfo) => {
  // console.log('复制线路弹窗的确认事件回调', item)

  // 原线路的一些唯一参数需要删除或重置
  item.id = -1
  item.createTime = undefined
  item.updateTime = undefined
  item.lineNumber = ''
  item.lineGatewayIds = []
  item.lineGateways = []
  item.cityCodeGroups?.forEach((list) => {
    list.id = undefined
  })

  // 关闭复制线路弹窗
  dialogCopyLineVisible.value = false

  // 缓存当前供应商信息
  supplierStore.currentSupplier = currentSupplierItem.value
  // 初始化供应线路信息
  supplierStore.editingSupplierLine = { id: -1, ...item }
  supplierStore.readonly = false

  // 切换路由到编辑线路子页面
  router.push({ name: 'SupplierLineDetail' })
}

// ---------------------------------------- 复制供应商线路弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

onMounted(() => {
  // console.log('onMounted')
  updateAllIndustryList()
})
onActivated(() => {
  // console.log('onActivated')
  // 读取URL参数，筛选供应商
  supplierSearchVal.value = route.query?.callLineSupplierName as string ?? ''
  // 更新供应商列表
  const current = JSON.parse(JSON.stringify(supplierStore.currentSupplier))
  supplierStore.clear()
  updateSupplierAllList(current)
  // 如果需要提示挂载黑名单
  if (supplierStore.needRemindBlackGroup) {
    setTimeout(() => {
      ElMessageBox.alert(
        `<span>根据业务需要，人工直呼线路需挂载“<span style="color: #f00;">【手动】甲方埋点号码</span>”黑名单，请前往设置</span>`,
        '请注意',
        {
          showClose: false,
          confirmButtonText: '去设置',
          appendTo: document.body,
          dangerouslyUseHTMLString: true,
          customStyle: {
            maxWidth: '600px',
          },
        }
      ).then(() => {
        supplierStore.needRemindBlackGroup = false
        listTabName.value = LIST_TAB_NAME.BLACKLIST
        updateListTab()
      }).catch(() => {
      }).finally(() => {
      })
    }, 0)
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
:deep(.el-table) {
  font-size: var(--el-font-size-base);
}
:deep(.tab-box) {
  height: auto;
  padding: 0;
  /* 供应商列表标签卡 */
  &.supplier-tab {
    padding: 8px 12px 0;
    .normal-tab {
      width: 100%;
      height: 100%;
      padding: 4px 12px;
      border-bottom: none;
      font-size: 14px;
      line-height: 22px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  /* 列表模块标签卡 */
  &.list-tab {
    padding: 12px 8px 0;
    .normal-tab {
      /* width: fit-content; */
      width: 80px;
      height: 100%;
      padding: 8px 0;
      /* padding: 8px 12px; */
      border-bottom: none;
      font-size: 16px;
      line-height: 17px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
/*文本框内的标签*/
:deep(.el-cascader__tags .el-tag) {
  margin: 0 0 0 4px;
}
/* 标签卡模块 标签卡 */
.tab-container {
  padding: 0;
  text-align: left;
  /* 标签卡顶部 */
  .tab-header {
    padding: 12px 16px 0;
  }
  /* 标签卡顶部，表单项 */
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
