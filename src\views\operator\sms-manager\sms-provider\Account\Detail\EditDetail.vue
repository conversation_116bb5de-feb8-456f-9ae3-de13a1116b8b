<template>
  <!--详情标题-->
  <HeaderBox :title="[{title: '短信供应商'},{title:isEdit?'编辑短信对接账号':'创建短信对接账号'}]" />

  <!--详情主体-->
  <el-scrollbar class="submodule-detail" wrap-class="detail-main">
    <!--表单组件-->
    <Form ref="formWrapRef" :readonly="false" />
  </el-scrollbar>

  <!--详情底部-->
  <div class="submodule-detail tw-bg-white">
    <div class="detail-footer tw-max-w-[1000px] tw-mx-auto">
      <div class="detail-footer-inner">
        <el-button :disabled="loadingConfirm" :icon="CloseBold" @click="onClickCancel">
          取消
        </el-button>
        <el-button :loading="loadingConfirm" type="primary" :icon="Select" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, ref, toRaw } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import router from '@/router'
import { Throttle } from '@/utils/utils'
import {
  SmsAccountBusinessTypeEnum,
  SmsAccountServiceProviderEnum,
  SmsAccountStatusEnum,
  SmsProtocolEnum,
  SmsProviderAccountParams
} from '@/type/sms'
import { useSmsProviderStore } from '@/store/sms-provider'
import { useUserStore } from '@/store/user'
import { smsAccountModel } from '@/api/sms'
import to from 'await-to-js'
import Form from './components/Form.vue'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const userStore = useUserStore()
const smsProviderStore = useSmsProviderStore()

// 编辑模式 true 编辑 false 新增
const isEdit = computed(() => {
  return typeof formWrapRef.value?.form?.id === 'number'
})

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单容器DOM
const formWrapRef = ref()

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formWrapRef.value && formWrapRef.value?.formRef && formWrapRef.value.formRef.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
        duration: 3000,
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 获取表单组件的表单数据
  if (!formWrapRef.value?.form) {
    ElMessage({
      message: '没有正确读取表单数据，请重试保存',
      type: 'warning',
    })
    return
  }
  const form = JSON.parse(JSON.stringify(toRaw(formWrapRef.value?.form)))

  // 处理参数
  const params: SmsProviderAccountParams = {
    supplierId: form.supplierId ?? smsProviderStore.currentProvider.id,
    supplierNumber: form.supplierNumber ?? smsProviderStore.currentProvider.supplierNumber,
    supplierName: form.supplierName ?? smsProviderStore.currentProvider.supplierName,
    supplierProfile: form.supplierProfile ?? smsProviderStore.currentProvider.supplierProfile,

    id: form.id ?? undefined,
    groupId: form.groupId ?? userStore.groupId,
    account: form.account, // 所属商户（主账号）

    smsAccountNumber: form.smsAccountNumber ?? undefined, // 账号编号
    smsAccountName: form.smsAccountName, // 账号名称
    enableStatus: Object.values(SmsAccountStatusEnum).includes(<SmsAccountStatusEnum>form.enableStatus)
      ? form.enableStatus
      : SmsAccountStatusEnum['启用'], // 启用状态
    pending: form.pending || false, // 挂起状态
    notes: form.notes || '', // 运营备注

    smsProtocol: Object.values(SmsProtocolEnum).includes(<SmsProtocolEnum>form.smsProtocol)
      ? form.smsProtocol
      : SmsProtocolEnum['CMPP'], // 对接协议
    version: form.version, // 协议版本
    connectAddress: form.connectAddress, // 对接地址
    connectPort: form.connectPort, // 对接端口
    extensionCode: form.extensionCode ?? null, // 扩展码
    connectAccount: form.connectAccount, // 账号
    password: form.password, // 密码
    srcId: form.srcId, // 接入码
    serviceId: form.serviceId, // 服务ID
    maxChannels: form.maxChannels || 1, // 最大连接数
    isReturn: form.isReturn || false, // 是否回执
    returnTimeout: form.returnTimeout, // 回执超时时间
    sendDelay: form.sendDelay || 0, // 发送延迟
    configInfo: form.configInfo || '', // 配置信息

    singleSubmitLimit: form.singleSubmitLimit ?? null, // 单次提交上限
    singleDaySubmitLimit: form.singleDaySubmitLimit ?? null, // 单日提交上限
    secondIndustries: form.secondIndustries?.length ? form.secondIndustries : [], // 适用行业
    smsAccountBusinessType: Object.values(SmsAccountBusinessTypeEnum).includes(<SmsAccountBusinessTypeEnum>form.smsAccountBusinessType)
      ? form.smsAccountBusinessType
      : SmsAccountBusinessTypeEnum['群发'], // 适用业务
    submitSpeedLimit: form.submitSpeedLimit ?? null, // 提交速率限制

    submitRestrictions: form.submitRestrictions?.length ? form.submitRestrictions : [], // 提交限制
    sendRestrictions: form.sendRestrictions?.length ? form.sendRestrictions : [],  // 发送限制

    disableTimeSlots: form.disableTimeSlots?.length ? form.disableTimeSlots : [], // 时间限制

    billingCycle: form.billingCycle, // 计费周期
    unitPrice: form.unitPrice, // 短信单价

    serviceProvider: Object.values(SmsAccountServiceProviderEnum).includes(<SmsAccountServiceProviderEnum>form.serviceProvider)
      ? form.serviceProvider
      : SmsAccountServiceProviderEnum['中国移动'], // 支持运营商
    cityCodes: form.cityCodes?.length ? form.cityCodes : [], // 支持省市
  }

  // 请求接口
  let error
  if (isEdit.value) {
    // 编辑
    const [err, _] = await to(smsAccountModel.editAccount(params))
    error = err
  } else {
    // 新建
    const [err, _] = await to(smsAccountModel.addAccount(params))
    error = err
  }

  // 返回失败结果
  if (error) {
    ElMessage({
      message: '保存失败',
      type: 'error',
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }

  // 返回成功结果
  ElMessage({
    message: '保存成功',
    type: 'success',
  })
  // 关闭弹窗
  closeDialog()
  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  router.back()
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
