<template>
  <div class="header-box tw-flex tw-items-center tw-float-right tw-justify-end tw-h-[56px]">
    <slot name="seat"></slot>
    <el-button v-if="isDev" class="tw-mx-1" @click="expotCollectInterface()" link type="primary">
      <span>收集接口</span>
    </el-button>
    <span v-if="accountType === 0" class="tw-mr-1 tw-text-[13px] tw-text-[var(--primary-black-color-300)]">运营端</span>
    <!--账号信息-->
    <el-dropdown @command="handleCommand">
      <!--显示内容-->
      <div
        style="border: 0"
        class="tw-flex tw-items-center tw-text-[13px] tw-text-[var(--primary-black-color-300)] hover:tw-text-[var(--el-color-primary)] tw-truncate tw-max-w-[240px] tw-font-[400]"
      >
       
        <!--图标-->
        <el-icon :size="20" class="tw-mr-[8px]" color="inherit">
          <SvgIcon v-if="accountType === 0" color="inherit" name="user-o" />
          <SvgIcon v-else color="inherit" name="user-m" />
        </el-icon>
        <!--账号名-->
        {{ isMasterAccount ? '【主账号】' : '' }}
        {{ account }}
      </div>

      <!--下拉菜单-->
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-if="userStore.accountType === 1" command="information"><span>账号信息</span></el-dropdown-item>
          <el-dropdown-item command="password"><span>修改密码</span></el-dropdown-item>
          <el-dropdown-item command="logout">退出</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
  <CollectInterfaceDialog
    v-model:visible="collectInterfaceVisible"
  >
  </CollectInterfaceDialog>
  <PasswordEditDialog
    v-model:editPassWordVisible="editPassWordVisible"
    :passwordData="passwordData"
    :isSelf="true"
    @confirm="confirmPassword"
  >
  </PasswordEditDialog>
  <el-dialog
    v-model="checkInfoVisible"
    width="400px"
    align-center
    :close-on-click-modal="true"
    @close="closeAccountDialog"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">账号信息</div>
    </template>
    <div class="tw-text-[13px] tw-p-[16px] tw-text-left tw-leading-[24px]">
      <div class="account-item">
        <span class="account-label">账号：</span>
        <span class="account-info">{{ userStore.account || '-' }}</span>
      </div>
      <div class="account-item">
        <span class="account-label">姓名：</span>
        <span class="account-info">{{ accountInfo?.name || '-' }}</span>
      </div>
      <div class="account-item">
        <span class="account-label">所属主账号：</span>
        <span class="account-info">{{ accountInfo?.mainAccount || '-' }}</span>
      </div>
      <div class="account-item">
        <span class="account-label">主账号信息：</span>
        <span class="account-info">{{ accountInfo?.mainAccountName || '-' }}</span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeAccountDialog">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { useRouteStore } from "@/store/routes"
import { userModel } from '@/api/user'
import { storeToRefs } from 'pinia'
import { ColumnsItem } from '@/type/common'
import { router } from "@/router"
import { AppRouteRecordRawT } from '@/type/route'
import PasswordEditDialog from '@/components/PasswordEditDialog.vue'
import CollectInterfaceDialog from '@/components/CollectInterfaceDialog.vue'
import { useSeatPhoneStore } from '@/store/seat-phone'
import to from 'await-to-js'

const userStore = useUserStore()
const isMasterAccount = userStore.isMasterAccount || false
const isDev = import.meta.env.MODE.includes('development')
const routeStore = useRouteStore()
const { asyncRouts } = storeToRefs(routeStore)
const menuOptions: ColumnsItem[] = []
const { account, accountType } = userStore
const seatPhoneStore = useSeatPhoneStore()

/**
 * 递归处理路由信息
 * @param list {AppRouteRecordRawT[]} 当前递归层次的路由信息列表
 */
const recurseRouteName = (list: AppRouteRecordRawT[]) => {
  // 遍历当前层级列表
  list.forEach(subItem => {
    // 将路由信息加入搜索框的下拉菜单列表里
    menuOptions.push({
      property: subItem.meta.title,
      label: subItem.name
    })
    // 如果路由有children属性，继续深入递归处理
    if (subItem.children && subItem.children.length > 0) {
      recurseRouteName(subItem.children)
    }
  })
}
// 将路由信息中的name和title解析到搜索框的下拉菜单列表里
recurseRouteName(asyncRouts.value)
const editPassWordVisible = ref(false)
const checkInfoVisible = ref(false)
const passwordData = reactive<{
  id: number,
  account: string,
  oldPassword: string,
  newPassword: string
}>({
  id: -1,
  account: '',
  oldPassword: '',
  newPassword: ''
})
const accountInfo = ref<null | {
  account: string,
  name: string,
  mainAccount: string,
  mainAccountName: string,
}>(null)
const handleCommand = async (command: string) => {
  // 退出登录
  if (command === 'logout') {
    // 如果打开了坐席工作台，当前账号下线坐席并重置坐席信息
    if (router.currentRoute.value.name === 'Workbench') {
      await seatPhoneStore.requestOffline()
    }
    // 回到登录页
    setTimeout(() => {
      // 清空用户信息
      userStore.logout()
      router.push("/login")
    }, 100)
  }
  // 修改密码
  if (command === 'password') {
    editPassWordVisible.value = true
    passwordData.id = userStore.userId as number
    passwordData.account = userStore.account
    passwordData.oldPassword = ''
  }
  // 查看账号信息
  if (command === 'information' && userStore.accountType === 1) {
    const [err, res] = await to(userModel.findAccountInfo({
      account: userStore.account,
      groupId: userStore.groupId,
    }))
    accountInfo.value = res || null
    checkInfoVisible.value = true
  }
}

const closeAccountDialog = () => {
  accountInfo.value = null
  checkInfoVisible.value = false
}

const confirmPassword = async (params: {
  id: number,
  oldPassword: string
  newPassword: string
}) => {
  await userModel.changePassword(params)
  ElMessage({
    type: 'success',
    message: '操作成功'
  })
  editPassWordVisible.value = false
}

const collectInterfaceVisible = ref(false)
const expotCollectInterface = async () => {
  collectInterfaceVisible.value = true
}
</script>

<style scoped lang="postcss" type="text/postcss">
.header-box {
  :deep(.el-input__wrapper) {
    border-radius: 4px;
  }
  .el-dropdown-link:focus {
    outline: none;
  }
  .el-button:focus-visible {
    outline: none;
  }
}
.account-item {
  margin-bottom: 10px;
  line-height: 24px;
  display: flex;
  &:last-child {
    margin-bottom: 0px;
  }
  .account-label {
    text-align: right;
    width: 90px;
    flex: 0 0 auto;
  }
  .account-info {
    color: #969799;
    word-break: break-all;
    flex-shrink: 1;
    flex-grow: 1;
  }
}

</style>
