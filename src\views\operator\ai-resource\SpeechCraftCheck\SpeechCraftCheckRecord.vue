<template>
  <!--模块主体-->
  <div v-loading="loading" class="tw-min-w-[1080px] tw-bg-white tw-flex tw-flex-col tw-h-[calc(100vh-130px)] tw-overflow-hidden">

    <!--搜索区块-->
    <div class="tw-w-full tw-p-[16px] tw-grow-0 tw-shrink-0">
      <div class="tw-grid tw-grid-cols-4 tw-gap-3 tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.scriptName"
            placeholder="话术名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.userAccount"
            placeholder="登录账号"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-select
            v-model.trim="searchForm.commitType"
            placeholder="提交类型"
            class="tw-w-full"
            clearable
          >
            <el-option
              v-for="optionItem in Object.values(scriptCommitTypeList)"
              :key="optionItem.name"
              :value="optionItem.val"
              :label="optionItem.text"
            />
          </el-select>
        </div>
        <div class="item">
          <el-select
            v-model.trim="searchForm.checkRes"
            placeholder="审核结果"
            class="tw-w-full"
            clearable
          >
            <el-option
              v-for="optionItem in Object.values(scriptCheckResList)"
              :key="optionItem.name"
              :value="optionItem.val"
              :label="optionItem.text"
            />
          </el-select>
        </div>
        <div class="tw-col-span-2 item">
          <TimePickerBox
            class="tw-w-full"
            v-model:start="searchForm.startCommitTime"
            v-model:end="searchForm.endCommitTime"
            placeholder="提交时间范围"
            :clearable="false"
          />
        </div>
        <div class="tw-col-span-2 item">
          <TimePickerBox
            class="tw-w-full"
            v-model:start="searchForm.startCheckTime"
            v-model:end="searchForm.endCheckTime"
            placeholder="审核时间范围"
            :clearable="false"
          />
        </div>

      </div>
      <div class="tw-flex tw-justify-end tw-mt-[12px]">
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>

    <!--数据区块-->
    <!--表格容器-->
    <el-table
      stripe
      v-loading="loading || loadingList"
      class="tw-grow"
      :data="tableData"
      :header-cell-style="tableHeaderStyle"
    >
      <el-table-column align="left" prop="scriptName" label="话术名称" show-overflow-tooltip width="280" />
      <el-table-column align="left" prop="userAccount" label="登录账号" show-overflow-tooltip width="160" :formatter="formatterEmptyData"/>
      <el-table-column align="left" prop="userName" label="姓名" show-overflow-tooltip width="100" :formatter="formatterEmptyData"/>
      <el-table-column align="center" prop="commitType" label="提交类型" show-overflow-tooltip width="100" :formatter="formatterEmptyData">
        <template #default="{row}">
          <div class="status-box-mini tw-m-auto" :class="scriptCommitTypeMap.get(row.commitType)?.color">
            {{ scriptCommitTypeMap.get(row.commitType)?.text ?? '' }}
          </div>
         
        </template>
      </el-table-column>
      <el-table-column align="center" prop="checkRes" label="审核结果" width="100" :formatter="formatterEmptyData">
        <template #default="{row}">
          <div class="status-box-mini tw-m-auto" :class="scriptCheckResMap.get(row.checkRes)?.color">
            {{ scriptCheckResMap.get(row.checkRes)?.text ?? '' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" prop="opinion" label="审核意见" min-width="200" :formatter="formatterEmptyData"/>
      <el-table-column align="center" prop="updateTime" label="审核时间" width="160">
        <template #default="{row}">
          {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>

      <!--空数据提示-->
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <!--分页-->
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="pageNum"
      :total="tableSize || 0"
      @search="search"
      @update="updatePage"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'

import { ScriptCheckRecord } from '@/type/speech-craft'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { Throttle, formatterEmptyData } from '@/utils/utils'
import { scriptCheckModel } from '@/api/speech-craft'
import dayjs from 'dayjs'
import {
  scriptCheckResList,
  scriptCheckResMap,
  scriptCommitTypeList,
  scriptCommitTypeMap
} from '@/assets/js/map-script'
import { tableHeaderStyle } from '@/assets/js/constant'
import PaginationBox from '@/components/PaginationBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'

// ---------- 通用 开始 ----------

// 全局变量
const globalStore = useGlobalStore()
// 正在加载
const { loading } = storeToRefs(globalStore)

// ---------- 通用 结束 ----------

// ---------- 搜索 开始 ----------


// 搜索条件
class SearchOrigin {
  scriptName = null
  userAccount = null
  commitType = null
  checkRes = null
  startCommitTime = dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss')
  endCommitTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  startCheckTime = dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss')
  endCheckTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
}
const searchForm = reactive<ScriptCheckRecord>(new SearchOrigin())


// 搜索筛选条件
const search = () => {
  // 将指定好的筛选条件发给接口，获取列表数据
  updateAllList()
}
// 重置筛选条件
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}


// ---------- 搜索 结束 ----------

// ---------- 列表 开始 ----------

// 正在加载列表数据
const loadingList = ref<boolean>(false)
// 正在加载列表数据，节流锁
const throttleLoadingList = new Throttle(loadingList)

// 表格所有数据，用户接口返回结果的本地缓存
const tableAllData = ref<ScriptCheckRecord[]>()
// tableData是tableAllData的子集，前者深拷贝后者，两者不干扰

// 当前页码，从1开始
const pageNum = ref<number>(1)
// 每页大小
const pageSize = ref<number>(20)
// 列表总长度
const tableSize = ref<number>(0)

// 表格数据，用于当前页面展示
const tableData = computed(() => {
  return tableAllData.value?.slice((pageNum.value - 1) * pageSize.value, pageNum.value * pageSize.value) || []
})
/**
 * 修改当前页码
 */
 const updatePage = (p: number, s: number) => {
  pageNum.value = p
  pageSize.value = s
}

/**
 * 请求接口并更新全部列表数据
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleLoadingList.check()) {
    return
  }
  throttleLoadingList.lock()

  try {
    // 请求接口
    const res = await scriptCheckModel.getScriptCheckList(searchForm)

    // 更新列表数据
    const tempList = Array.isArray(res) ? res : []
    // 将列表按审核时间倒序排序
    tableAllData.value = tempList.sort((a, b) => {
      return dayjs(a.updateTime).isAfter(dayjs(b.updateTime)) ? -1 : 1
    })
    // 更新列表总长度
    tableSize.value = tableAllData.value.length

    // 页码从1开始
    pageNum.value = 1

  } catch (err) {
    ElMessage({
      message: '话术审核列表查询失败',
      type: 'error',
    })
  } finally {
    // 节流锁解锁
    throttleLoadingList.unlock()
  }
}


// 初始化
updateAllList()

// ---------- 列表 结束 ----------
</script>

<style scoped lang="postcss">
.item {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.el-table {
  font-size: var(--el-font-size-base);
  :deep(.cell) {
    padding: 0 8px;
  }
  :deep(.caret-wrapper) {
    display: none;
  }
}
</style>
