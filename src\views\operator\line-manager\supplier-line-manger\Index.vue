<template>
  <HeaderBox title="供应线路" class="tw-grow-0 tw-shrink-0"/>
  <div class="line-container">
    <TabsBox v-model:active="activeTab" :tabList="tabList"></TabsBox>
    <keep-alive>
      <Monitor v-if="activeTab == '运行监控'"/>
      <Line v-else-if="activeTab == '线路列表'"/>
      <Dosage v-else-if="activeTab == '用量统计'"/>
      <el-empty v-else/>
    </keep-alive>
   
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, computed, ref, watch, onDeactivated } from 'vue'
import { useRoute } from 'vue-router'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
import { onBeforeRouteLeave } from 'vue-router'
const Line = defineAsyncComponent({loader: () => import('./Line.vue')})
const Monitor = defineAsyncComponent({loader: () => import('./Monitor.vue')})
const Dosage = defineAsyncComponent({loader: () => import('./Dosage.vue')})

// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap['供应线路'].id]

// 路由
const route = useRoute();
const tabMap = {
  1: '线路列表',
  2: '运行监控',
  3: '用量统计',
}
const tabList = computed(() => {
  const res: string[] = []
  permissions.includes(routeMap['供应线路'].permissions['运行监控']) && res.push('运行监控')
  permissions.includes(routeMap['供应线路'].permissions['线路列表']) && res.push('线路列表')
  permissions.includes(routeMap['供应线路'].permissions['用量统计']) && res.push('用量统计')
  return res
})

const activeTab = ref(tabList.value[0] || '')
watch(() => route.query.activeTab, () => {
  const t = route.query.activeTab as unknown as keyof typeof tabMap
  if (route.query.activeTab) {
    activeTab.value = tabMap[t]
  }
}, {
  deep: true, immediate: true
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-container {
  width: 100%;
  box-sizing: border-box;
  min-width: 1080px;
  padding: 16px;
  height: calc(100vh - 55px);
  position: relative;
  font-size: 13px;
}
</style>