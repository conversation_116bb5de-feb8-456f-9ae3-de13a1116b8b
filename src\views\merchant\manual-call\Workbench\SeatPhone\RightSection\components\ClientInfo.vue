<template>
  <div v-show="seatPhoneStore.currentClue.name||seatPhoneStore.currentClue.comment" class="section-block">
    <div class="section-block-header">
      客户信息
    </div>
    <div class="section-block-main">
      <p v-show="seatPhoneStore.currentClue.name" class="info-item">
        <span class="info-title">姓名：</span>
        <span class="info-content">{{ seatPhoneStore.currentClue.name ?? '' }}</span>
      </p>

      <!--备注保留3行，溢出滚动-->
      <el-scrollbar v-show="seatPhoneStore.currentClue.comment" class="info-item note">
        <span class="info-title">备注：</span>
        <span class="info-content">{{ seatPhoneStore.currentClue.comment ?? '' }}</span>
      </el-scrollbar>

      <p v-show="resendSmsButtonVisible" class="info-item tw-mt-[8px] tw-flex tw-flex-row tw-justify-end">
        <el-button type="primary" size="small" :loading="seatPhoneStore.loadingResendSms" @click="onClickResendSms">
          重新发送
        </el-button>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSeatPhoneStore } from '@/store/seat-phone'
import { computed } from 'vue'

// ---------------------------------------- 通用 开始 ----------------------------------------

const seatPhoneStore = useSeatPhoneStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 重新发送短信 开始 ----------------------------------------

const resendSmsButtonVisible = computed(() => {
  // 线索的备注以【开头，表示这个字符串是一个包含短信签名和短信内容的短信全文
  const sms: string = (seatPhoneStore.currentClue.comment ?? '').trim()
  return sms.startsWith('【')
})

/**
 * 点击重新发送短信按钮
 */
const onClickResendSms = () => {
  seatPhoneStore.resendSms()
}

// ---------------------------------------- 重新发送短信 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.section-block-header {
  flex: auto;
  padding: 8px 12px;
  border-bottom: 1px solid #E1E3E6;
  background: #FFF;
  color: #313233;
  font-size: 14px;
  font-weight: 600;
}
.section-block-main {
  width: 100%;
  padding: 8px 16px;
}
.note {
  overflow-y: auto;
  max-height: 70px;
  margin: 8px 0 0;
  line-height: 20px;
}
</style>
