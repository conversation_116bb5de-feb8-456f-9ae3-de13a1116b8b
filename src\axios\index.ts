import Request from './request'
import { AxiosResponse } from 'axios'

import type { RequestConfig } from './request/types'

// export interface HttpResponse<T> {
//   statusCode: number
//   desc: string
//   result: T
// }

// // 重写返回类型
// interface HttpRequestConfig<T, R> extends RequestConfig<HttpResponse<R>> {
//   data?: T
// }

// 根据页面URL协议，确定请求URL协议、服务器地址、路径公共前缀
let baseURL = location.protocol === 'https:'
  ? import.meta.env.VITE_API_ORIGIN_HTTPS
  : import.meta.env.VITE_API_ORIGIN_HTTP

export const request = new Request({
  baseURL: baseURL,
  // baseURL: './market',
  timeout: 1000 * 60 * 5,
  interceptors: {
    // 请求拦截器
    requestInterceptors: config => config,
    // 响应拦截器
    responseInterceptors: (result: AxiosResponse) => {
      return result
    },
  },
})

// export const http = <D = any, T = any>(config: HttpRequestConfig<D, T>) => {
//   const { method = 'GET' } = config
//   if (method === 'get' || method === 'GET') {
//     config.params = config.data
//   }
//   return request.request<HttpResponse<T>>(config)
// }
export const http = (config: RequestConfig) => {
  const { method = 'GET' } = config

  /* 参数兼容性处理 */
  // 不区分大小写
  if (method.toUpperCase() === 'GET') {
    // data是POST请求用的
    // params是GET请求用的
    // 如果GET请求只给了data参数但没给params参数，params照搬data的
    if (config.data) {
      config.params = config.data
    }
  }

  return request.request(config)
}
// 取消请求
export const cancelRequest = (url: string | string[]) => {
  return request.cancelRequest(url)
}
// 取消全部请求
export const cancelAllRequest = () => {
  return request.cancelAllRequest()
}
