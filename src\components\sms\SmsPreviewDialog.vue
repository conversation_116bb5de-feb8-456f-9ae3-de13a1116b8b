<template>
  <el-dialog
    :model-value="props.visible"
    class="merchant-dialog sms-preview-dialog"
    align-center
    width="422px"
    :show-close="false"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-scrollbar class="form-dialog-main">
      <div class="phone-wrapper">
        <div class="phone-content">
          <el-scrollbar class="phone-bubble">
            <div class="phone-bubble-content" v-html="smsContent" />
          </el-scrollbar>
          <div class="back-button" @click="onClickCancel" />
        </div>
        <div class="phone-border" />
      </div>
    </el-scrollbar>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, ref, watch } from 'vue'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  content: string,
}>(), {
  visible: false,
  content: '',
})
const emits = defineEmits([
  'update:visible',
  'close'
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

/**
 * 更新表单
 */
const updateForm = () => {
  // 将换行符转换成br标签，将空格转换成nbsp
  // \s包含空格和换行，所以应该先处理换行，再处理一般的空格
  smsContent.value = (props.content ?? '')
    .replace(/\n/g, '<br/>')
    .replace(/\s/g, '&nbsp;')
}
/**
 * 重置表单
 */
const resetForm = () => {
  smsContent.value = ''
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  emits('close')
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 短信内容 开始 ----------------------------------------

// 短信内容
const smsContent = ref<string>('')

// ---------------------------------------- 短信内容 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    updateForm()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.phone-wrapper {
  position: relative;
  width: 422px;
  height: 854px;
}
.phone-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 422px;
  height: 854px;
  background: url(@/assets/img/phone-border.png) no-repeat center;
  background-size: contain;
  pointer-events: none;
}
.phone-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 403px;
  height: 840px;
  background: url(@/assets/img/phone-content.png) no-repeat center;
  background-size: contain;
}
.phone-bubble {
  position: absolute;
  top: 200px;
  left: 26px;
  overflow-y: auto;
  width: 340px;
  height: 500px;
  /* 隐藏滚动条，但保留滚动功能 */
  &::-webkit-scrollbar {
    display: none;
  }
  /* 兼容IE和Edge */
  & {
    -ms-overflow-style: none;
  }
  /* 兼容Firefox */
  & {
    scrollbar-width: none;
  }
  .phone-bubble-content {
    position: relative;
    width: 274px;
    height: auto;
    padding: 7px 12px;
    border-radius: 20px;
    background: #E9E9EB;
    color: #000;
    font-size: 15px;
    line-height: 20px;
    text-align: left;
    word-break: break-all;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -3px;
      width: 17px;
      height: 17px;
      background: url(@/assets/img/bubble-tail.png) no-repeat center;
      background-size: contain;
    }
  }
}
.back-button {
  position: absolute;
  top: 70px;
  left: 14px;
  width: 60px;
  height: 60px;
  cursor: pointer;
}
</style>
