<template>
  <el-table
    :data="list"
    stripe
    border
    :header-cell-style="tableHeaderStyle"
  >
    <el-table-column align="left" prop="lineName" label="线路名称" min-width="160" show-overflow-tooltip />
    <el-table-column align="center" prop="lineNumber" label="线路编号" min-width="160" />
    <el-table-column align="left" prop="callLineSupplierName" label="供应商名称" min-width="120" show-overflow-tooltip />
    <el-table-column align="center" prop="enableStatus" label="启用状态" width="80" show-overflow-tooltip>
      <template #default="{row}:{row:SupplierLineInfo}">
        <div
          class="status-box-mini"
          :class="row.enableStatus === supplierLineStatusList.enabled.val ? 'green-status' : 'orange-status'"
        >
          {{ row.enableStatus ? supplierLineStatusMap.get(row.enableStatus)?.text ?? '' : '' }}
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="lineGateways" label="线路网关" width="120" show-overflow-tooltip>
      <template #default="{row}:{row:SupplierLineInfo}">
        {{ getSupplierLineGatewayText(row.lineGateways ?? []) }}
      </template>
    </el-table-column>
    <el-table-column align="left" prop="concurrentLimit" label="并发上限" width="120" />
    <el-table-column align="left" prop="masterCallNumber" label="主叫号码" min-width="120" show-overflow-tooltip />
    <el-table-column align="left" prop="displayCallNumber" label="外显号码" min-width="160" show-overflow-tooltip />
    <el-table-column align="left" prop="secondIndustries" label="支持行业" min-width="160" show-overflow-tooltip>
      <template #default="{row}:{row:SupplierLineInfo}">
        <div class="tw-truncate">
          {{ getSupplyLineSecondIndustriesText(row.secondIndustries || []) }}
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="billingCycle" label="计费周期" width="120" show-overflow-tooltip />
    <el-table-column align="left" prop="unitPrice" label="线路单价(元)" width="120" show-overflow-tooltip />
    <el-table-column align="left" prop="supplyLimit" fixed="right" label="最大可支配并发" width="120">
      <template #default="{row}:{row:SupplierLineInfo}">
        <div class="tw-flex tw-items-center tw-flex-nowrap">
          <span>
            {{ row.supplyLimit ?? '-' }}
          </span>
          <div class="tw-ml-[8px]">
            <el-button link @click="onClickEditMaxConcurrency(row)">
              <el-icon :size="18" color="#165DFF">
                <SvgIcon name="edit2" />
              </el-icon>
            </el-button>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="priorityStatus" fixed="right" label="优先状态" width="90">
      <template #default="{row}:{row:SupplierLineInfo}">
        <el-switch
          v-model="row.priorityStatus"
          inline-prompt
          active-text="是"
          inactive-text="否"
          active-color="var(--primary-blue-color)"
          @click="changePriorityStatus(row)"
        />
      </template>
    </el-table-column>
    <el-table-column align="right" fixed="right" label="操作" width="160">
      <template #default="{row}:{row:SupplierLineInfo}">
        <el-switch
          v-model="row.pending"
          inline-prompt
          active-text="已挂起"
          inactive-text="未挂起"
          @click="changePending(row)"
        />
        <el-switch
          v-model="row.pendingStatus"
          class="tw-ml-[8px]"
          inline-prompt
          active-text="已临停"
          inactive-text="未临停"
          active-color="var(--primary-orange-color)"
          @click="changePendingStatus(row)"
        />
      </template>
    </el-table-column>
  </el-table>

  <!--并发编辑弹窗-->
  <DialogConcurrency
    v-model:visible.sync="dialogConcurrencyVisible"
    :data="currentSupplyLine"
    @confirm="onDialogConcurrencyConfirm"
  />
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { SupplierLineInfo } from '@/type/supplier'
import { supplierLineStatusList, supplierLineStatusMap } from '@/assets/js/map-supplier'
import { getSupplierLineGatewayText, getSupplyLineSecondIndustriesText } from '@/utils/line'
import { merchantSupplyLineModel } from '@/api/merchant'
import { useMerchantStore } from '@/store/merchant'
import { ElMessage } from 'element-plus'
import to from 'await-to-js'
import { supplierModel } from '@/api/supplier'
import Confirm from '@/components/message-box'
import SvgIcon from '@/components/SvgIcon.vue'
import DialogConcurrency from './DialogConcurrency.vue'
import { trace } from '@/utils/trace'
import { MerchantLineTypeEnum } from '@/type/merchant'

// ---------------------------------------- 通用 开始 ----------------------------------------

const merchantStore = useMerchantStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表格 开始 ----------------------------------------

// 供应线路列表
const list = ref<SupplierLineInfo[]>([])

/**
 * 更新供应线路列表
 */
const updateList = async () => {
  const [err, res] = <[any, SupplierLineInfo[]]>await to(merchantSupplyLineModel.getList({
    tenantLineNumber: merchantStore.editingMerchantLine.lineNumber,
  }))
  if (err) {
    ElMessage({
      type: 'error',
      message: '无法获取供应线路列表',
    })
    return
  }
  list.value = res?.length ? res : []
}
/**
 * 处理切换开关的通用函数
 * @param row 线路信息
 * @param status 当前状态
 * @param type 类型
 */
const handleSwitch = (row: SupplierLineInfo, status: boolean, type: '挂起' | '临停') => {
  let notes = ''
  if (row?.notes) {
    let regex = /【(.+?)】/g;
    let options = row?.notes.match(regex)
    notes = options?.join('') || ''
  }
  Confirm({
    text: `<p>您确定要${status ? type : ('取消' + type)}【${row?.lineName || ''}】?</p>` + (
      notes ? `<p style="margin-top:6px;color:#E54B17;font-weight:600;">备注：${notes || ''}</p>` : ''
    ),
    type: 'warning',
    title: `${status ? type : ('取消' + type)}确认`,
    confirmText: `${status ? type : '确认'}`
  }).then(async () => {
    let err: null | Error = null
    if (type === '挂起') {
      const params = {
        supplyLineNumber: row.lineNumber ?? '',
        pendingStatus: status || false,
      }
      trace({
        page: `编辑商户线路-线路配置-${status ? '' : '取消'}挂起线路`,
        params,
      })
      const res = await to(supplierModel.switchLine(params))
      err = res[0]
    } else if (type === '临停') {
      const params = {
        pendingStatus: status || false,
        supplyLineNumber: row.lineNumber,
        tenantLineNumber: merchantStore.editingMerchantLine.lineNumber,
      }
      trace({
        page: `编辑商户线路-线路配置-${status ? '' : '取消'}临停线路`,
        params,
      })
      const res = await to(merchantSupplyLineModel.switchPendingStatus(params))
      err = res[0]
    }
    !err && ElMessage.success('操作成功')
  }).catch(() => {
    if (type === '挂起') {
      row.pending = !status
    } else if (type === '临停') {
      row.pendingStatus = !status
    }
  }).finally(() => {
  })
}
/**
 * 点击编辑最大可支配并发
 */
const onClickEditMaxConcurrency = (row: SupplierLineInfo) => {
  // 显示最大可支配并发编辑弹窗
  currentSupplyLine.value = row ?? {}
  dialogConcurrencyVisible.value = true
}
/**
 * 点击线路挂起开关
 * @param row 线路信息
 */
const changePending = async (row: SupplierLineInfo) => {
  handleSwitch(row, row.pending || false, '挂起')
}
/**
 * 点击线路临停开关
 * @param row 线路信息
 */
const changePendingStatus = (row: SupplierLineInfo) => {
  handleSwitch(row, row.pendingStatus || false, '临停')
}

/** 
 * 修改优先状态
 * @param row 线路信息
 */
const changePriorityStatus = async (row: SupplierLineInfo) => {
  // 判断是否设置了最大可支配并发，如无则无法修改优先线路
  if (!row.supplyLimit) {
    ElMessage.warning('请先设置最大可支配并发')
    row.priorityStatus = !row.priorityStatus
    return
  }
  // if (!!row.caps) {
  //   ElMessage.warning('无法同时设置速率限制（caps）和优先线路')
  //   row.priorityStatus = !row.priorityStatus
  //   return
  // }
  if (row.lineType === MerchantLineTypeEnum['人工直呼'] && row.priorityStatus) {
    ElMessage.warning('人工直呼线路无法设置优先线路')
    row.priorityStatus = !row.priorityStatus
    return
  }

  let notes = ''
  if (row?.notes) {
    let regex = /【(.+?)】/g;
    let options = row?.notes.match(regex)
    notes = options?.join('') || ''
  }
  Confirm({
    text: `<p>您确定要${row.priorityStatus ? '设置' : '取消'}【${row?.lineName || ''}】为优先线路?</p>` + (
      notes ? `<p style="margin-top:6px;color:#E54B17;font-weight:600;">备注：${notes || ''}</p>` : ''
    ),
    type: 'warning',
    title: `操作确认`,
  }).then(async () => {
    const params = {
      tenantLineNumber: merchantStore.editingMerchantLine.lineNumber || '',
      supplyLineNumber: row?.lineNumber || '',
      isPriority: row.priorityStatus!,
    }
    trace({ page: `商户管理-${row.priorityStatus ? '设置' : '取消'}【${row?.lineName || ''}】为优先线路`, params })
    const [err2] = await to(merchantSupplyLineModel.switchPriorityStatus(params))
    !err2 && ElMessage.success('操作成功')
  }).catch(() => {
  }).finally(() => {
    updateList()
  })
}

// ---------------------------------------- 表格 结束 ----------------------------------------

// ---------------------------------------- 并发编辑弹窗 开始 ----------------------------------------

// 并发编辑弹窗是否可见
const dialogConcurrencyVisible = ref(false)
// 当前编辑的供应线路信息
const currentSupplyLine = ref<SupplierLineInfo>({})

/**
 * 开发编辑弹窗，提交表单
 */
const onDialogConcurrencyConfirm = () => {
  currentSupplyLine.value = {}
  updateList()
}

// ---------------------------------------- 并发编辑弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

onMounted(updateList)

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.el-table :deep(.cell) {
  font-size: 13px;
}
</style>
