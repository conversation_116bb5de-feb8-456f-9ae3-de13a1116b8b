<template>
  <HeaderBox :title="titleList" />
  <div class="dosage-daily-container">
    <div class="tw-w-full tw-bg-white tw-mb-[16px] tw-p-[16px]">
      <div class="tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-gap-3 tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model="searchForm.supplyLineName"
            placeholder="供应线路名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.supplyLineNumber"
            placeholder="供应线路编号"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.masterCallNumber"
            placeholder="主叫号码"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.supplierName"
            placeholder="供应商名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model="searchForm.supplierNumber"
            placeholder="供应商编号"
            clearable
            @keyup.enter="search"
          />
        </div>
      </div>
      <div class="tw-flex tw-justify-end tw-mt-[12px]">
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
    <div class="tw-mb-[8px] tw-flex tw-justify-end tw-items-center">
      <button @click="exportDetails2Excel" class="common-btn"><el-icon size="--el-font-size-base" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>批量导出</button>
      <button @click="exportCurrent2Excel" class="common-btn tw-ml-[8px]"><el-icon size="--el-font-size-base" class="tw-mr-0.5"><SvgIcon name="download3" color="none"></SvgIcon></el-icon>导出数据</button>
    </div>
    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      ref="tableRef"
      :row-key="(row:SupplierDosageDailyItem) => row.supplyLineName! + row.supplyLineNumber! + '1'"
      :header-cell-style="tableHeaderStyle"
      :row-class-name="getTotalRowClass"
      @sort-change="handleSortChange"
      stripe
      border
    >
      <el-table-column property="supplyLineName" label="供应线路名称" align="left" fixed="left" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.supplyLineName || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="supplyLineType" label="线路类型" align="center" width="80" :formatter="formatterEmptyData" show-overflow-tooltip fixed="left">
        <template #default="{ row }">
          {{ getSupplierLineTypeText(row?.supplyLineType) }}
        </template>
      </el-table-column>
      <el-table-column property="supplyLineNumber" label="线路编号" align="left" fixed="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="supplierName" label="供应商名称" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="supplierNumber" label="供应商编号" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="masterCallNumber" label="主叫号码" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>

      <el-table-column property="calculateNumOfSixty" label="计量数（60s)" sortable="custom" align="left" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.calculateNumOfSixty) }}
        </template>
      </el-table-column>
      <el-table-column property="calculateNumOfSix" label="计量数（6s)" sortable="custom" align="left" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.calculateNumOfSix) }}
        </template>
      </el-table-column>
      <el-table-column property="totalCallNum" label="呼叫总量" align="left" sortable="custom" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.totalCallNum) }}
        </template>
      </el-table-column>
      <el-table-column property="totalConnectNum" label="接通总量" align="left" sortable="custom" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.totalConnectNum) }}
        </template>
      </el-table-column>
      <el-table-column property="connectRate" label="接通率" sortable="custom" align="left" min-width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="totalConnectedDuration" label="接通时长" sortable="custom" align="left" min-width="160" :formatter="(row:any, column:any, data:number)=>formatMsDuration(data/1000)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="averageConnectedDuration" label="平均接通时长" align="left" sortable="custom" min-width="160" :formatter="(row:any, column:any, data:number)=>formatMsDuration(data/1000)">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="silenceCallProportion" label="无声通话占比" align="left" sortable="custom" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="silenceHangup" sortable="custom" label="沉默挂机" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="assistant" sortable="custom" label="小助理" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="promptSound" sortable="custom" label="运营商提示音" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="oneSecondConnectedProportion" label="秒挂（1s）占比" sortable="custom" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="twoSecondConnectedProportion" label="秒挂（2s）占比" sortable="custom" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="callFailedProportion" label="送呼失败" sortable="custom" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="transCallSeatNum" sortable="custom" label="转人工占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classANum" sortable="custom" label="A类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classBNum" sortable="custom" label="B类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classCNum" sortable="custom" label="C类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="classDNum" sortable="custom" label="D类占比" align="left" min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="unitPrice" sortable="custom" label="线路单价(元)" fixed="right" align="left" width="120" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template v-slot="{ row }">
          <span>{{ row.unitPrice }}</span>
          <el-button v-if="row.supplyLineName && !['合计', '总计'].includes(row.supplyLineName)" class="tw-ml-[6px]" link type="primary" @click="editPrice(row)">
            <el-icon><SvgIcon name="edit2" /></el-icon>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="60" align="right" fixed="right">
        <template #default="{ row }">
          <el-button v-if="row.supplyLineName && !['合计', '总计'].includes(row.supplyLineName)" type="primary" link @click="goDetail(row)">详情</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  <el-dialog
    v-model="editPriceVisible"
    width="480px"
    class="dialog-form"
    align-center
    @closed="editPriceVisible=false"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">修改线路单价</div>
    </template>
    <el-form :model="currentEditInfo" ref="editPriceRef" :label-width="80">
      <el-form-item  label="修改日期：" prop="date" class="info-title">
        {{ currentEditInfo.date }}
      </el-form-item>
      <el-form-item  label="线路名称：" prop="supplyLineName" class="info-title">
        {{ currentEditInfo.supplyLineName }}
      </el-form-item>
      <el-form-item  label="线路单价：" prop="unitPrice">
        <el-input-number v-model="currentEditInfo.unitPrice" style="width: 100%" :controls="false" :precision="3" :min="0" :max="20000" placeholder="请输入集线比，1-20000"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="editPriceVisible=false" :icon="CloseBold">取消</el-button>
        <el-button :loading="editLoading" type="primary" @click="confirmEditPrice" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, watch, markRaw  } from 'vue'
import { ElMessage, } from 'element-plus'
import { CaretTop, CaretBottom, CloseBold, Select } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import PaginationBox from '@/components/PaginationBox.vue'
import { lineSupplierModel } from '@/api/line'
import { SupplierDosageDailyParams, SupplierDosageDailyItem, SupplierDosageDetailsItem } from '@/type/line'
import { exportExcel, generateExcelBook, } from '@/utils/export'
import dayjs from 'dayjs'
import { formatterEmptyData, formatNumber, formatMsDuration, handleTableSort } from '@/utils/utils'
import { getSupplierLineTypeText, getMerchantLineTypeText } from '@/utils/line'
import router from '@/router'
import { useRoute } from 'vue-router'
import HeaderBox from '@/components/HeaderBox.vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import JSZip from 'jszip'
import FileSaver from 'file-saver';
import type { FormInstance, } from 'element-plus'
import to from 'await-to-js'
import { trace } from '@/utils/trace'

const route = useRoute();
// const date = route.query.date as string
const globalStore = useGlobalStore()
const { loading, primaryIndustryList } = storeToRefs(globalStore)
const currentPage = ref(1)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<SupplierDosageDailyItem[]>([])
const total = ref(0)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const titleList = computed(() => [
  {title: '供应用量统计', name: 'SupplierLineManger',},
  {title: route.query.date as string},
])

const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const getTotalRowClass = ({ row }: {row: SupplierDosageDailyItem}) => {
  if ([row.supplyLineNumber,row.supplyLineName].includes('合计') || [row.supplyLineNumber,row.supplyLineName].includes('总计')) {
    return 'tw-font-[700] tw-bg-[#f7f8fa]'
  } else {
    return undefined
  }
}
class searchOrigin {
  supplyLineName = undefined
  supplyLineNumber = undefined
  supplierName = undefined
  supplierNumber = undefined
  masterCallNumber = undefined
  date = dayjs(route.query.date as string).format('YYYY-MM-DD')
}
const tableRef = ref(null)
const searchForm = reactive<SupplierDosageDailyParams>(new searchOrigin())

const clearSearchForm = () => {
  Object.assign(searchForm, new searchOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}
const search = async () => {
  loading.value = true
  tableData.value = (await lineSupplierModel.getDailyDosage(searchForm) || []) as SupplierDosageDailyItem[]
  total.value = tableData.value?.length || 0
  loading.value = false
}
watch(() => route.query.date, () => {
  searchForm.date = route.query.date as string
  searchForm.date && search()
}, { deep: true })
search()
// 导出当前列表数据
const exportCurrent2Excel = () => {
  if (!tableData.value || tableData.value.length < 1) {
    return ElMessage({
      type: 'warning', message: '暂无数据可导出'
    })
  }
  const data = tableData.value.map(item => {
    const { calculateNumOfSix, calculateNumOfSixty, totalCallNum, totalConnectNum, connectRate, totalConnectedDuration,
      averageConnectedDuration, silenceCallProportion, oneSecondConnectedProportion, twoSecondConnectedProportion,
      callFailedProportion, masterCallNumber, supplierName, supplierNumber, supplyLineName, supplyLineNumber, supplyLineType,
      silenceHangup, assistant,  promptSound, classANum, classBNum, classCNum, classDNum, transCallSeatNum, unitPrice
    } = item
    return {
      '线路名称': supplyLineName,
      '线路编号': supplyLineNumber,
      '线路类型': supplyLineType ? getSupplierLineTypeText(supplyLineType) : '',
      '供应商名称': supplierName,
      '供应商编号': supplierNumber,
      '主叫号码': masterCallNumber,
      '计量数（60s）': calculateNumOfSixty,
      '计量数（6s）': calculateNumOfSix,
      '线路单价': unitPrice ?? '',
      '呼叫总量': totalCallNum,
      '接通总量': totalConnectNum,
      '接通率': connectRate,
      '接通时长': totalConnectedDuration,
      '平均接通时长': averageConnectedDuration,
      '无声通话占比': silenceCallProportion,
      '沉默挂机': silenceHangup || 0,
      '小助理': assistant || 0,
      '运营商提示音': promptSound || 0,
      '秒挂（1s）占比': oneSecondConnectedProportion,
      '秒挂（2s）占比': twoSecondConnectedProportion,
      '送呼失败': callFailedProportion,
      '转人工占比': transCallSeatNum || 0,
      'A类占比': classANum || 0,
      'B类占比': classBNum || 0,
      'C类占比': classCNum || 0,
      'D类占比': classDNum || 0,
    }
  })
  trace({
    page: `线路运营-供应线路-用量统计-详情-导出数据`,
    params: data,
  })
  exportExcel(data, `供应线路 -【${dayjs(route.query.date as string).format('YYYY-MM-DD')}】用量统计详情.xlsx`);
}

// 导出列表每项的详情数据并压缩zip
const exportDetails2Excel = async () => {
  if (!tableData.value || tableData.value.length < 1) {
    return ElMessage({
      type: 'warning', message: '暂无数据可导出'
    })
  }
  const zip = new JSZip();
  await Promise.all(tableData.value.map(async item => {
    if (item.supplyLineName !== '总计') {
      const arr = (await lineSupplierModel.getDailyDosageDetails({
        supplyLineNumber: item.supplyLineNumber,
        date: route.query.date as string,
      }) || []) as SupplierDosageDetailsItem[]
      const data = arr.map(subItem => {
        const { calculateNumOfSix, calculateNumOfSixty, totalCallNum, totalConnectNum, connectRate, totalConnectedDuration,
          averageConnectedDuration, silenceCallProportion, oneSecondConnectedProportion, twoSecondConnectedProportion,
          callFailedProportion, account, tenantLineName, tenantLineNumber, tenantName, tenantNumber, tenantLineType,
          silenceHangup, assistant,  promptSound, classANum, classBNum, classCNum, classDNum, transCallSeatNum
        } = subItem
        return {
          '商户线路名称': tenantLineName,
          '商户线路编号': tenantLineNumber,
          '线路类型': tenantLineType ? getMerchantLineTypeText(tenantLineType) : '',
          '所属账号': account,
          '计量数（60s）': calculateNumOfSixty,
          '计量数（6s）': calculateNumOfSix,
          '呼叫总量': totalCallNum,
          '接通总量': totalConnectNum,
          '接通率': connectRate,
          '接通时长': totalConnectedDuration,
          '平均接通时长': averageConnectedDuration,
          '无声通话占比': silenceCallProportion,
          '沉默挂机': silenceHangup || 0,
          '小助理': assistant || 0,
          '运营商提示音': promptSound || 0,
          '秒挂（1s）占比': oneSecondConnectedProportion,
          '秒挂（2s）占比': twoSecondConnectedProportion,
          '送呼失败': callFailedProportion,
          '转人工占比': transCallSeatNum || 0,
          'A类占比': classANum || 0,
          'B类占比': classBNum || 0,
          'C类占比': classCNum || 0,
          'D类占比': classDNum || 0,
          '商户名称': tenantName,
          '商户编号': tenantNumber,
        }
      })
      const res = generateExcelBook(data)
      zip.file(`【${dayjs(route.query.date as string).format('YYYY-MM-DD')}】供应线路【${item.supplyLineName}】用量统计详情.xlsx`, res)
    }
  }))
  zip.generateAsync({ type: 'blob' })
  .then((content) => {
    FileSaver.saveAs(content, `【${dayjs(route.query.date as string).format('YYYY-MM-DD')}】供应线路用量统计详情.zip`);
  })
  .catch((error) => {
    ElMessage.error('压缩文件生成失败:' + error);
  });
}

// 导出当前列表下所有详情数据
const goDetail = (row: any) => {
  router.push({
    name: 'SupplierLineDosageDailyDetails',
    query: {
      supplyLineNumber: row.supplyLineNumber,
      supplyLineName: row.supplyLineName,
      date: route.query.date,
    }
  })
}

const currentEditInfo = reactive<Partial<{
  date: string,
  supplyLineNumber: string,
  supplyLineName: string,
  unitPrice: number
}>>({
  date: undefined,
  supplyLineNumber: undefined,
  supplyLineName: undefined,
  unitPrice: undefined
})
const editPriceVisible = ref(false)
const editLoading = ref(false)
const editPriceRef = ref<null | FormInstance>(null)
const confirmEditPrice = () => {
  editPriceRef.value?.validate(async valid => {
    if (!valid) return
    editLoading.value = true
    trace({
      page: `线路运营-供应线路-用量统计-详情-编辑线路单价`,
      params: {
        supplyLineNumber: currentEditInfo.supplyLineNumber,
        supplyLineName: currentEditInfo.supplyLineName,
        date: currentEditInfo.date,
        new: currentEditInfo.unitPrice,
        old: tableData.value?.find(item => item.supplyLineNumber === currentEditInfo.supplyLineNumber)?.unitPrice || null,
      },
    })
    const [err] = await to(lineSupplierModel.editSupplyLineEverydayPrice({
      date: currentEditInfo.date!,
      supplyLineNumber: currentEditInfo.supplyLineNumber!,
      unitPrice: currentEditInfo.unitPrice
    }))
    if (!err) {
      ElMessage.success('修改成功')
      editPriceVisible.value = false
      search()
    }
    editLoading.value = false
  })
}
const editPrice = (row: SupplierDosageDailyItem) => {
  if (!row.supplyLineNumber || [row.supplyLineNumber,row.supplyLineName].includes('合计') || [row.supplyLineNumber,row.supplyLineName].includes('总计')) return
  Object.assign(currentEditInfo, {
    date: dayjs(route.query.date as string).format('YYYY-MM-DD'),
    supplyLineNumber: row.supplyLineNumber,
    supplyLineName: row.supplyLineName,
    unitPrice: row.unitPrice
  })
  editPriceVisible.value = true
}

</script>

<style scoped lang="postcss" type="text/postcss">
.dosage-daily-container {
  width: 100%;
  overflow-x: hidden;
  min-width: 1080px;
  box-sizing: border-box;
  padding: 16px;
  display: flex;
  flex-direction: column;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
}
</style>