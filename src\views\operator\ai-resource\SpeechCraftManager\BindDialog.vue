<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    class="dialog-form"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">话术绑定</div>
    </template>
    <el-form
      v-if="!resultData || !resultData.length"
      :model="addData"
      :rules="rules"
      label-width="90px"
      ref="addFormRef"
    >
      <el-form-item label="话术名称：">
        <span class="info-title">{{ props.data?.scriptName || '' }}</span>
      </el-form-item>
      <el-form-item label="绑定账号：" prop="groupIds">
        <SelectBox 
          v-model:selectVal="addData.groupIds"
          :options="masterAccountList"
          name="account"
          val="groupId"
          placeholder="请选择主账号"
          filterable
          class="tw-grow"
          canSelectAll
          multiple
          :limitNum="5"
        >
        </SelectBox>
      </el-form-item>
    </el-form>
    <el-table
      v-else
      class="tw-mt-[12px]"
      :data="resultData"
      style="width: 100%"
      :header-cell-style="tableHeaderStyle"
    >
      <el-table-column align="left" label="序号" width="64">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="left" property="account" label="主账号" width="200" show-overflow-tooltip></el-table-column>
      <el-table-column align="left" property="err" label="结果" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.err" class="tw-text-[#E54B17]">{{ row.err }}</span>
          <span v-else>成功</span>
        </template>
      </el-table-column>
        <el-table-column align="right" property="err" label="操作" width="80">
        <template #default="{ row }">
          <el-button link type="primary" @click="goMerchantPage(row.account)">前往商户</el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">{{ !resultData || !resultData.length ? '取消' : '关闭' }}</el-button>
        <template v-if="!resultData || !resultData.length">
          <el-button :loading="loading" type="primary" @click="confirm(true)" :icon="Select">确定并跳转</el-button>
          <el-button :loading="loading" type="primary" @click="confirm(false)" :icon="Select">确定</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onUnmounted, } from 'vue'
import { SpeechCraftInfoItem, } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { merchantModel } from '@/api/merchant'
import type { FormInstance, } from 'element-plus'
import to from 'await-to-js';
import { trace } from '@/utils/trace';
import { monitorStatisticModel } from '@/api/monitor-statistic'
import SelectBox from '@/components/SelectBox.vue'
import { router } from "@/router"
import { tableHeaderStyle } from '@/assets/js/constant'

const emits = defineEmits(['update:visible'])
const props = defineProps<{
  visible: boolean;
  data?: SpeechCraftInfoItem | null;
}>();
const loading = ref(false)
const dialogVisible = ref(false)
const addData = reactive<Partial<{
  scriptId: number,
  groupIds: string[],
  scriptStringId: string
}>>({
  scriptId: undefined,
  scriptStringId: undefined,
  groupIds: undefined,
})
const addFormRef = ref<FormInstance | null>(null)

type ResultDataItem = {
  account: string,
  groupId: string,
  err: string | null | undefined,
}

const resultData = ref<ResultDataItem[] | null>([])
const rules = {
  groupIds: [{ required: true, message: '请选择主账号', trigger: 'change' }]
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

/**
 *  @description: 话术绑定后，自动/点击前往商户管理-任务模板（会自动拉起创建任务模板）
 *  @param {string} account 即将跳转的主账号名称
 *  @return {void}
 */
const goMerchantPage = (account: string) => {
  if (!account) return
  router.push({
    name: 'MerchantManager',
    query: {
      account: account,
      tabName: '任务模板',
    },
  })
  ElMessage({
    type: 'success',
    message: `已跳转至【${account}】`,
    duration: 6000,
  })
}

/**
 *  @description: 话术绑定确认
 *  @param {boolean} goMerchant  是否前往商户管理-任务模板创建页面：true/false
 *  @return {void}
 */
const confirm = (goMerchant: boolean) => {
  if (!addData.scriptId || !addData.scriptStringId) return ElMessage.warning('获取话术信息异常')

  addFormRef.value && addFormRef.value.validate(async (valid) => {
    if (valid) {
      trace({ page: `话术制作-绑定话术${addData.scriptId}`, params: addData })
      loading.value = true
      resultData.value = []
      const accounts = masterAccountList.value?.filter(item => addData.groupIds?.includes(item.groupId)) || []
      if (accounts?.length !== addData.groupIds?.length) {
        return ElMessage.warning('主账号不匹配')
      }
      await Promise.all(accounts?.map(async item => {
        const groupId = item.groupId
        const data: ResultDataItem = {
          err: null,
          groupId,
          account: item.account,
        }
        const tenantId = groupId ? +(groupId?.split('_')[1]) || undefined : undefined
        if (!tenantId) {
          data.err = '获取商户失败'
          ElMessage.warning('获取商户失败')
        } else {
          const [err] = await to(merchantModel.bindMerchantScript({
            scriptId: addData.scriptId!,
            scriptStringId: addData.scriptStringId!,
            tenantId: tenantId!,
            groupId,
          }))
          // @ts-ignore
          err && (data.err = err?.message || err?.msg || `失败`)
        }
        resultData.value?.push(data)
      }) || [])

      if (!resultData.value.some(item => !!item.err)) {
        ElMessage.success('操作成功')
        // 延迟1s，自动跳转商户管理，让用户查看绑定结果
        if (goMerchant) {
          const account = resultData.value?.find(item => !item.err)?.account || ''
          let timer: NodeJS.Timeout | null = setTimeout(() => {
            loading.value = false
            timer && clearTimeout(timer)
            timer = null
            goMerchantPage(account)
          }, 1000)
        }
      } else {
        loading.value = false
      }
    }
  })
}

const masterAccountList = ref<{
  account: string,
  groupId: string,
}[] | null>([]) // 主账号列表，全量
const getMasterAccountList = async () => {
  loading.value = true
  const [err, res] = await to(monitorStatisticModel.getAllMainAccount())
  masterAccountList.value = (res || []).filter(item => !!item.account && !!item.groupId)
  loading.value = false
}

watch(() => props.visible, async n => {
  dialogVisible.value = n
  if (n) {
    Object.assign(addData, {
      scriptId: props.data?.id,
      scriptStringId: props.data?.scriptStringId,
      groupIds: undefined,
    })
    await getMasterAccountList()
    addFormRef.value && addFormRef.value.clearValidate()
  } else {
    resultData.value = null
    masterAccountList.value = null
  }
})

onUnmounted(() => {
  resultData.value = null
  masterAccountList.value = null
})
</script>
