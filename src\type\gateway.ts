import { SupplierLineInfo } from '@/type/supplier'
export interface GatewayItem {
  id?: number,
  gatewayNumber?: string,
  name?: string,
  deleted?: boolean,
  concurrentLimit?: number, // 并发上限
  concurrentCount?: number, // 实际并发
  caps?: number,
  createTime?: string,
  updateTime?: string,
  callingRestrictions?: string[],
  dialingRestrictions?: string[],
  notes?: string,
  supplyLineNumbers?: string[],
}
export interface LineInfo extends SupplierLineInfo{

}