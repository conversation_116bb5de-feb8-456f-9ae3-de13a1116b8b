<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialog-form"
    width="640px"
    align-center
    @close="cancel"
    :close-on-click-modal="isChecked"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">批量设置</div>
    </template>
    <el-form
      :model="addData"
      :rules="rules"
      class="tw-mt-[12px]"
      label-width="88px"
      ref="editRef"
      :disabled="isChecked"
    >
      <div class="info-title tw-text-left tw-ml-[14px] tw-mb-1">
        已选择<span class="tw-text-[#007AFF]">【{{ props.corpusIds?.length || 0 }}】</span>个语料
      </div>
      <!-- 属性选择 -->
      <el-form-item label="选择属性：">
        <el-select
          v-model="selectedAttribute"
          placeholder="请选择要设置的属性"
          class="tw-w-full"
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="5"
          multiple
        >
          <el-option
            v-for="item in attributeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 语境类型设置 -->
      <el-form-item v-if="selectedAttribute?.includes('isOpenContext')" label="语境类型：" prop="isOpenContext">
        <div class="tw-flex tw-flex-col tw-items-start tw-w-full">
          <el-radio-group v-model="addData.isOpenContext">
            <el-radio :label="true">开放</el-radio>
            <el-radio :label="false">封闭</el-radio>
          </el-radio-group>
          <div v-if="!!addData.isOpenContext" class="tw-flex tw-items-center">
            <span class="tw-w-[80px] tw-shrink-0 tw-grow-0 tw-text-right">开放范围：</span>
            <el-radio-group v-model="addData.openScopeType" class="tw-ml-[6px]">
              <el-radio :label="OpenScopeTypeEnum['全部']">全部</el-radio>
              <el-radio v-if="!isNormal" :label="OpenScopeTypeEnum['继承原语境']">继承原语境</el-radio>
              <el-radio :label="OpenScopeTypeEnum['自定义']">自定义</el-radio>
            </el-radio-group>
          </div>
          <el-form-item v-if="!!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['自定义']" label="选择分组：" prop="groupOpenScope">
            <SelectBox
              v-model:selectVal="addData.groupOpenScope"
              :options="groupOptions"
              name="name"
              val="id"
              placeholder="请选择知识库分组"
              filterable
              class="tw-grow"
              canSelectAll
              multiple
            >
            </SelectBox>
            <el-button v-if="isNormal" class="tw-ml-[4px]" link type="primary" @click="copyCanvasOpenScope">
              复用{{props.corpusType === CorpusTypeEnum['深层沟通-普通语料'] ? '深层沟通' : '主动流程'}}
            </el-button>
          </el-form-item>
        </div>
      </el-form-item>
      <!-- 触发转人工 -->
      <el-form-item v-if="selectedAttribute?.includes('listenInOrTakeOver')" label="触发转人工："  prop="listenInOrTakeOver">
        <el-radio-group v-model="addData.listenInOrTakeOver" class="tw-ml-[6px]">
          <el-radio :label="true">开启</el-radio>
          <el-radio :label="false">关闭</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="selectedAttribute?.includes('smsTriggerName')" label="触发短信：">
        <el-radio-group v-model="ifSendSms" class="tw-ml-[6px]">
          <el-radio :label="true">开启</el-radio>
          <el-radio :label="false">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="ifSendSms && selectedAttribute?.includes('smsTriggerName')" label="触发点名称：" prop="smsTriggerName">
        <el-input v-model="addData.smsTriggerName" placeholder="请输入触发点名称，不超过8个字符，同一个话术不可重复" show-word-limit clearable />
      </el-form-item>
      <el-form-item v-if="selectedAttribute?.includes('maxWaitingTime')" label="最长等待：" prop="maxWaitingTime">
        <InputNumberBox v-model:value="addData.maxWaitingTime" placeholder="最长等待" append="毫秒"/>
      </el-form-item>
      <el-form-item v-if="selectedAttribute?.includes('aiLabels')" label="意向标签：" prop="aiLabelIds">
        <SelectBox 
          v-model:selectVal="addData.aiLabelIds"
          :options="scriptStore.intentionTagOptions"
          :canCopy="!isChecked"
          copyName="aiLabelIds"
          name="labelName"
          val="id"
          :limitNum="20"
          placeholder="请选择意向标签"
          :filterable="true" 
          class="tw-flex-grow"
          :multiple="true"
        >
        </SelectBox>
      </el-form-item>
      <el-form-item v-if="selectedAttribute?.includes('aiIntentionTypeId')" label="意向分类：" prop="aiIntentionTypeId">
        <el-select
          v-model="addData.aiIntentionTypeId"
          clearable
          placeholder="请选择意向分类"
          style="width:100%"
        >
          <el-option
            v-for="item in intentionLevelOptions"
            :key="item.id"
            :label="item.intentionType + ' - ' + item.intentionName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div>
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" :icon="Select" @click="confirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onUnmounted, computed } from 'vue'
import { ScriptCorpusItem, CorpusTypeEnum, OpenScopeTypeEnum } from '@/type/speech-craft'
import { ElMessage } from 'element-plus'
import { findValueInEnum } from '@/utils/utils'
import type { FormInstance } from 'element-plus'
import { scriptCanvasModel } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { CloseBold, Select } from '@element-plus/icons-vue'
import SelectBox from '@/components/SelectBox.vue'
import to from 'await-to-js';
import { CorpusEditOrigin } from './constant'
import { IntentionType, LabelItem } from '@/type/IntentionType'
import { scriptCorpusModel } from '@/api/speech-craft'
import { trace } from '@/utils/trace'
import Confirm from '@/components/message-box'

// props & emits
const props = withDefaults(defineProps<{

  visible: boolean;
  corpusIds: number[];
  corpusType: CorpusTypeEnum;
  hasHangup?: boolean;
}>(), {
  hasHangup: false,
});
const emits = defineEmits(['confirm', 'update:visible'])

const loading = ref(false)
const dialogVisible = ref(props.visible)
const isNormal = computed(() => props.corpusType && [
  CorpusTypeEnum['主动流程-普通语料'],
  CorpusTypeEnum['深层沟通-普通语料'],
].includes(props.corpusType))

/** 常量 */
const scriptStore = useScriptStore()
const isChecked = scriptStore.isChecked

/** 变量 */
const ifSendSms = ref(false) // 默认不触发短信不显示触发点
const addData = reactive<Partial<ScriptCorpusItem>>(new CorpusEditOrigin())
const editRef = ref<FormInstance  | null>(null)

// 批量编辑相关变量
const selectedAttribute = ref<(keyof ScriptCorpusItem)[] | null>(null)
const attributeOptions = [
  { label: '语境类型', value: 'isOpenContext' },
  { label: '触发转人工', value: 'listenInOrTakeOver' },
  // { label: '触发短信', value: 'smsTriggerName' },
  { label: '最长等待', value: 'maxWaitingTime' },
  { label: '意向标签', value: 'aiLabels' },
  { label: '意向分类', value: 'aiIntentionTypeId' },
]
const groupOptions = ref<{ id: number, name: string }[] | null>([])
const intentionLevelOptions = ref<IntentionType[] | null>([]) // 意向分类 选项
const intentionTagOptions = ref<LabelItem[] | null>([]) // 意向标签 选项

/** 处理函数开始 */

/** 语料开放范围为自定义 + 该画布自定义开放范围存在，支持用户复用画布的开放范围 */
const copyCanvasOpenScope = async () => {
  // 查询当前画布的开放范围
  const [_, res] = await to(scriptCanvasModel.findCanvasOpenScopeByCanvasId({canvasId: scriptStore.currentCanvas?.id!}))
  if (!res || !res.length) return ElMessage.warning(
    `当前${props.corpusType === CorpusTypeEnum['深层沟通-普通语料'] ? '深层沟通' : '主动流程'}为封闭，不可复用`
  )
  addData.groupOpenScope = res.map(item => item.id!) || []
}


/** 规则校验 */
const rules = {
  isOpenContext: [
    { required: true, message: '请选择语境类型', trigger: 'change' },
    { validator: (_rule: any, _value: any, callback: any) => {
      if (addData.isOpenContext && !addData.openScopeType) {
        return callback(new Error('请选择开放类型'))
      }
      return callback()
    }, trigger: ['change']},
  ],
  groupOpenScope: [
    { required: true, message: '请选择知识库分组开放范围', trigger: 'change' },
  ],
   maxWaitingTime: [
    { required: true, message: '请输入最长等待时间', trigger: 'blur' },
  ],
  listenInOrTakeOver: [
    { required: true, message: '请选择是否支持转人工', trigger: 'change' },
  ],
  smsTriggerName: [
    { required: true, message: '请输入触发点名称名', trigger: 'blur' },
    { max: 8, message: '触发点名称名不能超过8个字符', trigger: 'blur' },
  ],
}
/** 规则校验 */

// 底部确认处理函数
const confirm = async () => {
  if (!selectedAttribute.value || selectedAttribute.value.length === 0) {
    ElMessage.warning('请选择要编辑的属性')
    return
  }

  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params: Partial<ScriptCorpusItem> & {
        corpusIds: number[]
      } = {
        corpusIds: props.corpusIds,
        scriptId: scriptStore.id,
      }

      // 参数映射配置
      const paramMappings: Record<string, () => void> = {
        isOpenContext: () => {
          params.isOpenContext = addData.isOpenContext
          params.openScopeType = addData.isOpenContext ? addData.openScopeType : undefined
          params.groupOpenScope = addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['自定义']
            ? addData.groupOpenScope
            : (addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['全部']
              ? groupOptions.value?.map(item => item.id)
              : undefined)
        },
        smsTriggerName: () => {
          params.smsTriggerName = ifSendSms.value ? addData.smsTriggerName : undefined
        },
        listenInOrTakeOver: () => {
          params.listenInOrTakeOver = addData.listenInOrTakeOver
        },
        maxWaitingTime: () => {
          params.maxWaitingTime = addData.maxWaitingTime
        },
        aiLabels: () => {
          params.aiLabels = scriptStore.intentionTagOptions?.filter(item => addData.aiLabelIds?.includes(item.id as number)) || []
        },
        aiIntentionTypeId: () => {
          // 意向分类 置空需要设置空字符串
          params.aiIntentionType = intentionLevelOptions.value?.find(item => addData.aiIntentionTypeId == item.id) || {} as IntentionType

        }
      }

      // 根据选中的属性执行对应的参数设置
      selectedAttribute.value?.forEach(attr => {
        paramMappings[attr]?.()
      })

      // 二次确认提醒，触发转人工不能设置在挂机语料
      if (selectedAttribute.value?.includes('listenInOrTakeOver')
        && params.listenInOrTakeOver
        && !!props.hasHangup
      ) {
        ElMessage.warning('请务在挂机语料设置触发转人工')
        loading.value = false
        return
      }

      trace({
        page: `话术编辑-批量编辑语料${findValueInEnum(props.corpusType, CorpusTypeEnum)}(${scriptStore.id})`,
        params
      })
      const [err] = await to(props.corpusType === CorpusTypeEnum['基本问答'] 
        ? scriptCorpusModel.batchUpdateKnowledgeBaseQA(params)
        : scriptCorpusModel.batchUpdateOrdinaryCorpus(params)
      )
      
      loading.value = false
      if (!err) {
        ElMessage.success('批量更新语境类型设置成功')
        emits('confirm', params)
        cancel()
      }
    }
  })
}
// 底部取消处理函数
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible' , false)
}

// 初始化选项
const init = async() => {
  loading.value = true
  selectedAttribute.value = null
  ifSendSms.value = false
  Object.assign(addData, new CorpusEditOrigin())
  // 获取开发范围
  const [err, res] = await to(scriptStore.getKnowledgeGroupOptions())
  groupOptions.value = res || []
  // 获取意向分类
  intentionLevelOptions.value = await scriptStore.getIntentionLevelOptions()
  // 获取意向标签
  intentionTagOptions.value = await scriptStore.getIntentionTagOptions()
  loading.value = false
}
const clearData = () => {
  groupOptions.value = null
  intentionLevelOptions.value = null
  intentionTagOptions.value = null
}

/** 处理函数结束 */

/** watch开始 */
// 监听visible
watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if(props.visible) {
    init()
  }
})

onUnmounted(() => {
  clearData()
})


</script>

<style lang="postcss" type="text/postcss" scoped>
.dialog-form {
  .el-col {
    display: flex;
    align-items: center;
    height: 32px;
    padding: 0 6px;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
.text {
  font-size: var(--el-font-size-base);
  line-height: 24px;
  margin-right: 10px;
  text-align: left;
}
.status-box-mini {
  margin: 0 auto;
}
.handle {
  display: flex;
  align-items: center;
  padding-left: 12px;
}
</style>
