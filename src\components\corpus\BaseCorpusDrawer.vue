<template>
  <el-drawer
    v-model="dialogVisible"
    align-center
    :size="drawerWidth"
    class="dialog-form"
    with-header
    :before-close="cancel"
    :close-on-click-modal="isChecked"
  >
    <template #header>
      <div class="tw-flex tw-w-full tw-items-center tw-h-[48px]">
        <span class="tw-shrink-0 tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ findValueInEnum(addData.corpusType, CorpusTypeEnum) }}</span>
      </div>
    </template>
    <el-scrollbar
      ref="scrollRef"
      :max-height="'calc(100vh - 80px)'"
      wrap-class="tw-p-[12px] tw-bg-[#f2f3f5]"
      view-class="tw-text-[13px]"
    >
      <el-form
        :model="addData"
        :rules="rules"
        :disabled="isChecked"
        label-width="90px"
        ref="editRef"
        scroll-to-error
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
      >
        <div class="tw-font-[600] tw-text-left tw-my-[6px] tw-text-[14px]">基础设置</div>
        <div class="tw-bg-white tw-px-[12px] tw-py-[8px] tw-rounded-[4px] tw-mb-[12px]">
          <el-form-item label="语料名称：" prop="name">
            <el-input v-model.trim="addData.name" clearable placeholder="请输入语料名称（40字以内）" maxlength="40"/>
          </el-form-item>
          <el-form-item v-if="addData.corpusType === CorpusTypeEnum['基本问答']" label="问答类型：" prop="queryType">
            <el-select v-model="addData.queryType" placeholder="请选择问答类型" style="width:100%">
              <el-option v-for="item in enum2Options(QueryTypeEnum)" :key="item.value" :label="item.name" :value="item.value"/>
            </el-select>
          </el-form-item>
          <template v-if="isPriorOrQA">
            <el-form-item v-if="dialogVisible && addData.semCombineEntity?.satisfySemConditions" label-width="0" prop="satisfySemConditions">
              <MultCorpusRuleBox
                v-model:data="addData.semCombineEntity.satisfySemConditions"
                :readonly="isChecked"
                required
                title="满足条件"
              />
            </el-form-item>
            <el-form-item v-if="dialogVisible && addData.semCombineEntity?.excludeSemConditions" label-width="0" prop="excludeSemConditions">
              <MultCorpusRuleBox
                v-model:data="addData.semCombineEntity.excludeSemConditions"
                :readonly="isChecked"
                title="排除条件"
              />
            </el-form-item>
          </template>
          <!-- 针对最高优先和基本问答，如果连接到挂机，文字内容仅支持单段 -->
          <el-form-item v-if="isPriorOrQA" label="AI回答后：" prop="connectType">
            <el-select v-model="addData.connectType" placeholder="请选择连接到" style="width:100%">
              <el-option
                v-for="item in connectTypeOption"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="isInterrupt" label="AI回答后：" prop="connectType">
            <el-input disabled :model-value="connectInfoMap.get(addData.corpusType!) || '-'"></el-input>
          </el-form-item>
          <el-form-item v-if="addData.connectType === ConnectTypeEnum['指定主动流程']" label="主动流程：" prop="connectCorpusId">
            <el-select 
              v-model="addData.connectCorpusId"
              placeholder="选择要连接到的主动流程"
              style="width:100%"
              @change="handleConnectTypeChange"
            >
              <el-option
                v-for="item in masterProcessOptions"
                :key="item.headCorpusId"
                :label="item.name"
                :value="item.headCorpusId"
              />
            </el-select>
          </el-form-item>
          <!-- 文字内容
           【v1.0、挂机语料、重复、沉默、打断、垫句、承接语料】-------- 中仅展示一个输入框
           【v2.0中非挂机预料】----------------------------------- 展示多个输入框
           -->
          <el-form-item v-if="isPriorOrQA && addData.connectType !== ConnectTypeEnum['挂机'] && !!multiContentVersion" label="文字内容：" prop="scriptMultiContents">
            <div class="tw-flex tw-items-end tw-w-full">
              <div v-if="addData.scriptMultiContents?.length" class="tw-w-full" v-for="(item, index) in addData.scriptMultiContents || []" :key="index">
                <EditableContent
                  ref="editContentRef"
                  :editable="!isChecked"
                  closeable
                  v-model:needUpdate="needUpdate"
                  :corpus="addData"
                  v-model:content="addData.scriptMultiContents[index].scriptUnitContents"
                  :maxLength="300"
                />
              </div>
            </div>
          </el-form-item>
          <el-form-item v-else label="文字内容：" prop="scriptMultiContents">
            <el-input
              v-if="addData.scriptMultiContents && addData.scriptMultiContents[0]?.scriptUnitContents[0]"
              v-model="addData.scriptMultiContents[0].scriptUnitContents[0].content"
              type="textarea" :autosize="{minRows: 3, maxRows: 6}"
              :placeholder="`请输入语料的文本内容（${maxContentLength}字以内）`"
              clearable
              show-word-limit
              :maxlength="maxContentLength"
            />
          </el-form-item>

          <template v-if="isPriorOrQA">
            <el-form-item label="语境类型：" prop="isOpenContext">
              <el-radio-group v-model="addData.isOpenContext" class="tw-ml-[6px]">
                <el-radio :label="true">开放</el-radio>
                <el-radio :label="false">封闭</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="!!addData.isOpenContext" label="开放范围：" prop="openScopeType">
              <el-radio-group v-model="addData.openScopeType" class="tw-ml-[6px]">
                <el-radio :label="OpenScopeTypeEnum['全部']">全部</el-radio>
                <el-radio :label="OpenScopeTypeEnum['继承原语境']">继承原语境</el-radio>
                <el-radio :label="OpenScopeTypeEnum['自定义']">自定义</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="!!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['自定义']" label="选择分组：" prop="groupOpenScope">
              <SelectBox 
                v-model:selectVal="addData.groupOpenScope"
                :options="groupOptions"
                name="name"
                val="id"
                placeholder="请选择知识库分组"
                filterable
                class="tw-grow"
                multiple
              >
              </SelectBox>
            </el-form-item>
          </template>
        </div>
        <CorpusOtherSettings v-if="!(addData.corpusType && [CorpusTypeEnum['打断垫句'], CorpusTypeEnum['续播垫句']].includes(addData.corpusType))" :corpusData="addData" @update:corpusData="handleCorpusOtherDataChange"></CorpusOtherSettings>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div>
        <div v-if="!isInterrupt" class="tw-float-left">
          <el-button :loading="loading" type="primary" @click="confirm(true)">{{isChecked ? '查看' : ''}}打断设置</el-button>
        </div>
        <div class="tw-float-right">
          <el-button @click="cancel" :icon="CloseBold">{{isChecked ? '关闭' : '取消'}}</el-button>
          <el-button v-if="!isChecked" :loading="loading" type="primary" :icon="Select" @click="confirm(false)">确定</el-button>
        </div>
      </div>
    </template>
    <CorpusContentSettingDrawer v-model:visible="corpusContentVisible" :corpusId="corpusId!" @close="handleCloseOuter" fromEdit/>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, defineAsyncComponent, } from 'vue'
import { ScriptCorpusItem, CorpusTypeEnum, ScriptBaseInfo, corpusTypeOption, ScriptCorpusOtherItem, ConnectTypeEnum,
  QueryTypeEnum, OpenScopeTypeEnum,
 } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import { enum2Options, findValueInEnum, pickAttrFromObj } from '@/utils/utils'
import type { FormInstance, } from 'element-plus'
import { checkCorpusRules } from '@/components/corpus/constant'
import { useScriptStore } from '@/store/script'
import { CloseBold, Select } from '@element-plus/icons-vue'
import SelectBox from '@/components/SelectBox.vue'
import to from 'await-to-js';
import { handleCorpusMultiContent } from '@/utils/script'
import MultCorpusRuleBox from '@/components/corpus/MultCorpusConditionBox.vue'
import { trace } from '@/utils/trace'

const CorpusOtherSettings = defineAsyncComponent({ loader:() => { return import('./CorpusOtherSettings.vue')}})
const EditableContent = defineAsyncComponent({loader: () => import('@/components/editable-content/Index.vue')})


// props & emits
const emits = defineEmits(['update:data', 'update:visible', 'play'])
const props = defineProps<{
  visible: boolean;
  corpusData: ScriptCorpusItem;
}>();

/** 常量 */
const scriptStore = useScriptStore() // 话术信息
const editId = scriptStore.id // 话术id
const isChecked = scriptStore.isChecked // true: 话术查看, false: 话术编辑
const multiContentVersion = scriptStore.multiContentVersion
const drawerWidth = ref(window.innerWidth > 1400 ? '70%' : '900px')

const connectTypeOption = enum2Options(ConnectTypeEnum)

/** 变量 */
const loading = ref(false)
const dialogVisible = ref(false)
const addData = reactive<ScriptCorpusItem>(props.corpusData)
const editRef = ref<FormInstance  | null>(null) // 表格ref
// 选项信息
const masterProcessOptions = ref<{
  id: number,
  name: string,
  headCorpusId: number,
}[]>([]) // 主流程列表
// 知识库分组列表
const groupOptions = ref<{
  id: number,
  name: string
}[] | null>([])

// computed：是否是最高优先和基本问答
const isPriorOrQA = computed(() => {
  return addData.corpusType && [CorpusTypeEnum['最高优先'], CorpusTypeEnum['基本问答']].includes(addData.corpusType)
})
// computed：是否是打断垫句、续播垫句、承接语料
const isInterrupt = computed(() => addData.corpusType && [CorpusTypeEnum['打断垫句'], CorpusTypeEnum['续播垫句'], CorpusTypeEnum['承接语料']].includes(addData.corpusType)) // 是否是中断语料
const maxContentLength = computed(() => addData.corpusType && [CorpusTypeEnum['打断垫句'], CorpusTypeEnum['续播垫句']].includes(addData.corpusType) ? 10 : 300)
const connectInfoMap = new Map<CorpusTypeEnum, string>([
  [CorpusTypeEnum['打断垫句'], '开始打断等待'],
  [CorpusTypeEnum['续播垫句'], '立即续播'],
  [CorpusTypeEnum['承接语料'], '等待用户回答'],
])
/** 变量 */

/** 处理函数开始 */

// 初始化选项、语料数据处理
const needUpdate = ref(false)
const initOptions = async() => {
  try {
    groupOptions.value = await scriptStore.getKnowledgeGroupOptions()
    masterProcessOptions.value = await scriptStore.getProcessOptions()
  } catch (err) {
    ElMessage({
      message: '获取选项数据有误',
      type: 'error',
    })
  }
}
// 接受【其他设置】组件emits【update:corpusData】更新参数
const handleCorpusOtherDataChange = (val: ScriptCorpusOtherItem) => {
  Object.assign(addData, val)
}

/**
 * 模块：校验
 */

// 语料名称 校验函数
const validateName = (rule: any, value: any, callback: any) => {
  const reg = /[$#_?~&\/]/
  if (reg.test(value)) {
    callback(new Error('语料名称请勿出现特殊符号（$#_?~、&/等)'))
  }
  if (value.length > 20 || value.length < 2) {
    callback(new Error('语料名称长度为2-20'))
  }
  callback()
}
// 内容名称 校验函数
const validateContent = (rule: any, value: any, callback: any) => {
  if (!addData.scriptMultiContents || addData.scriptMultiContents.length < 1) {
    return callback(new Error('请输入文字内容'))
  }
  const val = addData.scriptMultiContents[0].scriptUnitContents?.reduce((a, b) => a + (b.content || ''), '') || ''
  if (!val) {
    return callback(new Error('请输入文字内容'))
  }
  const reg = /[$]/
  if (reg.test(val)) {
    return callback(new Error('文字内容请勿出现特殊符号$'))
  }
  return callback()
}
const rules = {
  name: [
    { required: true, message: '请输入语料名称', trigger: 'blur' },
    { validator: validateName, trigger: 'blur' },
  ],
  connectCorpusId: [
    { required: true, message: '请选择连接的主动流程', trigger: 'change' },
  ],
  satisfySemConditions: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkCorpusRules(addData.semCombineEntity?.satisfySemConditions, true)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
  excludeSemConditions: [
    { validator: (rule: any, value: any, callback: any) => {
      const errMsg = checkCorpusRules(addData.semCombineEntity?.excludeSemConditions, false)
      return errMsg ? callback(new Error(errMsg)) : callback()
    }, trigger: ['change', 'blur']},
  ],
  maxWaitingTime: [
    { required: true, message: '请输入最长等待时间', trigger: 'blur' },
  ],
  listenInOrTakeOver: [
    { required: true, message: '请选择是否支持转人工', trigger: 'change' },
  ],
  smsTriggerName: [
    { required: true, message: '请输入触发点名称名', trigger: 'blur' },
    { max: 8, message: '触发点名称名不能超过8个字符', trigger: 'blur' },
  ],
  scriptMultiContents: [
    { required: true, message: '请输入文字内容', trigger: 'blur' },
    { validator: validateContent, trigger: 'blur' },
  ],
  isOpenContext: [
    { required: true, message: '请选择语境类型', trigger: 'change' },
  ],
  openScopeType: [
    { required: true, message: '请选择开放范围', trigger: 'change' },
  ],
  groupOpenScope: [
    { required: true, message: '请选择知识库分组开放范围', trigger: 'change' },
  ],
}

// 打断设置参数
const corpusContentVisible = ref(false)
const corpusId = ref<number | null>(null)
// 底部确认、取消
const confirm = async (goContentSetting?: boolean) => {
  // 查看模式，查看打断设置
  if (goContentSetting && addData.id && isChecked) {
    corpusId.value = addData.id!
    corpusContentVisible.value = true
    return
  }

  // 编辑模式
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      if (addData.connectType !== ConnectTypeEnum['指定主动流程']) {
        addData.connectCorpusId = null
      }
      if (isPriorOrQA.value) {
        addData.openScopeType = !!addData.isOpenContext ? addData.openScopeType : undefined
        addData.groupOpenScope =  !!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['自定义'] ? addData.groupOpenScope
        :(!!addData.isOpenContext && addData.openScopeType === OpenScopeTypeEnum['全部'] ? groupOptions.value?.map(item => item.id) : undefined)
      }
      // 针对
      // 1）针对v1.0版本话术
      // 2）挂机的最高优先和基本问答
      // 需要删除非第一段的文字内容（文字内容如果存在第一段必有数据）
      if (!multiContentVersion || (isPriorOrQA.value && addData.connectType === ConnectTypeEnum['挂机'])) {
        if (addData.scriptMultiContents && addData.scriptMultiContents[0] && addData.scriptMultiContents[0].scriptUnitContents) {
          addData.scriptMultiContents[0].scriptUnitContents = addData.scriptMultiContents[0].scriptUnitContents.flatMap((item, index) => {
            if (index === 0) {
              return [item]
            } else if(item.id) {
              return [{
                ...item,
                deleted: true,
              }]
            } else {
              return []
            }
          })
        }
      }
      const params: ScriptCorpusItem = pickAttrFromObj(addData, corpusTypeOption[addData.corpusType!].keys)
      const [err, data] = await to(corpusTypeOption[addData.corpusType!].api(params)) as [any, ScriptCorpusItem]
      await trace({ page: addData.id ? `话术编辑-编辑语料(${addData.scriptId}-${addData.id})` : `话术编辑(${addData.scriptId})-新增语料`, params: params })
      loading.value = false
      if (data) {
        emits('update:data', data)
        if (goContentSetting && data.id) {
          corpusId.value = data.id || addData.id!
          corpusContentVisible.value = true
          ElMessage.success('语料编辑内容已保存')
        } else {
          cancel()
          ElMessage.success('操作成功')
        }
      }
    }
  })
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
/** 通过语句设置暴露的方法，判断是否需要关闭弹窗 */
const handleCloseOuter = (val: boolean) => {
  // 从打断设置返回时，由于之前保存的优先级和分支信息不一定为最新的，再重新通过打断设置的传参更新下
  if (!!val) {
    Object.assign(addData, val)
    // 多语句文本数据处理
    addData.scriptMultiContents = handleCorpusMultiContent(addData.scriptMultiContents, editId, {
      id: addData.id,
      corpusType: addData.corpusType!,
    })
    // 命中排除处理
    if (isPriorOrQA.value) {
      if (!addData.semCombineEntity) {
        addData.semCombineEntity = { satisfySemConditions: [], excludeSemConditions: [] }
      } else if(!addData.semCombineEntity.satisfySemConditions || !addData.semCombineEntity.excludeSemConditions) {
        addData.semCombineEntity.satisfySemConditions = addData.semCombineEntity.satisfySemConditions || []
        addData.semCombineEntity.excludeSemConditions = addData.semCombineEntity.excludeSemConditions || []
      }
    }
    needUpdate.value = true
  } else {
    cancel()
  }
}
/** 处理函数结束 */

/** watch开始 */ 
// 监听连接类型，联动打断类型
const handleConnectTypeChange = () => {
  if (addData.connectType === ConnectTypeEnum['挂机']) {
    addData.listenInOrTakeOver = false
  }
}
// 监听入参弹窗visible，对addData数据进行更新和处理
const scrollRef = ref()
watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if(props.visible) {
    initOptions()
    Object.assign(addData, JSON.parse(JSON.stringify(props.corpusData)))
    // 多语句文本数据处理
    addData.scriptMultiContents = handleCorpusMultiContent(addData.scriptMultiContents, editId, {
      id: addData.id,
      corpusType: addData.corpusType!,
    })
    // 命中排除处理
    if (isPriorOrQA.value) {
      if (!addData.semCombineEntity) {
        addData.semCombineEntity = { satisfySemConditions: [], excludeSemConditions: [] }
      } else if(!addData.semCombineEntity.satisfySemConditions || !addData.semCombineEntity.excludeSemConditions) {
        addData.semCombineEntity.satisfySemConditions = addData.semCombineEntity.satisfySemConditions || []
        addData.semCombineEntity.excludeSemConditions = addData.semCombineEntity.excludeSemConditions || []
      }
    }
    needUpdate.value = true
    setTimeout(() => {
      editRef.value && editRef.value.clearValidate()
      scrollRef.value?.scrollTo({top: 0})
    }, 200)
  }
})
/** watch结束 */
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.text {
  font-size: 14px;
  line-height: 24px;
  margin-right: 10px;
  text-align: left;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 16px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
}
</style>
