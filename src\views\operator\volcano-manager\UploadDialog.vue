<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    align-center
    width="800px"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">
        {{ step === 1 ? '导入确认' : '导入结果'}}
        </div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px] tw-my-[16px]"
      view-class="tw-flex tw-flex-col tw-items-center tw-leading-[24px]"
    >
      <div class="tw-text-left tw-w-full tw-text-[13px]">
        <template v-if="step === 1">
          <span class="tw-flex-shrink-0 tw-mr-1">上传数量：</span>
          <span class="info-title">{{ props.uploadData?.length || 0 }}</span>
        </template>
       
        <template v-if="step === 2">
          <span class="tw-flex-shrink-0 tw-mr-1">成功数量：</span>
          <span class="tw-text-[#165DFF]">{{ (resultData?.length || 0) - (errorResultData?.length || 0) }}</span>
          <span class="tw-flex-shrink-0 tw-mr-1 tw-ml-[50px]">失败数量：</span>
          <span class="tw-text-[#E54B17]">{{ errorResultData?.length || 0 }}</span>
        </template>
       
      </div>
      <el-table
        v-if="props.uploadData && props.uploadData?.length>0"
        class="tw-mt-[12px]"
        :data="step === 1 ? props.uploadData : resultData"
        style="width: 100%"
        :header-cell-style="tableHeaderStyle"
      >
        <el-table-column align="left" label="序号" width="64">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column property="account" label="所属账号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="channelNumber" label="渠道号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="messageSign" label="短信签名" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="volcanoSmsTemplate" label="火山短信模板" show-overflow-tooltip  align="left" min-width="240" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column v-if="step === 2" property="recordStatus" fixed="right" label="执行状态" align="center" width="120">
          <template #default="{ row }">
          <span
            v-if="row?.recordStatus"
            class="status-box-mini tw-mx-auto"
            :class="row.recordStatus === RecordStatusEnum['成功'] ? 'green-status' : 'red-status'"
          >
            {{ findValueInEnum(row.recordStatus, RecordStatusEnum) }}
          </span>
          <span v-else>-</span>
        </template>
        </el-table-column>
      </el-table>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="loading" @click="cancel" :icon="CloseBold">关闭</el-button>
        <el-button v-if="step === 1" :loading="loading" type="primary" @click="confirm" :icon="Select">{{!loading ? '确认上传' : '上传中'}}</el-button>
        <el-button v-if="step === 2 && errorResultData?.length" type="primary" @click="exportError" :icon="Select">导出失败</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { VolcanoTemplateLogItem, RecordStatusEnum } from '@/type/volcano'
import { tableHeaderStyle } from '@/assets/js/constant'
import { findValueInEnum, formatterEmptyData } from '@/utils/utils'
import { volcanoModel } from '@/api/volcano'
import { to } from 'await-to-js'
import { dayjs, ElMessage } from 'element-plus'
import { trace } from '@/utils/trace'
import { exportExcel } from '@/utils/export'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  uploadData: VolcanoTemplateLogItem[] | null;
}>();

const visible = ref(false)
const loading = ref(false)
const step = ref(1)
const resultData = ref<VolcanoTemplateLogItem[] | null>([]) // 后端返回的结果
const errorResultData = computed(() => resultData.value?.filter(item => item.recordStatus === RecordStatusEnum['失败']) || [])
const confirm = async () => {
  if (!props.uploadData?.length) return ElMessage.warning('导入模板文件为空')
  loading.value = true
  await trace({ page: '火山运营-导入模板：开始', params: props.uploadData })
  const [err, res] = await to(volcanoModel.uploadVolcanoTemplate(props.uploadData))
  await trace({ page: '火山运营-导入模板：完成', params: err || res })
  resultData.value = res || []
  emits('confirm')
  if (errorResultData.value?.length) {
    step.value = 2
  } else {
    cancel()
  }
  loading.value = false
}

const exportError = () => {
  const data: any[] = errorResultData.value?.map((item, index) => {
    return {
      '所属账号': item.account,
      '渠道号': item.channelNumber,
      '短信签名': item.messageSign,
      '火山短信模板': item.volcanoSmsTemplate,
    }
  }) || []
  if (!data || data?.length < 1) {
    loading.value = false
    return ElMessage.warning('无导入失败数据')
  }
  exportExcel(data,  `火山上传失败${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`)
  cancel()
}
const cancel = () => {
  emits('update:visible', false)
}

watch(() => props.visible, () => {
  visible.value = props.visible
  if (props.visible) {
    step.value = 1
  } else {
    resultData.value = null
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-scrollbar__bar.is-vertical) {
  top: 18px;
}
.el-table {
  font-size: 13px;
}
</style>