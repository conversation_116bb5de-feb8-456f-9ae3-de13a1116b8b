+
<template>
  <el-dialog
    :model-value="props.visible"
    class="seat-workbench-dialog"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        坐席设置
      </div>
    </template>

    <el-scrollbar class="form-dialog-main">
      <div class="form-dialog-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="140"
        >
          <div class="form-block">
            <div class="form-block-title">
              人机协同
            </div>
            <el-form-item label="自动签入任务：" prop="autoCheckInTask" required>
              <div class="tw-flex">
                <el-switch
                  v-model="form.autoCheckInTask"
                  class="tw-flex-none"
                  style="--el-switch-on-color: #165DFF; --el-switch-off-color: #C8C9CC;"
                  inline-prompt
                  :active-value="true"
                  :inactive-value="false"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </div>
            </el-form-item>
            <el-form-item label="自动监听/接听：" prop="autoAccept" required>
              <div class="tw-flex">
                <el-switch
                  v-model="form.autoAccept"
                  class="tw-flex-none"
                  style="--el-switch-on-color: #165DFF; --el-switch-off-color: #C8C9CC;"
                  inline-prompt
                  :active-value="true"
                  :inactive-value="false"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </div>
            </el-form-item>
          </div>

          <div class="form-block">
            <div class="form-block-title">
              人工直呼
            </div>
            <el-form-item label="自动拨打下一个：" prop="autoCallNext" required>
              <div class="tw-flex">
                <el-switch
                  v-model="form.autoCallNext"
                  class="tw-flex-none"
                  style="--el-switch-on-color: #165DFF; --el-switch-off-color: #C8C9CC;"
                  inline-prompt
                  :active-value="true"
                  :inactive-value="false"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </div>
            </el-form-item>
            <el-form-item label="自动重发备注短信：" prop="autoResendSms" required>
              <div class="tw-flex">
                <el-switch
                  v-model="form.autoResendSms"
                  class="tw-flex-none"
                  style="--el-switch-on-color: #165DFF; --el-switch-off-color: #C8C9CC;"
                  inline-prompt
                  :active-value="true"
                  :inactive-value="false"
                  active-text="开启"
                  inactive-text="关闭"
                />
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { SeatSetting } from '@/type/seat'
import { nextTick, reactive, ref, toRaw, watch } from 'vue'
import { ElMessage, FormRules } from 'element-plus'
import { Throttle } from '@/utils/utils'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { useSeatSettingStore } from '@/store/seat/seat-setting'
import { storeToRefs } from 'pinia'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
}>()
const emits = defineEmits([
  'update:visible',
])

const seatSettingStore = useSeatSettingStore()
const { seatSetting } = storeToRefs(seatSettingStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 弹窗 开始 ----------------------------------------

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}

// ---------------------------------------- 弹窗 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据
const formDefault = (): SeatSetting => {
  return {
    autoCheckInTask: false,
    autoAccept: false,
    autoCallNext: false,
    autoResendSms: false,
  }
}
// 表单数据
const form: SeatSetting = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 更新表单
 */
const updateForm = () => {
  Object.assign(form, JSON.parse(JSON.stringify(toRaw(seatSetting.value))))
}
/**
 * 提交表单
 */
const submit = async () => {
  // 提交节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 更新表单
  seatSettingStore.updateSeatSetting(JSON.parse(JSON.stringify(toRaw(form))))

  // 关闭弹窗
  closeDialog()

  // 提交节流锁解锁
  throttleConfirm.unlock()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    updateForm()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
