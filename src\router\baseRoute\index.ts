import { AppRouteRecordRawT } from "@/type/route";

export const baseRoutes: Array<AppRouteRecordRawT> = [
  {
    path: '/',
    name: 'index',
    redirect: '/login',
    meta: {
      title: 'home',
      id: 'common'
    }
  },
  {
    path: '/login',
    name: 'Login',
    meta: {
      id: 'common',
      title: '登录',
    },
    component: () => import('@/views/Login.vue')
  },
  {
    path: "/403",
    name: "403",
    component: () => import('@/views/error-pages/403.vue'),
    meta: {
      title: "页面无权限",
      id: 'common',
    }
  },
  {
    path: "/404",
    name: "404",
    component: () => import('@/views/error-pages/404.vue'),
    meta: {
      title: "页面丢失",
      id: 'common',
    }
  },
  // {
  //   path: "/",
  //   name: "首页",
  //   component: () => import('@/views/layout/Index.vue'),
  //   // redirect: '/dashboard',
  //   meta: {
  //     title: "首页",
  //     type: 1,
  //     id: 'common',
  //   },
  // }
]
