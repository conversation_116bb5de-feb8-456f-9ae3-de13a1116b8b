// 火山运营

export enum RecordStatusEnum {
  '成功' = '1',
  '进行中' = '0',
  '失败' = '2',
}

export enum RecordStatusForPlanEnum {
  '成功' = '0',
  '短链失败' = '1',
  '请求火山失败' = '2',
  '模板失败' = '3',
}

export interface VolcanoTemplateLogItem {
  id: number;
  groupId: string;
  account: string; // 所属账号
  channelNumber: string; // 渠道号
  messageSign: string; // 短信签名
  volcanoSmsTemplate: string; // 火山短信模板
  recordStatus: RecordStatusEnum; // 执行状态
  createTime: string;
  updateTime: string; // 执行时间
}

export interface VolcanoTaskLogItem {
  id: number;
  packageId: string;
  modelName: string; // 人群包名
  status: RecordStatusEnum;  // 执行状态
  createTime: string;
  updateTime: string; // 执行时间
  taskTemplateName?: string; // 火山任务模板名，前端根据modelName中开头中括号获取

  // 以下参数未使用
  accountId: string;
  description: string;
  downloadCount: number;
  generateTime: string;
  mobileURL: string;
  
  receivedNumber: number;
  sign: string;
  totalNumber: number;
  volcanoTaskId: string;
}

// 营销计划
export interface MarketingPlanItem {
  groupId: string;
  channelNumber: string;
  tagName?: string; // 标签名
  marketingType?: string; // 营销类型，1，2，3
  modelName?: string; // 模板名称
  operatorName?: string; // 运营商名称
  originalLink?: string; // 网申链接
  smsSign?: string; // 短信签名
  smsContent?: string; // 短信文案
  volcanoAccount: string; // 火山账号
  account: string; // 白泽账号
  updateBy: string; // 操作账号
  recordStatus?: RecordStatusForPlanEnum; // 执行状态
  templateId?: number; // 白泽任务模板id
  updateTime: string;
}

export interface ApplySmsTemplateItem {
  account?: string;
  channelNumber?: string;
  createTime?: string;
  groupId?: string;
  id?: number;
  marketingType?: string;
  recordStatus?: string;
  shortLink?: string;
  smsContent?: string;
  smsSign?: string;
  updateTime?: string;
  updateBy?: string;
  volcanoAccount?: string;
  volcanoApplyId?: string;
  volcanoChannelType?: string;
  volcanoSmsContent?: string;
  volcanoSmsName?: string;
  volcanoTemplateId?: string;
}
