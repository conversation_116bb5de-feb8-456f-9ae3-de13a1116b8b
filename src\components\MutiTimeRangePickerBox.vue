<template>
  <div class="tw-w-full tw-flex tw-flex-col">
    <div class="tw-flex tw-h-[30px]">
      <el-button v-if="['HH:mm',].includes(props.format)" link type="primary" @click="addOne()">新增</el-button>
      <el-button link type="primary" @click="selectAll">全选</el-button>
      <el-button type="danger" link @click="delAll">清空</el-button>
    </div>
    <div class="tw-w-full time-container">
      <div v-for="(item, index) in timeArr" :key="index" class="tw-flex tw-mb-[6px]">
        <el-time-picker
          v-model="timeArr[index]"
          is-range
          start-placeholder="开始时间"
          end-placeholder="开始时间"
          format="HH:mm"
          :disabled-hours="disabledHoursFn"
          :disabled-minutes="disabledMinutes"
          value-format="HH:mm"
          @visible-change="(v: boolean) => !v && handleTimeChange()"
        />
        <el-button :disabled="timeArr?.length <= 1" link :type="timeArr?.length <= 1 ? 'default':'danger'" class="tw-ml-[8px]" @click="delOne(index)">删除</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { watch, ref, } from 'vue'

// 组件入参props
const props = withDefaults(defineProps<{
  startWorkTimeList: (string | undefined)[] | undefined,
  endWorkTimeList: (string | undefined)[]  | undefined,
  format?: string,
  separator?: string,
  defaultStart: string,
  defaultEnd: string,
}>(), {
  format: 'HH:mm',
})
// emit
const emits = defineEmits([
  'update',
])

const timeArr = ref<[string | undefined, string | undefined][]>([])
watch ([() => props.startWorkTimeList, () => props.endWorkTimeList], () => {
  timeArr.value = []
  if (!props.startWorkTimeList?.length || !props.endWorkTimeList?.length) return
  props.startWorkTimeList?.forEach((timeVal, index) => {
    props.startWorkTimeList && props.endWorkTimeList && timeArr.value.push([props.startWorkTimeList[index], props.endWorkTimeList[index]])
  })
}, {deep: true, immediate: true})

const delAll = () => {
  selectAll()
}
const delOne = (index: number) => {
  if (index >= 0 && timeArr.value.length >= 1 ) {
    timeArr.value?.splice(index, 1)
    handleTimeChange()
  } else {
    delAll()
  }
}
const addOne = () => {
  timeArr.value?.push([props.defaultStart, props.defaultEnd])
  handleTimeChange()
}
const selectAll = () => {
  timeArr.value = [[props.defaultStart, props.defaultEnd]]// [[dayjs(props.defaultStart), dayjs(props.defaultEnd)]]
  handleTimeChange()
}

const disabledHoursFn = () => {
  const res:number[] = []
  for(let i = 0; i<=24; i++) {
    if (`${i}:59`.padStart(5, '0') < props.defaultStart || `${i}:00`.padStart(5, '0') > props.defaultEnd) {
      res.push(i)
    }
  }
  return res
}
const disabledMinutes = (hour: number) => {
  const res:number[] = []
  for(let i = 0; i<=59; i++) {
    if (`${(hour + '').padStart(2, '0')}:${(i + '').padStart(2, '0')}` < props.defaultStart || `${(hour + '').padStart(2, '0')}:${(i + '').padStart(2, '0')}` > props.defaultEnd) {
      res.push(i)
    }
  }
  return res
}

const handleTimeChange = () => {
  const startWorkTimeList: string[] = []
  const endWorkTimeList: string[] = []
  timeArr.value?.sort((a, b) => a[0] ? (a[0] < b[0]! ? -1 : 1) : 1)
  timeArr.value?.map(item => {
    if (item[0] && item[1]) {
      startWorkTimeList.push(item[0])
      endWorkTimeList.push(item[1])
    }
  })
  emits('update', startWorkTimeList, endWorkTimeList)
}

</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-date-editor .el-range-input)  {
  text-align: left;
  padding-left: 24px;
}
</style>
