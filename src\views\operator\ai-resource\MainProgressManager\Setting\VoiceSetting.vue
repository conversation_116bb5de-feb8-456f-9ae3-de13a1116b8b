<template>
  <el-scrollbar class="voice-set-container" view-class="tw-pb-2 tw-px-1" v-loading="loading">
    <p class="tw-text-[13px] tw-text-[#969799] tw-m-[8px] tw-text-right">以下设置项如业务侧无特别要求，使用默认值即可</p>
    <el-row class="title">
      <el-col :span="3" class="tw-font-semibold">参数影响范围</el-col>
      <el-col :span="7" class="tw-font-semibold">场景说明</el-col>
      <el-col :span="14" class="tw-font-semibold">参数设置
        <el-button
          v-if="!isChecked && editStatus"
          type="primary"
          class="tw-absolute tw-right-[10px] tw-top-1"
          @click="reset"
        >
          重置
        </el-button>
      </el-col>
    </el-row>
    <el-row class="border">
      <el-col :span="3">敏感度</el-col>
      <el-col :span="21">
        <el-row class="border">
          <el-col :span="8">判断说话</el-col>
          <el-col :span="16">
            <div class="tw-flex">
              <div class="tw-grow">
                <el-row class="label">响度</el-row>
                <el-row class="tips"><el-icon :size="14"><WarningFilled /></el-icon>当声音响度超过该数值时，机器人判断出现声音。</el-row>
              </div>
              <div class="value">
                <InputNumberBox v-model:value="editData.loudness" append="" :min="0" :disabled="!editStatus"/>
              </div>
            </div>

            <div class="tw-flex">
              <div class="tw-grow">
                <el-row class="label">样本时长<span class="tw-text-[var(--primary-red-color)]">（该配置暂不生效，请勿设置）</span></el-row>
                <el-row class="tips"><el-icon :size="14"><WarningFilled /></el-icon>该参数值越大，则AI对短促的声音越迟钝</el-row>
              </div>
              <div class="value">
                <InputNumberBox v-model:value="editData.sampleTime" append="ms" :min="0" :disabled="!editStatus"/>
              </div>
            </div>

            <div class="tw-flex">
              <div class="tw-grow">
                <el-row class="label">判断开始说话敏感度<span class="tw-text-[var(--primary-red-color)]">（该配置暂不生效，请勿设置）</span></el-row>
                <el-row class="tips"><el-icon :size="14"><WarningFilled /></el-icon>该参数值越大，则AI对开始说话越迟钝</el-row>
              </div>
              <div class="value">
                <InputNumberBox v-model:value="editData.startTalkingSensitivity" append="%" :min="0" :disabled="!editStatus"/>
              </div>
            </div>

            <div class="tw-flex">
              <div class="tw-grow">
                <el-row class="label">判断退出说话敏感度<span class="tw-text-[var(--primary-red-color)]">（该配置暂不生效，请勿设置）</span></el-row>
                <el-row class="tips"><el-icon :size="14"><WarningFilled /></el-icon>该参数值越大，则AI对停止说话越迟钝</el-row>
              </div>
              <div class="value">
                <InputNumberBox v-model:value="editData.stopTalkingSensitivity" append="%" :min="0" :disabled="!editStatus"/>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row class="border">
          <el-col :span="8">判断停顿</el-col>
          <el-col :span="16" class="tw-flex">
            <div class="tw-grow">
              <el-row class="label">停顿时长超过</el-row>
              <el-row class="tips"><el-icon :size="14"><WarningFilled /></el-icon>该参数值越大，则AI对停顿的感知越迟钝</el-row>
            </div>
            <div class="value">
              <InputNumberBox v-model:value="editData.pauseTime" append="ms" :min="0" :disabled="!editStatus"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">判断一句话说完</el-col>
          <el-col :span="16" class="tw-flex">
            <div class="tw-grow">
              <el-row class="label">停顿时长超过</el-row>
              <el-row class="tips"><el-icon :size="14"><WarningFilled /></el-icon>该参数值越大，则AI对停顿的感知越迟钝</el-row>
            </div>
            <div class="value">
              <InputNumberBox v-model:value="editData.sentencePauseTime" append="ms" :min="0" :disabled="!editStatus"/>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row class="border">
      <el-col :span="3">最大限制</el-col>
      <el-col :span="7">最长单句长度</el-col>
      <el-col :span="14" class="tw-flex">
        <div class="tw-grow">
          <el-row class="label">最大单句时长不超过</el-row>
          <el-row class="tips"><el-icon :size="14"><WarningFilled /></el-icon>该参数值决定AI理解一次接听方语义的最长等待时间</el-row>
        </div>
        <div class="value">
          <InputNumberBox v-model:value="editData.maxSentenceTime" append="ms" :min="0" :disabled="!editStatus"/>
        </div>
      </el-col>
    </el-row>
    <el-row class="border">
      <el-col :span="3">打断停顿</el-col>
      <el-col :span="21">
        <el-row>
          <el-col :span="8">发声打断停顿</el-col>
          <el-col :span="16" class="tw-flex border">
            <div class="tw-grow">
              <el-row class="label"><span>发声打断时，最小停顿等待时间</span></el-row>
              <el-row class="tips"><el-icon :size="14"><WarningFilled /></el-icon>打断后，AI等待客户讲话的最小时间长度</el-row>
            </div>
            <div class="value">
              <el-select v-model="editData.vocalStopWaitTime" :disabled="!editStatus">
                <el-option v-for="item in waitTimeList" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
              <span class="tw-text-[var(--primary-black-color-400)] tw-grow-0 tw-w-[24px]">
                ms
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">语义打断时，最小停顿等待时间</el-col>
          <el-col :span="16" class="tw-flex">
            <div class="tw-grow">
              <el-row class="label"><span>语义打断时，最小停顿等待时间</span></el-row>
              <el-row class="tips"><el-icon :size="14"><WarningFilled /></el-icon> 打断后，AI等待客户讲话的最小时间长度</el-row>
            </div>
            <div class="value">
              <el-select v-model="editData.meaningStopWaitTime" :disabled="!editStatus">
                <el-option v-for="item in waitTimeList" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
              <span class="tw-text-[var(--primary-black-color-400)] tw-grow-0 tw-w-[24px]">
                ms
              </span>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </el-scrollbar>
  <div v-if="!isChecked" class=" tw-h-[60px] tw-flex tw-items-center tw-right-0 tw-justify-end tw-pr-[50px] tw-w-[100%] tw-bg-white">
    <el-button v-if="editStatus" type="primary" @click="save">保存</el-button>
    <el-button v-else type="primary" @click="edit">编辑</el-button>
    <el-button v-if="editStatus" @click="cancel">取消</el-button>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onDeactivated, onActivated , } from 'vue'
import { ElMessage } from 'element-plus'
import { WarningFilled } from '@element-plus/icons-vue'
import { useScriptStore } from '@/store/script'
import { AudioParams, AudioParamsOrigin } from '@/type/speech-craft'
import { scriptAudioModel } from '@/api/speech-craft'
import Confirm from '@/components/message-box'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import InputNumberBox from '@/components/InputNumberBox.vue'
import { trace } from '@/utils/trace'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const scriptId = scriptStore.id
const isChecked = scriptStore.isChecked
const editStatus = ref(false)
const editData = reactive<AudioParams>(new AudioParamsOrigin(scriptId))


// 最小停顿等待时间
const waitTimeList = [
  {name: 100, value: 100},
  {name: 500, value: 500},
  {name: 1000, value: 1000},
  {name: 1250, value: 1250},
  {name: 1500, value: 1500},
  {name: 1750, value: 1750},
  {name: 2000, value: 2000},
  {name: 2250, value: 2250},
  {name: 2500, value: 2500},
  {name: 2750, value: 2750},
  {name: 3000, value: 3000},
]


// 操作区
const initSet = async () => {
  loading.value = true
  try {
    const data = await scriptAudioModel.findAudioParameters({id: scriptId}) as AudioParams
    Object.assign(editData, data)
  } catch(err) {
    ElMessage({
      message: '获取数据失败',
      type: 'error',
    })
  }
  loading.value = false
}
const reset = () => {
  Object.assign(editData, new AudioParamsOrigin(scriptId))
  ElMessage({
    message: '声学参数重置完成',
    type: 'success',
  })
}
const edit = () => {
  editStatus.value = true
}
const cancel = () => {
  editStatus.value = false
  initSet()
}
const save = () => {
  Confirm({ 
    text: `您确定要保存吗?`,
    type: 'warning',
    title: `保存确认`,
    confirmText: '保存'
  }).then(async () => {
    loading.value = true
    try {
      trace({
        page: `话术编辑-声学设置-编辑(${scriptId})`,
        params: editData,
      })
      await scriptAudioModel.saveAudioParameters(editData)
      editStatus.value = false
      ElMessage({
        message: '设置保存成功',
        type: 'success',
      })
      initSet()
    } catch(err) {
      ElMessage({
        message: '设置保存失败',
        type: 'error',
      })
    }
    loading.value = false
  }).catch(() => {})
}

// 操作
onActivated(() => {
  initSet()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.voice-set-container {
  width: 100%;
  position: relative;
  height: calc(100vh - 280px);
  box-sizing: border-box;
  overflow-y: auto;
  font-size: 15px;
  background-color: #fff;
  .el-row, .el-col {
    text-align: center;
    align-items: center;
    flex-wrap: nowrap;
  }
  :deep(.el-row) {
    padding: 5px auto;
  }
  .el-input-number {
    width: 90%;
  }
  .title {
    text-align: justify;
    font-size: 17px;
    line-height: 50px;
    border-top: 2px solid #eee;
    border-bottom: 2px solid #eee;
  }
  .border {
    border-bottom: 1px solid #eee;
  }
  .tips {
    color: #bbb;
    text-align: justify;
    font-size: 13px;
    line-height: 30px;
    flex-wrap: no-wrap;
  }
  .label {
    font-size: 14px;
    line-height: 36px;
  }
  .value {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 12px;
    .el-input {
      width: 250px;
      height: 40px;
    }
    .el-select {
      width: 225px;
      height: 40px;
    
    }
    /* span {
      margin-left: 10px;
    } */
  }
}
</style>
