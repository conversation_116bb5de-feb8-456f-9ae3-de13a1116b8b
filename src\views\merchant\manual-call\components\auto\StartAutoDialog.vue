<template>
  <el-dialog
    v-model="dialogVisible"
    width="480px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">开始执行</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
      >
        <el-form-item label="自动结束：" prop="type">
          <el-radio-group v-model="editData.autoStop">
            <el-radio label="0">永久生效</el-radio>
            <el-radio label="1">指定日期</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="editData.autoStop=='1'" label="结束日期：" prop="autoStopTime">
          <el-date-picker
            v-model="editData.autoStopTime"
            type="date"
            placeholder="请选择结束日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            :clearable="false"
            :shortcuts="shortcuts"
            :disabledDate="disabledDate"
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div>
        <el-button @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm">开始执行</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive,} from 'vue'
import { ClueAutoActionInfo } from '@/type/clue'
import { clueManagerModel } from '@/api/clue'
import { ElMessage, } from 'element-plus'
import type { FormInstance, } from 'element-plus'
import dayjs from 'dayjs'
import to from 'await-to-js'

const emits = defineEmits(['update:visible', 'confirm'])
const loading = ref(false)
const props = defineProps<{
  visible: boolean,
  data: ClueAutoActionInfo | null,
  type: number
}>();
const editData = reactive<ClueAutoActionInfo>({
  id: undefined,
  autoStop: '0',
  status: '1',
  autoStopTime: undefined,
})
const dialogVisible = ref(props.visible)
const editRef = ref<FormInstance  | null>(null)
const shortcuts = [
  { text: '今日', value: dayjs() },
  { text: '明天', value: dayjs().add(1, 'day') },
  { text: '3天后', value: dayjs().add(3, 'day') },
  { text: '一周', value: dayjs().add(7, 'day') },
  { text: '一月后', value: dayjs().add(1, 'month') },
]
// 返回组件禁用的日期，用于限制可选日期范围
const disabledDate = (time: Date) => {
  const _minTime = Date.now()
  return time.getTime() < _minTime
}
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const rules = {
  autoStopTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: (rule: any, value: string, callback: any) => {
      if (value && value < dayjs().format('YYYY-MM-DD')) {
        return callback(new Error('结束时间不能小于当前时间'))
      }
      return callback()
    }, trigger: 'change' },
  ],
}

const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = {
        id: editData.id,
        autoStop: editData.autoStop,
        autoStopTime: editData.autoStop == '0' ? undefined : editData.autoStopTime,
        status: '1',
      }
      const obj: Record<number, Function> = {
        0: clueManagerModel.switchAutoImport,
        1: clueManagerModel.switchAutoSend,
        2: clueManagerModel.switchAutoAllocate,
        3: clueManagerModel.switchAutoArchive,
      }
      const res = await to(obj[props.type](params))
      loading.value = false
      if (!res[0]) {
        ElMessage({
        type: 'success',
        message: '操作成功'
        })
        emits('confirm')
        cancel()
      }

    }
  })
}
watch(props, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    props.data && Object.assign(editData, props.data)
    editData.autoStop = editData.autoStop || '0'
  }
}, {
  deep: true
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.dialog-form .el-form .el-form-item {
  margin-bottom: 13px;
}
</style>
