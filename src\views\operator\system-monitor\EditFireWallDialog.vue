<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">防火墙批量操作</div>
    </template>
    <el-form
      :model="addData"
      :rules="rules"
      label-width="77px"
      ref="formRef"
    >
      <el-form-item label="防火墙：" prop="fwFlags">
        <el-select
          class="tw-w-full"
          v-model="addData.fwFlags"
          placeholder="请选防火墙"
          multiple
          filterable
          clearable
        >
          <el-option
            v-for="item in fwOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作类型：" prop="secondIndustryId">
          <el-select
            class="tw-w-full"
            v-model="addData.type"
            placeholder="一级行业"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
      </el-form-item>
      <el-form-item v-if="addData.type === 2" prop="threshold" label="阈值：">
        <el-input-number v-model="addData.threshold" :precision="0" :controls="false" style="width: 100%" placeholder="请输入阈值" clearable :min="0"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import type { FormInstance, FormRules } from 'element-plus'
import to from 'await-to-js'

const props = defineProps<{
  visible: boolean;
}>();
const loading = ref(false)
const dialogVisible = ref(props.visible)
const rules: FormRules = {
  fwFlags: [
    { required: true,  message: '请选择防火墙', trigger: ['change'] },
  ],
  threshold: [
    { required: true,  message: '请输入阈值', type: 'number', trigger: ['change', 'input'] },
  ],
}
const typeOptions = [
  {label: '批量启动', value: 0},
  {label: '批量停止', value: 1},
  {label: '批量调整阈值', value: 2},
]
const fwOptions = ref<{label: string, value: string}[]>([])
const emits = defineEmits(['update:visible', 'confirm'])
const addData = reactive<{
  fwFlags: string[],
  type: number,
  threshold?: number,
}>({
  fwFlags: [],
  type: 0,
  threshold: undefined,
})

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const formRef = ref<FormInstance  | null>(null)
const confirm = () => {  
  formRef.value && formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      let data: [Error | null, any] = [null, null]
      switch(addData.type) {
        case 0: data = await to(monitorStatisticModel.startEngineByFws({
          fwFlags: addData.fwFlags.join(',')
        }));break;
        case 1: data = await to(monitorStatisticModel.stopEngineByFws({
          fwFlags: addData.fwFlags.join(',')
        }));break;
        case 2: data = await to(monitorStatisticModel.editEngineThresholdByFws({
          fwFlags: addData.fwFlags.join(','),
          threshold: addData.threshold!
        }));break;
      }
      loading.value = false
      if (data && !data[0]) {
        dialogVisible.value = false
        close()
        ElMessage.success('操作成功')
        emits('confirm')
      }
    }
  })
}
watch(() => props.visible, async n => {
  dialogVisible.value = n
  if (n) {
    loading.value = false
    addData.fwFlags = []
    addData.threshold = 0
    const [err, data] = await to(monitorStatisticModel.getEngineFws())
    fwOptions.value = data?.map(item => ({label: item, value: item})) || []
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
</style>