<template>
  <el-dialog
    v-model="dialogVisible"
    width="480px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">空闲坐席</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-py-[12px] tw-px-[2px]"
      view-class="tw-grid tw-grid-cols-3 tw-gap-[8px]"
    >
      <div v-for="item in seatList" :key="item.id" class="info-data-box-inner tw-bg-[#f5f7fa] tw-leading-[20px]">
        <div class="info-content tw-truncate">{{ item.account }}</div>
        <div class="info-title-deep tw-font-[600]">{{ formatDuration(dayjs().diff(dayjs(item.callSeatRestTimeStart), 's')) || '-' }}</div>
      </div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive,} from 'vue'
import dayjs from 'dayjs'
import { formatDuration } from '@/utils/utils'

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  list: {
    id: number, callSeatRestTimeStart?: string, account?: string
  }[],
  visible: boolean
}>();

const dialogVisible = ref(props.visible)
const seatList = ref(props.list)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, n => {
  dialogVisible.value = props.visible
  if (n) {
    seatList.value = props.list
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.info-data-box-inner {
  padding: 4px 8px;
  gap: 0;
}
</style>
