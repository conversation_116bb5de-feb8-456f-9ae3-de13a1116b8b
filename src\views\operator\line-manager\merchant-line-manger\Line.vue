<template>
  <div class="line-manger-container">
    <div class="tw-w-full tw-bg-white tw-p-[16px] tw-grow-0 tw-shrink-0">
      <div class="tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-gap-3 tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.tenantLineName"
            placeholder="线路名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.tenantLineNumber"
            placeholder="线路编号"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-select v-model="searchForm.status" placeholder="生效状态" clearable @change="search()">
            <el-option
              v-for="lineStatusItem in Object.values(supplierLineStatusList)"
              :key="lineStatusItem.name"
              :value="lineStatusItem.val"
              :label="lineStatusItem.text"
            />
          </el-select>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.tenantName"
            placeholder="商户名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.tenantNumber"
            placeholder="商户编号"
            clearable
            @keyup.enter="search"
          />
        </div>
      </div>
      <div class="tw-flex tw-justify-end tw-mt-[12px]">
        <el-button type="primary" link @click="clearSearchForm">
          <el-icon size="--el-font-size-base">
            <SvgIcon name="reset" color="var(--el-color-primary)" />
          </el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" link @click="search">
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)">
            <SvgIcon name="search" color="none" />
          </el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableTempData"
      class="tw-grow"
      row-key="tenantLineNumber"
      :header-cell-style="tableHeaderStyle"
      stripe
      border
      @sort-change="handleSortChange"
    >
      <el-table-column
        property="tenantLineName"
        label="商户线路名称"
        align="left"
        min-width="240"
        :formatter="formatterEmptyData"
        show-overflow-tooltip
        fixed="left"
      ></el-table-column>
      <el-table-column
        property="lineType"
        label="线路类型"
        align="center"
        width="80"
        :formatter="formatterEmptyData"
        show-overflow-tooltip
        fixed="left"
      >
        <template #default="{ row }: { row: { tenantLineType: MerchantLineTypeEnum}}">
          {{ getMerchantLineTypeText(row?.tenantLineType) }}
        </template>
      </el-table-column>
      <el-table-column
        property="tenantLineNumber"
        label="线路编号"
        align="left"
        min-width="160"
        :formatter="formatterEmptyData"
        show-overflow-tooltip
        fixed="left"
      ></el-table-column>
      <el-table-column
        property="tenantName"
        label="商户名称"
        align="left"
        min-width="160"
        :formatter="formatterEmptyData"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <el-button v-if="row.tenantName" type="primary" link @click="goTenantRoute(row.tenantName)">{{
              row.tenantName
            }}
          </el-button>
          <template v-else>-</template>
        </template>
      </el-table-column>
      <el-table-column
        property="tenantNumber"
        label="商户编号"
        align="left"
        min-width="120"
        show-overflow-tooltip
        :formatter="formatterEmptyData"
      ></el-table-column>
      <el-table-column
        property="status"
        label="生效状态"
        align="center"
        min-width="100"
        :formatter="formatterEmptyData"
      >
        <template #default="{ row }">
          <span class="status-box" :class="row.status === 'ENABLE' ? 'green-status' : 'orange-status'">
            {{ row.status === 'ENABLE' ? '启用' : '停用' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column property="concurrentLimit" sortable label="并发限制" align="left" min-width="120">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-start">
            <span>{{ column.label }}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon
                :size="11"
                :color="orderCol==='concurrentLimit' && orderType==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"
              >
                <CaretTop />
              </el-icon>
              <el-icon
                :size="11"
                class="tw-mt-[-5px]"
                :color="orderCol==='concurrentLimit' && orderType==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"
              >
                <CaretBottom />
              </el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatNumber(row.concurrentLimit) }}
        </template>
      </el-table-column>
      <el-table-column property="updateTime" sortable label="最后使用日期" align="center" min-width="160">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{ column.label }}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon
                :size="11"
                :color="orderCol==='updateTime' && orderType==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"
              >
                <CaretTop />
              </el-icon>
              <el-icon
                :size="11"
                class="tw-mt-[-5px]"
                :color="orderCol==='updateTime' && orderType==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"
              >
                <CaretBottom />
              </el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="100" align="right" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="goEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="goDetail(row)">详情</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total || 0"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onActivated, onUnmounted, onDeactivated } from 'vue'
import { ElMessage, } from 'element-plus'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { formatNumber, formatterEmptyData, handleTableSort } from '@/utils/utils'
import { MerchantLineTypeEnum } from '@/type/merchant'
import { supplierLineStatusList, } from '@/assets/js/map-supplier'
import { TenantLineParam, TenantLineItem, } from '@/type/line'
import PaginationBox from '@/components/PaginationBox.vue'
import { lineMerchantModel } from '@/api/line'
import dayjs from 'dayjs'
import { tableHeaderStyle } from '@/assets/js/constant'
import { goMerchantLineDetail, getMerchantLineTypeText } from '@/utils/line'
import router from '@/router'
import { useRoute } from 'vue-router'

const globalStore = useGlobalStore()
const { loading, } = storeToRefs(globalStore)
// 分页信息
const currentPage = ref(1)
const pageSize = ref(20)
const tableData = ref<TenantLineItem[] | null>([])
const total = ref(0)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  globalStore.pageSize = s
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return (data || []).slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
// 表格排序
const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}

// 搜索
class searchOrigin {
  tenantLineName = undefined
  tenantLineNumber = undefined
  status = undefined
  tenantNumber = undefined
  tenantName = undefined
}

const searchForm = reactive<TenantLineParam>(new searchOrigin())
const search = async () => {
  loading.value = true
  tableData.value = (await lineMerchantModel.getLineList(searchForm) || []) as TenantLineItem[]
  total.value = tableData.value?.length || 0
  loading.value = false
}
// 重置
const clearSearchForm = () => {
  Object.assign(searchForm, new searchOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}

// 点击跳转商户管理路由
const goTenantRoute = (name: string) => {
  name && router.push({
    name: 'MerchantManager',
    query: {
      tenantName: name
    }
  })
}
// 表格 -- 前往【编辑】
const goEdit = async (row: TenantLineItem) => {
  goMerchantLineDetail(row, true)
}
// 表格 -- 前往【详情】
const goDetail = async (row: TenantLineItem) => {
  router.push({
    name: 'MerchantLineMonitorDetails',
    query: {
      tenantLineNumber: row.tenantLineNumber,
      tenantLineName: row.tenantLineName,
      tenantNumber: row.tenantNumber,
      groupId: row.groupId,
    }
  })
}

// 初始化信息
onActivated(() => {
  const route = useRoute();
  searchForm.tenantLineNumber = route.query.tenantLineNumber as string || ''
  pageSize.value = globalStore.pageSize || 50
  search()
})
onDeactivated(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-manger-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  display: flex;
  flex-direction: column;

  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .label {
      width: 120px
    }

    :deep(.el-input__wrapper) {
      width: 100%;
    }

    .el-select {
      width: 100%;
    }
  }

  .el-table {
    font-size: 13px;
  }

  .table-btn-box {
    display: flex;

    .el-button {
      width: 60px;
    }
  }

  :deep(.el-table .caret-wrapper) {
    display: none;
  }
}
</style>
