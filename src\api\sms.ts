import { http } from '@/axios'
import {
  SmsAccountItem,
  SmsAccountPendingParams,
  SmsAccountWarningConfig,
  SmsProviderAccountParams,
  SmsProviderItem,
  SmsProviderParams,
  SmsSendParam,
  SmsStatisticItem,
  SmsStatisticSearch,
  SmsUrlItem
} from '@/type/sms'
import { filterEmptyParams } from '@/utils/utils'
import { workbenchApiUrlNewPrefix } from './common'
import { UpwardSmsParams } from '@/type/sms'
import { cancelRequest } from '@/axios'
import { ResponseData } from '@/axios/request/types'

// 短信供应商
export const smsProviderModel = {
  // 按状态获取供应商列表
  findProviderList: async (params: SmsProviderParams) => {
    return http({
      url: '/AiSpeech/smsSupplier/findSmsSuppliersByStatus',
      method: 'GET',
      params,
    }).then(res => res as unknown as SmsProviderItem[])
  },
  // 按ID查找单个供应商
  findProviderById: async (params: SmsProviderParams) => {
    return http({
      url: '/AiSpeech/smsSupplier/findOneSmsSupplierById',
      method: 'POST',
      params,
    }).then(res => res as unknown as SmsProviderItem[])
  },
  // 新增供应商
  addProvider: async (data: SmsProviderParams) => {
    return http({
      url: '/AiSpeech/smsSupplier/addOneSmsSupplier',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsProviderItem[])
  },
  // 编辑供应商
  editProvider: async (data: SmsProviderParams) => {
    return http({
      url: '/AiSpeech/smsSupplier/editOneSmsSupplier',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsProviderItem[])
  },
}

// 短信对接账号
export const smsAccountModel = {
  // 获取所有供应商的全部账号列表
  findAllAccountList: async (data: SmsProviderAccountParams) => {
    return http({
      url: '/AiSpeech/smsAccount/findSmsAccountListByConditions',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsProviderItem[])
  },
  // 获取当前供应商的账号列表
  findAccountList: async (data: SmsProviderAccountParams) => {
    return http({
      url: '/AiSpeech/smsAccount/findSmsAccountListByConditions',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsProviderItem[])
  },
  // 按ID查找单个账号
  findAccountById: async (params: SmsProviderParams) => {
    return http({
      url: '/AiSpeech/smsAccount/findOneSmsAccountById',
      method: 'POST',
      params,
    }).then(res => res as unknown as SmsProviderItem[])
  },
  // 新增账号
  addAccount: async (data: SmsProviderParams) => {
    return http({
      url: '/AiSpeech/smsAccount/addOneSmsAccount',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsProviderItem[])
  },
  // 编辑账号
  editAccount: async (data: SmsProviderParams) => {
    return http({
      url: '/AiSpeech/smsAccount/editOneSmsAccount',
      method: 'POST',
      data,
    }).then(res => res as unknown as SmsProviderItem[])
  },
  // 修改短信对接账号挂起状态
  switchSmsAccountStatus: async (params: SmsAccountPendingParams) => {
    return http({
      url: '/AiSpeech/smsAccount/changePendingStatus',
      method: 'POST',
      params,
    }).then(res => res as unknown as SmsAccountItem)
  },
  // 查询短信账号预警
  findSmsAccountWarning: async (params: {
    accountNumber: string
  }) => {
    return http({
      url: '/AiSpeech/smsAccount/findBySmsAccountWarnAccountNumber',
      method: 'POST',
      params,
    }).then(res => res as unknown as SmsAccountWarningConfig)
  },
  // 保存短信账号预警
  saveSmsAccountWarning: async (data: SmsAccountWarningConfig) => {
    return http({
      url: '/AiSpeech/smsAccount/editSmsAccountWarn',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}

// 短链
export const smsUrlModel = {
  // 短链列表
  findNormalList: (data: SmsUrlItem) => {
    return http({
      url: "/AiSpeech/shortLinkOrdinary/findLinksByConditions",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as SmsUrlItem[])
  },
  // 根据groupId短链列表
  findNormalListByGroupId: (data: { groupId: string }) => {
    return http({
      url: "/AiSpeech/shortLinkOrdinary/findLinksByGroupId",
      method: "GET",
      data,
    }).then(res => res as unknown as SmsUrlItem[])
  },

  saveNormal: (data: SmsUrlItem) => {
    return http({
      url: data.id ? "/AiSpeech/shortLinkOrdinary/editOneShortLinkOrdinary" : "/AiSpeech/shortLinkOrdinary/addOneShortLinkOrdinary",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  deleteNormal: (params: { id: number }) => {
    return http({
      url: "/AiSpeech/shortLinkOrdinary/deleteOneShortLinkOrdinaryById",
      method: "DELETE",
      params,
    }).then(res => res as unknown)
  },
  switchNormal: (params: { id: number, enableStatus: string }) => {
    return http({
      url: "/AiSpeech/shortLinkOrdinary/changeShortLinkOrdinaryStatus",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  // 千人千链 列表
  findThousandList: (data: SmsUrlItem) => {
    return http({
      url: "/AiSpeech/shortLinkThousand/findLinksByConditions",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as SmsUrlItem[])
  },
  // 根据groupId查询千人千链
  findThousandListByGroupId: (data: { groupId: string }) => {
    return http({
      url: "/AiSpeech/shortLinkThousand/findLinksByGroupId",
      method: "GET",
      data,
    }).then(res => res as unknown as SmsUrlItem[])
  },

  saveThousand: (data: SmsUrlItem) => {
    return http({
      url: data.id ? "/AiSpeech/shortLinkThousand/editOneShortLinkThousand" : "/AiSpeech/shortLinkThousand/addOneShortLinkThousand",
      method: "POST",
      data,
    }).then(res => res as unknown)
  },
  deleteThousand: (params: { id: number }) => {
    return http({
      url: "/AiSpeech/shortLinkThousand/deleteOneShortLinkThousandById",
      method: "DELETE",
      params,
    }).then(res => res as unknown)
  },
  switchThousand: (params: { id: number, enableStatus: string }) => {
    return http({
      url: "/AiSpeech/shortLinkThousand/changeShortLinkThousandStatus",
      method: "POST",
      params,
    }).then(res => res as unknown)
  },
  // 千人千链 统计部分
  // 获取数据统计
  findNoramlStatisticList: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkOrdinaryMonitorForDomain",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as SmsStatisticItem[])
  },
  // 获取普通短链点击时间分布
  findNormalStatisticByDate: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkOrdinaryMonitorForClickRate",
      method: "POST",
      data,
    }).then(res => res as unknown as Record<string, { clickCount: number, sendCount: number, rate: number }>)
  },
  // 获取普通短链点击地区分布
  findNormalStatisticByArea: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkOrdinaryMonitorForProvinces",
      method: "POST",
      data,
    }).then(res => res as unknown as Record<string, { clickCount: number, sendCount: number, rate: number }>)
  },
  // 获取普通短链点击设备分布
  findNormalStatisticByDevice: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkOrdinaryMonitorForDeviceType",
      method: "POST",
      data,
    }).then(res => res as unknown as Record<string, { clickCount: number, sendCount: number, rate: number }>)
  },
  // 获取普通短链点击浏览器分布
  findNormalStatisticByBrowser: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkOrdinaryMonitorForBrowserType",
      method: "POST",
      data,
    }).then(res => res as unknown as Record<string, { clickCount: number, sendCount: number, rate: number }>)
  },

  // 获取千人千链数据统计
  findThousandStatisticList: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkThousandMonitorForDomain",
      method: "POST",
      data: filterEmptyParams(data),
    }).then(res => res as unknown as SmsStatisticItem[])
  },
  // 获取千人千链点击时间分布
  findThousandStatisticByDate: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkThousandMonitorForClickRate",
      method: "POST",
      data,
    }).then(res => res as unknown as Record<string, { clickCount: number, sendCount: number, rate: number }>)
  },
  // 获取千人千链点击地区分布
  findThousandStatisticByArea: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkThousandMonitorForProvinces",
      method: "POST",
      data,
    }).then(res => res as unknown as Record<string, { clickCount: number, sendCount: number, rate: number }>)
  },
  // 获取千人千链点击设备分布
  findThousandStatisticByDevice: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkThousandMonitorForDeviceType",
      method: "POST",
      data,
    }).then(res => res as unknown as Record<string, { clickCount: number, sendCount: number, rate: number }>)
  },
  // 获取千人千链点击浏览器分布
  findThousandStatisticByBrowser: (data: SmsStatisticSearch) => {
    return http({
      url: "/AiMonitor/shortLinkMonitorData/getShortLinkThousandMonitorForBrowserType",
      method: "POST",
      data,
    }).then(res => res as unknown as Record<string, { clickCount: number, sendCount: number, rate: number }>)
  },
  findNormalDomainList: () => {
    return http({
      url: "/AiSpeech/shortLink/getDomainsAvailable",
      method: "GET",
    }).then(res => res as unknown as string[])
  },
  findVolcanoDomainList: () => {
    return http({
      url: "/AiSpeech/shortLink/getVolcanoDomainsAvailable",
      method: "GET",
    }).then(res => res as unknown as string[])
  },
}

// 发送短信
export const smsSendModel = {
  // 线索详情发送短信
  sendSmsInClueDetail: async (data: SmsSendParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/sms/sendClueDirectSms'
        : '/AiSpeech/message/sendClueDirectSms',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人工直呼发送短信
  sendSmsInDirectCall: async (data: SmsSendParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/sms/sendHumanDirectSms'
        : '/AiSpeech/message/sendHumanDirectSms',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  // 人机协同发送短信
  sendSmsInHumanMachine: async (data: SmsSendParam) => {
    return http({
      url: workbenchApiUrlNewPrefix
        ? '/AiWorkbench/sms/sendHumanMachineDirectSms'
        : '/AiSpeech/message/sendHumanMachineDirectSms',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
}

// 上行短信
export const upwardSmsModel = {
  // 查询上行短信列表
  findRecordList: (data: UpwardSmsParams, isOperation: boolean = false) => {
    data.startPage = (data.startPage! >= 0) ? data.startPage : 0
    isOperation ? cancelRequest('/AiSpeech/message/findMessageRecordListForOperation') : cancelRequest('/AiSpeech/message/findMessageRecordList')
    return http({
      data: filterEmptyParams(data),
      url: isOperation ? "/AiSpeech/smsMoRecord/findMessageRecordListForOperation" : "/AiSpeech/smsMoRecord/findMessageRecordList",
      method: "POST",
    }).then(res => res as unknown as ResponseData)
  },
  getRecordNum: (data: UpwardSmsParams) => {
    cancelRequest('/AiSpeech/smsMoRecord/findMessageRecordListNum')
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/smsMoRecord/findMessageRecordListNum",
      method: "POST",
    }).then(res => res as unknown as ResponseData)
  },
}
