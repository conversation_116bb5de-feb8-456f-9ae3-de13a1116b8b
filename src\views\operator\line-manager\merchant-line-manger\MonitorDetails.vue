<template>
  <HeaderBox :title="titleList" :can-refresh="true" @refresh="refreshAction"/>
  <el-scrollbar v-loading="loading" class="monitor-details-container" view-class="tw-flex tw-flex-col">
    <TabsBox v-model:active="activeTab" :tabList="tabList">
      <el-button class="tw-absolute tw-top-[2px] tw-right-[12px]" type="primary" @click="editLine">编辑线路</el-button>
    </TabsBox>
    <keep-alive>
      <MonitorDetailsDistribute v-if="activeTab===tabList[0]" :lineInfo="lineTotalInfo" v-model:refresh="needTotalRefresh"/>
      <div v-else>
        <LineChartBox :lineInfo="lineTotalInfo" :type="1" v-model:refresh="needTotalRefresh"></LineChartBox>
      </div>
    </keep-alive>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, watch, onMounted, onUnmounted, defineAsyncComponent } from 'vue'
import HeaderBox from '@/components/HeaderBox.vue'
import { onBeforeRouteLeave, useRoute } from 'vue-router'
import { SupplierMonitorInfoItem, TenantMonitorDetailsParams, MonitorInfoItem, SupplierLineInfoItem } from '@/type/line'
import TabsBox from '@/components/TabsBox.vue'
import { lineMerchantModel } from '@/api/line'
import { goMerchantLineDetail, } from '@/utils/line'

const MonitorDetailsDistribute = defineAsyncComponent({loader: () => import('./MonitorDetailsDistribute.vue')})
const LineChartBox = defineAsyncComponent({loader: () => import('../components/LineChartBox.vue')})
// 获取路由传参
const route = useRoute();
// 顶部面包屑
const titleList = computed(() => [
  {title: '运行监控', name: 'MerchantLineManger',},
  {title: route.query.tenantLineName ? `【${route.query.tenantLineName}】详情` : '详情'},
])

const tabList = ['数据分布', '数据内容']
const activeTab = ref(tabList[0])

// 表格选中线路信息
const lineTotalInfo = ref<MonitorInfoItem | null>(null)

const needTotalRefresh = ref(false) // 外部全部的并发

const refreshAction = () => {
  needTotalRefresh.value = true
}

const loading = ref(false)
const editLine = () => {
  if (!lineTotalInfo.value) return
  goMerchantLineDetail({
    tenantLineNumber: route.query.tenantLineNumber as string,
    tenantNumber: route.query.tenantNumber as string,
    account: route.query.account as string,
    groupId: route.query.groupId as string,
  })
}
const init = async () => {
  loading.value = true
  const res = await lineMerchantModel.getMonitorList({
    tenantLineName: route.query.tenantLineName as string,
    tenantLineNumber: route.query.tenantLineNumber as string,
    recentMin: undefined
  }) as MonitorInfoItem[]
  lineTotalInfo.value = res[0] || {
    tenantLineName: route.query.tenantLineName as string,
    tenantLineNumber: route.query.tenantLineNumber as string,
  }
  loading.value = false
}

init()
watch(() => route.query, () => {
  init()
}, {deep: true})

// 【定时更新】每隔5分钟触发刷新，【进入刷新】包括tab切换和浏览器标签切换，触发刷新
// 定时器
const timer = ref()
onMounted(() => {
  timer.value = setInterval(() => {
    refreshAction()
  }, 5 * 60 * 1000)
})
const clearAll = () => {
  clearInterval(timer.value)
  timer.value = null
}
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.monitor-details-container {
  width: 100%;
  overflow-x: hidden;
  min-width: 1080px;
  box-sizing: border-box;
  padding: 16px;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
    :deep(.cell) {
      padding: 0 8px;
    }
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
  :deep(.el-table th .cell) {
    line-height: 13px;
  }
}
</style>
