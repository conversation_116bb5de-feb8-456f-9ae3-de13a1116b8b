import { supplierLineTypeList } from '@/type/supplier'

export const dialogLineFormRules = {
  // 线路名称
  lineName: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('线路名称不能为空'))
      } else {
        callback()
      }
    }
  }],
  // 线路类型
  lineType: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!Object.keys(supplierLineTypeList).includes(value)) {
        callback(new Error('线路类型不正确'))
      } else {
        callback()
      }
    },
  }],
  // 启用状态
  enableStatus: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('启用状态不能为空'))
      } else {
        callback()
      }
    }
  }],
  // 主叫号码
  masterCallNumber: [
    // { required: true, trigger: ['blur',], message: '主叫号码不能为空' },
    // {
    //   required: true, trigger: ['blur'], validator: (rule: any, value: any, callback: any) => {
    //     if (!value) {
    //       callback(new Error('主叫号码不能为空'))
    //     } else {
    //       if (/^\d{5,20}$/.test(value)) {
    //         callback()
    //       } else {
    //         callback(new Error('主叫号码格式不正确，请输入5-20位数字'))
    //       }
    //     }
    //   }
    // }
  ],
  // 注册IP
  registerIp: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('注册IP不能为空'))
      } else {
        // 将输入的字符串按半角句号分隔并解析
        const arr = value.split('.')
        // 只有4组数字都是0-255的自然数才通过校验
        const checked = arr.length === 4 && arr.every((item: string) => {
          const num = parseInt(item)
          return !Number.isNaN(num) && 0 <= num && num <= 255
        })
        checked ? callback() : callback(new Error('注册IP格式不正确，请输入4组0-255的数字，并以半角句号隔开，格式：xxx.xxx.xxx.xxx'))
      }
    }
  }],
  // 注册端口
  registerPort: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('注册端口不能为空'))
      } else {
        if (/^\d+$/.test(value) && value - 0 >= 0 && value - 0 <= 65536) {
          callback()
        } else {
          callback(new Error('注册端口格式不正确，请输入0-65535的数字'))
        }
      }
    }
  }],
  // 并发上限
  concurrentLimit: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('并发上限不能为空'))
      } else {
        if (/^\d+$/.test(value) && value - 0 > 0) {
          callback()
        } else {
          callback(new Error('并发上限格式不正确，请输入正整数'))
        }
      }
    }
  }],
  // 速率限制
  caps: [{
    required: false, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
      } else {
        if (/^\d+$/.test(value) && value > 0 && value <= 1000) {
          callback()
        } else {
          callback(new Error('CAPS格式不正确，请输入1~1000的正整数'))
        }
      }
    }
  }],
  // 外呼类型
  outboundTypes: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value || Array.isArray(!value) || !value.length) {
        callback(new Error('外呼类型不能为空'))
      } else {
        callback()
      }
    }
  }],
  // 适用行业 二级行业
  secondIndustries: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value || Array.isArray(!value) || !value.length) {
        callback(new Error('适用行业不能为空'))
      } else {
        callback()
      }
    }
  }],
  // 适用行业
  primaryIndustry: [{ required: true, trigger: ['blur', 'change'], message: '适用行业不能为空' }],
  // 所属运营商
  serviceProviders: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value || Array.isArray(!value) || !value.length) {
        callback(new Error('所属运营商不能为空'))
      } else {
        callback()
      }
    }
  }],
  // 接入类型
  lineAccessType: [{ required: true, trigger: ['blur', 'change'], message: '接入类型不能为空' }],
  // 外显号码：仅做备注用，不限制
  displayCallNumber: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('外显号码不能为空'))
        // } else {
        //   if (/^[0-9]{5,20}$/.test(value)) {
        //     callback()
        //   } else {
        //     callback(new Error('外显号码格式不正确，请输入5-20位数字或字母'))
        //   }
      }
      callback()
    }
  }],
  // 线路拨打限制
  callingRestrictions: [{
    trigger: ['blur', 'change'], validator: (rule: any, value: string[], callback: any) => {
      if (value && value.length > 0) {
        const hours: string[] = []
        value.map(item => {
          const arr = item.split('-')
          if (!arr[0] || !arr[1]) {
            callback(new Error('线路拨打限制规则存在空值'))
          } else {
            hours.includes(arr[1]) ? callback(new Error('线路拨打限制规则存在相同时间段')) : hours.push(arr[1])
          }
        })
      }
      callback()
    }
  }],
  // 线路拨通限制
  dialingRestrictions: [{
    trigger: ['blur', 'change'], validator: (rule: any, value: string[], callback: any) => {
      if (value && value.length > 0) {
        const hours: string[] = []
        value.map(item => {
          const arr = item.split('-')
          if (!arr[0] || !arr[1]) {
            callback(new Error('线路拨通限制规则存在空值'))
          } else {
            hours.includes(arr[1]) ? callback(new Error('线路拨通限制规则存在相同时间段')) : hours.push(arr[1])
          }
        })
      }
      callback()
    }
  }],
  // 平台拨打限制
  callingRestrictionsGlobal: [{
    trigger: ['blur', 'change'], validator: (rule: any, value: string[], callback: any) => {
      if (value && value.length > 0) {
        const hours: string[] = []
        value.map(item => {
          const arr = item.split('-')
          if (!arr[0] || !arr[1]) {
            callback(new Error('平台拨打限制规则存在空值'))
          } else {
            hours.includes(arr[1]) ? callback(new Error('平台拨打限制规则存在相同时间段')) : hours.push(arr[1])
          }
        })
      }
      callback()
    }
  }],
  // 拨通限制
  dialingRestrictionsGlobal: [{
    trigger: ['blur', 'change'], validator: (rule: any, value: string[], callback: any) => {
      if (value && value.length > 0) {
        const hours: string[] = []
        value.map(item => {
          const arr = item.split('-')
          if (!arr[0] || !arr[1]) {
            callback(new Error('平台拨通限制规则存在空值'))
          } else {
            hours.includes(arr[1]) ? callback(new Error('平台拨通限制规则存在相同时间段')) : hours.push(arr[1])
          }
        })
      }
      callback()
    }
  }],
  // 线路单价
  unitPrice: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (value === undefined || value === null) {
        callback(new Error('线路单价不能为空'))
      } else {
        callback()
      }
    }
  }],
  // 外呼范围
  scope: [{
    required: true, trigger: ['blur', 'change'], validator: (rule: any, value: any, callback: any) => {
      if (!value || !Object.keys(value).length) {
        callback(new Error('外呼范围不能为空'))
      } else {
        callback()
      }
    }
  }],
}
