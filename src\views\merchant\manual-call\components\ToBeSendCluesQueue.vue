<template>
  <div class="search-box">
    <div class="tw-grid tw-grid-cols-5 tw-gap-[8px]" :class="isExpand ? 'tw-pb-[8px]':'tw-pb-[12px]'">
      <div class="item-col">
        <div class="label">号码/名单编号：</div>
        <InputPhonesBox
          v-model:value="searchForm.phone"
          @search="search()"
        />
      </div>
      <div class="item-col">
        <span class="label">姓名：</span>
        <el-input
          v-model="searchForm.name"
          placeholder="请输入姓名"
          @keyup.enter="search()"
          clearable
        >
        </el-input>
      </div>
      <div class="item-col">
        <div class="label">线索来源：</div>
        <el-select v-model="searchForm.fromType" placeholder="请选择线索来源" clearable>
          <el-option v-for="item in clueFromOption" :label="item.name" :value="item.value" :key="item.value"/>
        </el-select>
      </div>
      <div class="item-col">
        <div class="label">可下发状态：</div>
        <el-select v-model="searchForm.canBeSend" placeholder="请选择可下发状态" clearable>
          <el-option label="是" :value="true"/>
          <el-option label="否" :value="false"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class="label">导入时间：</span>
        <TimePickerBox
          v-model:start="searchForm.importTimeStart"
          v-model:end="searchForm.importTimeEnd"
          :maxRange="60*60*24*31*1000"
          :clearable="false"
        />
      </div>
    </div>
    <div v-show="isExpand" class="tw-grid tw-grid-cols-5 tw-gap-[8px] tw-pb-[12px]">
      <div class="item-col">
        <span class="label">省：</span>
        <el-select v-model="searchForm.provinceCode" placeholder="请选择省" filterable clearable @change="searchForm.cityCode=''">
          <el-option v-for="item in provinceList||[]" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class="label">市：</span>
        <el-select v-model="searchForm.cityCode" placeholder="请选择市" filterable clearable>
          <el-option v-for="item in cityList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class="label">外呼任务：</span>
        <SelectPageBox
          v-model:selectVal="searchForm.taskIds"
          :options="taskList||[]"
          name="taskName"
          val="id"
          placeholder="外呼任务"
          :filterable="true"
          class="tw-flex-grow"
          isRemote
          :loading="loadingTask"
          @update:options="updateTaskList"
          :total="taskNum"
          multiple
          canSelectAll
        >
        </SelectPageBox>
      </div>
      <div class="item-col">
        <span class="label">执行话术：</span>
        <SelectPageBox
          v-model:selectVal="searchForm.scriptStringIds"
          :options="speechCraftList||[]"
          name="scriptName"
          val="scriptStringId"
          placeholder="请选择执行话术"
          :filterable="true"
          class="tw-flex-grow"
          multiple
          canSelectAll
        >
        </SelectPageBox>
      </div>
      <div class="item-col">
        <span class="label">呼出时间：</span>
        <TimePickerBox
          v-model:start="searchForm.callOutTimeStart"
          v-model:end="searchForm.callOutTimeEnd"
        />
      </div>
      <div class="item-col">
        <span class="label">呼叫状态：</span>
        <el-select
          v-model="searchForm.callStatusCodes"
          placeholder="请选择呼叫状态"
          multiple
          collapse-tags
          :max-collapse-tags="1"
          collapse-tags-tooltip
          clearable
        >
          <el-option v-for="item in clueCallStatusOptions" :key="item.name" :label="item.name" :value="item.value"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class="label">分类结果：</span>
        <el-select
          v-model="searchForm.intentionClasses"
          placeholder="请选择分类结果"
          multiple
          collapse-tags
          :max-collapse-tags="2"
          collapse-tags-tooltip
          clearable
        >
          <el-option v-for="item in IntentionClassEnum" :key="item" :label="item" :value="item"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class="label">标签：</span>
        <el-input
          v-model="searchForm.label"
          placeholder="请输入标签"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div>
      <!-- <div class="item-col">
        <span class="label">公司：</span>
        <el-input
          v-model.trim="searchForm.company"
          placeholder="请输入公司"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div>
      <div class="item-col">
        <span class="label">备注：</span>
        <el-input
          v-model.trim="searchForm.comment"
          placeholder="请输入备注"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div> -->
      <div class="item-col">
        <span class="label">已加入任务：</span>
        <SelectPageBox
          v-model:selectVal="searchForm.joinedTaskIds"
          :options="taskList||[]"
          name="taskName"
          val="id"
          placeholder="已加入任务"
          :filterable="true"
          class="tw-flex-grow"
          isRemote
          :loading="loadingTask"
          @update:options="updateTaskList"
          :total="taskNum"
          multiple
          canSelectAll
        >
        </SelectPageBox>
      </div>
      <!-- <div class="item-col">
        <span class="label">加入任务状态：</span>
        <el-select
          v-model="searchForm.joinedTaskStatus"
          placeholder="请选加入任务状态"
          multiple
          collapse-tags
          :max-collapse-tags="2"
          collapse-tags-tooltip
          clearable
        >
          <el-option v-for="item in taskStatusOption" :key="item.value" :label="item.name" :value="item.value"/>
        </el-select>
      </div> -->
      <div class="item-col">
        <span class="label">通时：</span>
        <div class="tw-flex tw-items-center">
          <InputNumberBox v-model:value="searchForm.minCallDuration" :max="searchForm.maxCallDuration || Number.MAX_VALUE" placeholder="最低" style="width: 47%" append="秒"/>
          <span class="tw-w-[14px] tw-shrink-0">-</span>
          <InputNumberBox v-model:value="searchForm.maxCallDuration" placeholder="最高" style="width: 47%" append="秒" :min="searchForm.minCallDuration || 0"/>
        </div>
      </div>
      <div class="item-col">
        <span class="label">对话：</span>
        <div class="tw-flex tw-items-center">
          <InputNumberBox v-model:value="searchForm.minCycleCount" :max="searchForm.maxCycleCount || Number.MAX_VALUE" placeholder="最低" style="width: 47%" append="轮"/>
          <span class="tw-w-[14px] tw-shrink-0">-</span>
          <InputNumberBox v-model:value="searchForm.maxCycleCount" placeholder="最高" style="width: 47%" append="轮" :min="searchForm.minCycleCount || 0"/>
        </div>
      </div>
      <div class="item-col">
        <span class="label">说话：</span>
        <div class="tw-flex tw-items-center">
          <InputNumberBox v-model:value="searchForm.minSayCount" :max="searchForm.maxSayCount || Number.MAX_VALUE" placeholder="最低" style="width: 47%" append="次"/>
          <span class="tw-w-[14px] tw-shrink-0">-</span>
          <InputNumberBox v-model:value="searchForm.maxSayCount" placeholder="最高" style="width: 47%" append="次" :min="searchForm.minSayCount || 0"/>
          </div>
      </div>
    </div>
    <div class="tw-flex tw-justify-end tw-pt-[12px] tw-items-center tw-border-t-[1px] tw-h-[32px]">
      <div>
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search()" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
        <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon></el-button>
        <el-button type="primary" v-else @click="isExpand=true" link>展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon></el-button>
      </div>
    </div>
  </div>
  <div class="tw-float-right tw-pb-[8px] tw-px-[16px] tw-text-[13px] tw-h-[44px] tw-bg-white">
    <el-dropdown class="tw-ml-[12px]" @command="handleImportOperator">
      <el-button>
        <span>导入线索</span>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in batchImportList" :key="item.value" :command="item.value" >{{ item.name }}</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-dropdown v-model="autoType" class="tw-ml-[12px]" @command="handleAutoOperator">
      <el-button>
        <span>自动操作</span>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="(item, index) in autoList" :key="item.value" :command="item.value" >
            <div class="tw-flex tw-items-center">
              <span
                class="tw-w-[5px] tw-h-[5px] tw-rounded-full"
                :class="autoStatus[index]=='1' ? 'tw-bg-[#13BF77]':'tw-bg-[#969799]'"
              ></span>
              <span class="tw-ml-[6px] tw-text-[13px]">{{ item.name }}</span>
            </div>
           
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-dropdown class="tw-ml-[12px]" @command="handleBatchOperator">
      <el-button type="primary">
        <span>批量操作</span>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in batchOperatorList" :key="item.value" :command="item.value" >{{ item.name }}</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
  <ClueTable @update:table="search" :loading="loading" :tableData="tableData||[]" v-model:selectClues="selectClues" :clueType="props.clueType" :groupType="props.groupType">
    <!-- <template v-slot:operate="{ row, index }">
      <el-button type="primary" link @click="goDetail(row, index)">详情</el-button>
    </template> -->
  </ClueTable>
  <!-- 导入线索弹窗 -->
  <AddClueByFileDialog
    v-model:visible="importVisible[0]"
    @confirm="updateClueData()"
  ></AddClueByFileDialog>
  <AddClueByPhoneDialog
    v-model:visible="importVisible[1]"
    @confirm="updateClueData()"
  ></AddClueByPhoneDialog>
  <AddClueByRecordDialog
    v-model:visible="importVisible[2]"
    @confirm="updateClueData()"
  ></AddClueByRecordDialog>
  <!-- 下发线索弹窗 -->
  <DistributeDialog
    v-model:visible="batchVisibleList[0]"
    :list="selectIds||[]"
    @confirm="handleBatchOperator()"
  ></DistributeDialog>
  <!-- 加入任务弹窗 -->
  <JoinTaskDialog
    v-model:visible="batchVisibleList[1]"
    :list="selectIds||[]"
    @confirm="handleBatchOperator()"
  ></JoinTaskDialog>
  <!-- 自动下发配置弹窗 -->
  <AutoActionDrawer
    v-model:visible="autoSendVisible"
    :type="autoType"
    @update:visible="getAutoInfo"
  ></AutoActionDrawer>
</template>

<script lang="ts" setup>
import { ClueItem, ClueSearchInfo, ClueStatusEnum, TaskStatusEnum, clueCallStatusOptions, ClueFromTypeEnum, SearchFormOrigin, } from '@/type/clue'
import { SpeechCraftInfoItem, } from '@/type/speech-craft'
import { IntentionClassEnum } from '@/type/common'
import { enum2Options } from '@/utils/utils'
import { trace } from '@/utils/trace'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
import to from 'await-to-js'
// api
import { clueManagerModel } from '@/api/clue'
import { aiOutboundTaskModel } from '@/api/ai-report'
// 组件
import InputNumberBox from '@/components/InputNumberBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import ClueTable from './ClueTable.vue'
import AddClueByPhoneDialog from './AddClueByPhoneDialog.vue'
import AddClueByRecordDialog  from './AddClueByRecordDialog.vue'
import AddClueByFileDialog from './AddClueByFileDialog.vue'
import DistributeDialog from './DistributeDialog.vue'
import JoinTaskDialog from './JoinTaskDialog.vue'
import AutoActionDrawer from './auto/Index.vue'
import Confirm from '@/components/message-box'
import InputPhonesBox from '@/components/InputPhonesBox.vue'

// 插件、依赖等
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import { ArrowUp, ArrowDown, } from '@element-plus/icons-vue'
import { reactive, computed, ref, watch, onDeactivated, onActivated } from 'vue'
import { onBeforeRouteLeave } from 'vue-router';

const props = defineProps<{
  clueType: ClueStatusEnum;
  needRefresh?: boolean;
  groupType: number
}>();
const emits = defineEmits(['update:clue'])

const globalStore = useGlobalStore()
const loading = ref(false)
const taskStore = useTaskStore()
// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap[props.groupType === 1 ? '坐席组线索' : '线索管理'].id]

const isExpand = ref(false)

const clueFromOption = enum2Options(ClueFromTypeEnum)
const taskStatusOption = enum2Options(TaskStatusEnum)
/** 搜索、列表等主体数据 */
const searchForm = reactive<ClueSearchInfo>(new SearchFormOrigin(userStore.groupId, props.clueType))
const provinceList = ref<string[]|null>([])
const provinceAllMap = ref<{ [key: string]: string[] }| null>({})
const cityList = computed(() => {
  if (!provinceAllMap.value) return []
  const provinces = Object.keys(provinceAllMap.value)
  const province = provinces.find(item => searchForm.provinceCode && item.includes(searchForm.provinceCode))
  return searchForm.provinceCode && province ? (provinceAllMap.value[province] || []) : (Object.values(provinceAllMap.value).flat() || [])
})
const tableData = ref<ClueItem[]|null>([])
const search = async () => {
  loading.value = true
  const params = JSON.parse(JSON.stringify(searchForm))
  params.callStatusCodes = params.callStatusCodes?.length > 0 ? params.callStatusCodes.join(',')?.split(',') : undefined
  const res = await clueManagerModel.getToBeSendClueList(params) || []
  tableData.value = res.sort((a,b) => dayjs(a.importTime).isAfter(b.importTime) ? -1 : 1)
  selectClues.value = []
  selectIds.value = []
  loading.value = false
}
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchFormOrigin(userStore.groupId, props.clueType))
}
/** 线索操作 【开始】 */

/** 
 * 本地文件导入线索 0
 * 单个任务名单/通话记录导入线索 1
 * 多个任务，通话记录导入 2
*/
const batchImportList = [
  {name: '文件导入', value: 0},
  {name: '任务导入', value: 1},
  {name: '通话导入', value: 2},
]
const importVisible = ref<boolean[]>(Array.from({length: batchImportList.length}, () => false)) // 本地文件导入线索
const handleImportOperator = (type: 0 | 1 | 2) => {
  batchVisibleList.value = Array.from({length: batchOperatorList.length}, () => false)
  importVisible.value[type] = true
}
/** 批量操作 */
const batchOperatorList = [
  {name: '下发线索', value: 0},
  {name: '加入任务', value: 1},
  {name: '归档线索', value: 2},
]
const selectClues = ref<ClueItem[]|null>([]) // 选中数据
const selectIds = ref<number[]|null>([])
const batchVisibleList = ref<boolean[]>(Array.from({length: batchOperatorList.length}, () => false))
// 归档操作
const handleArchive = async () => {
  if (selectIds.value && selectIds.value?.length > 0) {
    await clueManagerModel.batchArchiveClues(selectIds.value)
    trace({
      page: '线索管理-待下发-归档线索',
      params: selectIds.value
    })
    ElMessage({
      type: 'success',
      message: '归档成功！'
    })
    updateClueData()
  } else {
    ElMessage({
      type: 'warning',
      message: '请至少勾选1条线索数据！'
    })
  }
}
const handleBatchOperator = (type?: number) => {
  batchVisibleList.value = Array.from({length: batchOperatorList.length}, () => false)
  if (typeof type === 'number') {
    if (selectClues.value && selectClues.value?.length > 0) {
      selectIds.value = []
      selectClues.value?.map(item => {
        if (!!item.canBeSend) {
          item.id && selectIds.value!.push(item.id)
        }
      })
      if (selectIds.value.length === 0) {
        return ElMessage({
          type: 'warning',
          message: '已选择数据下发状态均为否，无法操作！'
        })
      }
      const unableSendNum = selectClues.value.length - selectIds.value.length
      if (type === 2) {
        Confirm({
          text: unableSendNum ?
          `已选择【${selectClues.value.length}条】数据，其中【${unableSendNum}】数据下发状态为“否”，不支持操作。请选择处理方式`
          :`已选择【${selectClues.value.length}条】数据`,
          type: 'warning',
          title: '归档线索',
          confirmText: unableSendNum ?  '去除后归档' : '归档',
        }).then(() => {
          handleArchive()
        }).catch(() => {})
      } else {
        if (unableSendNum) {
          Confirm({
            text: `已选择【${selectClues.value.length}条】数据，其中【${unableSendNum}】数据下发状态为“否”，不支持操作。请选择处理方式`,
            type: 'warning',
            title: '筛选线索',
            confirmText: unableSendNum ?  '去除后分配' : '分配',
          }).then(() => {
            batchVisibleList.value[type] = true
          }).catch(() => {})
        } else {
          batchVisibleList.value[type] = true
        }
      }
    } else {
      ElMessage({
        type: 'warning',
        message: '请至少勾选1条线索数据！'
      })
    }
  } else {
    updateClueData()
  }
}


/** 自动操作 */
const autoList = [
  {name: '自动导入', value: 0},
  {name: '自动下发', value: 1},
]
const autoSendVisible = ref(false)
const autoType = ref(0)
// 获取自动分配规则状态
const autoStatus = ref<string[]>(['0', '0'])
const getAutoInfo = async (visible: boolean = false) => {
  if (visible) return
  const res1 = await to(clueManagerModel.getAutoImportInfo())
  const res2 = await to(clueManagerModel.getAutoSendInfo())
  if (res1[1]) {
    autoStatus.value[0] = res1[1].status || '0'
  } else {
    autoStatus.value[0] = '0'
  }
  if (res2[1]) {
    autoStatus.value[1] = res2[1].status || '0'
  } else {
    autoStatus.value[1] = '0'
  }
}
const handleAutoOperator =(type: 0 | 1) => {
  autoSendVisible.value = true
  autoType.value = type
}
/** 更新线索数据和列表数据 */
const updateClueData = () => {
  search()
  emits('update:clue')
}

const speechCraftList = ref<SpeechCraftInfoItem[]|null>([])
const taskList = ref<{
    id: number;
    taskName: string;
}[] | null>([])
const taskNum = ref(0)
const init = async () => {
  await globalStore.getProvinceInfo()
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
  speechCraftList.value = await taskStore.getAllScriptListOptions() as SpeechCraftInfoItem[]
  updateTaskList()
  search()
  getAutoInfo()
}
const loadingTask = ref(false)
const updateTaskList = async (val: string = '', startPage: number = 0, pageSize: number = 30) => {
  loadingTask.value = true
  const data = await aiOutboundTaskModel.findTaskList({
    startTime: dayjs().add(-1, 'month').format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    taskName: val ? val : undefined,
    startPage: startPage,
    pageNum: pageSize,
  })
  taskNum.value = data?.total || 0
  taskList.value = data?.data as {id: number, taskName: string}[] || []
  loadingTask.value = false
}
// 表格区
onActivated(() => {
  init()
})

const clearData = () => {
  selectClues.value = null
  selectIds.value = null
  tableData.value = null
  provinceAllMap.value = null
  provinceList.value = null
  speechCraftList.value = null
  taskList.value = null
}
onDeactivated(() => {
  clearData()
})
onBeforeRouteLeave(() => {
  clearData()
})

watch(() => props.needRefresh, n => {
  n && search()
})
watch(() => props.clueType, () => {
  clearSearchForm()
  search()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.module-container-inner {
  .phone-msg {
    display: flex;
    align-items: center;
    span {
      width: 140px;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon {
      display: none;
    }
    &:hover .el-icon {
      display: inline-block;
    }
  }
  .audio-mode {
    position: fixed;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);
    z-index: 99;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
}
</style>
