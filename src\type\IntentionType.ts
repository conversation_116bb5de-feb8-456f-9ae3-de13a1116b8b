import { ScriptCorpusItem } from "./speech-craft"

export interface BaseEntity {
  id: number
  createTime: string
  updateTime: string
}

export interface IntentionType {
  id?: number
  intentionType: string
  scriptId?: number
  sequence?: number
  intentionName: string
  remark?: string
}

export interface LabelItem {
  createTime?: string
  updateTime?: string
  sequence?: number
  scriptId: number
  id?: number
  labelName: string
  scriptCorpusList?: Pick<ScriptCorpusItem, 'id' | 'name' | 'corpusType'>[]
  advancedRuleList?: Pick<AdvancedRulesItem, 'id' | 'ruleName'>[]
}

export enum RuleTypeEnum {
  '命中问答个数' = 'HIT_ANSWER_NUM',
  '命中问答类型' = 'HIT_ANSWER_TYPE',
  '命中问答分组' = 'HIT_ANSWER_GROUP',
  '命中标签个数' = 'HIT_TAG_NUM',
  '命中标签次数' = 'HIT_TAG_TIMES',
  '命中语义个数' = 'HIT_SEMANTIC_NUM',
  '命中语义次数' = 'HIT_SEMANTIC_TIMES',
  '命中语义标签' = 'HIT_SEMANTIC_LABEL',
  '命中主动流程' = 'HIT_MASTER_PROCESS',
  '命中深层沟通' = 'HIT_DEEP_COMMUNICATION',
  '命中流程语料' = 'HIT_PROCESS_CORPUS',
  '命中分支' = 'HIT_BRANCH',
  // '命中查询事件' = 'HIT_QUERY_EVENT',
  // '命中触发事件' = 'HIT_TRIGGER_EVENT',
  '命中客户回复' = 'HIT_MATCH_WORDS',
  // '发声打断次数' = 'HIT_SOUND_INTERRUPT_TIMES',
  '语义打断次数' = 'HIT_SEMANTICS_INTERRUPT_TIMES',
  // '打断次数' = 'HIT_INTERRUPT_TIMES',
  '交互次数' = 'HIT_INTERACT_NUM',
  '说话次数' = 'HIT_SPEAK_NUM',
  '说话次数倍于交互次数' = 'HIT_SPEAK_INTERACT',
  '通话时长' = 'HIT_CALL_DURATION',
  '挂机方' = 'HIT_HANG_UP',
  '触发转人工' = 'HIT_TRANS_TO_HUMAN',
  '被人工监听' = 'HIT_LISTEN_IN',
  '被人工接听' = 'HIT_TAKE_OVER',
  '触发发短信' = 'HIT_TRIGGER_SMS',
}

export enum AnswerTypeEnum {
  '业务问答' = 'INDUSTRY_QUERY',
  '通用问答' = 'COMMON_QUERY',
  '最高优先' = 'FUNC_PRIOR_QA',
}

export enum HangUpEnum {
  'AI' = 'AI',
  '客户' = '用户',
  '坐席' = '坐席'
}

export enum OperatorEnum {
  '≤' = '≤',
  '≥' = '≥',
  '=' = '=',
}
export enum ITypeEnum {
  'input',
  'mult-input',
  'mult-select-string',
  'mult-select-list',
  'single-select',
  'cascader',
}

export type IRuleSetting = {
  name: string,
  options?: {
    type: ITypeEnum,
    enum?: Record<string, unknown>, // 单选选项【type===select-select】
    optionName?: string,  // 使用 store 中的选项【type===select-select | mult-select】
    emitPath?: boolean, // 级联是否需要中间参数【type===cascader】
    depth?: number, // 级联深度
    id?: string, // 选项中的value字段【type===select】
    name?: string, // 选项中的label字段【type===select】
    suffix?: string,  // 选项中的选项后缀【type===select】
    copy?: string, // 是否支持复制，使用 globalStore 中的copyInfo复制【type===mult-select】
    limit?: number, // 字数限制，【type===input】
  },  // 多选
  numText?: string,  // minNum maxNum num 的后缀
  property: (keyof RuleItem)[], // 对应的属性
}

export const ruleTypePropteryMap =  new Map<RuleTypeEnum, IRuleSetting>([
  [RuleTypeEnum['命中问答个数'], { 
    name: '命中问答',
    options: {
      type: ITypeEnum['mult-select-string'],
      optionName: 'qaAndPriorOptions',
      copy: 'copyAnswerIds',
      suffix: 'suffix',
    },
    numText: '个',
    property: ['hitAnswerIds', 'num'],
  }],
  [RuleTypeEnum['命中问答类型'], {
    name: '命中问答类型',
    options: {
      type: ITypeEnum['single-select'],
      enum: AnswerTypeEnum,
      id: 'value'
    },
    numText: '个',
    property: ['hitAnswerType', 'minNum', 'maxNum']
  }],
  [RuleTypeEnum['命中问答分组'], {
    name: '命中问答分组',
    options: {
      type: ITypeEnum['single-select'],
      optionName: 'knowledgeGroupOptions',
      copy: 'knowledgeGroupIds',
    },
    numText: '次',
    property: ['hitKnowledgeIds', 'minNum', 'maxNum']
  }],
  [RuleTypeEnum['命中标签个数'], { 
    name: '命中标签',
    options: {
      type: ITypeEnum['mult-select-string'],
      optionName: 'intentionTagOptions',
      copy: 'aiLabelIds',
      name: 'labelName',
    },
    numText: '个',
    property: ['hitTagIds', 'num']
  }],
  [RuleTypeEnum['命中标签次数'], {
    name: '命中标签',
    options: {
      type: ITypeEnum['mult-select-string'],
      optionName: 'intentionTagOptions',
      copy: 'aiLabelIds',
      name: 'labelName',
    },
    numText: '次',
    property: ['hitTagIds', 'minNum', 'maxNum']
  }],
  [RuleTypeEnum['命中语义个数'], {
    name: '命中语义',
    options: {
      type: ITypeEnum['mult-select-string'],
      optionName: 'semanticOptions',
      copy: 'semanticIds',
      suffix: 'suffix'
    },
    property: ['hitSemanticIds', 'num'],
  }],
  [RuleTypeEnum['命中语义次数'], {
    name: '命中语义',
    options: {
      type: ITypeEnum['mult-select-string'],
      optionName: 'semanticOptions',
      copy: 'semanticIds',
      suffix: 'suffix'
    },
    numText: '次',
    property: ['hitSemanticIds', 'minNum', 'maxNum']
  }],
  [RuleTypeEnum['命中语义标签'], {
    name: '命中语义标签',
    numText: '次',
    options: {
      type: ITypeEnum['mult-select-string'],
      optionName: 'semanticLabelOptions',
      copy: 'semanticLabelIds',
    },
    property: ['hitSemanticLabelIds', 'minNum', 'maxNum']
  }],
  [RuleTypeEnum['命中主动流程'], {
    name: '命中主动流程',
    options: {
      type: ITypeEnum['single-select'],
      optionName: 'masterProcessOptions',
      copy: 'masterProcessIds',
      id: 'headCorpusId',
    },
    property: ['hitMasterProcessIds'],
  }],
  [RuleTypeEnum['命中深层沟通'], {
    name: '命中深层沟通',
    options: {
      type: ITypeEnum['cascader'],
      optionName: 'allDeepCorpusOption',
      emitPath: true,
      depth: 2
    },
    property: ['hitKnowledgeIds', 'hitDeepCommunicationIds']
  }],
  [RuleTypeEnum['命中流程语料'], {
    name: '命中流程语料',
    options: {
      type: ITypeEnum['cascader'],
      optionName: 'processCorpusOptions',
      emitPath: true,
      depth: 2
    },
    property: ['hitMasterProcessIds', 'hitCorpusIds']
  }],
  [RuleTypeEnum['命中分支'], {
    name: '命中分支',
    options: {
      type: ITypeEnum['cascader'],
      depth: 3,
      optionName: 'processCorpusBranchOptions',
      emitPath: true,
    },
    property: ['hitMasterProcessIds', 'hitCorpusIds', 'hitBranchIds']
  }],
  // [RuleTypeEnum['命中查询事件'], {
  //   name: '命中查询事件',
  //   numText: '次',
  //   property: ['hitQueryEvents', 'minNum', 'maxNum']
  // }],
  // [RuleTypeEnum['命中触发事件'], {
  //   name: '命中触发事件',
  //   options: {
  //     type: ITypeEnum['mult-select-string'],
      
  //     optionName: 'getEventValuesOptions',
  //   },
  //   numText: '次',
  //   property: ['hitTriggerEvents', 'minNum', 'maxNum']
  // }],
  [RuleTypeEnum['命中客户回复'], {
    name: '命中客户回复',
    options: {
      type: ITypeEnum['input'],
      limit: 800,
    },
    property: ['matchWords']
  }],
  // [RuleTypeEnum['发声打断次数'], {
  //   name: '发声打断次数',
  //   numText: '次',
  //   property: ['minNum', 'maxNum']
  // }],
  [RuleTypeEnum['语义打断次数'], {
    name: '语义打断次数',
    numText: '次',
    property: ['minNum', 'maxNum']
  }],
  // [RuleTypeEnum['打断次数'], {
  //   name: '打断次数',
  //   numText: '次',
  //   property: ['minNum', 'maxNum']
  // }],
  [RuleTypeEnum['交互次数'], {
    name: '交互次数',
    numText: '次',
    property: ['minNum', 'maxNum']
  }],
  [RuleTypeEnum['说话次数'], {
    name: '说话次数',
    numText: '次',
    property: ['minNum', 'maxNum']
  }],
  [RuleTypeEnum['说话次数倍于交互次数'], {
    name: '说话次数倍于交互次数',
    options: {
      type: ITypeEnum['single-select'],
      enum: OperatorEnum,
      id: 'value'
    },
    numText: '倍',
    property: ['speakInteractOperator', 'num']
  }],
  [RuleTypeEnum['通话时长'], {
    name: '通话时长',
    numText: '秒',
    property: ['minNum', 'maxNum']
  }],
  [RuleTypeEnum['挂机方'], {
    name: '挂机方',
    options: {
      type: ITypeEnum['single-select'],
      enum: HangUpEnum,
      id: 'value',
    },
    property: ['hangUp'],
  }],
  [RuleTypeEnum['触发转人工'], { name: '触发转人工', property: ['yn'] }],
  [RuleTypeEnum['被人工监听'], { name: '被人工监听', property: ['yn'] }],
  [RuleTypeEnum['被人工接听'], { name: '被人工接听', property: ['yn'] }],
  [RuleTypeEnum['触发发短信'], { name: '触发发短信', property: ['yn'] }],
])

export interface RuleItem {
  id?: number
  scriptId?: number
  conditionUniqueId?: number
  ifDelete?: boolean // 是否删除，用于编辑高级规则时，提醒后端该规则（已有id）需要删除
  conditionType?: RuleTypeEnum,
  // conditionScene?: SceneEnum,  //场景 高级规则 / 最终意向等级

  num?: number;
  minNum?: number;
  maxNum?: number;
  yn?: boolean;

  hitAnswerIds?: string;
  hitAnswerType?: string;
  hitTagIds?: string;
  hitSemanticIds?: string;
  hitSemanticLabelIds?: string;
  hitMasterProcessIds?: string; // 单选
  hitKnowledgeIds?: string;
  hitDeepCommunicationIds?: string; // 单选
  hitCorpusIds?: string; // 单选
  hitBranchIds?: string; // 单选
  hitQueryEvents?: string;
  hitTriggerEvents?: string; // 单选
  matchWords?: string;
  speakInteractOperator?: string;
  hangUp?: HangUpEnum;
}

export interface MutiRuleItem {
  conditionUniqueId?: number,
  ifDelete?: boolean,  // 是否删除，用于编辑高级规则时，提醒后端该规则组（已有id）需要删除
  advancedRuleConditionDTOList: RuleItem[]
}
export  interface AdvancedRulesItem {
  id?: number
  scriptId: number,
  order?: number,
  ruleName?: string,
  comment?: string,
  matchConditionList: MutiRuleItem[],
  excludeConditionList: MutiRuleItem[],
  intentionLevelName?: string
  intentionLevelId?: number | string
  intentionTagName?: string
  intentionTagId?: string
}
export class AdvancedRulesOrigin implements AdvancedRulesItem {
  id = undefined;
  scriptId: number;
  ordere = undefined;
  ruleName = undefined;
  matchConditionList = [];
  excludeConditionList = []
  intentionLevelName?: string;
  intentionLevelId = undefined;
  comment = undefined;
  intentionTagName = undefined;
  intentionTagId = undefined
  constructor(scriptId: number) {
    this.scriptId = scriptId;
  }
}

export class RuleItemOrigin implements RuleItem {
  id = undefined;
  conditionUniqueId = undefined;
  conditionType;
  // conditionScene;
  ifDelete = false

  num = undefined;
  minNum = undefined;
  maxNum = undefined;
  yn = undefined;

  hitAnswerIds = undefined;
  hitAnswerType = undefined;
  hitTagIds = undefined;
  hitTimeTagIds = undefined;
  hitSemanticIds = undefined;
  hitTimesSemanticsIds = undefined;
  hitSemanticsTagIds = undefined;
  hitMasterProcessIds = undefined;
  hitKnowledge = undefined;
  hitDeepCommunication = undefined;
  hitCorpusIds = undefined;
  hitBranchIds = undefined;
  hitQueryEvents = undefined;
  hitTriggerEventNames = undefined;
  hitKnowledgeIds = undefined;
  matchWords = undefined;
  speakInteractOperator = undefined;
  hangUp = undefined;
  constructor(type?: RuleTypeEnum) {
    this.conditionType = type || undefined
    // this.conditionScene = scene
  }
}

// 最终意向
export  interface FinalIntentionRuleItem {
  id?: number
  scriptId: number,
  excludeConditionList: MutiRuleItem[],
  intentionLevelName?: string
  intentionLevelId?: number | string
}

export class FinalIntentionRuleOrigin implements FinalIntentionRuleItem {
  id = undefined;
  scriptId: number;
  excludeConditionList = []
  intentionLevelName?: string;
  intentionLevelId = undefined;
  constructor(scriptId: number) {
    this.scriptId = scriptId;
  }
}
