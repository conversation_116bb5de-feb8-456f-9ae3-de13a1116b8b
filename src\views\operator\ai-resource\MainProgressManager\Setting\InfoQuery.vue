<template>
  <div class="info-set-container">
    <div class="tw-flex tw-flex-col tw-grow-0 tw-mt-[16px] tw-my-[8px] tw-mx-[16px]">
      <div class="tw-flex tw-items-center">
        <el-input
          v-model="searchForm.name"
          style="width:250px"
          placeholder="请输入信息查询名称（20字以内）"
          maxlength="20"
          clearable
          @keyup.enter="search"
        >
        </el-input>
        <el-button type="primary" link class="tw-ml-1" @click="search">
          <el-icon :size="16" class="tw-mr-0.5">
            <SvgIcon name="filter" color="none"></SvgIcon>
          </el-icon>
          筛选
        </el-button>
      </div>
      <div class="tw-flex tw-justify-between tw-items-end">
        <span class="info-title">用于查询分支，需业务侧和研发侧均支持后才能使用</span>
        <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="edit()">新增信息</el-button>
      </div>
    </div>
    <el-table
      :data="tableData || []"
      style="width: 100%"
      :header-cell-style="{background:'#F7F8FA', color: '#646566', padding: 'auto 8px'}"
      class="tw-grow"
      row-key="id"
      stripe
    >
      <el-table-column type="expand" width="36">
        <template #default="props">
          <el-table
            :data="props.row.infoQueryValues?.sort((a: InfoQueryValueItem, b: InfoQueryValueItem) => a.weight! - b.weight!)"
            :header-cell-style="{background:'#F7F8FA', color: '#646566', padding: 'auto 8px', borderBottom: 'none'}"
          >
            <el-table-column label="" align="left" min-width="60" class="tw-bg-[#F7F8FA]"></el-table-column> 
            <el-table-column label="优先级" prop="weight" align="left" min-width="60"></el-table-column> 
            <el-table-column label="信息字段值" prop="value" align="left" min-width="120"/>
            <el-table-column label="释义" prop="definition" align="left" min-width="120"/>
            <el-table-column label="备注" prop="note" align="left" min-width="100"/>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column property="infoFieldName" label="信息字段名" align="left" min-width="160"></el-table-column>
      <el-table-column property="fieldDefinition" align="left" label="显示名" min-width="120"></el-table-column>
      <el-table-column align="right" label="操作" fixed="right" min-width="120">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">编辑</el-button>
          <el-button :disabled="!!isChecked" :type="!isChecked ? 'primary': 'default'" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableDataOrigin || tableDataOrigin.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  <InfoQueryDialog v-if="infoDialogVisible" :visible="infoDialogVisible" :infoQueryData="infoQueryData" @update="search" @close="closeInfoQueryDialog"/>
</template>

<script lang="ts" setup>
import { InfoQueryItem, InfoQueryValueItem } from '@/type/speech-craft'
import { scriptInfoModel } from '@/api/speech-craft'
import { Search, Plus, CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { reactive, computed, ref, onActivated, onDeactivated } from 'vue'
import { trace } from '@/utils/trace'
import { ElMessage } from 'element-plus'
import PaginationBox from '@/components/PaginationBox.vue'
import { pickAttrFromObj } from '@/utils/utils'
import InfoQueryDialog from './InfoQueryDialog.vue'
import { useScriptStore } from '@/store/script'
import Confirm from '@/components/message-box'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const scriptLongId = scriptStore.id
const isChecked = scriptStore.isChecked
const searchForm = reactive<{
  name: string
}>({
  name: '',
})
// 表格区
const pageSizeList = [20, 50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}
const tableData = computed(() => {
  if (!tableDataOrigin.value) return []
  const name = searchForm.name.trim()
  const data: InfoQueryItem[] = name ? tableDataOrigin.value.filter(item => item.infoFieldName.includes(name)) : tableDataOrigin.value
  total.value = data?.length || 0
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
const tableDataOrigin = ref<InfoQueryItem[] | null>([])
// 搜索区
const search = async () => {
  loading.value = true
  try {
    tableDataOrigin.value = await scriptStore.getInfoQueryOptions(true)
  } catch(err) {
    ElMessage({
      message: '获取信息查询数据失败',
      type: 'error',
    })
  }
  loading.value = false
}
// 操作区
const infoDialogVisible = ref(false)
class infoQueryDataOrigin {
  id = undefined
  scriptLongId = scriptLongId
  infoFieldName = ''
  fieldDefinition = ''
  infoQueryValues = []
}
const infoQueryData = reactive<InfoQueryItem>(new infoQueryDataOrigin())
const edit = (row?: InfoQueryItem) => {
  if (row && row.id) {
    Object.assign(infoQueryData, pickAttrFromObj(row, ['id', 'scriptLongId', 'infoFieldName', 'fieldDefinition', 'infoQueryValues']))
  } else {
    Object.assign(infoQueryData, new infoQueryDataOrigin())
  }
  infoDialogVisible.value = true
}
const closeInfoQueryDialog = () => {
  infoDialogVisible.value = false
}
const delAction = async (id: number) => {
  loading.value = true
  trace({
    page: `话术编辑-信息查询设置-删除信息查询(${scriptLongId})`,
    params: {id: id},
  })
  await scriptInfoModel.deleteInfoQueryKey({id}) as boolean
  ElMessage({
    message: '删除成功',
    type: 'success',
  })
  search()
  loading.value = false
}
const del = (row: InfoQueryItem) => {
  Confirm({ 
    text: `您确定要删除信息查询【${row.infoFieldName}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}
onActivated(() => {
  search()
})
onDeactivated(() => {
  tableDataOrigin.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.info-set-container {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  overflow-y: auto;
  height: calc(100vh - 230px);
  display: flex;
  flex-direction: column;
  .el-table {
    font-size: var(--el-font-size-base);
    color: var(--primary-black-color-500);
    :deep(td.el-table__expanded-cell) {
      margin: 0;
      padding: 0;
      .el-table {
        border-left: 36px solid #f7f8fa;
      }
    } 
  }
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
</style>