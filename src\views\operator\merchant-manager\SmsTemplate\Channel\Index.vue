<template>
  <HeaderBox :title="titleList" />
  <!--模块主体-->
  
  <div class="module-container">
    <TabsBox v-model:active="activeTab" :tabList="tabList"></TabsBox>
    <SmsChannel v-if="activeTab === tabList[0]"/>
    <SmsChannelWarn v-else />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed } from 'vue'
import TabsBox from '@/components/TabsBox.vue'
import SmsChannel from './SmsChannel.vue'
import SmsChannelWarn from './SmsChannelWarn.vue'

const titleList = computed(() => [
  {title: '商户管理', },
  {title: '短信通道配置'},
])
const tabList: string[] = ['通道列表', '通道预警']

const activeTab = ref(tabList[0] || '')
</script>

<style scoped lang="postcss">
</style>
