<template>
  <div>
    <!-- 商户线路选择模块 -->
    <div class="tw-my-[6px] tw-flex" >
      <el-select v-model="lineNumber" class="tw-w-[300px]" placeholder="选择商户线路" filterable @change="handleLineChange">
        <el-option
          v-for="lineItem in merchantLinesList"
          :key="lineItem.lineNumber"
          :value="lineItem.lineNumber"
          :label="lineItem.lineName"
        />
      </el-select>
    </div>

    <!-- 当前商户线路，分时段接通率 -->
    <div v-loading="!!loading1" class="tw-my-[12px] tw-bg-white tw-py-[16px] tw-w-full tw-relative">
      <div class="form-dialog-header tw-flex tw-px-[16px] tw-items-center">
        <div>接通率统计</div>
        <el-tooltip content="刷新" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="updatePutThroughData">
            <SvgIcon name="reset" color="inherit"/>
          </el-icon>
        </el-tooltip>
      </div>
      <MixLineBarChart :data="lineChartYList || []" :legends="putThroughLegends" :tooltip-sort="putThroughSort" xName="时间">
        <div class="tw-absolute tw-right-1 tw-top-0 tw-z-10">
          <el-select v-model="interval" style="width: 90px;height:24px" @change="updatePutThroughData">
            <el-option v-for="item in intervalList" :key="item.val" :label="item.name" :value="item.val" />
          </el-select>
        </div>
      </MixLineBarChart>
    </div>

    <!-- 当前商户线路，并发趋势 -->
    <LineConcurrentBox :lineInfo="lineNumber" v-model:refresh="needTotalRefresh"/>
  </div>
</template>

<script lang="ts" setup>
import { watch, defineAsyncComponent, ref, onMounted, onUnmounted, } from 'vue'
import MixLineBarChart from '@/components/charts/MixLineBarChart.vue'
import to from 'await-to-js'
import { time2Minute, } from '@/utils/utils'
import { useGlobalStore } from '@/store/globalInfo'
import { onBeforeRouteLeave } from 'vue-router'
import { MerchantLineInfo } from '@/type/merchant'
import { merchantModel } from '@/api/merchant'
import { lineChartModel, } from '@/api/line'
import { MonitorChartSearchInfo, MonitorChartInfo, } from '@/type/line'
import dayjs from 'dayjs'
import { OperatorEnum, } from '@/type/common'

const LineConcurrentBox = defineAsyncComponent({loader: () => import('@/views/operator/line-manager/components/LineConcurrentBox.vue')})

const props = defineProps<{
  needUpdate: boolean
}>();
const emits = defineEmits(['update:needUpdate',])

const globalStore = useGlobalStore()

type MonitorChartItem = {
  name: string;
  xName?: string
  value1: number;
  value2: number;
  value3?: number;
}

const handleLineChange = () => {
  globalStore.lineNumber = lineNumber.value || globalStore.lineNumber || ''
  needTotalRefresh.value = true
  updatePutThroughData()
}

/** 并发统计 */
const needTotalRefresh = ref(false) // 是否要刷新并发统计数据

/** 接通率统计 */
const putThroughLegends = ['外呼量', '呼叫接通率', '接通数']
const putThroughSort = [0, 2, 1]
const intervalList = [
  { name: '5min', val: 5 },
  { name: '10min', val: 10 },
  { name: '15min', val: 15 },
  { name: '30min', val: 30 },
  { name: '60min', val: 60 },
]
const interval = ref(intervalList[0].val)
const loading1 = ref(false)
const lineChartYList = ref<MonitorChartItem[] | null>([])
const updatePutThroughData = async () => {
  loading1.value = true
  const params: MonitorChartSearchInfo = {
    tenantLineNumber: lineNumber.value,
    size: interval.value,
    type: 'CONNECT',
    date: dayjs().format('YYYY-MM-DD'),
    operators: Object.values(OperatorEnum),
  }

  const [_, data] = await to(lineChartModel.getTodayMonitorLineInfo(params)) as [any, MonitorChartInfo]

  if (data && Object.keys(data) && Object.keys(data).length > 0) {
    lineChartYList.value = []
    Object.keys(data).map(item => {
      lineChartYList.value?.push({
        name: item.trim(),
        xName: item.split('-')[0],
        value1: data[item].total || 0,
        value2: +(data[item].num / data[item].total).toFixed(4),
        value3: data[item].num || 0,
      })
    })
    lineChartYList.value.sort((a, b) => {
      const bb = time2Minute(b.name.split('-')[0])
      const aa = time2Minute(a.name.split('-')[0])
      return aa - bb
    })
  } else {
    lineChartYList.value = []
  }
  loading1.value = false
}



const merchantLinesList = ref<MerchantLineInfo[] | null>([]) // 全部开启的商户线路
const lineNumber = ref(globalStore.lineNumber || '') // 选择的商户线路

const init = async () => {
  merchantLinesList.value = <MerchantLineInfo[]>await merchantModel.getAllMerchantLines()
  lineNumber.value = lineNumber.value || globalStore.lineNumber || merchantLinesList.value[0]?.lineNumber || ''
  lineNumber.value && handleLineChange()
  emits('update:needUpdate', false)
}
watch(() => props.needUpdate, n => {
  n && init()
})
init()

const clearAllData = () => {
  lineChartYList.value = null
  merchantLinesList.value = null
}
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style scoped lang="postcss" type="text/postcss">

</style>