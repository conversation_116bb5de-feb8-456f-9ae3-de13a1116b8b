import { createApp } from "vue";
import App from "./App.vue";
import "element-plus/dist/index.css";
import store from "@/store";
import router from "@/router";
import "./index.pcss";
import "jquery";
import ElementPlus from "element-plus";
import { useRouteStore } from "@/store/routes";
import { addRoute } from "@/router/permission";
import SvgIcon from "@/components/SvgIcon.vue";
import "virtual:svg-icons-register";
import zhCn from "element-plus/es/locale/lang/zh-cn";

const app = createApp(App);
// 挂载pinia,router
app.use(ElementPlus, {locale: zhCn}).use(store).component("svg-icon", SvgIcon)
const routeStore = useRouteStore();
if (!routeStore.initStatus && routeStore.asyncRouts && routeStore.asyncRouts.length > 0) {
  addRoute(routeStore.asyncRouts, []);
}
app.use(router);

// 预加载指令
app.directive('preload', (el, binding) => {
  const img = new Image();
  img.src = binding.value
  img.onload = () => {
    el.src = binding.value
  };
  img.onerror = (event) => {
    console.log('Failed to preload the image', event);
  };
});
app.directive('h5focus', (el: HTMLElement) => {
    if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
      el.addEventListener('touchend', event => {
        if (event.target instanceof HTMLInputElement) {
          event.target.focus()
        }
      })
    }
  }
)

// 挂载实例
// app.mount("#app");
router.isReady().then(() => {
  app.mount("#app");
});
