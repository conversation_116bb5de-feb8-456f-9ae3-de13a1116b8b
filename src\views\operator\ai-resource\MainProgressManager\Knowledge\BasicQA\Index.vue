<template>
  <div class="basic-QA-container">
    <div class="tw-mb-[6px] tw-flex tw-items-center tw-grow-0">
      <el-input
        v-model.trim="searchForm.name"
        style="width:250px"
        placeholder="请输入语料名称（20字以内）"
        maxlength="20"
        clearable
        @keyup.enter="search"
        @clear="search"
      >
      </el-input>
      <el-button type="primary" class="tw-ml-1" @click="search()" link>
        <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
        <span>查询</span>
      </el-button>
    </div>
    <div class="tw-mb-[8px] tw-flex tw-justify-between tw-items-center tw-text-[13px]">
      <div class="tw-flex tw-items-center">
        <span class="tw-ml-[6px] tw-text-[var(--primary-black-color-300)]">已选：</span>
        <span class="tw-text-[var(--primary-black-color-300)]">{{ selectLen || 0 }}</span>
        <span class="tw-text-[var(--primary-black-color-300)]">/</span>
        <span class="tw-text-[var(--primary-black-color-300)]">{{ tableData?.length || 0 }}</span>
      </div>
      <div>
        <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="edit()">
          新建问答
        </el-button>
        <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="loadAction()">
          导入问答
        </el-button>
        <el-dropdown v-if="!isChecked && tableData && tableData?.length>0" class="tw-ml-[12px]" @command="handleBatchOperate">
          <el-button type="primary">
            <el-icon :size="16"><SvgIcon name="edit"></SvgIcon></el-icon>
            <span>批量操作</span>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="edit">
                <span>批量设置</span>
              </el-dropdown-item>
              <el-dropdown-item command="move">
                <span>批量移动</span>
              </el-dropdown-item>
              <el-dropdown-item command="copy">
                <span>批量复制</span>
              </el-dropdown-item>
              <el-dropdown-item command="del">
                <span>批量删除</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      ref="tableRef"
      row-key="id"
      v-loading="loading"
      stripe
      border
    >
      <el-table-column v-if="!isChecked" width="36" fixed="left" align="left" type="selection"></el-table-column>
      <el-table-column property="weight" fixed="left" align="left" label="权重" width="48"></el-table-column>
      <el-table-column property="name" fixed="left" label="语料名称" align="left" width="200">
        <template #default="{ row }">
          <div class="tw-w-full tw-flex tw-items-center">
            <span class="tw-line-clamp-2">{{ row.name ?? '-' }}</span>
            <el-icon v-if="row.smsTriggerName" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-sms"></SvgIcon></el-icon>
            <el-icon v-if="row.listenInOrTakeOver" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-human"></SvgIcon></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="queryType" align="center" label="问答类型" width="120">
        <template #default="{ row }">
          {{ findValueInEnum(row.queryType, QueryTypeEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="content" align="left" label="文字内容" min-width="300">
        <template #default="{ row }">
          <li v-if="row.content && row.content.length > 0" v-for="item in row.content || []">
            <span>{{ item || '-' }}</span>
          </li>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="matchText" align="left" label="满足条件" min-width="240">
        <template #default="{ row, $index }">
          <div v-if="row.matchText && row.matchText.length" v-for="(item, index) in row.matchText||[]" :key="`${$index}-${index}`">
            <el-divider v-if="index" border-style="dashed">or</el-divider>
            <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
              {{ item2 || '-' }}
            </li>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column property="excludeText" align="left" label="排除条件" min-width="240">
        <template #default="{ row, $index }">
          <div v-if="row.excludeText && row.excludeText.length" v-for="(item, index) in row.excludeText||[]" :key="`${$index}-${index}`">
            <el-divider v-if="index" border-style="dashed">or</el-divider>
            <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
              {{ item2 || '-' }}
            </li>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column property="connectType" align="center" label="AI回答后" width="120">
        <template #default="{ row }">
          {{ findValueInEnum(row.connectType, ConnectTypeEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="eventTriggerValueIds" align="left" label="触发事件" width="120">
        <template #default="{ row }">
          {{ filterEventValueIds(row.eventTriggerValueIds) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="aiIntentionType" align="center" label="分类" width="80">
        <template #default="{ row }">
          {{ row.aiIntentionType ? row.aiIntentionType.intentionType : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="标签" min-width="160" align="left">
        <template #default="{ row }">
          {{ row.aiLabels ? (row.aiLabels.map((item: LabelItem) =>item.labelName).join(',') || '-') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="updateTime" align="center" label="最后更新时间" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="right" label="操作" fixed="right" :width="isChecked ? 120 : 200">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">
            {{!isChecked ? '编辑' : '查看'}}
          </el-button>
          <el-button v-if="!isChecked" type="primary" link @click="copy(row)">
            复制
          </el-button>
          <el-button type="primary" link @click="editContent(row)">
            打断设置
          </el-button>
          <el-button v-if="!isChecked" type="danger" link @click="del(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
  </div>
  <BaseCorpusDrawer v-model:visible="corpusDialogVisible" :corpusData="corpusData" @update:data="search"/>
  <CorpusContentSettingDrawer v-model:visible="corpusContentVisible" :corpusId="corpusData?.id!"/>
  <BatchOperationDialog v-model:visible="batchOperationDialogVisible" :type="batchType" :data="batchData" @confirm="search"/>
  <CorpusBatchEditDialog
    v-model:visible="batchEditDialogVisible"
    :hasHangup="hasHangupinBatchEdit"
    :corpusType="CorpusTypeEnum['基本问答']"
    :corpusIds="batchData.map(item => item.id!) || []"
    @confirm="search()"
  />
  <LoadDialog
    v-model:visible="loadVisible"
    :groupId="props.groupId"
    @confirm="search"
  />
</template>

<script lang="ts" setup>
import { QueryTypeEnum, CorpusTypeEnum, EventValueItem, ScriptCorpusItem, corpusTypeOption, ConnectTypeEnum, ScriptBaseInfo, CorpusDataOrigin } from '@/type/speech-craft'
import { scriptCorpusModel, } from '@/api/speech-craft'
import { CaretTop, CaretBottom, Plus } from '@element-plus/icons-vue'
import { reactive, computed, ref, onDeactivated, onActivated, watch } from 'vue'
import dayjs from 'dayjs'
import { ElMessage, TableInstance } from 'element-plus'
import { tableHeaderStyle } from '@/assets/js/constant'
import { translateCorpusRules } from "@/components/corpus/constant";
import { LabelItem, } from "@/type/IntentionType";
import BaseCorpusDrawer from '@/components/corpus/BaseCorpusDrawer.vue'
import BatchOperationDialog from './BatchOperationDialog.vue'
import CorpusBatchEditDialog from '@/components/corpus/CorpusBatchEditDialog.vue'
import CorpusContentSettingDrawer from '@/components/corpus/CorpusContentSettingDrawer.vue'
import { useScriptStore } from '@/store/script'
import Confirm from '@/components/message-box'
import { findValueInEnum, pickAttrFromObj } from '@/utils/utils'
import { removeInfoInMultiContent } from '@/utils/script'
import to from 'await-to-js'
import { onBeforeRouteLeave } from 'vue-router'
import SvgIcon from '@/components/SvgIcon.vue';
import LoadDialog from './LoadDialog.vue'
import { trace } from '@/utils/trace'

const props = defineProps<{
  groupName?: string;
  groupId: number;
}>();

const loading = ref(false)
const scriptStore = useScriptStore()
const scriptId = scriptStore.id
const isChecked = scriptStore.isChecked

const searchForm = reactive<{
  name: string
}>({
  name: '',
})

/** 列表 分页和搜索 开始 */
// 列表-分页变量
const tableRef = ref<null | TableInstance>(null)

// 表格原始数据
const tableData = ref<ScriptCorpusItem[] | null>([])
const search = async () => {
  if (!props.groupId) return
  loading.value = true
  const [err, data] = await to(scriptCorpusModel.findBaseQAListByGroupId({
    groupId: props.groupId,
  }))
  tableData.value = (data || []).sort((a,b) =>  (a.weight||0) - (b.weight||0)).flatMap(item => {
    const scriptMultiContents = item.scriptMultiContents || []
    const content: string[] | undefined = (scriptMultiContents && scriptMultiContents.length) ? scriptMultiContents[0].scriptUnitContents?.reduce((a, b) => ([...a, b.content || '']), [] as string[]) || undefined : undefined
    return item.name?.includes(searchForm.name) ?[{
      ...item,
      content: content,
      matchText: translateCorpusRules(item.semCombineEntity?.satisfySemConditions || []),
      excludeText: translateCorpusRules(item.semCombineEntity?.excludeSemConditions || []),
    }] : []
  })
  loading.value = false
}
/** 分页和搜索 结束 */

/** 新建和编辑 基本问答 开始 */
const corpusDialogVisible = ref(false)

const corpusData = reactive<ScriptCorpusItem>(new CorpusDataOrigin(scriptId, CorpusTypeEnum['基本问答'], props.groupId))
const edit = (row?: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row,
      corpusTypeOption[corpusData.corpusType!].keys
    ))
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId, CorpusTypeEnum['基本问答'], props.groupId))
  }
  corpusDialogVisible.value = true
}
/** 新建和编辑 基本问答 结束 */

/** 复制单个基本问答 */
const copy = (row: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row,
      corpusTypeOption[corpusData.corpusType!].keys
    ))
    corpusData.id = undefined
    corpusData.name = corpusData.name + '（复制）'
    corpusData.scriptMultiContents = removeInfoInMultiContent(corpusData.scriptMultiContents || [])
    ElMessage.warning('复制成功，确定后生效。注：打断设置需重新设置。')
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId, CorpusTypeEnum['基本问答'], props.groupId))
  }
  console.log(row);
  console.log(corpusData);
  
  corpusDialogVisible.value = true
}
/** 复制单个基本问答 结束 */

/** 语句打断设置 */
const corpusContentVisible = ref(false)
const editContent = (row: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row,
      corpusTypeOption[corpusData.corpusType!].keys
    ))
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId, CorpusTypeEnum['基本问答'], props.groupId))
  }
  corpusContentVisible.value = true
}

/** 删除、批量删除 基本问答 开始 */
// 删除
const delAction = async (id: number) => {
  loading.value = true
  trace({
    page: `话术编辑-基本问答-删除(${scriptId})`,
    params: { id },
  })
  await scriptCorpusModel.deleteBaseQACorpus({corpusId: id}) as boolean
  ElMessage({
    message: '删除成功',
    type: 'success',
  })
  search()
  loading.value = false
}
const del = (row: ScriptCorpusItem) => {
  Confirm({ 
    text: `您确定要删除基本问答【${row.name}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}
const selectLen = computed(() => tableRef.value?.getSelectionRows()?.length || 0)

/** 批量操作 */
const handleBatchOperate = (command: string) => {
  const ids = tableRef.value?.getSelectionRows()?.map((item: ScriptCorpusItem) => item.id)
  if(!ids || ids.length < 1) {
    return ElMessage({
      type: 'warning',
      message: '请选择需要操作的问答'
    })
  }
  switch (command) {
    case 'edit': {
      batchEdit();
      break;
    }

    case 'move': {
      batchMove();
      break;
    }
    case 'copy': {
      batchCopy();
      break;
    }
    case 'del': {
      batchDel();
      break;
    }
  }
}
// 批量删除
const batchDel = () => {
  const ids = tableRef.value?.getSelectionRows()?.map((item: ScriptCorpusItem) => item.id)
  if(!ids || ids.length < 1) {
    return ElMessage({
      type: 'warning',
      message: '请选择需要删除的问答'
    })
  }
  Confirm({ 
    text: `您确定要删除共${ids.length}个基本问答吗？`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(async () => {
    loading.value = true
    // isSelectAll.value ? await scriptCorpusModel.deleteAllBaseQACorpus({scriptId }) as boolean
    await scriptCorpusModel.batchDeleteBaseQACorpus({knowledgeQAIds: ids, scriptId }) as boolean
    ElMessage({
      message: '删除成功',
      type: 'success',
    })
    search()
    loading.value = false
  }).catch(() => {})
}

// 批量编辑
const batchEditDialogVisible = ref(false)
const hasHangupinBatchEdit = ref(false)
const batchEdit = () => {
  batchData.value = tableRef.value?.getSelectionRows() || []
  hasHangupinBatchEdit.value = batchData.value?.some(item => item.connectType === ConnectTypeEnum['挂机'])
  batchEditDialogVisible.value = true
}


// 批量移动
const batchType = ref<'move' | 'copy'>('move')
const batchOperationDialogVisible = ref(false)
const batchData = ref<ScriptCorpusItem[]>([])
const batchMove = () => {
  batchData.value = tableRef.value?.getSelectionRows() || []
  batchOperationDialogVisible.value = true
  batchType.value = 'move'
}
// 批量复制
const batchCopy = () => {
  batchData.value = tableRef.value?.getSelectionRows() || []
  batchOperationDialogVisible.value = true
  batchType.value = 'copy'
}

// 从生效中的话术导入话术中的基本问答
const loadVisible = ref(false)
const loadAction  = () => {
  loadVisible.value = true
}

const eventValuesOptions = reactive<{[key: string]: EventValueItem}>({})
const init = async () => {
  await scriptStore.getSemanticOptions()
  const res = await scriptStore.getEventOptions()
  if (res && res.length > 0) {
    res.map(item => {
      item.eventValuemap && Object.assign(eventValuesOptions, item.eventValuemap)
    })
  }
}
const filterEventValueIds = (ids?: number[]) => {
  if (ids && ids.length > 0) {
    const eventTriggerValueArr: string[] = []
    ids && ids?.length > 0 && ids?.map(item => {
      eventValuesOptions[item] && eventTriggerValueArr.push(`${(eventValuesOptions[item].name || '')}(${(eventValuesOptions[item].explanation || '')})`)
    })
    return eventTriggerValueArr.join(',') || ''
  } else {
    return ''
  }
}

onActivated(() => {
  init()
  search()
})
const clearAll = () => {
  tableRef.value?.clearSelection()
  tableData.value = null
  tableRef.value = null
}
onDeactivated(() => {
  clearAll()
})
onBeforeRouteLeave(() =>{
  clearAll()
})

watch(() => props.groupId, n => {
  n && search()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.basic-QA-container {
  width: 100%;
  padding: 16px 12px 0;
  position: relative;
  box-sizing: border-box;
  overflow-y: auto;
  height: calc(100vh - 225px);
  display: flex;
  flex-direction: column;
  .el-table {
    font-size: var(--el-font-size-base);
    :deep(.cell) {
      padding: 0 8px;
      line-height: 20px;
    }
    .option-checkbox {
      display: block;
      cursor: pointer;
      width: 14px;
      height: 14px;
      border-radius: 2px;
      border-width: 1px;
      position: relative;
      margin-right: 8px;
      .el-icon {
        display: none;
        position: absolute;
        top: 1px;
        left: 0px;
      }
    }
    .selected {
      font-weight: normal;
      &.option-checkbox {
        background-color: #165DFF;
        border-color: #165DFF;
        .el-icon {
          display:inline-block;
        }
        
      }
    }
    &.selected::after {
      display: none;
      content: '';
    }
  }
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
</style>
