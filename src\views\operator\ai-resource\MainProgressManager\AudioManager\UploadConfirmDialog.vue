<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    align-center
    class="script-audio-dialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        上传确认
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <div class="tw-text-[var(--primary-red-color)]">
          <span>
            音频名称和语料名称不同，已自动帮你修改，请进行验听
          </span>
          <el-button type="primary" link @click="onClickPlay">
            <el-icon :size="20">
              <svg-icon name="play-circle" />
            </el-icon>
          </el-button>
        </div>
        <div class="tw-mt-[8px]">
          <span>语料名称：</span>
          <span>{{ props.data?.contentName || '' }}</span>
        </div>
        <div class="tw-mt-[8px]">
          <span>文字内容：</span>
          <span>{{ props.data?.content || '' }}</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <slot name="upload"></slot>
        <el-button type="primary" :disabled="!isPlayed" @click="onClickConfirm">
          验听无误
        </el-button>
      </div>
    </template>

    <MiniAudio
      v-if="tempAudio.id && tempAudio.id > 0"
      v-model:audioStatus="audioStatus"
      v-model:playRate="playRate"
      :tempAudio="tempAudio"
      @close="handleAudioPlay()"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { AudioItem, } from '@/type/speech-craft'
import { reactive, ref, watch } from 'vue'
import MiniAudio from '@/components/MiniAudio.vue'
import { TempAudioOrigin, AudioInfoItem } from './constant'
import to from 'await-to-js'
import { scriptCorpusModel } from '@/api/speech-craft'
import { ElMessage } from 'element-plus'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  data: AudioItem | null,
}>(), {
  visible: false,
  data: () => ({}),
})
const emits = defineEmits([
  'update:visible',
  'update:data',
  'play',
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 弹窗 开始 ----------------------------------------

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  handleAudioPlay()
  emits('update:visible', false)
}
/**
 * 点击确定按钮
 */
const onClickConfirm = async () => {
  // 切换音频验听状态
  const [err] = await to(scriptCorpusModel.updateCorpusAudioStatus({ id: tempAudio.id! }))
  // 返回失败结果
  if (err) {
    ElMessage({
      message: '无法设置为验听无误，请重试',
      type: 'error',
    })
    return
  }
  // 返回成功结果
  isPlayed.value = true
  // 更新列表里当前音频的验听状态
  emits('play', props.data?.id)
  closeDialog()
}

// ---------------------------------------- 弹窗 结束 ----------------------------------------

// ---------------------------------------- 音频播放 开始 ----------------------------------------

const audioStatus = ref<'pause' | 'play' | 'none'>('none')
const playRate = ref(1)
const tempAudio = reactive<AudioInfoItem>(new TempAudioOrigin())
const isPlayed = ref(false)
const handleAudioPlay = async (row?: AudioItem | null) => {
  const isSame = tempAudio.id === row?.id && tempAudio.audioPath === row?.audioPath
  // 接口字段里语料名称使用contentName，音频播放器组件里使用name字段，这里保留兼容
  Object.assign(tempAudio, row ? {
    id: row.id,
    name: row.contentName,
    content: row.content,
    audioPath: row.audioPath,
    isPlayed: row.isPlayed,
  } : new TempAudioOrigin())
  if (isSame) {
    audioStatus.value = audioStatus.value == 'play' ? 'pause' : 'play'
  } else {
    audioStatus.value = 'play'
    isPlayed.value = true
  }
}

// ---------------------------------------- 音频播放 结束 ----------------------------------------

// ---------------------------------------- 验听 开始 ----------------------------------------

/**
 * 点击播放按钮
 */
const onClickPlay = () => {
  handleAudioPlay(props.data)
}
/**
 * 点击重新上传按钮
 */
// const onClickUpload = () => {
//   emits('upload')
//   // 重新上传
// }

// ---------------------------------------- 验听 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
