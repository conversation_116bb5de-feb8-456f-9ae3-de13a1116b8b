<template>
  <div class="sms-container">
    <div class="search-box">
      <div class="tw-grid tw-grid-cols-6 tw-gap-[8px]">
        <div class="item-col">
          <span class="label">执行状态：</span>
          <el-select
            v-model="searchForm.status"
            clearable
            placeholder="请选择执行状态"
            style="width: 100%;"
            @change="search()"
          >
            <el-option
              v-for="item in enum2Options(RecordStatusForPlanEnum)"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="item-col tw-col-span-2">
          <span class="label">执行时间：</span>
          <TimePickerBox
            v-model:start="searchForm.startTime"
            v-model:end="searchForm.endTime"
            placeholder="执行时间"
            separator="-"
            :maxRange="60*60*1000*24*7"
            :clearable="false"
            format="YYYY-MM-DD HH:mm:ss"
            @change="search()"
          />
        </div>
        <div class="item-btn">
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
      <div class="tw-flex tw-items-center tw-justify-end">
        <el-button @click="downloadTemplate">
          <el-icon :size="16" class="tw-mr-0.5">
            <SvgIcon name="download3" color="none"></SvgIcon>
          </el-icon>
          <span>下载导入模板</span>
        </el-button>
        <el-dropdown @command="handleUploadCommand" class="tw-cursor-pointer tw-text-[#323233] tw-mx-1">
          <el-button type="primary">
            <el-icon :size="16">
            <SvgIcon name="upload" color="none"></SvgIcon>
            </el-icon>
            <span>导入营销计划</span>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="1" :key="1">新建计划</el-dropdown-item>
              <el-dropdown-item :command="2" :key="2">重传计划</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button @click="downloadError">
          <el-icon :size="16" class="tw-mr-0.5">
            <SvgIcon name="download3" color="none"></SvgIcon>
          </el-icon>
          <span>导出失败</span>
        </el-button>
        <input ref="fileRef" type="file" @change="handleFileChange" accept=".xls,.xlsx" class="batch-upload tw-hidden"/>
      </div>
    </div>

    <el-table
      :data="tableTempData"
      v-loading="loading"
      class="tw-grow"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      @sort-change="handleSortChange"
      stripe
      border
    >
      <el-table-column property="channelNumber" label="渠道号" fixed="left" align="left" width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="tagName" label="标签名" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="marketingType" label="营销类型" align="center" width="80">
        <template #default="{ row }">
          {{ row.marketingType || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="modelName" label="模型名称" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="operatorName" label="运营商" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="originalLink" label="网申链接" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="smsSign" label="短信签名" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="smsContent" label="短信文案" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="volcanoAccount" label="火山账号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="account" label="白泽账号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="templateId" label="白泽任务模版id" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="updateBy" label="操作账号" show-overflow-tooltip  align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="recordStatus" label="执行状态" fixed="right" align="center" width="160">
        <template #default="{ row }">
          <span
            v-if="row?.recordStatus"
            class="status-box-mini tw-mx-auto"
            :class="recordStatusForPlanClass[row.recordStatus as RecordStatusForPlanEnum] || 'blue-status'"
          >
            {{ findValueInEnum(row.recordStatus, RecordStatusForPlanEnum) || row.recordStatus }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="updateTime" label="执行时间" fixed="right" align="center" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          {{ row.updateTime ? dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
  <UploadDialog
    v-model:visible="uploadVisible"
    :uploadData="uploadData"
    :type="uploadType"
    @confirm="search"
  >
  </UploadDialog>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted, } from 'vue'
import { ElMessage, } from 'element-plus'
import dayjs from 'dayjs'
import PaginationBox from '@/components/PaginationBox.vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { enum2Options, findValueInEnum, formatterEmptyData, handleTableSort } from '@/utils/utils'
import { generateExcelByAoa, readXlsx, exportExcel } from '@/utils/export'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js'
import { volcanoModel } from '@/api/volcano'
import { MarketingPlanItem, RecordStatusForPlanEnum } from '@/type/volcano'
import SvgIcon from '@/components/SvgIcon.vue'
import UploadDialog from './UploadPlanDialog.vue'
import { recordStatusForPlanClass } from './constant'
import { useUserStore } from '@/store/user'

const userInfo = useUserStore()

const loading = ref(false)
const currentPage = ref(1)
const total = ref(0)
const pageSizeList = [20, 50, 100]
const pageSize = ref(pageSizeList[0])
const tableData = ref<MarketingPlanItem[] | null>([])
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

const orderCol = ref('')
const orderType = ref('')
const handleSortChange = (params: { prop: string, order: string }) => {
  orderCol.value = params.prop
  orderType.value = params.order
}
const tableTempData = computed(() => {
  const data = handleTableSort(tableData.value || [], orderCol.value, orderType.value)
  return data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

const searchForm = reactive<{
  status?: RecordStatusForPlanEnum,
  startTime?: string,
  endTime?: string
}>({
  status: undefined,
  startTime: dayjs().subtract(1, 'd').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  endTime:  dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
})
const search = async () => {
  if (loading.value) return ElMessage.warning('请勿频繁操作')
  loading.value = true
  const res = await to(volcanoModel.findMarketingPlanList(searchForm))
  orderCol.value = 'updateTime'
  orderType.value = 'descending'
  tableData.value = res[1] || []
  total.value = tableData.value?.length || 0
  loading.value = false
}

const fileRef = ref() // 上传文件
// 模板字段
const uploadTitleObject: Record<string, keyof MarketingPlanItem> = {
  '渠道号': 'channelNumber',
  '标签名': 'tagName',
  '营销类型': 'marketingType',
  '模型名称': 'modelName',
  '运营商': 'operatorName',
  '网申链接': 'originalLink',
  '短信签名': 'smsSign',
  '短信文案': 'smsContent',
  '火山账号': 'volcanoAccount',
  '白泽账号': 'account',
  '白泽任务模版id': 'templateId',
}
const uploadData = ref<MarketingPlanItem[] | null>([])
const uploadVisible = ref(false)
const uploadType = ref<number>(1)
const handleUploadCommand = (command: number) => {
  uploadType.value = command
  handleUpload()
}
const handleUpload = () => {
  // @ts-ignore
  document.querySelector('.batch-upload')!.click()
}
// 上传文件转化为表格数据
const handleFileChange = async (e: Event) => {
  loading.value = true
  // 读取xls
  const { data } = await readXlsx(e) as { data: Record<string, any>[] }
  if (!data?.length)  {
    loading.value = false
    return ElMessage.error('上传文件为空')
  }

  // xls数据转换为需要的数据
  const err: string[] = []
  const channelNumberSet = new Set()
  uploadData.value = data.flatMap((item, index) => {
    const type = item['营销类型'] ? (item['营销类型'] + '').trim() : ''
    if (!type || !['1', '2', '3'].includes(type)) {
      err.push(`${item['渠道号']}：营销类型为空或错误`)
      return []
    }
    const res: Partial<MarketingPlanItem> = {
      updateBy: userInfo.account
    }
    Object.keys(uploadTitleObject).forEach(title => {
      if (title === '网申链接' && item[title]) {
        try {
          const url = new URL((item[title] + '').trim());
          
          if (!url.searchParams.has('channel')) {
            url.searchParams.set('channel', item['渠道号']);
          }
          if (url.pathname === '/') {
            url.pathname = '';
          }
          // @ts-ignore
          res[uploadTitleObject[title]] =  url.toString();
        } catch (error) {
          err.push(`${item['渠道号']}：网申链接格式错误`)
        }
      } else {
        // @ts-ignore
        res[uploadTitleObject[title]] = item[title] ? (item[title] + '').trim() : undefined
      }
      
      
    })
    if (!res.channelNumber) {
      err.push(`第${index + 1}行：渠道号为空`)
      return []
    } else if (channelNumberSet.has(res.channelNumber)) {
      err.push(`${item['渠道号']}：渠道号重复`)
      return []
    } else {
      channelNumberSet.add(res.channelNumber)
    }
    if (['1', '3'].includes(type) && (!res.volcanoAccount || !res.account || !res.templateId)) {
      err.push(`${item['渠道号']}：火山账号、白泽账号、白泽任务模板id存在空`)
      return []
    } else if (type == '2' && (!res.originalLink || !res.smsSign || !res.smsContent || !res.volcanoAccount || !res.account)) {
      err.push(`${item['渠道号']}：网申链接、短信签名、短信文案、火山账号、白泽账号存在空`)
      return []
    }
    return [res as MarketingPlanItem]
  }) || []
  // 清空set
  channelNumberSet.clear()
  // 清空已上传文件
  fileRef.value.value = null
  // 清除loading
  loading.value = false
  // 校验异常则不进入打开执行弹窗
  if (err.length) return ElMessage.warning(err.join('\n'))
  // 校验通过，打开执行弹窗
  uploadVisible.value = true
}

const downloadTemplate = async () => {
  generateExcelByAoa([
    Object.keys(uploadTitleObject)
  ], `火山营销计划模板.xlsx`, )
}

// 导出列表中失败的数据
const downloadError = async () => {
  const data: any[] = tableData.value?.flatMap((item, index) => {
    if (item.recordStatus == RecordStatusForPlanEnum['成功']) return []
    return [{
      '渠道号': item.channelNumber || '',
      '标签名': item.tagName || '',
      '营销类型': item.marketingType || '',
      '模型名称': item.modelName || '',
      '运营商': item.operatorName || '',
      '网申链接': item.originalLink || '',
      '短信签名': item.smsSign || '',
      '短信文案': item.smsContent || '',
      '火山账号': item.volcanoAccount || '',
      '白泽账号': item.account || '',
      '白泽任务模版id': item.templateId || '',
    }]
  }) || []
  if (!data || data?.length < 1) {
    loading.value = false
    return ElMessage.warning('无导入失败数据')
  }
  exportExcel(data,  `营销计划上传失败${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`)
}

onMounted(() => {
  search()
})
onUnmounted(() => {
  tableData.value = null
})

</script>

<style scoped lang="postcss" type="text/postcss">
.sms-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
}
.status-box-mini {
  width: 100px;
}
</style>
