<template>
  <el-select
    v-model="selectVal"
    class="select-dom"
    clearable
    :multiple="true"
    :multiple-limit="props.limitNum"
    :collapse-tags="true"
    :collapse-tags-tooltip="true"
    :placeholder="props.placeholder || '请选择'"
    popper-class="popper-dom"
    :style="props.styleStr"
    @change="updateSelect"
    @visible-change="handleVisibleChange"
  >
    <template #header>
      <div class="tw-flex tw-flex-col">
        <el-input
          v-if="props.filterable" 
          v-model.trim="searchVal"
          class="tw-mb-[8px] tw-h-[32px]"
          ref="inputRef"
          :placeholder="props.filterPlaceholder || '请输入搜索内容'"
          size="small"
          clearable
          @input="(val: string) => filterOption(val, true)"
        >
          <template #suffix>
            <el-icon><SvgIcon name="search" /></el-icon>
          </template>
        </el-input>
        <div v-if="isEmpty" class="tw-pl-[1px] tw-pr-[3px] tw-text-[13px] tw-mb-[8px]">
          <div class="tw-text-left tw-font-[600] tw-mb-[6px]">过滤选项列表</div>
          <div class="tw-text-center tw-text-[#969799]">暂无数据</div>
        </div>
        <div class="tw-flex tw-justify-between tw-items-center tw-pl-[1px] tw-pr-[3px] tw-text-[13px]">
          <span class="tw-text-left tw-font-[600]">{{ title }}</span>
          <div v-if="options && options?.length>0" class="tw-flex-grow tw-flex">
            <template v-if="props.canCopy">
              <el-button type="primary" link @click="handleCopy" style="margin-left: 8px;">
                复制
              </el-button>
              <el-tooltip :disabled="!copyIds?.length">
                <template #content>
                  <div  class="tw-w-[300px]">
                    <div class="tw-border-b tw-border-gray-400 tw-pb-[4px]">
                      <div class="tw-text-left tw-truncate">复制内容</div>
                    </div>
                    <p class="tw-mt-[4px] tw-grid tw-grid-cols-3 tw-text-left tw-gap-[5px]">
                      <span v-for="item in copyIds" class="tw-truncate">
                      {{ filterId(item)}}
                      </span>
                    </p>
                  </div>
                </template>
                <el-button
                  :disabled="!copyIds||copyIds.length<1"
                  style="margin-left: 8px;"
                  :type="!copyIds||copyIds.length<1 ? 'default':'primary'"
                  link
                  @click="pasteIds"
                >粘贴</el-button>
              </el-tooltip>
            </template>
            <template v-if="props.canSelectAll">
              <el-button
                style="margin-left: 8px;"
                link
                type="primary"
                @click="selectAll"
              >全选</el-button>
            </template>
            <template v-if="props.canReverseSelectAll">
              <el-button
                style="margin-left: 8px;"
                link
                type="primary"
                @click="reverseSelectAll"
              >全量反选</el-button>
            </template>
            <template v-if="props.canSelectAll || props.canCopy">
              <el-button type="danger" link @click="delAll" style="margin-left: 8px;">
                清空
              </el-button>
            </template>
          </div>
          <span class="tw-max-w-[70px] tw-text-[#969799] tw-text-right tw-ml-[30px]">{{ `已选  ${selectVal?.length || 0}/${props.options?.length || 0}` }}</span>
        </div>
      </div>
    </template>
      <div v-loading="loading" class="tw-absolute tw-w-full tw-h-full tw-top-0 tw-left-0">
      </div>
      <el-option
        v-for="item in options"
        :key="props.val ? (item[props.val] || item) : item"
        :label="item[props.name]"
        :value="props.isString ? item[props.val] + '' : item[props.val]"
        style="font-size: 13px"
      >
        <div class="tw-flex tw-items-center">
          <span class="option-checkbox">
            <el-icon :size="12" color="#fff"><SvgIcon name="gou"/>  </el-icon>
          </span>
          <span class="tw-truncate">{{ props.name ? (item[props.name] || item) : item }}</span>
        </div>
        <!-- 支持通过suffix传参增加后缀 -->
        <div v-if="!!props.suffix && item[props.suffix]" class="tw-text-gray-400">
          {{ item[props.suffix] }}
        </div>
        <!-- 支持通过插槽增加后缀 -->
        <slot name="option-tips" :option="item"></slot>
      </el-option>  
      <div>
      </div>
  </el-select>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onUnmounted } from 'vue'
import { ElMessage, } from 'element-plus'
import { onBeforeRouteLeave } from 'vue-router'
import { useGlobalStore } from '@/store/globalInfo'
// 组件入参props
const props = withDefaults(defineProps<{
  selectVal?: any[], // 当前选中数据
  selectValStr?: string, // 当前选中数据，字符串拼接形式
  options: any[] | null, // 选项列表
  name?: string, // 选项列表中代表name的属性
  val?: string, // 选项列表中代表value的属性
  suffix?: string, // 选项列表中代表后缀的属性, 且会加入筛选
  limitNum?: number, // 多选上限
  placeholder?: string, // 选择框占位符
  filterPlaceholder?: string, // 搜索框占位符
  filterable?: boolean, // 是否支持搜索，搜索框显示
  styleStr?: string, // 当前dom的style
  isString?: boolean,
  canCopy?: boolean, // 是否支持复制、粘贴，并控制复制粘贴按钮显示。
  copyName?: string, // 粘贴ids列表，
  canSelectAll?: boolean, // 是否支持全选，若控制最多选择数量，则按顺序选择至上限，并控制复制粘贴按钮显示。
  canReverseSelectAll?: boolean, // 是否支持反选，若控制最多选择数量，则按顺序选择至上限，并控制复制粘贴按钮显示。
}>(), {
  multiple: false,
  limitNum: 0, // 默认值0，0代表无限制
  filterable: false,
  canReverseSelectAll: false,
  name: 'name',
  val: 'value',
})
// emit
const emits = defineEmits([
  'update:selectVal', 'update:selectValStr'
])
const globalStore = useGlobalStore()
const selectVal = ref<any[] | null>(props.selectVal || (!!props.selectValStr ? props.selectValStr?.split(',') : [])) // 选中的数据
const options = ref<null | any[]>(props.options || []) // 选项列表
const loading = ref(false)
const title = computed(() => {
  if (!!searchVal.value && !isEmpty.value) {
    return '过滤选项列表'
  } else {
    return props.placeholder ? props.placeholder.replace('请选择', '') + '列表' : '选项列表'
  }
})
const copyIds = computed(() => {
  if (!props.canCopy || !props.copyName) return []
  return globalStore.findCopyInfoByName(props.copyName) || []
}) // 复制的数据

const searchVal = ref('') // 搜索输入框内容
const inputRef = ref() // 搜索输入框ref
const needFocus = ref(true)

/** 搜索功能，过滤选项 */
// 防止触发太多次，使用定时器触发防抖功能
const timer = ref<ReturnType<typeof setTimeout> | null>(null)
const isEmpty = ref(false)
const filterOption = (val: string, showLoading: boolean = false) => {
  timer.value && clearTimeout(timer.value)
  timer.value = setTimeout(async () => {
    if (val) {
      options.value = props.options?.filter(item => item[props.name as keyof typeof item].includes(val) || (props.suffix && item[props.suffix  as keyof typeof item]?.includes(val))) || []
    } else {
      options.value = props.options || []
    }
    if ((!options.value || options.value.length < 1) && props.options && props.options?.length > 0) {
      options.value = props.options
      isEmpty.value = true
    } else {
      isEmpty.value = false
    }
    timer.value = null
  }, 200)
}

// 翻译粘贴板（sessionStorage缓存）存在的信息
const filterId = <T>(id: T) => {
  const vv = props.options?.find(v=> {
    return (props.val ? v[props.val] : v)==id
  })
  if (vv) {
    return props.name ? (vv[props.name] || '') : (vv || '')
  } else {
    return ''
  }
}
// 清空选择数据
const delAll = () => {
  selectVal.value = []
  updateSelect()
}
// 复制功能
const handleCopy = () => {
  if (!selectVal.value || selectVal.value?.length<1 || !props.copyName || !props.canCopy) {
    return ElMessage.warning('复制内容不可为空')
  } else {
    globalStore.addCopyInfo({
      name: props.copyName,
      value: selectVal.value || [],
    })
    ElMessage.success('复制成功')
  }
}
// 粘贴功能
const pasteIds = () => {
  if (!props.canCopy || !props.copyName || !copyIds.value || copyIds.value.length < 0) return
  const optionIds = props.options?.map(item => props.val ? item[props.val] : item)
  const copyFilterIds = copyIds.value.filter(v => optionIds?.includes(v))
  selectVal.value = [...new Set([...(selectVal.value || []), ...(copyFilterIds||[])])]
  ElMessage.success('粘贴完成')
  globalStore.deleteCopyeInfoByName(props.copyName)
  props.isString ? emits('update:selectValStr', selectVal.value.join(',') || undefined)
  : emits('update:selectVal', selectVal.value||[])
}
// 全选功能
const selectAll = () => {
  if (props.limitNum) {
    selectVal.value = (options.value || []).slice(0, props.limitNum).map(item => props.val ? (item[props.val] || item) : item)
  } else {
    selectVal.value = (options.value || []).map(item => props.val ? (item[props.val] || item) : item)
  }
  props.isString ? emits('update:selectValStr', selectVal.value.join(',') || undefined)
  : emits('update:selectVal', selectVal.value||[])
}

// 反选
const reverseSelectAll = () => {
  const res = (props.options || []).flatMap(item => {
    const value = props.val ? (item[props.val] || item) : item
    if (selectVal.value?.includes(value)) {
      return []
    } else {
      return [value]
    }
  }) || []
  if (props.limitNum) {
    selectVal.value = res.slice(0, props.limitNum) || []
  } else {
    selectVal.value = res || []
  }
  props.isString ? emits('update:selectValStr', selectVal.value.join(',') || undefined)
  : emits('update:selectVal', selectVal.value||[])
}

// 更新选中数据,并判断是否需要继续聚焦搜索框，暴露给父组件
const updateSelect = () => {
  const newItem = selectVal.value?.at(-1) || undefined
  if (newItem) {
    const index = (options.value || []).findIndex(item => (props.val ? item[props.val] || item : item) === newItem)
    if(index >= 6 || !searchVal.value) {
      needFocus.value = false
    } else {
      needFocus.value = true
    }
  }
  props.isString ? emits('update:selectValStr', selectVal.value?.join(',') || undefined)
  : emits('update:selectVal', selectVal.value||[])
}

// 下拉框显示、消失触发函数
const handleVisibleChange = (v: boolean) => {
  // 保证首次进入是最顶部的
  if (v) {
    setTimeout(() => {
      inputRef.value?.focus()
    }, 500)
  } else {
    // 失焦后将搜索内容清空，并重置搜索触发的定时器
    searchVal.value = ''
    filterOption(searchVal.value)
  }
}
/** watch开始 */
// 监听入参，更新选中数据和选项变化
watch(props, () => {
  filterOption(searchVal.value)
  selectVal.value = props.isString ? (!!props.selectValStr ? props.selectValStr.split(',') : []) : props.selectVal || []
}, {deep: true, immediate: true})

const clearAllData = () => {
  timer.value && clearTimeout(timer.value)
  timer.value = null
  inputRef.value = null
  options.value = null
  selectVal.value = null
}
onUnmounted(() => {
  clearAllData()
})
onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>

.select-dom {
  :deep(.el-select-tags-wrapper) {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: hidden;
    .el-tag {
      padding: 0 2px;
    }
    .el-tag__content {
      display: flex;
      align-items: center;
    }
  }
}
.popper-dom {
  .el-button {
    font-size: 13px;
  }
  .el-select-dropdown__item {
    padding: 8px;
  }
  .el-select-dropdown.is-multiple .el-select-dropdown__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .option-checkbox {
      width: 14px;
      height: 14px;
      border-radius: 2px;
      border-width: 1px;
      position: relative;
      margin-right: 8px;
      .el-icon {
        display: none;
        position: absolute;
        top: 1px;
        left: 0px;
      }
    }
    &.selected {
      font-weight: normal;
      color: #165DFF;
      .option-checkbox {
        background-color: #165DFF;
        border-color: #165DFF;
        .el-icon {
          display:inline-block;
        }
      }
    }
    &.selected::after {
      display: none;
      content: '';
    }
  }
}
</style>
