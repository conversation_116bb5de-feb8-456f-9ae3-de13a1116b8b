<template>
  <el-dialog
    v-model="dialogVisible"
    width="720px"
    :close-on-click-modal="false"
    class="dialog-form"
    align-center
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ title }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="editData"
        :disabled="!getPermission"
        :rules="rules"
        label-width="90px"
        scroll-to-error
        :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
        ref="editRef"
      >
        <el-form-item v-if="props.type==='batch'" label="修改的内容：" prop="select">
          <el-select
            v-model="selectItems"
            class="tw-w-full"
            placeholder="请选择需要修改的内容"
            multiple
            collapse-tags
            filterable
            collapse-tags-tooltip
            @change="handleSelectItems"
          >
            <el-option v-for="item in itemsList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="props.type==='template'" label="模板名称：" prop="templateName">
          <el-input v-model.trim="editData.templateName" placeholder="任务模板名称100字以内" clearable/>
        </el-form-item>
        <el-form-item  v-if="props.type==='template'" label="备注说明：" prop="comment">
          <el-input v-model.trim="editData.comment" placeholder="请输入备注说明" :maxlength="150" :autosize="{ minRows: 2, maxRows: 6 }" type="textarea"/>
        </el-form-item>
        <el-form-item v-if="props.type==='task' || props.type==='template'" label="任务名称：" prop="taskName">
          <el-input v-model.trim="editData.taskName" placeholder="任务名称100字以内" clearable/>
        </el-form-item>
        <el-form-item v-if="props.type==='template'" label="任务类型：" prop="taskType">
          <el-radio-group v-model="editData.taskType" @change="handleTaskTypeChange" :disabled="!!editData.id">
            <el-radio v-for="item in taskTypeOption" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="props.type!=='batch' && ((userStore.tenantId && [11, 21, 23, 26].includes(userStore.tenantId)) || isDev)" label="隔日续呼：" prop="nextDayCall">
          <el-switch
            v-model="editData.nextDayCall"
            :disabled="props.type==='task' && !!editData.id"
            inline-prompt
            :active-value="1"
            :inactive-value="0"
            active-text="开"
            inactive-text="关"
          />
        </el-form-item>
        <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('scriptStringId'))" label="执行话术：" prop="scriptStringId">
          <el-select v-if="props.type!=='template'" v-model="scriptSelectType" class="tw-w-[120px] tw-mr-[6px]" placeholder="请选择类型" @change="handleSelectTypeChange()">
            <el-option label="执行话术" :value="0" />
            <el-option label="执行模板" :value="1" />
          </el-select>
          <el-select v-if="scriptSelectType===0" v-model="editData.scriptStringId" class="tw-grow" placeholder="请选择执行话术" filterable clearable @change="handleScriptChange()">
            <el-option v-for="item in speechCraftList" :key="item.scriptStringId" :label="item.scriptName" :value="item.scriptStringId" />
          </el-select>
          <el-select v-if="scriptSelectType===1" v-model="scriptTemplateId" class="tw-grow" placeholder="请选择执行模板" filterable clearable @change="handleSelectTemplateChange()">
            <el-option v-for="item in taskTemplateList" :key="item.id" :value="item.id" :label="`${item.id}-${item.speechCraftName}`">
              <div class="tw-flex tw-items-center tw-w-full tw-justify-between">
                <span>{{ item.id }}</span>
                <span class="info-title">{{ item.speechCraftName }}</span>
              </div>
            </el-option>
            <template #label="{ label, value }">
              <div class="tw-flex tw-items-center tw-w-full tw-justify-between">
                <span>{{ value + '-' }}</span>
                <span class="info-title">{{ label }}</span>
              </div>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item v-if="editData.scriptStringId && currentSmsTriggerNames && currentSmsTriggerNames.length" label="触发短信：" prop="scriptSms">
          <div v-if="editData.scriptSms" v-for="(item, index) in currentSmsTriggerNames" :key="item" class="tw-flex tw-items-center">
            <span class="tw-shrink-0">{{ item + '：' }}</span>
            <el-select
              v-model="editData.scriptSms[index].smsTemplateId"
              clearable
              filterable
              class="tw-grow"
              placeholder="请选择短信模板"
              style="width:400px"
            >
              <el-option
                v-for="item in smsTemplateListInIndustry"
                :key="item.id"
                :label="item.templateName"
                :value="item.id"
              />
            </el-select>
          </div>
        </el-form-item>
        <template v-if="editData.taskType===TaskTypeEnum['人机协同']">
          <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('callTeamIds'))" label="坐席组：" prop="callTeamIds">
            <el-select v-model="editData.callTeamIds" class="tw-w-full" placeholder="请选择坐席组" multiple collapse-tags collapse-tags-tooltip>
              <el-option v-for="item in callTeamList" :key="item.id" :label="item.callTeamName" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('callTeamPushType'))" label="推送方式：" prop="callTeamPushType">
            <el-select v-model="editData.callTeamPushType" disabled class="tw-w-full" placeholder="请选择坐席推送方式">
              <el-option v-for="item in callTeamPushList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('callTeamHandleType'))" label="处理方式：" prop="callTeamHandleType">
            <el-select v-model="editData.callTeamHandleType" class="tw-w-full" placeholder="请选择坐席处理方式">
              <el-option v-for="item in callTeamHandleOption" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('lineRatio'))" prop="lineRatio">
            <template #label>
              <div class="tw-flex tw-items-center">
                <span>集线比</span>
                <el-tooltip content="集线比：任务外呼并发数与签入坐席数的比率，建议值为1/接通率。在外呼时，任务外呼并发数=签入空闲坐席数*集线比，但≤商户线路并发上限。">
                  <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
                </el-tooltip>
                <span>：</span>
              </div>
            </template>
            <el-input-number v-model="editData.lineRatio" style="width: 100%" :controls="false" :precision="2" :min="1" :max="20000" placeholder="请输入集线比，1-20000"/>
          </el-form-item>
          <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('occupyRate'))" label="" prop="occupyRate">
            <template #label>
              <div class="tw-flex tw-items-center">
                <span>占用等级</span>
                <el-tooltip content="坐席占用等级：当单个坐席同时签入多个任务时，坐席占用等级越高的任务，分配到的坐席数越多，理论并发越多。">
                  <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
                </el-tooltip>
                <span>：</span>
              </div>
            </template>
            <el-select v-model="editData.occupyRate" class="tw-w-full" placeholder="请选择坐席占用等级">
              <el-option v-for="item in occupyRateList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('virtualSeatRatio'))" prop="virtualSeatRatio" label-width="110px">
            <template #label>
              <div class="tw-flex tw-items-center tw-text-[12px]">
                <span>虚拟坐席系数</span>
                <el-tooltip content="虚拟坐席系数：签入坐席全忙时，任务将以额外并发进行外呼。额外并发=集线比“签入坐席数“虚拟坐席系数。">
                  <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
                </el-tooltip>
                <span>：</span>
              </div>
            </template>
            <el-input-number v-model="editData.virtualSeatRatio" style="width: 100%" :controls="false" :precision="2" :min="0" :max="100" placeholder="请输入虚拟坐席系数，0-100"/>
          </el-form-item>
        </template>
        <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('startWorkTimeList'))" label="拨打时段：" prop="startWorkTimeList">
          <MutiTimeRangePickerBox
            :startWorkTimeList="editData.startWorkTimeList"
            :endWorkTimeList="editData.endWorkTimeList"
            :defaultStart="merchantTimeRange[0]"
            :defaultEnd="merchantTimeRange[1]"
            @update="handleTimeUpdate"
          />
        </el-form-item>
        <el-form-item v-if="props.type==='task' && editData.id" prop="isAutoStop" :label-width="1" >
          <div>
            <div class="tw-flex">
              <div class="tw-w-[90px] tw-text-right">是否止损：</div>{{editData.isAutoStop ? '是' : '否' }}
            </div>
            <div v-if="editData.isAutoStop" class="tw-flex">
              <div class="tw-w-[90px] tw-text-right">止损时间：</div>
              {{ editData.taskEndTime }}
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('autoReCall' as keyof TemplateBaseItem))" prop="autoRecall" >
          <template #label>
            <div class="tw-flex tw-items-center">
              <span>自动补呼</span>
              <el-tooltip content="呼叫队列中首呼未接通的名单进行重呼。">
                <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
              </el-tooltip>
              <span>：</span>
            </div>
          </template>

          <el-switch
            v-model="editData.autoReCall"
            inline-prompt
            :active-value="1"
            :inactive-value="0"
            active-text="开"
            inactive-text="关"
          />
        </el-form-item>
        <template v-if="editData.autoReCall">
          <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('autoReCall'))" label="分配方式：" prop="callRatioType">
            <el-select v-model="editData.callRatioType" class="tw-w-[520px]" placeholder="请选择资源分配方式">
              <el-option label="首呼优先分配" :value="1"></el-option>
              <el-option label="多轮次呼叫按比例分配" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('autoReCall'))" label="补呼间隔：" prop="firstRecallTime">
            <div class="tw-flex tw-w-full">
              <span class="tw-ml-1">
                <span>第一次：</span>
                <el-input-number v-model="editData.firstRecallTime" :controls="false" :precision="0" :min="1" :max="660" style="width:100px" placeholder="1-660"/>
                <span>&nbsp;分钟</span>
              </span>
              <span v-if="editData.firstRecallTime" class="tw-ml-[30px]">
                <span>第二次：</span>
                <el-input-number v-model="editData.secondRecallTime" :controls="false" :precision="0" :min="1" :max="660" style="width:100px" placeholder="1-660"/>
                <span>&nbsp;分钟</span>
              </span>
            </div>
          </el-form-item>
        </template>
        <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('scriptStringId'))" prop="hangUpSms" :label-width="1">
          <div class="tw-flex tw-items-center">
            <div class="tw-w-[89px] tw-text-right">挂机短信：</div>
            <el-button link type="primary" @click="addHangUpSms">新增</el-button>
            <el-button
              v-if="editData.hangUpSms && editData.hangUpSms.length > 0"
              link type="danger"
              @click="delAllHangUpSms"
            >清空</el-button>
          </div>
          <div class="tw-w-full sms-sort-box">
            <div v-show="editData.hangUpSms && editData.hangUpSms.length > 0" v-for="(item, index) in editData.hangUpSms||[]" :key="index" class="tw-my-[4px] tw-pl-[12px] tw-flex tw-items-center tw-gap-[4px]">
              <div class="handle tw-cursor-pointer">
                <el-icon><SvgIcon name="drag" color="var(--primary-black-color-400)"/></el-icon>
              </div>
              <el-select
                v-model="editData.hangUpSms![index].intentionType"
                style="width:120px"
                class="tw-grow-0"
                clearable
                placeholder="意向分类"
              >
                <el-option v-for="item in intentionClassOptions" :key="item" :label="item" :value="item"/>
              </el-select>
              <SelectBox 
                v-model:selectVal="editData.hangUpSms![index].labelIds"
                :options="intentionTagOptions||[]"
                name="labelName"
                val="labelName"
                :limitNum="20"
                placeholder="意向标签"
                style="width:320px"
                canSelectAll
                filterable
                multiple
              >
              </SelectBox>
              <el-select
                v-model="editData.hangUpSms![index].smsTemplateId"
                clearable
                filterable
                placeholder="短信模板"
                style="width:400px"
              >
                <el-option
                  v-for="item in smsTemplateListInIndustry"
                  :key="item.id"
                  :label="item.templateName"
                  :value="item.id"
                />
              </el-select>
              <el-button link type="danger" @click="delHangUpSms(index)">删除</el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="editData.hangUpSms && editData.hangUpSms.length > 0" label="发送排除：" prop="hangUpExcluded">
          <SelectBox 
            v-model:selectVal="editData.hangUpExcluded"
            :options="intentionTagOptions||[]"
            class="tw-basis-4 tw-grow"
            name="labelName"
            val="labelName"
            :limitNum="20"
            placeholder="请选择挂机短信排除标签"
            :filterable="true" 
            :multiple="true"
          >
          </SelectBox>
        </el-form-item>
        <el-form-item v-if="!(props.type==='batch' && !selectItems.includes('tenantBlackList'))" label="黑名单：" prop="tenantBlackList">
          <SelectBox 
            v-model:selectVal="editData.tenantBlackList"
            :options="blacklistOptions||[]"
            class="tw-grow"
            name="groupName"
            val="id"
            :limitNum="20"
            placeholder="请选择黑名单"
            filterable
            multiple
          >
          </SelectBox>
        </el-form-item>
        <el-form-item v-if="props.type === 'template' || (props.type === 'task' && !editData.id)" label="屏蔽地区：">
          <el-button link type="primary" @click="showBlock">{{ blockInfo || '无' }}</el-button>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer v-if="getPermission">
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :icon="Select" :loading="loading" type="primary" @click="confirm" >{{ editData?.id || props.type==='batch' ? '修改' : '创建' }}</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="blockVisible"
    width="960px"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">屏蔽地区</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
    >
    <CitySettingBox
      :taskRestrictData="taskRestrictData"
      :selectedOperatorList="selectedOperatorList"
      @update:data="handleCityUpdate"
    />
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="blockVisible=false" :icon="CloseBold">取消</el-button>
        <el-button type="primary" @click="confirmBlock" :icon="Select">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, defineAsyncComponent, onUnmounted, nextTick, toRaw, shallowRef } from 'vue';
import { TemplateBaseItem, TaskTypeEnum, CallTeamHandleEnum, CallTeamPushEnum, OccupyRateEnum, TaskManageOrigin } from '@/type/task'
import { ElMessage, } from 'element-plus'
import { RestrictModal, RestrictModalOrigin, OperatorEnum } from '@/type/common'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance, } from 'element-plus'
import { useTaskStore } from '@/store/taskInfo'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { MerchantScriptInfo, MerchantInfo, SmsTemplateStatusEnum, } from '@/type/merchant'
import { pickAttrFromObj, deduplicateBaseArray, enum2Options } from '@/utils/utils'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import { SeatTeam } from '@/type/seat'
import Confirm from '@/components/message-box'
import to from 'await-to-js';
import dayjs from 'dayjs'
import { LabelItem, } from '@/type/IntentionType'
import { scriptIntentionModel } from '@/api/speech-craft'
import { merchantModel, merchantSmsTemplateModel, } from '@/api/merchant'
import SelectBox from '@/components/SelectBox.vue'
import MutiTimeRangePickerBox from '@/components/MutiTimeRangePickerBox.vue'
import Sortable from 'sortablejs';
import { aiOutboundTaskTemplateModel } from '@/api/ai-report'
import { userModel } from '@/api/user'
import { BlackListGroupItem, } from '@/type/dataFilter'
import { merchantBlacklistModel } from '@/api/data-filter'

const CitySettingBox = defineAsyncComponent({ loader:() => { return import('@/components/CitySettingBox.vue')}})

/** props && emits */
const props = defineProps<{
  visible: boolean;
  dataRow: TemplateBaseItem | null;
  groupId?: string; // 运营端-任务模板页面需传groupId，商户端默认使用当前用户的groupId
  type: 'task' | 'batch' | 'template';
}>();
const emits = defineEmits(['update:visible', 'confirm'])

/** 全局变量 */
const userStore = useUserStore();
const loading = ref(false)
const taskStore = useTaskStore()

/** 批量操作选择修改项 */
// 支持修改的全量
const itemsList = computed (() => {
  const res = [
    {name: '执行话术', value: 'scriptStringId' },
    {name: '拨打时段', value: 'startWorkTimeList' },
    {name: '自动补呼', value: 'autoReCall' },
    {name: '黑名单', value: 'tenantBlackList' },
  ]
  if (editData.taskType === TaskTypeEnum['人机协同']) {
    res.push(...[
      {name: '坐席组', value: 'callTeamIds' },
      {name: '坐席推送方式', value: 'callTeamPushType' },
      {name: '坐席处理方式', value: 'callTeamHandleType' },
      {name: '集线比', value: 'lineRatio' },
      {name: '坐席占用等级', value: 'occupyRate' },
      {name: '虚拟坐席系数', value: 'virtualSeatRatio' },
    ])
  }
  return res
})
// 已选的修改项
const selectItems = shallowRef<(keyof TemplateBaseItem)[]>([])

// 已选的修改项变化处理函数
const handleSelectItems = (v: string[]) => {
  if (v.includes('callRatioType') || v.includes('firstRecallTime') && !v.includes('autoReCall')) {
    selectItems.value.push('autoReCall')
  }
  selectItems.value = deduplicateBaseArray(selectItems.value)
}

/** 变量 */
const editData = reactive<TemplateBaseItem>(props.dataRow || new TaskManageOrigin(TaskTypeEnum['AI外呼']))
const taskTypeStr = shallowRef(props.dataRow?.taskType===TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务')
const scriptOrigin = ref<string | null>(null)
const taskTypeOption = enum2Options(TaskTypeEnum)
const callTeamHandleOption = enum2Options(CallTeamHandleEnum)
const callTeamPushList = enum2Options(CallTeamPushEnum)
const occupyRateList = enum2Options(OccupyRateEnum)
const dialogVisible = ref(props.visible)
const speechCraftAllList = ref<MerchantScriptInfo[] | null>([])
const speechCraftMixList = ref<MerchantScriptInfo[] | null>([])
const speechCraftList = computed(() => editData.taskType === TaskTypeEnum['人机协同'] ? speechCraftMixList.value : speechCraftAllList.value)
const taskTemplateList = ref<TemplateBaseItem[] | null>([])
const callTeamList = ref<SeatTeam[] | null>([])
const smsTemplateList = ref<{id:number, templateName:string, secondIndustry: string}[] | null>([]) // 短信模板列表
const smsTemplateListInIndustry = computed(() => smsTemplateList.value?.filter(item => !item.secondIndustry || item.secondIndustry === currentSencondIndustry.value) || [])

const title = computed(() => {
  const taskTitleObj = {
    task: `${editData?.id ? '编辑' : '新建'}${taskTypeStr.value}`,
    template: `${editData?.id ? '编辑' : '新建'}任务模板`,
    batch: `批量编辑${taskTypeStr.value}`
  }
  return taskTitleObj[props.type]
})
// 用户权限获取
const getPermission = computed(() => {
  const permissions = userStore.permissions[routeMap[taskTypeStr.value].id]
  return !(props.type === 'task' && ((editData?.id && !permissions?.includes(routeMap[taskTypeStr.value].permissions['编辑任务'])) || (!editData?.id && !permissions?.includes(routeMap[taskTypeStr.value].permissions['创建任务']))))
})
// 是否dev环境
const isDev = import.meta.env.MODE.includes('development') || import.meta.env.MODE.includes('test')

// 任务运营时间，组件暴露变化处理函数
const handleTimeUpdate = (sList: string[], eList: string[]) => {
  editData.startWorkTimeList = sList || []
  editData.endWorkTimeList = eList || []
  editRef.value && editRef.value.clearValidate()
}

/** 任务类型变化处理，更新话术模板列表，清空话术选择 */
const handleTaskTypeChange = async () => {
  editData.scriptStringId = undefined
  currentSencondIndustry.value = null
  if (editData.taskType === TaskTypeEnum['人机协同']) {
    callTeamList.value = await taskStore.getCallTeamListOptions()
  }
}

// 根据话术，修改话术的标签和意向
const intentionTagOptions = ref<LabelItem[] | null>([]) // 标签列表
const intentionClassOptions = ['A', 'B', 'C', 'D', 'E', 'F', 'G'] // 意向分类列表
const blacklistOptions = ref<BlackListGroupItem[] | null>([])
const currentSencondIndustry = ref<string | null>(null)
const currentSmsTriggerNames = ref<string[] | null>(null)
const handleScriptChange = async () => {
  if (!editData.scriptStringId) {
    intentionTagOptions.value = []
  } else {
    const row = speechCraftList.value?.find(item => item.scriptStringId === editData.scriptStringId)
    if (row && row.scriptId) {
      currentSencondIndustry.value = row.secondIndustry || null
      currentSmsTriggerNames.value = row.smsTriggerNames ? JSON.parse(row.smsTriggerNames) : null
      if (currentSmsTriggerNames.value && currentSmsTriggerNames.value.length > 0) {
        editData.scriptSms =  currentSmsTriggerNames.value.map(item => {
          return {
            triggerName: item,
            smsTemplateId: editData.scriptSms?.find(item1 => item1.triggerName === item)?.smsTemplateId || undefined
          }
        })
      } else {
        editData.scriptSms = undefined
      }
      intentionTagOptions.value = await scriptIntentionModel.findLabelList(row.scriptId as number)
    } else {
      intentionTagOptions.value = []
    }
  }
}
// 批量操作，选择话术类型切换
const scriptSelectType = ref(0) // 0：执行话术 1：执行模板
const scriptTemplateId = ref<number | undefined>(undefined)
const handleSelectTypeChange = () => {
  editData.scriptStringId = undefined
  scriptTemplateId.value = undefined
  handleScriptChange()
}
const handleSelectTemplateChange = () => {
  const row = taskTemplateList.value?.find(item => item.id === scriptTemplateId.value)
  if (row) {
    editData.scriptStringId = row.scriptStringId
    handleScriptChange()
  }
}

/** 挂机短信模块 */
const delHangUpSms = (index: number) => {
  editData.hangUpSms && editData.hangUpSms?.splice(index, 1)
  editRef.value?.clearValidate()
}
const addHangUpSms = () => {
  if (!editData.scriptStringId) return ElMessage.warning('请先选择执行话术')
  if (!editData.hangUpSms || !editData.hangUpSms.length) {
    // 特殊逻辑：未选择挂机短信时，新增时，初始化挂机短信默认项ABCD
    editData.hangUpSms = [
      { intentionType: 'A', triggerOrder: 0, labelIds: [], smsTemplateId: undefined },
      { intentionType: 'B', triggerOrder: 1, labelIds: [], smsTemplateId: undefined },
      { intentionType: 'C', triggerOrder: 2, labelIds: [], smsTemplateId: undefined },
      { intentionType: 'D', triggerOrder: 3, labelIds: [], smsTemplateId: undefined },
    ]
  } else {
    editData.hangUpSms?.push({
      intentionType: undefined,
      labelIds: [],
      smsTemplateId: undefined,
      triggerOrder: editData.hangUpSms.length
    })
  }
  editRef.value?.clearValidate()
}

const delAllHangUpSms = () => {
  editData.hangUpSms = []
  editRef.value?.clearValidate()
}
// 挂机短信拖拽
const sortableSmsDom = ref()
const initSortable = async () => {
  if (sortableSmsDom.value) {
    sortableSmsDom.value.destroy()
    sortableSmsDom.value = null
  }
  const dom1 = document.querySelector('.sms-sort-box')  as HTMLElement
  dom1 && (sortableSmsDom.value = new Sortable(
    dom1, {
    animation: 300,
    handle: ".handle",
    forceFallback: true,
    onEnd: async (evt) => {
      let newIndex = evt.newIndex as number
      let oldIndex = evt.oldIndex as number
      if (oldIndex !== newIndex && editData.hangUpSms) {
        const data = toRaw(editData.hangUpSms)
        data.splice(newIndex, 0, data.splice(oldIndex, 1)[0]);
        editData.hangUpSms = []
        await nextTick()
        editData.hangUpSms = data
      }
    },
  }))
}

/** 屏蔽地区模块 */
const blockVisible = ref(false)
const selectedOperatorList = ref<("全部" | OperatorEnum)[]>([])
const taskRestrictData = reactive<RestrictModal>(new RestrictModalOrigin())
// 进入屏蔽城市查看窗口
const showBlock = () => {
  const { allRestrictProvince, allRestrictCity, ydRestrictProvince, ydRestrictCity, ltRestrictProvince, ltRestrictCity, dxRestrictCity, dxRestrictProvince, virtualRestrictCity, virtualRestrictProvince, unknownRestrictCity, unknownRestrictProvince } = editData
  Object.assign(taskRestrictData, {
    allRestrictProvince, allRestrictCity, ydRestrictProvince, ydRestrictCity, ltRestrictProvince, ltRestrictCity, dxRestrictCity, dxRestrictProvince, virtualRestrictCity, virtualRestrictProvince, unknownRestrictCity, unknownRestrictProvince
  })
  selectedOperatorList.value = []
  blockVisible.value = true
}
const handleCityUpdate = (data: RestrictModal , operators: ('全部' | OperatorEnum)[]) => {
  Object.assign(taskRestrictData, data)
  selectedOperatorList.value = operators
}
const confirmBlock = () => {
  blockVisible.value = false
  Object.assign(editData, taskRestrictData)
  selectedOperatorList.value = []
}
const blockInfo = computed(() => {
  const res: string[] = []
  if (props.type !== 'template' && !(props.type === 'task' && !editData.id)) return '-'
  // 全部
  if (editData.allRestrictProvince && editData.allRestrictCity) {
    const num1 = editData.allRestrictProvince.split(',')?.length || 0
    const num2 = editData.allRestrictCity.split(',')?.length || 0
    res.push(`全部：${num1}省${num2}市`)
  }
  // 移动
  if (editData.ydRestrictProvince && editData.ydRestrictCity) {
    const num1 = editData.ydRestrictProvince.split(',')?.length || 0
    const num2 = editData.ydRestrictCity.split(',')?.length || 0
    res.push(`移动：${num1}省${num2}市`)
  }
  // 联通
  if (editData.ltRestrictProvince && editData.ltRestrictCity) {
    const num1 = editData.ltRestrictProvince.split(',')?.length || 0
    const num2 = editData.ltRestrictCity.split(',')?.length || 0
    res.push(`联通：${num1}省${num2}市`)
  }
  // 电信
  if (editData.dxRestrictProvince && editData.dxRestrictCity) {
    const num1 = editData.dxRestrictProvince.split(',')?.length || 0
    const num2 = editData.dxRestrictCity.split(',')?.length || 0
    res.push(`电信：${num1}省${num2}市`)
  }
  // 未知
  if (editData.unknownRestrictProvince && editData.unknownRestrictCity) {
    const num1 = editData.unknownRestrictProvince.split(',')?.length || 0
    const num2 = editData.unknownRestrictCity.split(',')?.length || 0
    res.push(`未知：${num1}省${num2}市`)
  }
  return  res.join('，')
})

/** 表单提交、取消 */
const editRef = ref<FormInstance  | null>(null)
const validTimeList = (rule: any, value: any, callback: any) => {
  if (!editData.startWorkTimeList?.length || !editData.endWorkTimeList?.length) {
    return callback(new Error('时长选择不可为空'))
  }
  editData.startWorkTimeList?.forEach((item1, index1: number) => {
    if (!editData.startWorkTimeList![index1] || !editData.endWorkTimeList![index1]) {
      return callback(new Error('开始结束时间不能为空'))
    }
    // @ts-ignore
    if (editData.startWorkTimeList[index1]! < merchantTimeRange.value[0] || editData.endWorkTimeList[index1]! > merchantTimeRange.value[1]) {
      return callback(new Error(`选择时间超出可选范围(${merchantTimeRange.value[0]}至${merchantTimeRange.value[1]})`))
    }
    if (editData.startWorkTimeList![index1] == editData.endWorkTimeList![index1]) {
      return callback(new Error('开始结束时间不能相同'))
    }
    editData.startWorkTimeList?.forEach((item2, index2: number) => {
      if (index1 >= index2) {
        return
      }
      if (!editData.startWorkTimeList![index2] || !editData.endWorkTimeList![index2]) {
        return callback(new Error('开始结束时间不能为空'))
      }
      if (!(editData.startWorkTimeList![index1]! >= editData.endWorkTimeList![index2]! || editData.startWorkTimeList![index2]! >= editData.endWorkTimeList![index1]!)) {
        return callback(new Error('时间存在交集，请检查！'))
      }
    })
  })
  return callback()
}
const rules = {
  scriptStringId: [
    { required: true, message: '请选择执行话术', trigger: 'change' },
  ],
  comment: [
    { required: true, message: '请输入备注', trigger: 'blur' },
  ],
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 100, message: '任务名称长度必须在1-100个字符', trigger: 'blur' }
  ],
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 1, max: 100, message: '任务名称长度必须在1-100个字符', trigger: 'blur' }
  ],
  startWorkTimeList: [
    { type: 'array', required: true, message: '请选择工作时间范围', trigger: 'change' },
    { validator: validTimeList, trigger: 'change'}
  ],
  callRatioType: [
    { required: true, message: '请选择资源分配方式', trigger: 'change' },
  ],
  firstRecallTime: [
    { required: true, message: '请输入第一次补呼间隔', trigger: 'blur' },
  ],
  lineRatio: [
    { required: true, message: '请输入集线比', trigger: ['blur', 'change'] },
  ],
  callTeamPushType: [
    { required: true, message: '请选择坐席推送方式', trigger: ['blur', 'change'] },
  ],
  callTeamHandleType: [
    { required: true, message: '请选择坐席处理方式', trigger: ['blur', 'change'] },
  ],
  occupyRate: [
    { required: true, message: '请选择坐席占用等级', trigger: ['blur', 'change'] },
  ],
  virtualSeatRatio: [
    { required: true, message: '请输入虚拟坐席系数，0-100', trigger: ['blur', 'change'] },
  ],
  callTeamIds: [
    { required: true, message: '请选择坐席组', trigger: ['blur', 'change'] },
  ],
  scriptSms: [
    {
    validator: (rule: any, value: any, callback: any) => {
        let errMsg = ''
        editData.scriptSms && editData.scriptSms.map(item => {
          if (!item.smsTemplateId) {
            errMsg = '触发短信模板不可为空'
          }
        })
        return errMsg ? callback(new Error(errMsg)) : callback()
      },
      trigger: 'change'
    },
  ],
  hangUpSms: [
    {
      validator: (rule: any, value: any, callback: any) => {
        let errMsg = ''
      
        editData.hangUpSms && editData.hangUpSms.map((item, index) => {
          if (!item.intentionType && !item.labelIds?.length ) {
            errMsg = '挂机短信出发意向和标签至少选择一个'
          }
          if (!item.smsTemplateId) {
            errMsg = '挂机短信模板不可为空'
          }
        })
        return errMsg ? callback(new Error(errMsg)) : callback()
      },
      trigger: 'change'
    },
  ],
}

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      // 检查选择的话术是否存在/绑定（主要针对历史数据）
      const script = speechCraftList.value?.find(item => item.scriptStringId == editData.scriptStringId)
      if (!script && !(props.type === 'batch' && !selectItems.value.includes('scriptStringId'))) {
        return ElMessage.error('您选择的话术不存在')
      }
      // 批量操作，检查是否选择操作项
      if (props.type === 'batch' && !selectItems.value.length) {
        cancel()
        return
      }
      loading.value = true
      editData.speechCraftName = (script?.scriptName || '') as string
      editData.speechCraftId = (script?.scriptId ?? -1) as number
      editData.version = (script?.version  ?? -1) as number
      const { id = undefined, taskIds = undefined, templateName, comment, templateStatus = undefined,
        taskName, speechCraftName, speechCraftId, scriptStringId, version, taskType,
        callTeamIds, callTeamPushType, callTeamHandleType, lineRatio, occupyRate, virtualSeatRatio, // 人机协同参数
        autoReCall, callRatioType, firstRecallTime, secondRecallTime, nextDayCall,
        startWorkTimeList, endWorkTimeList, hangUpSms, hangUpExcluded, scriptSms, tenantBlackList } = editData
      const params: TemplateBaseItem = {
        speechCraftName, speechCraftId, scriptStringId, version, autoReCall, callRatioType, firstRecallTime, secondRecallTime,
        startWorkTimeList, endWorkTimeList, taskType, hangUpSms, scriptSms, tenantBlackList,
      }
      // 自动补呼为否，清空补呼参数
      if (autoReCall == 0) {
        params.callRatioType = undefined
        params.firstRecallTime = undefined
        params.secondRecallTime = undefined
      }
      // 挂机短信有值，挂机短信排除才生效
      if (hangUpSms && hangUpSms.length > 0) {
        params.hangUpExcluded = hangUpExcluded || undefined
      }
      if (props.type === 'template') {
        params.templateName = templateName
        params.comment = comment
        params.templateStatus = templateStatus || undefined
      }
      // 仅创建任务、创建编辑任务模板时有屏蔽地区
      if (props.type === 'template' || (props.type === 'task' && !editData.id)) {
        Object.assign(params, pickAttrFromObj(editData, restrictFields))
      }
      // 创建任务模板，二次确认是否配置短信
      if (props.type === 'template' && !editData.id) {
        const [err] = await to(Confirm({
          text: `<p class="tw-text-[var(--el-color-danger)]"></p>请再次确认挂机短信是否配置正确!
            <p>如不使用白泽发送挂机短信，请忽略</p>
          `,
          type: 'warning',
          title: '新建模板二次确认',
          confirmText: '确定',
        }))
        if (err) {
          loading.value = false
          return
        }
      }

      if (props.type === 'template' || props.type === 'task') {
        params.taskName = taskName
        params.nextDayCall = nextDayCall || 0
        params.id = id
        if (taskType === TaskTypeEnum['人机协同']) {
          Object.assign(params, {callTeamIds, callTeamPushType, callTeamHandleType, lineRatio, occupyRate, virtualSeatRatio})
        }
      }
      if (props.type === 'batch') {
        params.id = undefined
        params.taskIds = taskIds
        Object.assign(params, pickAttrFromObj(editData, selectItems.value))
        if (!selectItems.value.includes('scriptStringId')) {
          params.speechCraftName = undefined
          params.speechCraftId = undefined
          params.scriptStringId = undefined
          params.version = undefined
        }
        if (!selectItems.value.includes('startWorkTimeList')) {
          params.startWorkTimeList = undefined
          params.endWorkTimeList = undefined
        }
        if (!selectItems.value.includes('autoReCall')) {
          params.autoReCall = undefined
          params.callRatioType = undefined
          params.firstRecallTime = undefined
          params.secondRecallTime = undefined
        }
        if (!selectItems.value.includes('tenantBlackList')) {
          params.tenantBlackList = undefined
        }
        // 批量编辑任务话术时增加提示
        if (selectItems.value.includes('scriptStringId')) {
          const [err1] = await to(Confirm({
            text: `请确保修改后任务中的变量匹配，否则无法开启任务?`,
            type: 'warning',
            title: `修改确认`,
            confirmText: '确认修改',
          }))
          if (err1) return (loading.value = false)
        }
      }
      if (props.type === 'template' && id && scriptOrigin.value !== speechCraftName && scriptOrigin.value) {
        let exsitNum = 0
        if (userStore.accountType == 1) {
          const [_, res] = await to(aiOutboundTaskModel.findTaskList({
            startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            speechCraftName: scriptOrigin.value,
            startPage: 0,
            pageNum: 10,
          }))
          exsitNum = res?.total || 0
        } else {
          const [_, res] = await to(monitorStatisticModel.findMonitorListByCondition({
            account: userStore.account,
            ifFindAll: '1',
            startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            speechCraftName: scriptOrigin.value,
          })) as [any, any[]]
          exsitNum = res?.length || 0
        }

        if (exsitNum > 0) {
          Confirm({
            text: `检测到模版话术发生改变。请注意，此操作不会改变历史任务的话术！`,
            type: 'warning',
            title: '请注意',
            confirmText: '确定修改',
          }).then(async () => {
            emits('confirm', params)
          }).catch(() => {
            loading.value = false
          })
        } else {
          emits('confirm', params)
        }
      } else {
        emits('confirm', params)
      }
    } else {
      loading.value = false
    }
  })
}

/** 初始化数据 */
// 地区字段
const restrictFields: (keyof RestrictModal)[] = [
  'allRestrictProvince',
  'allRestrictCity',
  'ydRestrictProvince',
  'ydRestrictCity',
  'ltRestrictProvince',
  'ltRestrictCity',
  'dxRestrictCity',
  'dxRestrictProvince',
  'unknownRestrictCity',
  'unknownRestrictProvince',
];
const merchantTimeRange = shallowRef<string[]>(['08:00', '20:00'])
const init = async () => {
  loading.value = true
  const userInfo = useUserStore()
  const groupId = props.groupId || userInfo.groupId
  const tenantId = props.groupId ? Number(props.groupId?.split('_')[1]) : userInfo.tenantId
  const [err1, data1] = await to(merchantModel.getScriptList({ groupId: groupId || '', id: tenantId || undefined })) as [any, MerchantInfo]
  speechCraftAllList.value = ((data1||[]).relatedScriptList?.filter(item => item.active == 'ACTIVE') as MerchantScriptInfo[]) || []
  const [err2, data2] = await to(merchantModel.getManualScriptList({ groupId: groupId || '', id: tenantId || undefined })) as [any, MerchantInfo]
  speechCraftMixList.value  = ((data2||[]).relatedScriptList?.filter(item => item.active == 'ACTIVE') as MerchantScriptInfo[]) || []
  const [err3, data3] = await to(merchantSmsTemplateModel.getSmsTemplate({ groupId: groupId || '', templateStatus: SmsTemplateStatusEnum['启用']})) as [any, {id:number, templateName:string, secondIndustry: string}[]]
  smsTemplateList.value = (data3 || []).flatMap(item => !!item.id && !!item.templateName ? [{ id: item.id, templateName: item.templateName, secondIndustry: item.secondIndustry || '' }] : [])
  const [err4, data4]= await to(props.groupId ? merchantBlacklistModel.getGroupListByGroupId({groupId: props.groupId}) : merchantBlacklistModel.getGroupList({}))
  blacklistOptions.value = data4 || []
  if (props.groupId) {
    const [err, res] = await to (userModel.getMerchantTimeRange({ groupId: props.groupId })) as [any, string]
    merchantTimeRange.value = res?.split(',') || ['08:00', '20:00']
  } else {
    await userInfo.updateUserTimeRange()
    merchantTimeRange.value = userInfo.timeRange
  }
  if (dialogVisible.value) {
    taskTypeStr.value = props.dataRow?.taskType===TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务'
    scriptOrigin.value = props.dataRow?.speechCraftName || ''
    Object.assign(editData, JSON.parse(JSON.stringify(props.dataRow)))
    editRef.value && editRef.value.clearValidate()
    editData.callTeamPushType = editData.callTeamPushType || CallTeamPushEnum['轮循']
    editData.nextDayCall = editData.nextDayCall || 0
    if (!editData.id || props.type === 'batch') {
      editData.occupyRate = editData.occupyRate || OccupyRateEnum['中']
      editData.virtualSeatRatio = editData.virtualSeatRatio || 0
      const startWorkTimeList = props.dataRow?.startWorkTimeList || []
      const endWorkTimeList = props.dataRow?.endWorkTimeList || []
      editData.startWorkTimeList = startWorkTimeList.length ? startWorkTimeList : ['09:00', '13:30']
      editData.endWorkTimeList = endWorkTimeList.length ? endWorkTimeList : ['12:00', '19:00']
    }
    if (props.type === 'batch') {
      selectItems.value = []
    }
    if (props.type !== 'template') {
      taskTemplateList.value = await aiOutboundTaskTemplateModel.search({
        taskType: editData.taskType,
      }) || []
      scriptSelectType.value = 0
      scriptTemplateId.value = undefined
    }
    if (editData.taskType === TaskTypeEnum['人机协同']) {
      callTeamList.value = await taskStore.getCallTeamListOptions()
    }
    if (props.type === 'template' || (props.type === 'task' && !editData.id)) {
      restrictFields.forEach((field) => {
        editData[field] = props.dataRow ? props.dataRow[field] : null;
      });
    }
    handleScriptChange()
    initSortable()
  }
  loading.value = false
}

/** 清空数据 */
const clearData = () => {
  sortableSmsDom.value?.destroy()
  sortableSmsDom.value = null
  currentSencondIndustry.value = null
  currentSmsTriggerNames.value = null
  scriptOrigin.value = null
  speechCraftAllList.value = null
  speechCraftMixList.value = null
  smsTemplateList.value = null
  callTeamList.value = null
  intentionTagOptions.value = null
  Object.assign(editData, new TaskManageOrigin(TaskTypeEnum['AI外呼']))
}
watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
  } else {
    clearData()
  }
})

onUnmounted(() => {
  clearData()
})

</script>

<style lang="postcss" type="text/postcss" scoped>

</style>
