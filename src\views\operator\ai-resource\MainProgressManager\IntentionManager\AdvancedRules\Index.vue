<template>
  <div class="rules-container">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <span class="info-title tw-mr-1">请严格按照话术制作规范设置高级规则，勿随意添加</span>
      <div class="tw-flex tw-items-end">
        <el-checkbox v-model="forbidTagInProcess" :true-label="1" :false-label ="0" :disabled="isChecked" label="禁用流程中分类" @change="saveSetting"/>
        <el-checkbox v-model="orderFirst" class="tw-pr-1" :true-label="1" :false-label ="0" :disabled="isChecked" label="规则顺序优先" @change="saveSetting"/>
        <el-button @click="search()">刷新数据</el-button>
        <el-button v-if="!isChecked" @click="loadRules()">导入高级规则</el-button>
        <el-button v-if="!isChecked" type="primary" :icon="Plus" @click="edit()">新增高级规则</el-button>
      </div>
    </div>
    <el-table
      :data="tableData"
      ref="tableRef"
      id="label-draggable-table"
      style="width: 100%"
      class="tw-grow"
      row-key="id"
      border
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column v-if="!isChecked" label=" " fixed="left" align="left"  width="36">
        <div class="handle tw-cursor-pointer"><el-icon ><Switch /></el-icon></div>
      </el-table-column>
      <el-table-column property="order" align="center" fixed="left" width="60" label="优先级"></el-table-column>
      <el-table-column property="ruleName" align="left" fixed="left" width="120" label="规则名称"></el-table-column>
      <el-table-column align="left" label="规则满足条件"  min-width="300">
        <template #default="{ row, $index }">
          <div v-if="row.matchText && row.matchText.length" v-for="(item, index) in row.matchText||[]" :key="`${$index}-${index}`">
            <el-divider v-if="index" border-style="dashed">or</el-divider>
            <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
              {{ item2 || '-' }}
            </li>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column align="left" label="规则排除条件"  min-width="300">
        <template #default="{ row, $index }">
          <div v-if="row.excludeText && row.excludeText.length" v-for="(item, index) in row.excludeText||[]" :key="`${$index}-${index}`">
            <el-divider v-if="index" border-style="dashed">or</el-divider>
            <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
              {{ item2 || '-' }}
            </li>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column align="center" property="intentionLevelName" width="80" label="分类结果" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column align="left" property="intentionTagName" width="120" label="标签" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column label="操作" fixed="right" align="right" :width="isChecked ? 70 :160">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">{{!isChecked ? '编辑' : '查看'}}</el-button>
          <el-button v-if="!isChecked" type="primary" link @click="edit(row, true)">复制</el-button>
          <el-button v-if="!isChecked" type="danger" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
  </div>
  <LoadRulesDialog
    v-model:visible="loadRulesVisible"
    @confirm="confirm"
  />
  <RuleEditDialog
    v-model:visible="addDialogVisible"
    :editData="editData"
    @confirm="confirm"
  ></RuleEditDialog>
</template>

<script lang="ts" setup>
import { scriptIntentionModel, } from '@/api/speech-craft'
import { Plus, Switch, } from '@element-plus/icons-vue'
import { ref, onActivated, onDeactivated, } from 'vue'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import Sortable from "sortablejs";
import { useScriptStore } from '@/store/script'
import RuleEditDialog from './RuleEditDialog.vue'
import { AdvancedRulesItem, } from '@/type/IntentionType'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { formatterEmptyData } from '@/utils/utils'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'
import { translateMultRules } from '@/components/script-rules/constant'
import LoadRulesDialog from './LoadRulesDialog.vue'
import { trace } from '@/utils/trace'

const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked

// 表格区
const tableData = ref<AdvancedRulesItem[] | null>([])
const forbidTagInProcess = ref<0|1>(0)
const orderFirst = ref<0|1>(0)
// 搜索区
const search = async () => {
  loading.value = true
  const [_, data] = await to(scriptIntentionModel.findRulesList({
    scriptId: editId
  })) as [any, AdvancedRulesItem[]]
  tableData.value = (data || []).map(item => {
    const matchText = translateMultRules(item.matchConditionList)
    const excludeText = translateMultRules(item.excludeConditionList)
    return {
      ...item,
      matchText,
      excludeText,
    }
  })
  loading.value = false
}
const findSetting = async () => {
  const setting = await scriptIntentionModel.findRulesSetting({
    id: editId
  }) as {
    FORBID_TAG_IN_PROCESS: 0 | 1,
    ORDER_FIRST: 0 | 1,
  }
  forbidTagInProcess.value = setting.FORBID_TAG_IN_PROCESS || 0
  orderFirst.value = setting.ORDER_FIRST || 0
}
const saveSetting = async () => {
  loading.value = true
  const [err, _] = await to(scriptIntentionModel.saveRulesSetting({
    id: editId,
    forbidTagInProcess: forbidTagInProcess.value,
    orderFirst: orderFirst.value,
  }))
  !err && ElMessage({
    message: '操作成功',
    type: 'success',
  })
  loading.value = false
}

// 编辑
const editData = ref<AdvancedRulesItem | null>(null)
const addDialogVisible = ref(false)
const edit = (row?: AdvancedRulesItem, isCopy: boolean = false) => {
  if (row && row.id) {
    editData.value = JSON.parse(JSON.stringify(row));
    if (isCopy && editData.value) {
      editData.value.id = undefined
      editData.value.ruleName = editData.value.ruleName + '（复制）'
      editData.value.matchConditionList = editData.value.matchConditionList?.map(item => {
        item.conditionUniqueId = undefined
        item.advancedRuleConditionDTOList = item.advancedRuleConditionDTOList.map(item2 => {
          item2.conditionUniqueId = undefined
          item2.id = undefined
          return item2
        })
        return item
      })
      editData.value.excludeConditionList = editData.value.excludeConditionList?.map(item => {
        item.conditionUniqueId = undefined
        item.advancedRuleConditionDTOList = item.advancedRuleConditionDTOList.map(item2 => {
          item2.conditionUniqueId = undefined
          item2.id = undefined
          return item2
        })
        return item
      })
    }
  } else {
    editData.value = null
  }
  addDialogVisible.value = true
}

// 从生效中的话术导入话术中的高级规则
const loadRulesVisible = ref(false)
const loadRules  = () => {
  loadRulesVisible.value = true
}

const confirm = () => {
  search()
}

// 拖动
const tableRef = ref(null)
const sortableDom = ref<Sortable | null>(null)
const initTable = async () => {
  loading.value = true
  await findSetting()
  search()
  loading.value = false
}
const initSortable = () => {
  !isChecked && (sortableDom.value = Sortable.create(
    document.querySelector('#label-draggable-table .el-table__body tbody') as HTMLElement, {
    animation: 300,
    sort: !isChecked,
    // draggable: "tr",
    handle: ".handle",
    onEnd: async (evt) => {
      if (!tableData.value) return
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      const currRow = tableData.value.splice(oldIndex, 1)[0];
      tableData.value.splice(newIndex, 0, currRow);
      const list = tableData.value.map(item => item.id!) || []
      loading.value = true
      trace({ page: `话术编辑-意向分类-高级规则-排序(${editId})`, params: { advanceRulesIds: list } })
      await to(scriptIntentionModel.saveRulesOrder({
        advanceRulesIds: list,
        scriptId: editId,
      }))
      search()
      loading.value = false
    },
  }))
}
onBeforeRouteLeave(() => {
  sortableDom.value?.destroy()
  sortableDom.value = null
  // @ts-ignore
  tableData.value = null
})
// 操作区
const delAction = async (id: number) => {
  loading.value = true
  trace({ page: `话术编辑-意向分类-高级规则-删除(${editId})`, params: { id } })
  const [err] = await to(scriptIntentionModel.deleteOneRule({ id }))
  !err && ElMessage.success('删除成功')
  search()
  loading.value = false
}
const del = (row: AdvancedRulesItem) => {
  Confirm({
    text: `您确定要删除吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}

// 执行
onActivated(() => {
  initTable()
  initSortable()
})
onDeactivated(() => {
  sortableDom.value?.destroy()
  sortableDom.value = null
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.rules-container {
  width: 100%;
  height: calc(100vh - 250px);
  padding: 16px 12px 0;
  position: relative;
  box-sizing: border-box;
  padding-top: 10px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  font-size: var(--el-font-size-base);
  :deep(.el-checkbox__label) {
    --el-checkbox-font-size: 13px;
  }
  .el-table {
    font-size: var(--el-font-size-base);
    :deep(.cell) {
      padding: 0 8px;
      .el-divider--horizontal {
        margin: 12px 0;
      }
    }
  }
}
</style>
