import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import * as path from "path";
import Components from "unplugin-vue-components/vite";
import Icons from "unplugin-icons/vite";
import IconsResolver from "unplugin-icons/resolver";
import { AntDesignVueResolver, ElementPlusResolver } from "unplugin-vue-components/resolvers";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import commonjs from "rollup-plugin-commonjs";
import AutoImport from "unplugin-auto-import/vite";
import mkcert from 'vite-plugin-mkcert';
import fs from 'fs'

const buildVersionHook = (mode: string) => {
  return {
    name: 'build-life-hook',
    buildStart(){
      let now = new Date().toLocaleString().replace(/\//g,'-')
      let version = {
        publishTime: now,
        version: 'v8.4',
      }
      let versionPath = path.join(__dirname,'./public/versionData.json');
      console.log(mode);
      if (!mode.includes('development')) {
        fs.writeFile(versionPath,JSON.stringify(version),'utf8',(err: any)=>{
          if(err){
            console.log('写入文件失败', JSON.stringify(err));
          }
        })
      }

    },
    buildEnd() {
      let now = new Date().toLocaleString().replace(/\//g,'-')
      console.log('构建完成！' + now);
    },
  };
}

export default ({ mode }: {
  mode: string
}) => defineConfig({
  base: loadEnv(mode, process.cwd()).VITE_PUBLIC_PATH || '/',
  define: {
    // 生产环境下服务器端渲染（SSR）过程中hydration（激活）阶段的错误处理行为，
    // 服务器生成的初始HTML结构与客户端Vue组件渲染出的结构不匹配，就会发生hydration不匹配错误，
    // 此标志决定是否简化这类错误报告以提高性能和减少包体积。

    // 主要在开发调试时排查问题，并且目前白泽系统都是客户端渲染（CSR），并未用到服务器端渲染（SSR），所以可以放心关闭
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          prefix: "Icon",
        }),
      ],
      // dts: path.resolve(pathSrc, 'auto-imports.d.ts'),
      dts: true,
    }),
    Components({
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          enabledCollections: ["ep"],
        }),
        AntDesignVueResolver({
          importStyle: false, // css in js
        }),
      ],
      // dts: path.resolve(pathSrc, 'components.d.ts'),
    }),
    Icons({
      autoInstall: true,
    }),
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), "src/assets/icons")],
      // 指定symbolId格式
      symbolId: "icon-[dir]-[name]",
    }),
    mkcert(),
    buildVersionHook(mode),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, './src'),
    },
  },
  build: {
    assetsDir: "./static",
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      plugins: [commonjs()],
    },
  },
  esbuild: {
    // 此配置是为了在vue中使用jsx
    jsxFactory: "h",
    jsxFragment: "Fragment",
    jsxInject: "import { h } from 'vue'",
  },
  server: {
    // @ts-ignore
    https: false,
    port: 2002,
    host: "127.0.0.1",
    cors: true,
    open: true,
    hmr: true,
    watch: {
      ignored: [
        '**/node_modules/**',
        '**/dist/**',
        '**/.git/**',
        '**/public/**',
        '**/components.d.ts',
      ],
    },
    // 设置 https 代理
    // proxy: {
    //   "/market": {
    //     target: "http://*************:8201",
    //     changeOrigin: true,
    //     rewrite: (path: string) => path.replace(/^\/market/, ""),
    //   },
    // },
  },
});
