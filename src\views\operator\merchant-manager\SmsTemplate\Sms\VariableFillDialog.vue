<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    class="merchant-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        变量值填写
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="100px"
        >
          <!--所有自定义变量-->
          <CustomVariableFormItem :list="form" @update="onUpdateVariableSmsPojoList" />
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="onClickCancel">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { ElMessage, FormRules } from 'element-plus'
import { VariableSmsPojoItem } from '@/type/sms'
import CustomVariableFormItem from '@/components/sms/CustomVariableFormItem.vue'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  list: VariableSmsPojoItem[],
}>(), {
  visible: false,
  list: () => ([]),
})
const emits = defineEmits([
  'update:visible',
  'confirm'
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): VariableSmsPojoItem[] => {
  return []
}
// 表单数据
const form: VariableSmsPojoItem[] = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = () => {
  emits('confirm', form)
  // 关闭弹窗
  closeDialog()
}
/**
 * 更新表单
 */
const updateForm = () => {
  form.length = 0
  form.push(...props.list)
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 自定义变量 开始 ----------------------------------------

/**
 * 自定义变量组件 更新列表
 * @param list 新列表
 */
const onUpdateVariableSmsPojoList = (list: VariableSmsPojoItem[]) => {
  form.length = 0
  Object.assign(form, list)
}

// ---------------------------------------- 自定义变量 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
    updateForm()
  } else {
    resetForm()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
