<template>
  <div class="percent-box">
    <div class="percent-title">
      <span class="tw-flex tw-items-center">
        {{ props.title }}
        <el-tooltip v-if="props.tips" :show-after="500">
          <template #content>
            <div class="tw-text-left tw-max-w-[40vw]">
              {{ props.tips }}
            </div>
          </template>
          <el-icon :size="14" class="tw-ml-[3px] tw-cursor-pointer" color="var(--primary-black-color-400)"><SvgIcon  name="warning" /></el-icon>
        </el-tooltip>
      </span>
      <div class="tw-flex tw-items-center tw-pr-[12px]">
        <slot></slot>
        <el-icon v-if="sortType=='desc' && tableList?.length" @click="sortType='asc'" :size="14" color="var(--primary-black-color-400)"><SvgIcon name="arrow-down" /></el-icon>
        <el-icon v-if="sortType=='asc' && tableList?.length" @click="sortType='desc'" :size="14" color="var(--primary-black-color-400)"><SvgIcon name="arrow-up" /></el-icon>
      </div>
    </div>
    <ul v-if="tableList?.length > 0" >
      <li
        v-for="item in tableList"
        :key="(item.canvasId + '') + (item.corpusId + '') + (item.name || '')"
        class="tw-flex tw-py-[6px] tw-px-[4px] tw-w-full tw-items-center"
        :class="{'normal-progress': !props.currentItem || props.currentItem?.canvasId == item.canvasId,
        'tw-cursor-pointer': !!props.canShowDetails,
        'hover:tw-bg-[#EAF0FE]': item.canvasId && item.corpusType?.includes('ORDINARY') && ['挂机分布', '挂机率', '命中分布'].includes(props.title)}"
        @click="showDetails(item)"
      >
        <span v-if="!!props.needType && item.corpusType" class="corpusType-box" :class="props.currentItem && props.currentItem?.canvasId == item.canvasId ? 'tw-text-[var(--el-color-primary)]':'tw-text-[var(--primary-black-color-400)]'">
          {{ corpusType2Name[item.corpusType] || '未' }}
        </span>
        <el-tooltip
          :disabled="(item.corpusType?.includes('ORDINARY') ? item.canvasName || '' : item.corpusName || '').length <= 6"
          :show-after="500"
          :content="item.corpusType?.includes('ORDINARY') ? item.canvasName || '' : item.corpusName || ''"
        >
          <span class="tw-truncate tw-text-[13px] tw-pr-[8px] tw-w-[20%] tw-text-left" :class="props.currentItem && props.currentItem?.canvasId == item.canvasId ? 'tw-text-[var(--el-color-primary)]':'tw-text-[var(--primary-black-color-400)]'">
            {{ item.corpusType?.includes('ORDINARY') ? item.canvasName || '' : item.corpusName || item.name || '' }}
          </span>
        </el-tooltip>
        <el-progress :stroke-width="16" :percentage="getPercentVal(item, false)" class="tw-grow" color="var(--primary-black-color-400)">
          <div class="tw-text-[13px]">{{ props.showNumber ? formatNumber1(item.numerator) : getPercentVal(item, true)}}</div>
        </el-progress>
      </li>
    </ul>
    <el-empty v-else></el-empty>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { ScriptStatisticItem, CorpusTypeEnum, } from '@/type/speech-craft'
import { formatNumber, formatNumber1 } from '@/utils/utils'

const props = withDefaults(defineProps<{
  tableList: ScriptStatisticItem[],
  title: string,
  tips?: string
  currentItem?: ScriptStatisticItem | null,
  showNumber?: boolean // 仅展示数值，不展示百分比（当前用于语义）
  needType?: boolean // 展示语料类型
  canShowDetails?: boolean
}>(), {
  currentItem: undefined,
  needType: false,
  showNumber: false,
  canShowDetails: false,
  tips: undefined
})
const emits = defineEmits(['show-details'])

const tableList = computed(() => {
  return props.tableList.sort((a, b) => {
    if (props.showNumber) {
      return sortType.value == 'desc' ? (b.numerator - a.numerator) : -(b.numerator - a.numerator)
    }
    // @ts-ignore
    return sortType.value == 'desc' ? getPercentVal(b, false) - getPercentVal(a, false) : getPercentVal(a, false) - getPercentVal(b, false)
  })
})
const sortType = ref('desc')
const corpusType2Name: Record<string, string> = {
  [CorpusTypeEnum['主动流程-普通语料']]: '主',
  [CorpusTypeEnum['深层沟通-普通语料']]: '深',
  [CorpusTypeEnum['基本问答']]: '基',
  [CorpusTypeEnum['重复语料']]: '重',
  [CorpusTypeEnum['沉默语料']]: '沉',
  [CorpusTypeEnum['最高优先']]: '最',
  [CorpusTypeEnum['打断垫句']]: '断',
  [CorpusTypeEnum['续播垫句']]: '续',
  [CorpusTypeEnum['承接语料']]: '承',
}

const getPercentVal = (item: ScriptStatisticItem, isPercent: boolean) => {
  if (item.numerator * item.denominator > 0) {
    const data = (item.numerator / item.denominator * 100)
    if (isPercent) {
      return `${formatNumber(data, 2)}%(${formatNumber1(item.numerator)}/${formatNumber1(item.denominator)})`
    } else {
      return  data > 100 ? 100 : Number(formatNumber(data, 2))
    }
  } else {
    return  isPercent ? `0(${formatNumber1(item.numerator)}/${formatNumber1(item.denominator)})` : 0
  }
}
const showDetails = (item: ScriptStatisticItem) => {
  if (!props.canShowDetails) return
  emits('show-details', item, props.title)
}
</script>

<style scoped lang="postcss" type="text/postcss">
.percent-box {
  width: 100%;
  /* padding: 16px; */
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  ul {
    width: 100%;
  }
  .corpusType-box {
    width: 20px;
    height: 20px;
    font-size: var(--el-font-size-base);
    border: 1px solid;
    border-radius: 2px;
    display: flex;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
    margin-right: 4px;
    font-size: 12px;
    flex-shrink: 0;
  }
  .percent-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    height: 24px;
    margin-bottom: 6px;
  }
  :deep(.el-progress-bar__outer) {
    border-radius: 2px;
  }
  :deep(.el-progress-bar__inner) {
      border-radius: 2px;
    }
  .normal-progress {
    :deep(.el-progress-bar__inner) {
      border-radius: 2px;
      background: linear-gradient(90deg, #0167FF 0%, #019FFF 100%);
    }
  }
  .el-empty {
    width: 100%;
    height: 100%;
    --el-empty-padding: 0;
  }
}

</style>