<template>
  <div v-if="!(groupType===1 && clueType===ClueStatusEnum['已归档'])" class="tw-pb-[8px] tw-px-[16px] tw-flex tw-items-center tw-text-[13px] tw-bg-white tw-h-[44px]">
    <div class="tw-flex tw-items-center">
      <el-checkbox
        v-model="isSelectAll"
        :indeterminate="selectClues &&selectClues?.length>0&&selectClues?.length<props.tableData?.length"
        @change="handleSelectAll"
      >全部数据</el-checkbox>
      <span class="tw-ml-[12px] tw-text-[var(--primary-black-color-400)]">已选：</span>
      <span class="tw-text-[var(--primary-black-color-400)]">{{ selectClues?.length || 0 }}</span>
      <span class="tw-text-[var(--primary-black-color-400)]">/</span>
      <span class="tw-text-[var(--primary-black-color-400)]">{{ props.tableData?.length || 0 }}</span>
    </div>
  </div>
  <el-table
    :data="tableTempData"
    v-loading="!!props.loading"
    ref="tableRef"
    :header-cell-style="{background:'#F7F8FA', color: '#646566'}"
    stripe
    :row-key="(row: ClueItem) => row.id??'' + row.clueUniqueId??''"
  >
    <el-table-column v-if="!(groupType===1 && clueType===ClueStatusEnum['已归档'])" width="48" fixed="left" align="left">
      <template #header>
        <el-checkbox
          v-if="tableTempData.length>0"
          :model-value="selectIdsInCurrentPage?.length===tableTempData?.length"
          :indeterminate="selectIdsInCurrentPage?.length>0 && selectIdsInCurrentPage?.length<tableTempData?.length"
          @change="handleSelect()"
        ></el-checkbox>
      </template>
      <template #default="{ row }">
        <el-checkbox
          :model-value="selectIdsInCurrentPage?.includes(row.id)"
          @change="handleSelect(row)"
        ></el-checkbox>
      </template>
    </el-table-column>
    <el-table-column label="号码" fixed="left" align="left" width="170">
      <template #default="{ row, $index }">
        <div class="phone-msg" :class="currentIndex===$index ? 'tw-text-[#165DFF]':''">
          <span>{{ filterPhone(row.clueUniqueId, 7, 2) + ' ' + (row.operator || '') }}</span>
          <el-icon :size="14" class="hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer" @click="copy(row.clueUniqueId)"><SvgIcon name="copy" color="inherit"></SvgIcon></el-icon>
        </div>
      </template>
    </el-table-column>
    <el-table-column property="name" label="姓名" min-width="120" align="left" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
    <el-table-column property="company" label="公司" min-width="120" align="left" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
    <el-table-column property="comment" label="备注" min-width="120" align="left" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
    <el-table-column label="省市" align="left" min-width="160" show-overflow-tooltip>
      <template #default="{ row }">
        {{ row.province || row.city ? (row.province || '') + ' ' + (row.city || '') : '-' }}
      </template>
    </el-table-column>
    <template v-if="[ClueStatusEnum['待下发'], ClueStatusEnum['待分配']].includes(clueType)">
      <el-table-column label="线索来源" align="center" min-width="80" show-overflow-tooltip>
        <template #default="{ row }">
          {{ findValueInEnum(row.fromType, ClueFromTypeEnum)||'-' }}
        </template>
      </el-table-column>
      <el-table-column property="taskName" label="任务名称" align="left" min-width="240" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column property="scriptStringId" label="执行话术" align="left" min-width="280" show-overflow-tooltip>
        <template #default="{ row }">
          {{ speechCraftList?.find((item: SpeechCraftInfoItem) => item.scriptStringId == row.scriptStringId)?.scriptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="callOutTime" label="呼出时间" align="center" min-width="160" :formatter="formatTime">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="callStatus" label="呼叫状态" align="center" width="80">
        <template #default="{ row }">
          <span
            v-if="row?.callStatus"
            class="status-box-mini tw-m-auto"
            :class="getClueCallStatusClass(row?.callStatus)"
          >
            {{ findValueInEnum(row.callStatus, ClueCallStatusEnum) || '-' }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="intentionClass" label="分类结果" align="center" min-width="120" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="sayCount" label="说话次数" align="left" min-width="80" :formatter="formatterEmptyData"> </el-table-column>
      <el-table-column property="cycleCount" label="轮次" align="left" min-width="80" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="callDuration" label="通话时长" align="left" min-width="120">
        <template #default="{ row }">
          {{ (row.callDuration??-1)>-1 ? formatDuration(row?.callDuration/1000) || '0' : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="intentionLabels" label="标签" align="left" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
            {{ row.intentionLabels ? row.intentionLabels.split(',').join('、') : '-' }}
        </template>
      </el-table-column>
    </template>
    <!-- 待下发 -->
    <template v-if="[ClueStatusEnum['待下发']].includes(clueType)">
      <el-table-column property="canBeSend" label="可下发状态" align="center" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
            {{ findValueInStatus(row.canBeSend) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="joinedTaskName" label="已加入任务" align="left" :formatter="formatterEmptyData" min-width="280" show-overflow-tooltip>
      </el-table-column>
      <!-- <el-table-column property="joinedTaskStatus" label="任务状态" align="center" min-width="80" show-overflow-tooltip>
        <template #default="{ row }">
          {{  findValueInEnum(row.joinedTaskStatus, TaskStatusEnum) || '-'}}
        </template>
      </el-table-column> -->
    </template>
    <el-table-column
      property="callTeamId"
      v-if="groupType=== 0 && [ClueStatusEnum['待分配'], ClueStatusEnum['已分配'], ClueStatusEnum['已回收'], ClueStatusEnum['已归档']].includes(clueType)"
      :label="[ClueStatusEnum['待分配'], ClueStatusEnum['已分配']].includes(clueType) ? '负责坐席组' : '最后负责坐席组'"
      align="left"
      min-width="120"
      show-overflow-tooltip
    >
      <template #default="{ row }">
        {{ callTeamList?.find(item => item.id === row.callTeamId)?.callTeamName || '-' }}
      </template>
    </el-table-column>
    <template v-if="[ClueStatusEnum['已分配'], ClueStatusEnum['已回收'], ClueStatusEnum['已归档']].includes(clueType)">
      <el-table-column
        property="callSeatId"
        :label="[ClueStatusEnum['已分配']].includes(clueType) ? '负责坐席' : '最后负责坐席'"
        align="left"
        min-width="120"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ callSeatList?.find(item => item.id === row.callSeatId)?.account || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="latestFollowUpStatus" label="跟进状态" align="center" min-width="80">
        <template #default="{ row }">
          <span
            v-if="row?.latestFollowUpStatus"
            class="status-box-mini tw-m-auto"
            :class="getFollowStatusClass(row?.latestFollowUpStatus)"
          >
            {{ findValueInEnum(row?.latestFollowUpStatus, FollowUpStatusEnum) || '-' }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </template>

    <template v-if="[ClueStatusEnum['已分配']].includes(clueType)">
      <el-table-column property="examineStatus" label="审核状态" align="center" width="80">
        <template #default="{ row }">
          <span
            v-if="row?.examineStatus"
            class="status-box-mini tw-mx-auto"
            :class="getExamineStatusClass(row?.examineStatus)"
          >
            {{ findValueInEnum(row?.examineStatus, ExamineStatusEnum) || '-' }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column  property="followCount" label="跟进次数" align="left" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
            {{ row.followCount ?? '-' }}
        </template>
      </el-table-column>
      <el-table-column property="latestFollowUpTime" label="最近跟进时间" align="center" min-width="160" sortable :formatter="formatTime">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="latestCallStatus" label="最近通话状态" align="center" min-width="120">
        <template #default="{ row }">
          {{ findValueInEnum(row.latestCallStatus, ClueCallStatusEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="latestCallDuration" label="最近通话时长" align="left" min-width="120">
        <template #default="{ row }">
          {{ (row.latestCallDuration??-1) > -1 ? formatDuration(row.latestCallDuration/1000) : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="nextFollowUpTime" label="下次跟进时间" align="center" min-width="160" sortable :formatter="formatTime">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="star" label="星标" align="center" fixed="right" min-width="50">
        <template #default="{ row }">
          <el-icon :size="18" color="#FFAA3D">
            <SvgIcon v-if="row.star" name="star-active" />
            <SvgIcon v-else="!row.star" name="star-inactive"/>
          </el-icon>
        </template>
      </el-table-column>
      <el-table-column property="expire" label="是否过期" align="center" min-width="120" :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ findValueInStatus(row.expire) || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="latestFollowUpNote" label="跟进备注" align="center" min-width="120" :formatter="formatterEmptyData">
      </el-table-column>
    </template>
    <el-table-column v-if="[ClueStatusEnum['已分配'], ClueStatusEnum['已回收']].includes(clueType) || ([ClueStatusEnum['已归档']].includes(clueType) && groupType===1)" property="beAllocatedTime" label="分配时间" align="center" min-width="160" sortable :formatter="formatTime">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column v-if="[ClueStatusEnum['已分配']].includes(clueType)" property="autoRecoveryTime" label="自动回收时间" align="center" min-width="160" sortable :formatter="formatTime">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
    </el-table-column>


    <el-table-column v-if="[ClueStatusEnum['已分配'], ClueStatusEnum['已回收'], ClueStatusEnum['待分配'], ClueStatusEnum['已归档'],].includes(clueType)" property="beSendTime" label="下发时间" align="center" min-width="160" sortable :formatter="formatTime">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
    </el-table-column>

    <el-table-column property="importTime" label="导入时间" align="center" min-width="160" sortable :formatter="formatTime">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
    </el-table-column>

    <el-table-column v-if="[ClueStatusEnum['已回收']].includes(clueType)" property="recoveryTime" label="回收时间" align="center" min-width="160" sortable :formatter="formatTime">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column v-if="[ClueStatusEnum['已归档']].includes(clueType)" property="archivedTime" label="归档时间" align="center" min-width="160" sortable :formatter="formatTime">
      <template #header="{ column }">
        <div class="tw-flex tw-items-center tw-justify-center">
          <span>{{column.label}}</span>
          <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
            <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
            <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="操作" align="right" fixed="right" width="54">
      <template #default="{ row, $index }">
        <el-button type="primary" link @click="goDetail(row, $index)">详情</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty v-if="!props.tableData || props.tableData.length < 1" description="暂无数据" />
    </template>
  </el-table>
  <PaginationBox
    :pageSize="pageSize"
    :currentPage="currentPage"
    :total="total"
    @search="search"
    @update="updatePage"
  >
  </PaginationBox>
   <!-- 线索详情 -->
  <ClueDetailsDrawer
    v-model:visible="clueDrawerVisible"
    :clueData="currentClue"
    :permission="permissions"
    @update:data="search"
  />
</template>

<script lang="ts" setup>
// type
import { SpeechCraftInfoItem, } from '@/type/speech-craft'
import { SeatTeam, SeatMember, } from '@/type/seat'
import { ClueItem, ClueFromTypeEnum, ClueStatusEnum, ClueCallStatusEnum, FollowUpStatusEnum, ExamineStatusEnum } from '@/type/clue'

// 外部变量\方法
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import { getFollowStatusClass, getExamineStatusClass, getClueCallStatusClass, } from './constant'
import { copyText, filterPhone, formatterEmptyData, formatDuration, findValueInEnum, formatTime, findValueInStatus } from '@/utils/utils'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
// 组件
import PaginationBox from '@/components/PaginationBox.vue'
// 其他
import { onActivated, onDeactivated, computed, ref, watch, onUnmounted, defineAsyncComponent, onMounted } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { CaretBottom, CaretTop, } from '@element-plus/icons-vue'

const ClueDetailsDrawer = defineAsyncComponent({loader: () => import('./ClueDetailsDrawer.vue')})

const props = defineProps<{
  tableData: ClueItem[],
  selectClues: ClueItem[]|null,
  loading?: boolean,
  clueType: ClueStatusEnum,
  groupType: number, // 0: 线索管理； 1：团队管理
}>();
const emits = defineEmits(['update:visible', 'update:table', 'update:selectClues'])

const globalStore = useGlobalStore()
const taskStore = useTaskStore()
const userStore = useUserStore();

const clueType = ref(ClueStatusEnum['待下发'])

const permissions = computed(() => {
  const curPermissions = userStore.permissions[routeMap[props.groupType === 1 ? '坐席组线索' : '线索管理'].id]
  const res: Record<string, boolean> = {}
  if (props.clueType === ClueStatusEnum['已分配'] && curPermissions?.includes(routeMap[props.groupType === 1 ? '坐席组线索' : '线索管理'].permissions['线索审核'])) {
    res['审核线索'] = true
  }
  if (curPermissions?.includes(routeMap[props.groupType === 1 ? '坐席组线索' : '线索管理'].permissions['表单编辑'])) {
    res['编辑表单'] = true
  }
  if (props.clueType === ClueStatusEnum['已分配']) {
    res['修改跟进状态'] = true
    res['修改星标'] = true
  }
  return res
})

const groupType = ref(0)
/** 表格和分页 */
const tableRef = ref()
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(20)
const tableTempData = computed(() => {
  total.value = props.tableData?.length || 0
  return props.tableData?.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value) || []
})
const search = () => {
  emits('update:table')
}
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}

/** 批量选中操作（支持跨页选择） 模块【开始】 */
const isSelectAll = ref(false) // 是否全选
const selectClues = ref<ClueItem[] | null>(props.selectClues || []) // 选中的数据
const selectIdsInCurrentPage = computed(() => {
  const ids = tableTempData.value.map(item => item.id)
  const res: number[] = []
  selectClues.value?.map(item => {
    item.id && ids.includes(item.id) && res.push(item.id)
  })
  return res
})
// 对列表进行选中操作
const handleSelect = (row?: ClueItem) => {
  if (!selectClues.value) selectClues.value = []
  if (row) {
    const index = selectClues.value?.findIndex(item => item.id === row.id) ?? -1
    if (index >= 0) {
      selectClues.value?.splice(index, 1)
      isSelectAll.value = false
    } else {
      selectClues.value?.push(row)
      if (selectClues.value?.length === props.tableData?.length) {
        isSelectAll.value = true
      }
    }
  } else {
    if (selectIdsInCurrentPage.value.length < tableTempData.value.length) {
      selectIdsInCurrentPage.value
      tableTempData.value.map(item => {
        if (item.id && !selectIdsInCurrentPage.value.includes(item.id)) {
          selectClues.value?.push(item)
        }
      })
      if (selectClues.value?.length === props.tableData?.length) {
        isSelectAll.value = true
      }
    } else {
      const data = tableTempData.value.map(item => item.id)
      selectClues.value = selectClues.value?.filter(item => !data.includes(item.id)) || []
      isSelectAll.value = false
    }
  }
  emits('update:selectClues', selectClues.value||[])
}
// 对全选按钮进行操作
const handleSelectAll = (val: boolean) => {
  if (val) {
    selectClues.value = JSON.parse(JSON.stringify(props.tableData || []))
  } else {
    selectClues.value = []
  }
  emits('update:selectClues', selectClues.value||[])
}
/** 批量选中操作（支持跨页选择） 模块【结束】 */

const copy = (val: string) => {
  copyText(val || '')
}

const provinceList = ref<string[]|null>([])

const clueDrawerVisible = ref(false)
const currentClue = ref<ClueItem>({id: undefined, })
const currentIndex = ref(-1)
const goDetail = (row: ClueItem, index: number) => {
  clueDrawerVisible.value = true
  currentClue.value = row
  currentIndex.value = index
}
watch([() => props.tableData, () => props.clueType, () => props.groupType], () => {
  currentIndex.value = -1
  selectClues.value = props.selectClues || []
  isSelectAll.value = props.selectClues && props.selectClues?.length > 0 ? props.selectClues?.length === props.tableData?.length : false
  clueType.value = props.clueType
  groupType.value = props.groupType
}, {
  deep: true,
  immediate: true,
})

const speechCraftList = ref<SpeechCraftInfoItem[]|null>([])
const callTeamList = ref<SeatTeam[]|null>([])
const callSeatList = ref<SeatMember[]|null>([])
const init = async () => {
  currentIndex.value = -1
  await globalStore.getProvinceInfo()
  provinceList.value = globalStore.getProvinceList || []
  speechCraftList.value = await taskStore.getAllScriptListOptions()
  callTeamList.value = await taskStore.getCallTeamListOptions()
  callSeatList.value = props.groupType === 0 ? await taskStore.getCallGroupSeatListOptions() :  await taskStore.getCallTeamSeatListOptions()
}
onMounted(() => {
  init()
})
onActivated(() => init())
const clearAll = () => {
  currentIndex.value = -1
  provinceList.value = null
  speechCraftList.value = null
  callTeamList.value = null
  callSeatList.value = null
  selectClues.value = null
  tableRef.value = null
}

onDeactivated(() => {
  clearAll()
})
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.phone-msg {
  display: flex;
  align-items: center;
  span {
    width: 130px;
    flex-shrink: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-icon {
    display: none;
  }
  &:hover .el-icon {
    display: inline-block;
  }
}
.el-table {
  font-size: 13px;
  :deep(.cell) {
    padding: 0 8px;
  }
  .el-checkbox {
    height: 20px;
  }
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
.table-btn-box {
  display: flex;
  .el-button {
    width: 60px;
  }
}
</style>
