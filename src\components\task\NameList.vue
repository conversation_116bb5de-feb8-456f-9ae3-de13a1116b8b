<template>
  <div class="call-name-container">
    <div class="tw-w-full tw-bg-white tw-mb-[8px]">
      <div class="tw-grid tw-grid-cols-4 tw-gap-[8px] tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.phone"
            placeholder="号码/名单编号"
            @keyup.enter="search"
            clearable
          >
          </el-input>
        </div>
        <div class="item">
          <el-select v-model="searchForm.operator" placeholder="运营商" clearable>
            <el-option v-for="item in operatorList" :key="item" :label="item" :value="item"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="province" placeholder="省" filterable clearable>
            <el-option v-for="item in provinceList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="city" placeholder="市" filterable clearable>
            <el-option v-for="item in cityList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item"/>
          </el-select>
        </div>
        <template v-if="isExpand">
          <div class="item tw-col-span-2">
            <InputNumberBox v-model:value="searchForm.putThroughNumLeft" :max="searchForm.putThroughNumRight || Number.MAX_VALUE" placeholder="最低接通" style="width: 47%" append="次"/>
            <span>至</span>
            <InputNumberBox v-model:value="searchForm.putThroughNumRight" placeholder="最高接通" style="width: 47%" append="次" :min="searchForm.putThroughNumLeft || 0"/>
          </div>
          <div class="item tw-col-span-2">
            <span class="tw-w-[96px] tw-shrink-0">加入时间：</span>
            <TimePickerBox
              v-model:start="searchForm.addStartTime"
              v-model:end="searchForm.addEndTime"
              placeholder="加入时间"
              :splitToday="true"
              format="YYYY-MM-DD HH:mm:ss"
              class="tw-w-full"
              :clearable="false"
            />
          </div>
          <div class="item tw-col-span-2">
            <InputNumberBox v-model:value="searchForm.calledNumLeft" :max="searchForm.calledNumRight || Number.MAX_VALUE" placeholder="最低拨打" style="width: 47%" append="轮"/>
            <span>至</span>
            <InputNumberBox v-model:value="searchForm.calledNumRight" placeholder="最高拨打" style="width: 47%" append="轮" :min="searchForm.calledNumLeft || 0"/>
          </div>
          <div class="item tw-col-span-2">
            <span class="tw-w-[96px] tw-shrink-0">最后外呼时间：</span>
            <TimePickerBox
              v-model:start="searchForm.lastCallStartTime"
              v-model:end="searchForm.lastCallEndTime"
              placeholder="最后外呼时间"
              format="YYYY-MM-DD HH:mm:ss"
              class="tw-w-full"
            />
          </div>
        </template>
      </div>
      <div class="tw-mt-[12px] tw-h-[32px]">
        <el-button
          v-if="getPermission && currentTask.callStatus != TaskStatusEnum['进行中']"
          class="tw-float-left tw-w-[86px]"
          @click="addCallPhone"
          plain
          type="primary"
        >
          <el-icon size="13px"><SvgIcon name="add-circle" color="inherit"/></el-icon>
          <span>添加呼叫</span>
        </el-button>
        <div class="tw-float-right tw-leading-[32px]">
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
          <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon></el-button>
          <el-button type="primary" v-else @click="isExpand=true" link>展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon></el-button>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData"
      v-loading="loading"
      :row-key="(row: TaskCallItem) => (row.callId??'') + (row.phoneRecordId??'')"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column label="号码" align="left" min-width="170">
        <template #default="{ row }">
          <div class="phone-msg">
            <span>{{ filterPhone(row.phoneRecordId) + ' ' + (row.operator || '') }}</span>
            <el-tooltip content="复制" placement="right" :show-after="500">
              <el-icon :size="14" class="hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer" @click="copy(row.phoneRecordId)"><SvgIcon name="copy" color="inherit"></SvgIcon></el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column property="name" label="姓名" align="center"></el-table-column> -->
      <el-table-column label="省市" align="left" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.province || row.city ? (row.province || '') + ' ' + (row.city || '') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="callStatus" label="呼叫状态" align="center" min-width="80">
        <template #default="{ row }">
          <span
            v-if="row?.callStatus"
            class="status-box-mini tw-mx-auto"
            :class="filterStatusStyle(row?.callStatus)"
          >
            {{row.callStatus}}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column property="calledNum" label="接通/呼叫" align="center" min-width="160">
        <template #default="{ row }">
          {{ row.calledNum ? `${row.putThroughNum || 0}/${row.calledNum || 0}` : '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="成功/发送(本任务)" align="center">
        <template #default="{ row }">
          {{ row.putThroughNum + '/' + row.calledNum }}
        </template>
      </el-table-column> -->
      <el-table-column property="addTime" label="加入时间" align="center" sortable min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="finalCallTime" label="最后外呼时间" align="center" sortable min-width="160" :formatter="formatterEmptyData">
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      class="stick-pagination"
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="search"
      @update="updatePage"
    >
    </PaginationBox>
    <BatchAddCallDialog
      v-model:visible="batchAddCallVisible"
      isSingle
      :tableData="[props.currentTask]"
      :taskType="props.currentTask.taskType!"
      @confirm="search"
    ></BatchAddCallDialog>
  </div>
</template>

<script lang="ts" setup>
import { TaskCallItem, TaskCallSearchModal, TaskManageItem, TaskStatusEnum, TaskTypeEnum, } from '@/type/task'
import { reactive, computed, ref, watch, onActivated , } from 'vue'
import { aiOutboundTaskModel } from '@/api/ai-report'
import dayjs from 'dayjs'
import { copyText, filterPhone, formatterEmptyData } from '@/utils/utils'
import { ArrowUp, ArrowDown, CaretTop, CaretBottom,} from '@element-plus/icons-vue'
import PaginationBox from '@/components/PaginationBox.vue'
import { OperatorEnum } from '@/type/common'
import { useGlobalStore } from '@/store/globalInfo'
import { ResponseData } from '@/axios/request/types'
import routeMap from '@/router/asyncRoute/route-map'
import { useUserStore } from "@/store/user";
import TimePickerBox from '@/components/TimePickerBox.vue'
import InputNumberBox from '@/components/InputNumberBox.vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js';
import BatchAddCallDialog from '@/views/merchant/ai-report/OperationTool/BatchAddCallDialog.vue'

const props = defineProps<{
  currentTask: TaskManageItem;
  needRefresh: boolean;
}>();
const currentTask = props.currentTask
const emits = defineEmits(['update:needRefresh'])
const globalStore = useGlobalStore()
const loading = ref(false)

// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap[props.currentTask.taskType === TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务'].id]
const getPermission = computed(() => {
  return permissions?.includes(routeMap[props.currentTask.taskType === TaskTypeEnum['人机协同'] ? '人机协同任务' : 'AI外呼任务'].permissions['编辑任务'])
})

// 表格和分页
const tableData = ref<TaskCallItem []>([])
const pageSizeList = [20, 50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const isExpand = ref(false)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}
const filterStatusStyle = (status: string) => {
  switch (status) {
    case '呼叫中': return 'orange-status';
    case '呼叫成功': return 'green-status';
    case '呼叫完成': return 'green-status';
    default: return 'blue-status';
  }
}
const copy =(val: string) => {
  copyText(val || '')
}
class SearchFormOrigin {
  taskId = currentTask.id
  phone = ''
  operator = undefined
  province = undefined
  city = undefined
  callStatus = undefined
  calledNumLeft = undefined
  calledNumRight = undefined
  putThroughNumLeft = undefined
  putThroughNumRight = undefined
  addStartTime = dayjs(currentTask.createTime).startOf('day').format('YYYY-MM-DD HH:mm:ss')
  addEndTime = dayjs(props.currentTask.createTime).endOf('day').format('YYYY-MM-DD HH:mm:ss')
  lastCallStartTime = undefined
  lastCallEndTime = undefined
}
const callStatusArr = ref<string[]>([])
const search = async () => {
  loading.value = true
  searchForm.callStatus = (callStatusArr.value && callStatusArr.value.length > 0) ? callStatusArr.value.join(',') : ''
  const [_, res] = await to(aiOutboundTaskModel.findCallNameList({
    ...searchForm,
    startPage: currentPage.value > 1 ? currentPage.value - 1 : 0,
    pageNum: pageSize.value
  })) as [any, ResponseData]
  tableData.value = (res?.data || []) as TaskCallItem[]
  total.value = res?.total || 0
  loading.value = false
  emits('update:needRefresh', false)
}
const searchForm = reactive<TaskCallSearchModal>(new SearchFormOrigin())
watch(() => props.currentTask.id, () => {
  if (props.currentTask?.id && props.currentTask?.id > 0) {
    clearSearchForm()
  }
})
watch(() => props.needRefresh, n => {
  if (props.currentTask?.id && props.currentTask?.id > 0) {
    n && search()
  }
})
const city = ref<string | undefined>(undefined)
const province = ref<string | undefined>(undefined)
watch(province, () => {
  searchForm.province = province.value?.split(',')[1] || undefined
  city.value = undefined
})
watch(city, () => {
  searchForm.city = city.value?.split(',')[1] || undefined
})
const provinceList = ref<string[]>([])
const provinceAllMap = ref<{ [key: string]: string[] }>({})
const cityList = computed(() => {
  return province.value ? (provinceAllMap.value[province.value] || []) : (Object.values(provinceAllMap.value).flat() || [])
})
const initData = async () => {
  await globalStore.getProvinceInfo()
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
}
const operatorList = OperatorEnum

const clearSearchForm = () => {
  Object.assign(searchForm, new SearchFormOrigin())
  province.value = undefined
  city.value = undefined
}
// 添加呼叫
const batchAddCallVisible = ref(false)
const addCallPhone = () => {
  batchAddCallVisible.value = true
}
onActivated(() => {
  initData()
  search()
})

</script>

<style scoped lang="postcss" type="text/postcss">
.call-name-container {
  padding: 16px 0 0;
  width: 100%;
  box-sizing: border-box;
  font-size: 13px;
  :deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) .el-input__wrapper {
    width: 100%;
  }
  .phone-msg {
    display: flex;
    align-items: center;
    span {
      width: 130px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon {
      display: none;
    }
    &:hover .el-icon {
      display: inline-block;
    }
  }
  .item {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .label {
      flex-shrink: 0;
      flex-grow: 0;
      width: 30px;
    }
    :deep(.el-date-editor.el-input) {
      width: 47%;
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
  }
  .el-button {
    font-size: 13px;
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  .stick-pagination {
    position: sticky;
    bottom: 0;
    left: 0;
    background-color: #fff;
    z-index: 10;
  }
}
</style>
