<template>
  <HeaderBox title="上行短信记录" />
  <div class="call-record-container">
    <div class="tw-w-full tw-p-[16px] tw-bg-white tw-mb-[16px] tw-grow-0">
      <div class="tw-grid tw-grid-cols-6 tw-gap-[8px] tw-pb-[12px]">
        <div class="item">
          <InputPhonesBox
            v-model:value="searchForm.phone"
            @search="search()"
          />
        </div>
        <div class="item">
          <el-select
            v-model="searchForm.smsTemplateId"
            filterable
            placeholder="短信模板"
            clearable
          >
            <el-option v-for="item in smsTemplateList" :key="item.id" :label="item.templateName" :value="item.id">
              <span>{{ item.templateName }}</span>
              <span v-show="item.extra" class="tw-text-gray-400 tw-float-right">{{ `(${ item.extra })` }}</span>
            </el-option>
          </el-select>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.originalSmsContent"
            placeholder="短信内容"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.smsMoContent"
            placeholder="上行短信内容"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item tw-col-span-2">
          <span class="tw-w-[66px] tw-shrink-0">系统接收:</span>
          <TimePickerBox
            v-model:start="searchForm.systemReceiveTimeStart"
            v-model:end="searchForm.systemReceiveTimeEnd"
            splitToday
            :maxRange="60*60*24*31*1000"
            :clearable="false"
          />
        </div>
        <template v-if="isExpand">
          <div v-if="accountType === 0" class="item">
            <el-select
              v-model="searchForm.account"
              placeholder="商户账号"
              filterable
              clearable
            >
              <el-option v-for="item in masterAccountList" :key="item.account" :label="item.account" :value="item.account"/>
            </el-select>
          </div>
          <div class="item">
            <el-input
              v-model.trim="searchForm.name"
              placeholder="姓名"
              @keyup.enter="search()"
              clearable
            >
            </el-input>
          </div>
          <div class="item tw-col-span-2">
            <span class="tw-w-[72px] tw-shrink-0">原短信触发:</span>
            <TimePickerBox
              v-model:start="searchForm.smsTriggerTimeStart"
              v-model:end="searchForm.smsTriggerTimeEnd"
              :maxRange="60*60*24*31*1000"
            />
          </div>
          <div class="item tw-col-span-2">
            <span class="tw-w-[66px] tw-shrink-0">用户回复：</span>
            <TimePickerBox
              v-model:start="searchForm.replyTimeStart"
              v-model:end="searchForm.replyTimeEnd"
              :maxRange="60*60*24*31*1000"
            />
          </div>
          
          <div class="item">
            <el-input
              v-model.trim="searchForm.company"
              placeholder="公司"
              @keyup.enter="search()"
              clearable
            >
            </el-input>
          </div>
          <div class="item">
            <el-input
              v-model.trim="searchForm.remarks"
              placeholder="备注"
              @keyup.enter="search()"
              clearable
            >
            </el-input>
          </div>
        </template>
      </div>

      <div class="tw-flex tw-justify-end tw-pt-[12px] tw-items-center tw-border-t-[1px] tw-h-[32px]">
        <div class="tw-leading-[32px]">
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
          <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>
            收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon>
          </el-button>
          <el-button type="primary" v-else @click="isExpand=true" link>
            展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData"
      v-loading="loading"
      :header-cell-style="tableHeaderStyle"
      stripe
      border
      class="tw-grow"
      row-key="recordId"
    >
      <el-table-column label="号码" fixed="left" align="left" width="150">
        <template #default="scope: { row: UpwardSmsRecord }">
          <div class="phone-msg">
            <span>{{ filterPhone(scope.row.recordId) }}</span>
            <el-tooltip content="复制" placement="right" :show-after="500">
              <el-icon :size="14" class="hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer" @click="copy(scope.row.recordId)"><SvgIcon name="copy" color="inherit"></SvgIcon></el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="operator" label="运营商" align="left" width="90" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column label="省市" align="left" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.province || row.city ? (filterProvinceName(row.province) || '') + ' ' + (row.city || '') : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="name" label="姓名" align="left" min-width="80" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="company" label="公司" align="left" min-width="120" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="remarks" label="备注" align="left" min-width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="smsTemplateName" label="原短信模版" align="left" min-width="200" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="originalContent" label="原短信内容" align="left" min-width="200" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="originalTriggerTime" label="原短信触发时间" align="center" width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="userReplyTime" label="用户回复时间" align="center" width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="systemReceiveTime" label="系统接收时间" align="center" width="160" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="smsMoContent" label="上行短信内容" align="left" min-width="200" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <template v-if="accountType === 0">
        <el-table-column property="account" label="所属账号" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="smsAccountName" label="短信对接账号" align="left" min-width="180" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
        <el-table-column property="smsSupplierName" label="短信供应商" align="left" min-width="180" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      </template>
      <template v-slot:empty>
        <el-empty description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      class="tw-grow-0"
      :pageSize="pageSize"
      :dynamic-total="dynamicTotal"
      :currentPage="currentPage"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="search(currentPage)"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onUnmounted, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { copyText, filterPhone, formatterEmptyData, findValueInEnum } from '@/utils/utils'
import { onBeforeRouteLeave } from 'vue-router'
import to from 'await-to-js'
import { smsProviderModel, smsAccountModel, upwardSmsModel } from '@/api/sms'
import { merchantSmsTemplateModel, } from '@/api/merchant'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { filterProvinceName, } from './constants'
import { UpwardSmsParams, UpwardSmsRecord, UpwardSmsOrigin, BusinessTypeEnum, SmsProviderStatusEnum, SmsAccountItem } from '@/type/sms'
import dayjs from 'dayjs'
import { ArrowUp, ArrowDown, } from '@element-plus/icons-vue'
import { tableHeaderStyle } from '@/assets/js/constant'

import InputPhonesBox from '@/components/InputPhonesBox.vue'
import HeaderBox from '@/components/HeaderBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import PaginationBox from '@/components/PaginationBox.vue'


const userInfo = useUserStore()
const accountType = userInfo.accountType
const loading = ref(false)

const isExpand = ref(false)

// 表格和分页
const tableData = ref<UpwardSmsRecord[] | null>([])
const pageSizeList = [20, 50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])

const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search(currentPage.value, true)
}

const copy = (val: string) => {
  copyText(val || '')
}

// 搜索数据
const searchForm = reactive<UpwardSmsParams>(new UpwardSmsOrigin())

const dynamicTotal = ref(false)

const search = async (page: number = 1, onlyPageChange: boolean = false) => {
  if (loading.value) return ElMessage.warning('请勿频繁操作')
  loading.value = true
  dynamicTotal.value = !onlyPageChange
  searchForm.startPage = page > 1 ? page - 1 : 0
  currentPage.value = page
  searchForm.pageNum = pageSize.value
  
  const [_, res] = await to(upwardSmsModel.findRecordList(searchForm, accountType === 0))
  if (searchForm.phone) {
    tableData.value = (res?.data as UpwardSmsRecord[] || []).sort((a, b) => dayjs(a.systemReceiveTime).isAfter(dayjs(b.systemReceiveTime)) ? -1 : 1) || []
  } else {
    tableData.value = res?.data as UpwardSmsRecord[] || []
  }
  
  if (!onlyPageChange) {
    total.value = tableData.value.length === pageSize.value ? (res?.total || 0) : tableData.value.length
    const [_, res2] = await to(upwardSmsModel.getRecordNum(searchForm))
    total.value = res2?.total || 0
    dynamicTotal.value = false
  }
  loading.value = false
}

const smsTemplateList = ref<{templateName: string, id: number, extra: string}[] | null>([])
const masterAccountList = ref<{account: string, groupId: string}[] | null>([])

/**
 * 更新商户账号列表
 */
const updateMasterAccoutList = async () => {
  const [err, res] = await to(monitorStatisticModel.getAllMainAccount())
  masterAccountList.value = (res || []).filter(item => !!item.account && !!item.groupId)
}

/**
 * 更新短信供应商和短信账号列表
 */
// const updateSmsSupplierList = async () => {
//   const [err1, data1] = await to(smsProviderModel.findProviderList({status: SmsProviderStatusEnum['启用']}))
//   smsSupplierList.value = (data1 || []).map(item => ({ name: item.supplierName!, supplierNumber: item.supplierNumber! }))
//   const [err2, data2] = <[any, SmsAccountItem[]]>await to(smsAccountModel.findAllAccountList({}))
//   smsAccountList.value = (data2 || []).map(item => ({ name: item.smsAccountName!, smsAccountNumber: item.smsAccountNumber! }))
// }

/**
 * 更新短信模版列表
 */
const updateSmsTemplateList = async () => {
  const [err, data] = await to(accountType === 0 ?
    merchantSmsTemplateModel.getTotalSmsTemplate()
    : merchantSmsTemplateModel.getSmsTemplate({
      groupId: userInfo.groupId,
    })
  )
  smsTemplateList.value = (data || []).flatMap(item => !!item.id && !!item.templateName ? [{ id: item.id, templateName: item.templateName, extra: item.businessType ? findValueInEnum(item.businessType, BusinessTypeEnum) || '' : '' }] : [])
}

const clearSearchForm = () => {
  Object.assign(searchForm, new UpwardSmsOrigin())
}

const clearAllData = () => {
  clearSearchForm()
  tableData.value = null
  smsTemplateList.value = null
  // smsSupplierList.value = null
  // smsAccountList.value = null
  masterAccountList.value = null
}

onMounted(() => {
  updateSmsTemplateList()
  // updateSmsSupplierList()
  updateMasterAccoutList()
  search()
})

onUnmounted(() => {
  clearAllData()
})

onBeforeRouteLeave(() => {
  clearAllData()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.call-record-container {
  margin: 16px 16px 12px;
  width: calc(100% - 32px);
  box-sizing: border-box;
  font-size: 13px;
  overflow-y: auto;
  min-width: 1048px;
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .phone-msg {
    display: flex;
    align-items: center;
    span {
      width: 110px;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon {
      display: none;
    }
    &:hover .el-icon {
      display: inline-block;
    }
  }

  .item {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      flex-shrink: 0;
      flex-grow: 0;
      width: 30px;
    }
    :deep(.el-date-editor.el-input) {
      width: 47%;
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  
  .el-table {
    font-size: 13px;
  }
}
</style>
