<template>
  <el-dialog
    v-model="dialogVisible"
    width="480px"
    class="dialog-form"
    @close="cancel"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">导出明文数据</div>
    </template>
    <el-form
      :model="editData"
      :rules="rules"
      label-width="75px"
      ref="addFormRef"
    >
      <el-form-item label="查看明文：">
        <span class="info-title">共【<span class="tw-text-[var(--el-color-primary)]">{{props.total}}</span>】个，今日剩余查看明文次数【<span class="tw-text-[var(--el-color-primary)]">{{remainCount}}</span>】次</span>
      </el-form-item>
      <el-form-item label="导出数量：" prop="totalCount">
        <el-input-number
          style="width:100%"
          v-model="editData.totalCount"
          :precision="0"
          :controls="false"
          :placeholder="`请输入导出数量，最大为${Math.min(remainCount, props.total || 1)}`"
          clearable
          :min="1"
          :max="Math.min(remainCount, props.total || 1)"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">导出</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,computed, reactive, watch, } from 'vue'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import to from 'await-to-js';
import { SupplierPlatformLineRecordSearchParam, SupplierPlatformLineRecordItem, ErrorTypeEnum } from '@/type/supplier/supplier'
import { supplierModel } from '@/api/supplier/supplier'
import { trace } from '@/utils/trace'
import { exportExcel } from '@/utils/export'
import { findValueInEnum, formatDuration } from '@/utils/utils';

const emits = defineEmits(['update:visible', 'confirm'])
const props = defineProps<{
  visible: boolean;
  total: number;
  searchParams: SupplierPlatformLineRecordSearchParam;
}>();
const loading = ref(false)
const dialogVisible = ref(false)

const editData = reactive({
  totalCount: 1
})
const addFormRef = ref<FormInstance | null>(null)
const rules = computed(() => ({
  totalCount: [
    { required: true, message: '请输入导出数量', trigger: 'change' },
  ],
}))

const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const confirm = () => {  
  addFormRef.value && addFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = {
        ...props.searchParams,
        totalCount: editData.totalCount,
      }
      const [err, data] = await to(supplierModel.batchConvertPlainPhone(params))
      await trace({ page: '供应平台-线路数据-批量导出明文', params: {res: err || '成功', params} })

      // 更新下解密次数
      const [_, res] = await to(supplierModel.getRemainDecryptCount())
      remainCount.value = res || 0
      loading.value = false
      if (data?.records?.length) {
        export2Excel(data?.records || [])
        cancel()
        ElMessage.success(`导出成功，今日剩余查看明文次数为【${res}】次`)
      }
    }
  })
}

// 文件导出函数
const export2Excel = (list?: SupplierPlatformLineRecordItem[] | null) => {
  if (!list?.length) return ElMessage.warning('导出数据为空')
  const data = list?.map(item => {
    return {
      '号码': item.recordId ||'',
      '明文号码': item.plainPhone ||'',
      '省市': item.province + ' ' + item.city,
      '呼叫状态': item.callStatusStr ||'',
      '呼出时间': item.callOutTime ||'',
      '接通时间': item.talkTimeStart ||'',
      '挂断时间': item.talkTimeEnd ||'',
      '通话时长': formatDuration(item.callDurationSec) || '0',
      '异常类型': item.types?.map(v => findValueInEnum(v, ErrorTypeEnum)).join(',')
    }
  }) || []
  exportExcel(data, `【${props.searchParams.supplyLineNumber}】异常数据-【${props.searchParams.callOutTimeStart}-${props.searchParams.callOutTimeEnd}】-${editData.totalCount}个.xlsx`);
}

const remainCount = ref(1) // 剩余查看明文次数

watch(() => props.visible, async n => {
  if (n) {
    // 判断是否有可导出内容
    if (!props.total) {
      ElMessage.warning('可导出内容为空')
      cancel()
      return
    }
    // 检查查看明文次数是否 > 0
    loading.value = false
    const [_, data] = await to(supplierModel.getRemainDecryptCount())
    remainCount.value = data || 0
    editData.totalCount = Math.min(remainCount.value, props.total || 0)
    if (editData.totalCount <= 0) {
      ElMessage.warning('剩余查看明文次数不足')
      cancel()
    } else {
      dialogVisible.value = true
    }
  }
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-form {
  color: var(--primary-black-color-600);
  .el-form-item {
    margin-bottom: 14px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
}
.el-input-number .el-input__inner {
  text-align: left;
}
</style>