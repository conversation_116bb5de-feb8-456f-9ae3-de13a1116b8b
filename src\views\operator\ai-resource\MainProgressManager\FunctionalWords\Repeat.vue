<template>
  <div class="repeat-container">
    <div class="tw-mb-[8px] tw-flex tw-items-center tw-justify-between">
      <div class="info-title">
        <span class="tw-flex-shrink-0">重复语触发条件：</span>
        <span v-if="!!requirementStringList?.length" class="tw-grow tw-shrink">
          <template v-for="(item, index) in requirementStringList">
            <span v-if="requirementStringList.length > 1">【{{ index + 1 }}】</span>
            {{item?.join('，')}}
            <span>；</span>
          </template>
        </span>
        <span v-else>-</span>
        <el-button :type="tableData && tableData?.length>0 ? 'primary':'default'" link @click="editRequirement" class="tw-ml-[2px]" :disabled="!(tableData && tableData?.length>0)">
          {{isChecked ? '查看' : '修改'}}
        </el-button>
      </div>
      <div class="tw-flex tw-items-center tw-text-[13px]">
        <div class="info-title tw-ml-1">重复语料最后一个默认执行挂机</div>
        <el-button v-if="!isChecked" class="tw-ml-1" type="primary" :icon="Plus" @click="edit">新增</el-button>
      </div>
    </div>
    <el-table
      :data="tableData || []"
      style="width: 100%"
      id="repeat-draggable-boxes"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column v-if="!isChecked" label=" " align="left" width="80">
        <div class="handle tw-cursor-pointer"><el-icon ><Switch /></el-icon></div>
      </el-table-column>
      <el-table-column property="name" label="语料名称" align="left" width="120">
        <template #default="{ row }">
          <div class="tw-w-full tw-flex tw-items-center">
            <span class="tw-line-clamp-2">{{ row.name ?? '-' }}</span>
            <el-icon v-if="row.smsTriggerName" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-sms"></SvgIcon></el-icon>
            <el-icon v-if="row.listenInOrTakeOver" :size="14" color="var(--el-color-primary)" class="tw-ml-[4px]"><SvgIcon name="trigger-human"></SvgIcon></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="content" align="left" label="文字内容" min-width="200">
        <template #default="{ row }">
          <el-tooltip placement="top" trigger="click">
            <template #content><div class="tw-w-[20vw]" v-html="row.content"></div></template>
            <div class="tw-truncate">
              {{ row.content }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column property="eventTriggerValueIds" align="left" label="触发事件" min-width="120">
        <template #default="{ row }">
          <el-tooltip placement="top" trigger="click">
            <template #content><div class="tw-max-w-[20vw]">{{ filterEventValueIds(row.eventTriggerValueIds) }}</div></template>
            <div class="tw-truncate">
              {{ filterEventValueIds(row.eventTriggerValueIds)|| '-' }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column property="updateTime" align="center" label="最后更新时间" width="160" sortable>
        <template #header="{ column }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{column.label}}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretTop /></el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'"><CaretBottom /></el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" align="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="edit(row)">{{!isChecked ? '编辑' : '查看'}}</el-button>
          <el-button type="primary" link @click="editContent(row)">
            打断设置
          </el-button>
          <el-button v-if="!isChecked" type="danger" link @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
  </div>
  <RequirementDialog
    v-model:visible="requirementDialogVisible"
    :requirementData="requirementData"
    :phraseOptions="phraseOptions || []"
    @confirm="findRequirement()"
  />
  <BaseCorpusDrawer v-model:visible="corpusDialogVisible" :corpusData="corpusData" @update:data="search"/>
  <CorpusContentSettingDrawer v-model:visible="corpusContentVisible" :corpusId="corpusData?.id!"/>
</template>

<script lang="ts" setup>
import { ScriptCorpusItem, corpusTypeOption, CorpusDataOrigin, CorpusTypeEnum, EventValueItem, } from '@/type/speech-craft'
import { scriptCorpusModel, } from '@/api/speech-craft'
import { CaretTop, CaretBottom, Plus, Switch, } from '@element-plus/icons-vue'
import Sortable from "sortablejs";
import { reactive, ref, watch, onActivated, nextTick, onDeactivated } from 'vue'
import { ElMessage } from 'element-plus'
import { pickAttrFromObj } from "@/utils/utils";
import Confirm from '@/components/message-box'
import BaseCorpusDrawer from '@/components/corpus/BaseCorpusDrawer.vue'
import RequirementDialog from './RequirementDialog.vue'
import { useScriptStore } from '@/store/script'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from 'await-to-js';
import { translateCorpusRules } from "@/components/corpus/constant";
import { RequirementData, } from './constant'
import { trace } from '@/utils/trace';


const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const scriptStore = useScriptStore()
const scriptId = scriptStore.id
const isChecked = scriptStore.isChecked


const requirementData = ref<RequirementData | null>(null)
const requirementStringList = ref<string[][] | null>([])
// 表格区
const tableData = ref<ScriptCorpusItem[] | null>([])
// 搜索区
const search = async () => {
  loading.value = true
  const data = await to(scriptCorpusModel.findRepeatList({ scriptId }))
  tableData.value =  (data[1] || []).map(item => {
    const scriptMultiContents = item.scriptMultiContents || []
    const content = (scriptMultiContents && scriptMultiContents.length) ? scriptMultiContents[0].scriptUnitContents?.reduce((a, b) => a + b.content, '') || '' : ''
    return {
      ...item,
      content: content
    }
  })
  loading.value = false
}
const findRequirement = async () => {
  loading.value = true
  const data = await to(scriptCorpusModel.findRepeatRequirement({
    scriptId
  })) as [any, RequirementData | null]
  requirementData.value = data[1] || null
  requirementStringList.value = translateRequirement(requirementData.value) || []
  loading.value = false
}
// 操作区
const requirementDialogVisible = ref(false)

const editRequirement = () => {
  requirementDialogVisible.value = true
}
const translateRequirement = (data: RequirementData | null) => {
  if(!data) return[]
  const res = translateCorpusRules(data.semCombineEntity?.satisfySemConditions || [])
  return res
}
const corpusDialogVisible = ref(false)

const corpusData = reactive<ScriptCorpusItem>(new CorpusDataOrigin(scriptId, CorpusTypeEnum['重复语料']))
const edit = (row?: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row,
      corpusTypeOption[corpusData.corpusType!].keys
    ))
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId, CorpusTypeEnum['重复语料']))
  }
  corpusDialogVisible.value = true
}

const delAction = async (id: number) => {
  loading.value = true
  trace({
    page: `话术编辑-功能语料-重复语料：删除(${scriptId})`,
    params: { id },
  })
  const [err] = await to(scriptCorpusModel.deleteFuncCorpus({
    corpusId: id
  }))
  !err && ElMessage.success('删除成功')
  search()
  findRequirement()
  loading.value = false
}
const del = (row: ScriptCorpusItem) => {
  Confirm({ 
    text: `您确定要删除语料【${row.name}】吗?`,
    type: 'danger',
    title: `删除确认`,
    confirmText: '删除'
  }).then(() => {
    delAction(row.id as number)
  }).catch(() => {})
}

/** 语句打断设置 */
const corpusContentVisible = ref(false)
const editContent = (row: ScriptCorpusItem) => {
  if (row && row.id) {
    Object.assign(corpusData, pickAttrFromObj(row,
      corpusTypeOption[corpusData.corpusType!].keys
    ))
  } else {
    Object.assign(corpusData, new CorpusDataOrigin(scriptId, CorpusTypeEnum['最高优先']))
  }
  corpusContentVisible.value = true
}

const sortableDom = ref<Sortable | null>(null)
const initDraggableTable = () => {
  sortableDom.value = Sortable.create(
    document.querySelector('#repeat-draggable-boxes .el-table__body tbody') as HTMLElement, {
    animation: 300,
    sort: !isChecked,
    handle: ".handle",
    onEnd: async (evt) => {
      if (!tableData.value) return
      const newIndex = evt.newIndex as number
      const oldIndex = evt.oldIndex as number
      const currRow = tableData.value.splice(oldIndex, 1)[0];
      tableData.value.splice(newIndex, 0, currRow);
      const list = tableData.value.map(item => item.id!) || []
      loading.value = true
      const params = {
        corpusIdList: list,
        scriptId: scriptId,
      }
      trace({ page: `话术编辑-功能语料-重复语料-排序(${scriptId})`, params: params })
      await scriptCorpusModel.saveRepeatByOrder(params)
      tableData.value = []
      await nextTick()
      search()
      await nextTick()
      loading.value = false
    },
  });
}

const phraseOptions = ref<{
  id: number,
  name: string,
}[] | null>([]) // 核心语义列表
const eventValuesOptions = reactive<{[key: string]: EventValueItem}>({})
const filterEventValueIds = (ids?: number[]) => {
  if (ids && ids.length > 0) {
    const eventTriggerValueArr: string[] = []
    ids && ids?.length > 0 && ids?.map(item => {
      eventValuesOptions[item] && eventTriggerValueArr.push(`${(eventValuesOptions[item].name || '')}(${(eventValuesOptions[item].explanation || '')})`)
    })
    return eventTriggerValueArr.join(',') || ''
  } else {
    return ''
  }
}
// 执行区
const init = async () => {
  const res = await scriptStore.getEventOptions()
  if (res && res.length > 0) {
    res.map(item => {
      item.eventValuemap && Object.assign(eventValuesOptions, item.eventValuemap)
    })
  }
  phraseOptions.value = await scriptStore.getSemanticOptions()
  findRequirement()
}

onActivated(async () => {
  init()
  search()
  initDraggableTable()
})
onDeactivated(() => {
  sortableDom.value?.destroy()
  sortableDom.value = null
  phraseOptions.value = null
  tableData.value = null
  requirementStringList.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.repeat-container {
  width: 100%;
  padding: 12px;
  height: calc(100vh - 240px);
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.el-table {
  font-size: 13px;
}
:deep(.el-table .caret-wrapper) {
  display: none;
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
.info-content {
  display: flex;
  align-items: flex-start;
  max-width: 40vw;
  line-height: 20px;
  overflow: hidden;
}
</style>