import { defineStore } from 'pinia'
import { ref } from 'vue'
import { SeatMember, SeatPageEnum, SeatStatusEnum, SeatTeam } from '@/type/seat'
import { TaskManageItem } from '@/type/task'

export const useSeatInfoStore = defineStore('seatInfo', () => {
  // 当前坐席
  const currentSeat = ref<SeatMember>({})
  // 当前坐席组
  const currentSeatTeam = ref<SeatTeam>({})
  // 坐席是否在线
  const seatOnline = ref<boolean>(false)
  // 坐席是否工作（true 工作 false 休息）
  const seatBusy = ref<boolean>(true)
  // 坐席状态
  const seatStatus = ref(SeatStatusEnum.OFF_LINE)
  // 坐席已签入任务列表
  const seatTaskList = ref<TaskManageItem[]>([])
  // 坐席所处页面（线索列表或者通话抽屉）
  const seatPage = ref<SeatPageEnum>(SeatPageEnum.CLUE)

  /**
   * 重置坐席信息
   * @param clearCurrentSeat 是否清空当前坐席信息
   */
  const resetSeatInfo = (clearCurrentSeat: boolean = true) => {
    if (clearCurrentSeat) {
      currentSeat.value = {}
    }
    currentSeatTeam.value = {}
    seatOnline.value = false
    seatBusy.value = true
    seatStatus.value = SeatStatusEnum.OFF_LINE
    seatTaskList.value = []
    seatPage.value = SeatPageEnum.CLUE
  }

  /* 一些setter函数 */

  const updateCurrentSeat = (data: SeatMember) => {
    currentSeat.value = data || {}
  }
  const updateCurrentSeatTeam = (data: SeatTeam) => {
    currentSeatTeam.value = data || {}
  }
  const updateSeatOnline = (data: boolean) => {
    seatOnline.value = Boolean(data) || false
  }
  const updateSeatBusy = (data: boolean) => {
    seatBusy.value = Boolean(data) || true
  }
  const updateSeatStatus = (data: SeatStatusEnum) => {
    seatStatus.value = data || SeatStatusEnum.OFF_LINE
  }
  const updateSeatStatusByTaskList = (data?: TaskManageItem[]) => {
    seatStatus.value = (Array.isArray(data) ? data : seatTaskList.value)
      ? SeatStatusEnum.HUMAN_MACHINE_IDLE
      : SeatStatusEnum.MANUAL_DIRECT_IDLE
  }
  const updateSeatTaskList = (data: TaskManageItem[]) => {
    seatTaskList.value = data || []
  }
  const updateSeatPage = (data: SeatPageEnum) => {
    seatPage.value = data || SeatPageEnum.CLUE
  }

  return {
    currentSeat,
    currentSeatTeam,
    seatOnline,
    seatBusy,
    seatStatus,
    seatTaskList,
    seatPage,
    resetSeatInfo,
    updateCurrentSeat,
    updateCurrentSeatTeam,
    updateSeatOnline,
    updateSeatBusy,
    updateSeatStatus,
    updateSeatStatusByTaskList,
    updateSeatTaskList,
    updateSeatPage,
  }
})
