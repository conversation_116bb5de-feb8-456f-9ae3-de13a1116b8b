---
description: vue3前端规则1
globs:
alwaysApply: false
---

# Project Structure
ai-report/
├── .git/                     # Git 版本控制目录
├── .cursor/                  # Cursor 编辑器相关配置
├── .vscode/                  # VSCode 配置
├── dist/                     # 构建输出目录
├── node_modules/             # 依赖包目录
├── public/                   # 静态资源目录
├── src/                      # 源代码目录（核心）
│   ├── api/                  # API 接口定义
│   ├── assets/               # 静态资源（图片、图标等）
│   ├── axios/                # Axios 封装
│   ├── components/           # Vue 组件
│   ├── router/               # 路由配置
│   ├── store/                # 状态管理（Pinia）
│   ├── type/                 # TypeScript 类型定义
│   ├── utils/                # 工具函数
│   ├── views/                # 页面视图
│   │   ├── error-pages/      # 错误页面（404，403等）
│   │   ├── layout/           # 布局（侧边栏、主题、头部等）
│   │   ├── Login.vue         # 登录页面
│   │   ├── merchant/         # 商户端页面
│   │   └── operator/         # 运营端页面
│   ├── App.vue               # 根组件
│   ├── main.ts               # 应用入口
│   └── ...                   # 其他配置文件
├── .gitignore                # Git 忽略规则
├── build.js                  # 构建脚本
├── env.d.ts                  # 环境变量类型声明
├── index.html                # 入口 HTML
├── package.json              # 项目配置和依赖
├── postcss.config.cjs        # PostCSS 配置
├── tailwind.config.cjs       # Tailwind CSS 配置
├── tsconfig.json             # TypeScript 配置
├── tsconfig.config.json      # 扩展 TypeScript 配置
└── vite.config.ts            # Vite 构建配置

# Coding Standards

<template>
  <!-- Template content -->
</template>

<script setup lang="ts">
// Vue core imports
import { ref, computed, onMounted } from 'vue'

// Third-party libraries
import { storeToRefs } from 'pinia'

// Internal imports
import { useUserStore } from '@/store/user'
import type { User } from '@/types/user'

// Props definition
const props = defineProps<{
  userId: string
}>()

// Component logic
</script>

<style lang="postcss" scoped>
/* Component styles */
</style>

# 架构
vue3 + ts + pinia + elementplus + tailwindcss + axios + vue-router

# TypeScript Usage
对于接口类型你需要在src/type中找到interface或type或enum，如没有，需要自行创建，如有则引入

# Api Usage
对于需要使用的接口，你需要分析src/api中是否已有接口，如没有，需要自行创建；如有则引入。创建新文件需要遵循以下规则。
import { cancelRequest, http } from '@/axios'
import { } from '@/type/'
export const aiOutboundTaskModel = {
  importPhones: (data: any) => {
    return http({
      data,
      url: "AiSpeech/aiOutboundTask/importPhonesFromExcel",
      method: "POST",
    }).then(res => res as unknown)
  },
}

在文档中引用接口时，请务必使用以下格式

const [err, res] = await to(smsUrlModel.saveNormal(params))
if (err) ...
...

# State Management (Pinia)
import { defineStore } from 'pinia'
import { MerchantAccountInfo, } from '@/type/merchant'

// 当前选中的商户信息
const currentMerchant: MerchantInfo = {}

export const useMerchantStore = defineStore({
  id: 'merchant',
  state() {
    return {
      currentMerchant,
      // ...
    }
  },
  actions: {
    // ...
  },
  persist: [
    {
      storage: sessionStorage,
    }
  ]
})

# Routing
需要在src/router/asyncRoute/index.ts中添加路由，务必分辨是商户端还是运营端（与目录位置做对应）。

import MerchantBlacklist from '@/views/merchant/system-setting/Blacklist/Index.vue'

{
  path: '/merchant/system-setting/blacklist',
  name: 'MerchantBlacklist',
  meta: {
    id: routeMap['商户黑名单'].id,
    type: 2,
    title: routeMap['商户黑名单'].name,
  },
  component: MerchantBlacklist
}

在route-map.ts中添加页面权限。如
'商户黑名单': {
  id: '206-2',
  name: '黑名单设置',
  isMerchant: true,
  permissions: {}
},

# Style Guidelines
优先使用\src\index.pcss的样式，如样式不复杂，可以使用tailwindcss，如样式复杂且使用地方比较多，可以在文档style中添加class；
请确保样式能够在不同尺寸的电脑端运行；

# Permissions
当用户要求增加权限时，需要在src/router/asyncRoute/route-map中增加权限，并在具体按钮或指定处增加权限限制；

# 常量
项目src\assets\js\constant.ts和src\utils\constant.ts中存放全局变量；
文档同级constant中存在局部变量；
当需要使用常量（包括选项，初始化数据，表单规则，class等）时，务必从constant中引入，首先搜索全局和局部变量是否已经存在，如不存在则在合适的位置新增并引入；

# 错误处理/异步处理
对于单个await的错误处理（包括接口、表单校验、二次确认）和一些异步操作需要转为同步的情况，需要使用await，且使用await-to-js的to实现错误处理，减少try-catch的使用。
