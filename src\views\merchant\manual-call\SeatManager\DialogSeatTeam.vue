<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    align-center
    width="600px"
    @close="handleCancel"
  >
    <template #header>
      <div class="form-dialog-header">
        {{ props?.data?.id === -1 ? '新增坐席组' : '编辑坐席组' }}
      </div>
    </template>

    <!--弹窗主体-->
    <el-scrollbar class="form-dialog-main">
      <div class="form-dialog-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="100px"
        >
          <el-form-item label="坐席组名称：" prop="callTeamName">
            <el-input
              v-model.trim="form.callTeamName"
              clearable
              maxlength="20"
              placeholder="命名坐席组名称，20个字符以内"
              show-word-limit
              style="width: 472px;"
            />
          </el-form-item>

          <el-form-item label="人工线路：" prop="tenantLineNumber">
            <el-select
              v-model="form.tenantLineNumber"
              clearable
              filterable
              placeholder="暂未选择，人工线路为空"
              style="width: 472px;"
            >
              <el-option
                v-for="item in merchantLineAllList"
                :key="item.lineNumber"
                :label="item.lineName + (item.enableStatus === 'DISABLE' ? '（已停用）' : '')"
                :value="item.lineNumber"
              >
                <div class="tw-flex tw-justify-between">
                  <span>
                    {{ item.lineName }}
                  </span>
                  <span v-show="item.enableStatus === 'DISABLE'" class="tw-text-red-400">
                    已停用
                  </span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="坐席组长：" prop="leaderAccountId">
            <el-select
              v-model="form.leaderAccountId"
              clearable
              filterable
              placeholder="选择坐席组长"
              style="width: 472px;"
            >
              <el-option
                v-for="item in availableSeatLeaderList"
                :key="item.id"
                :label="item.account + '（' + item.name + '）' "
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="坐席成员：" prop="addCallSeatAccountIds">
            <el-table
              ref="seatTableRef"
              :data="selectedSeatMemberList"
              :header-cell-style="tableHeaderStyle"
              height="150"
              size="small"
              stripe
            >
              <el-table-column align="left" label="账号" min-width="100" prop="account" show-overflow-tooltip>
                <template #default="scope:{row:{account:string}}">
                  {{ scope?.row?.account || '-' }}
                </template>
              </el-table-column>
              <el-table-column align="left" label="姓名" min-width="100" prop="name" show-overflow-tooltip />
              <el-table-column align="left" label="角色" min-width="100" prop="role" show-overflow-tooltip>
                <template #default="scope:{row:{adminRole:string}}">
                  {{ scope?.row?.adminRole || '-' }}
                </template>
              </el-table-column>
              <el-table-column align="center" fixed="right" label="" width="80">
                <template #default="scope:{row:SeatMember}">
                  <el-button
                    v-if="checkSeatDeleteVisible(scope.row)"
                    link
                    type="danger"
                    @click="onClickDeleteMember(scope.row)"
                  >
                    取消添加
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="添加坐席：">
            <el-select
              v-model="selectedSeatMemberId"
              clearable
              filterable
              placeholder="选择坐席组成员"
              style="width: 472px;"
              @visible-change="onSeatMemberSelected"
            >
              <el-option
                v-for="item in leftAvailableSeatMemberList"
                :key="item.id"
                :label="item.account + '（' + item.name + '）' "
                :value="item.id"
              />
            </el-select>
            <div style="color: var(--primary-red-color); font-size: 13px;">
              账号加入坐席组后暂不支持删除，请确认无误后再添加
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" @click="handleCancel">
          取消
        </el-button>
        <el-button :icon="Select" type="primary" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, nextTick, reactive, ref, toRaw, watch } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { SeatLeader, SeatMember, SeatTeam, SeatTeamApiParam } from '@/type/seat'
import { ElMessage, ElMessageBox, FormRules } from 'element-plus'
import { MerchantLineInfo, MerchantLineTypeEnum, MerchantLineEnableStatusEnum } from '@/type/merchant'
import { merchantModel } from '@/api/merchant'
import { seatManagerModel, seatServerModel } from '@/api/seat'
import { useUserStore } from '@/store/user'
import { Throttle } from '@/utils/utils'
import { tableHeaderStyle } from '@/assets/js/constant'
import to from "await-to-js";

// ---------------------------------------- 通用 开始 ----------------------------------------

const userStore = useUserStore()

const props = defineProps<{
  visible: boolean,
  data: SeatTeam,
  type?: string,
  seatList?: SeatTeam[],
}>()
const emits = defineEmits([
  'close',
  'update'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

// 当前坐席组信息
const team: SeatTeam = reactive({})

watch(() => props.visible, (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      // 清除表单的校验结果
      formRef.value?.resetFields()

      // 坐席组信息
      Object.assign(team, JSON.parse(JSON.stringify(props.data)))

      // 表单数据
      // 有ID就使用传入的数据，没ID或者ID不正确就用表单默认值
      Object.assign(form, formDefault())
      if (props.data.id && props.data.id > -1) {
        prop2Form(props.data, form)
      }

      updateMerchantLineAllList()
      updateAvailableSeatLeaderList()
      updateAvailableSeatMemberList()
      updateSelectedSeatMemberList()
    }
  }
)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据
const formDefault = (): SeatTeamApiParam => {
  return {
    id: -1,
    groupId: userStore.groupId,
    callTeamId: undefined,
    callTeamName: '',
    tenantLineNumber: '',
    leaderAccountId: null,
    addCallSeatAccountIds: [],
  }
}
// 表单数据
const form: SeatTeamApiParam = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  callTeamName: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('坐席组名称不能为空'))
      } else {
        callback()
      }
    }
  },
  leaderAccountId: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('坐席组长不能为空'))
      } else {
        callback()
      }
    }
  },
  addCallSeatAccountIds: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!team?.callSeatIds?.length && !value?.length) {
        callback(new Error('坐席成员不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 清除表单的校验结果
  formRef.value?.resetFields()
  // 表单数据恢复默认值
  selectedSeatMemberId.value = null
  selectedSeatMemberList.value = []
  Object.keys(team).forEach((prop: string) => {
    team[<keyof SeatTeam>prop] = undefined
  })
  Object.assign(form, formDefault())
  // 清除表单的校验结果
  formRef.value?.resetFields()
}
/**
 * 提交表单
 */
const submit = async () => {
  // 提交节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  try {
    // 处理参数 组件参数不用传到接口参数
    const data: SeatTeamApiParam = toRaw(form)
    const params: SeatTeamApiParam = {
      id: data.id,
      groupId: data.groupId,
      callTeamId: data.id,
      callTeamName: data.callTeamName,
      tenantLineNumber: data.tenantLineNumber,
      leaderAccountId: data.leaderAccountId,
      addCallSeatAccountIds: data.addCallSeatAccountIds,
    }
    if (params.id === -1) {
      params.id = undefined
      params.callTeamId = undefined
    }

    // 请求接口
    const res = <SeatTeam>await seatManagerModel.saveSeatTeam(params)

    ElMessage({
      message: form.id! > -1 ? '编辑成功' : '创建成功',
      type: 'success',
    })
    emits('update')

    // 如果坐席成员列表不为空，就绑定FS账号
    if (res?.callSeats?.length) {
      await bindAllSeatMember(res?.callSeats)
    }

    // 关闭弹窗
    closeDialog()
  } catch (e) {
  } finally {
    // 提交节流锁解锁
    throttleConfirm.unlock()
  }
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  emits('close')
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}
/**
 * 组件props数据转换成组件表单数据
 */
const prop2Form = (data: Record<string, any>, form: Record<string, any>) => {
  const dataPropList = Object.keys(data)
  const formPropList = Object.keys(form)
  formPropList.forEach((prop: string) => {
    if (dataPropList.includes(prop)) {
      form[prop] = data[prop]
    }
  })
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 外呼线路 开始 ----------------------------------------

// 人工线路全部列表 + 当前选择线路
const merchantLineAllList = ref<MerchantLineInfo[]>([])

/**
 * 获取全部人工线路列表
 */
const updateMerchantLineAllList = async () => {
  const [err, res] = await to(merchantModel.getMerchantLineListByCondition({
    groupId: userStore.groupId,
  }))
  if (err) {
    ElMessage.error('无法获取人工线路列表')
    merchantLineAllList.value = []
    return
  }
  merchantLineAllList.value = (res || []).filter(item => {
    return (item.enableStatus === MerchantLineEnableStatusEnum['启用'] && item.lineType === MerchantLineTypeEnum['人工直呼'])
      || item.lineNumber === props.data.tenantLineNumber
  })
}

// ---------------------------------------- 外呼线路 结束 ----------------------------------------

// ---------------------------------------- 人员组成 开始 ----------------------------------------

// 人员组成表格DOM
const seatTableRef = ref()
// 可选坐席组长列表
const availableSeatLeaderList = ref<SeatLeader[]>([])
// 全部可选坐席成员列表
const allAvailableSeatMemberList = ref<SeatMember[]>([])
// 剩余可选坐席成员列表
const leftAvailableSeatMemberList = ref<SeatMember[]>([])
// 当前选中的添加坐席成员
const selectedSeatMemberId = ref<number | null>(null)

// 已选坐席成员列表
const selectedSeatMemberList = ref<SeatMember[]>([])

/**
 * 获取可选坐席组长列表
 */
const updateAvailableSeatLeaderList = async () => {
  try {
    // 处理参数
    const params = {
      groupId: userStore.groupId,
      callTeamId: form.id === -1 ? '' : form.id,
    }
    // 请求接口
    const res = <SeatLeader[]>await seatManagerModel.getAvailableLeader(params)
    // 更新列表
    availableSeatLeaderList.value = Array.isArray(res) ? res : []
    // 将当前选中的坐席组长也暂时添加进去，以便表单能够正常显示坐席组长名字
    const currentLeader = availableSeatLeaderList.value.find((leader: SeatLeader) => {
      return team.leaderAccountId === leader?.id
    })
    if (!currentLeader) {
      availableSeatLeaderList.value.unshift(JSON.parse(JSON.stringify(team.leaderAccount)))
    }
  } catch (e) {
  }
}
/**
 * 获取可选坐席成员列表
 */
const updateAvailableSeatMemberList = async () => {
  try {
    // 处理参数
    const params = {
      groupId: userStore.groupId,
      callTeamId: form.id === -1 ? '' : form.id,
    }
    // 请求接口
    const res = <SeatMember[]>await seatManagerModel.getAvailableMember(params)
    // 更新列表
    const arr = Array.isArray(res) ? res : []
    allAvailableSeatMemberList.value = JSON.parse(JSON.stringify(arr))
    leftAvailableSeatMemberList.value = JSON.parse(JSON.stringify(arr))
  } catch (e) {
  }
}
/**
 * 获取已选坐席成员列表
 */
const updateSelectedSeatMemberList = async () => {
  // 坐席组已经选中的成员
  selectedSeatMemberList.value = JSON.parse(JSON.stringify(team?.callSeats ?? [])) ?? []

  // 坐席组此次编辑新增的成员
  form?.addCallSeatAccountIds?.forEach((memberId: number) => {
    const item = allAvailableSeatMemberList.value?.find((member: SeatMember) => {
      return member?.id === memberId
    })
    if (item && !team.callSeatIds?.includes(item?.id ?? -1)) {
      selectedSeatMemberList.value.unshift(item)
    }
  })

  // 滚动到列表顶部
  await nextTick()
  seatTableRef.value?.setScrollTop(0)
}
/**
 * 判断坐席的删除按钮是否显示
 * @param {SeatMember} row 坐席成员信息
 */
const checkSeatDeleteVisible = (row: SeatMember) => {
  return !!form?.addCallSeatAccountIds?.find((item: number) => {
    return item === row?.id
  })
}
/**
 * 点击删除坐席按钮
 * @param {SeatMember} row 坐席成员信息
 */
const onClickDeleteMember = (row: SeatMember) => {
  // 从接口参数中移除
  const formIndex = form?.addCallSeatAccountIds?.findIndex((item: number) => {
    return item === row?.id
  }) ?? -1
  if (formIndex > -1) {
    form.addCallSeatAccountIds!.splice(formIndex, 1)
  }

  // 从已选列表中移除
  const addedIndex = selectedSeatMemberList.value?.findIndex((item: SeatMember) => {
    return item?.id === row?.id
  }) ?? -1
  if (addedIndex > -1) {
    selectedSeatMemberList.value.splice(addedIndex, 1)
  }

  // 重新添加到可选列表
  const deleteId = form.addCallSeatAccountIds![formIndex]
  const deleteItem = allAvailableSeatMemberList.value.find((item: SeatMember) => {
    return item?.id === deleteId
  })
  if (deleteItem) {
    leftAvailableSeatMemberList.value.push(deleteItem)
  }
}
/**
 * 添加坐席成员 下拉框切换显示隐藏
 * @param visible 显示隐藏状态
 */
const onSeatMemberSelected = async (visible: boolean) => {
  if (visible) {
    // 下拉框从隐藏到显示时
  } else {
    // 下拉框从显示到隐藏时
    await nextTick()

    // 如果点击清空按钮，会关闭选择器，这种情况不应该提交信息

    if (selectedSeatMemberId.value) {
      // 判断是否重复
      const has = team.callSeatIds?.includes(selectedSeatMemberId.value)
        || form.addCallSeatAccountIds?.includes(selectedSeatMemberId.value)

      if (has) {
        // 重复添加提示
        ElMessage({
          message: '该成员已被添加，请勿重复添加',
          type: 'warning',
        })
      } else {
        // 添加选中的坐席成员
        form.addCallSeatAccountIds?.push(selectedSeatMemberId.value ?? -1)
        // 可选成员列表里移除
        const index = leftAvailableSeatMemberList.value.findIndex((member: SeatMember) => {
          return member?.id === selectedSeatMemberId.value
        })
        if (index > -1) {
          leftAvailableSeatMemberList.value.splice(index, 1)
        }
        await nextTick()
        // 更新成员列表
        await updateSelectedSeatMemberList()
      }
      // 清空选择器
      selectedSeatMemberId.value = null
    }

    // 表单项校验
    formRef.value && formRef.value.validateField('addCallSeatAccountIds')
  }
}
/**
 * 所有坐席成员绑定FS账号
 * @param {SeatMember[]} seatList 坐席成员列表
 */
const bindAllSeatMember = async (seatList: SeatMember[]) => {
  // 所有坐席成员都尝试绑定FS账号，防止以前的坐席成员没有正确绑定，
  // 而且绑定FS账号的接口，同一账号重复绑定也会返回success，后端实际上保存的FS账号是不变的

  // 提取出每个坐席成员的坐席ID
  const tempIdList: any[] = seatList?.map((member: SeatMember) => {
    return member?.id
  }) || []
  // 筛选出数字，但是不包含NaN
  const idList: number[] = tempIdList.filter((id: any) => {
    return typeof id === 'number' && !Number.isNaN(id)
  })
  // 数组去重
  const seatIdList = <number[]>Array.from(new Set(idList))

  // 请求接口批量绑定
  const res = <{}>await seatServerModel.bind({
    // 以半角逗号隔开每个ID
    seatId: seatIdList?.join(',') ?? '',
  }) ?? {}
  const success = Object.values(res).every((val: any) => {
    return val === 'success'
  })
  if (success) {
    ElMessage({
      message: '所有坐席成员都成功绑定FS账号',
      type: 'success',
    })
  } else {
    let allStrList: string[] = []
    // 拼接错误原因字符串
    Object.keys(res).forEach((key: string) => {
      const seat = seatList.find((member: SeatMember) => {
        return member?.id + '' === key
      })
      const account = seat?.account ?? ''
      const reason = res[<keyof typeof res>key] ?? ''
      const str = '【' + account + '】' + reason + '\n'
      allStrList.push(str)
    })
    let finalStr = allStrList.join('')
    for (let i = 0; i < 20; i++) {
      finalStr = finalStr + finalStr
    }

    ElMessageBox.confirm(
      finalStr,
      '部分坐席绑定FS账号失败，请重试',
      {
        cancelButtonText: '关闭',
        confirmButtonText: '重试',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      }
    ).then(async () => {
      await bindAllSeatMember(seatList)
    }).catch(() => {
    })
  }
}

// ---------------------------------------- 人员组成 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
</style>
