<template>
  <el-dialog
    v-model="dialogVisible"
    width="900px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ props.title }}</div>
    </template>
      <el-table
        v-loading="loading"
        :data="tableData"
        ref="tableRef"
        id="label-draggable-table"
        style="width: 100%"
        row-key="id"
        border
        :header-cell-style="tableHeaderStyle"
        stripe
      >
        <el-table-column property="ruleName" align="left" fixed="left" width="120" label="规则名称"></el-table-column>
        <el-table-column align="left" label="规则满足条件"  min-width="280">
          <template #default="{ row, $index }">
            <div v-if="row.matchText && row.matchText.length" v-for="(item, index) in row.matchText||[]" :key="`${$index}-${index}`">
              <el-divider v-if="index" border-style="dashed">or</el-divider>
              <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
                {{ item2 || '-' }}
              </li>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="规则排除条件"  min-width="280">
          <template #default="{ row, $index }">
            <div v-if="row.excludeText && row.excludeText.length" v-for="(item, index) in row.excludeText||[]" :key="`${$index}-${index}`">
              <el-divider v-if="index" border-style="dashed">or</el-divider>
              <li v-for="(item2, index2) in item" :key="`${$index}-${index}-${index2}`">
                {{ item2 || '-' }}
              </li>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column align="center" property="intentionLevelName" width="80" label="分类结果" :formatter="formatterEmptyData"></el-table-column>
        <el-table-column align="left" property="intentionTagName" width="100" label="标签" :formatter="formatterEmptyData"></el-table-column>
        <template #empty>
          <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
        </template>
      </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, onUnmounted,} from 'vue'
import to from 'await-to-js'
import { AdvancedRulesItem, } from '@/type/IntentionType'
import { formatterEmptyData } from '@/utils/utils'
import { tableHeaderStyle } from '@/assets/js/constant'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { translateMultRules } from '@/components/script-rules/constant'
import { useScriptStore } from '@/store/script'
import { ElMessage } from 'element-plus'
import { onBeforeRouteLeave } from 'vue-router'


const emits = defineEmits(['update:data',])
const props = withDefaults(defineProps<{
  data: string | null,
  scriptId: number | undefined,
  title: string,
}>(), {
})

const loading = ref(false)
const tableData = ref<AdvancedRulesItem[] | null>(null)

const dialogVisible = ref(!!props.data)
const cancel = () => {
  dialogVisible.value = false
  emits('update:data', '')
}
const scriptStore = useScriptStore()
watch(() => props.data, async () => {
  dialogVisible.value = !!props.data
  if (!!props.data) {
    if (!props.scriptId) {
      ElMessage.warning('获取话术ID失败')
      return cancel()
    }
    loading.value = true
    // 更新高级规则、最终意向需要使用的数据
    scriptStore.id = props.scriptId
    await scriptStore.getQaAndPriorOptions()
    await scriptStore.getIntentionLevelOptions(true)
    await scriptStore.getIntentionTagOptions(true)
    await scriptStore.getProcessOptions(true)
    await scriptStore.getSemanticOptions(true)
    await scriptStore.getSemanticLabelOptions(true)
    await scriptStore.getEventOptions()
    await scriptStore.getKnowledgeGroupOptions()
    await scriptStore.getAllDeepCorpusOptions()
    const [_, res] = await to(aiOutboundTaskModel.findAdvancedRulesInRecord({ ruleIds: props.data }))
    tableData.value = (res || []).map(item => {
      const matchText = translateMultRules(item.matchConditionList)
      const excludeText = translateMultRules(item.excludeConditionList)
      return {
        ...item,
        matchText,
        excludeText,
      }
    })
    loading.value = false
  } else {
    tableData.value = null
    scriptStore.clearScriptData()
  }
})

onUnmounted(() => {
  tableData.value = null
  scriptStore.clearScriptData()
})
onBeforeRouteLeave(() => {
  tableData.value = null
  scriptStore.clearScriptData()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.info-data-box-inner {
  padding: 4px 8px;
  gap: 0;
}
.name-box {
  display: flex;
  height: 36px;
  padding: 8px 12px;
  align-items: center;
  gap: 10px;
  font-size: 13px;
  border-radius: 4px;
  background: #F0F2F5;
  position: relative;
  line-height: 20px;
  color: var(--primary-black-color-600);
}
.rectangle-left {
  border-radius: 0px 2px 2px 0px;
  width: 2px;
  height: 24px;
  position: absolute;
  left: 0;
  top: 6px;
  background-color: var(--primary-orange-color)
}
</style>
