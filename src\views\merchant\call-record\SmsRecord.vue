<template>
  <HeaderBox title="短信发送记录" />
  <div class="module-container">
    <div class="search-box tw-mb-[16px]">
      <div class="tw-grid tw-grid-cols-4 tw-gap-[10px]" :class="isExpand ? 'tw-pb-[8px]':'tw-pb-[12px]'">
        <div class="item">
          <InputPhonesBox
            v-model:value="searchForm.phone"
            @search="search()"
          />
        </div>
        <div class="item">
          <el-select
            v-model="searchForm.smsStatusList"
            placeholder="短信状态"
            multiple
            collapse-tags
            :max-collapse-tags="1"
            collapse-tags-tooltip
            clearable
          >
            <el-option v-for="item in smsStatusOption" :key="item.value" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div class="item tw-col-span-2">
          <span class="tw-w-[66px] tw-shrink-0">触发时间：</span>
          <TimePickerBox
            v-model:start="searchForm.triggerTimeStart"
            :splitToday="true"
            :maxRange="60*60*24*31*1000"
            @change="handleTimeChange()"
            v-model:end="searchForm.triggerTimeEnd"
            :clearable="false"
          />
        </div>
      </div>
      <div v-show="isExpand" class="tw-grid tw-grid-cols-4 tw-gap-[8px] tw-pb-[12px]">
        <div class="item">
          <el-select
            v-model="searchForm.messageTypeList"
            placeholder="短信类型"
            multiple
            collapse-tags
            :max-collapse-tags="2"
            collapse-tags-tooltip
            clearable
          >
            <el-option v-for="item in smsTypeOption" :key="item.value" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div v-if="accountType === 0" class="item">
          <el-select
            v-model="searchForm.sendType"
            placeholder="发送类型"
            clearable
          >
            <el-option v-for="item in sendTypeOption" :key="item.value" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div class="item">
          <el-select
            v-model="searchForm.receiptStatusList"
            placeholder="回执状态"
            multiple
            collapse-tags
            :max-collapse-tags="2"
            collapse-tags-tooltip
            clearable
          >
            <el-option v-for="item in receiptStatusOption" :key="item.value" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div class="item">
          <span class="tw-w-[66px] tw-shrink-0">回执时间：</span>
          <TimePickerBox
            v-model:start="searchForm.receiptTimeStart"
            v-model:end="searchForm.receiptTimeEnd"
            clearable
          />
        </div>
        <div class="item">
          <el-select
            v-model="searchForm.receiptTimeout"
            placeholder="回执是否超时"
            clearable
          >
            <el-option v-for="item in receiptTimeoutOption" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div class="item">
          <el-select
            v-model="searchForm.submitStatus"
            placeholder="提交状态"
            clearable
          >
            <el-option v-for="item in submitStatusOption" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div class="item">
          <span class="tw-w-[66px] tw-shrink-0">提交时间：</span>
          <TimePickerBox
            v-model:start="searchForm.submitTimeStart"
            v-model:end="searchForm.submitTimeEnd"
            clearable
          />
        </div>
        <div class="item">
          <SelectPageBox
            v-model:selectVal="searchForm.scriptStringId"
            :options="speechCraftList||[]"
            name="scriptName"
            val="scriptStringId"
            placeholder="执行话术"
            :filterable="true"
            class="tw-flex-grow"
            multiple
            canSelectAll
          >
          </SelectPageBox>
        </div>
        <div class="item">
          <SelectPageBox
            v-model:selectVal="searchForm.taskIdList"
            :options="taskList||[]"
            :loading="loadingTask"
            name="taskName"
            val="id"
            key="id"
            placeholder="外呼任务"
            :filterable="true"
            class="tw-flex-grow"
            isRemote
            @update:options="updateTaskList"
            :total="taskNum"
            multiple
            canSelectAll
          >
          </SelectPageBox>
        </div>
        <div class="item">
          <el-select
            v-model="searchForm.businessType"
            placeholder="业务类型"
            clearable
          >
            <el-option v-for="item in businessTypeEnumOption" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div v-if="accountType === 0" class="item">
          <el-select
            v-model="searchForm.compensationStatus"
            placeholder="补偿状态"
            clearable
          >
            <el-option v-for="item in compensationStatusOption" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div v-if="accountType === 0" class="item">
          <el-select
            v-model="searchForm.groupId"
            placeholder="商户账号"
            filterable
            clearable
          >
            <el-option v-for="item in masterAccountList" :key="item.account" :label="item.account" :value="item.groupId"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.operator" placeholder="运营商" clearable>
            <el-option v-for="item in operatorList" :key="item" :label="item" :value="item"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.province" placeholder="省" filterable clearable>
            <el-option v-for="item in provinceList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.city" placeholder="市" filterable clearable :filter-method="filterCity">
            <el-option v-for="item in cityList.slice(0, cityNum)" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
            <div v-if="cityList?.length > cityNum" class="tw-py-[2px] tw-z-10 tw-relative">
              <el-button type="primary" link @click="cityNum=cityNum+50">
                加载更多
              </el-button>
            </div>
          </el-select>
        </div>

        <div class="item">
          <SelectBox
            v-model:selectVal="searchForm.callTeamIdList"
            :options="callTeamList||[]"
            name="callTeamName"
            val="id"
            placeholder="坐席组"
            filterable
            class="tw-flex-grow"
            multiple
          >
            <template v-slot:option-tips="{ option }">
              <span class="tw-text-gray-400">{{ option.masterAccount?.account || ''}}</span>
            </template>
          </SelectBox>
        </div>
        <div class="item">
          <SelectBox
            v-model:selectVal="searchForm.callSeatIdList"
            :options="callSeatList||[]"
            name="account"
            val="id"
            placeholder="坐席"
            filterable
            class="tw-flex-grow"
            multiple
          >
            <template v-slot:option-tips="{ option }">
              <span class="tw-text-gray-400">{{ option.masterAccount?.account || '' }}</span>
            </template>
          </SelectBox>

        </div>

        <div class="item">
          <el-select
            v-model="searchForm.smsTemplateId"
            filterable
            placeholder="短信模板"
            clearable
          >
            <el-option v-for="item in smsTemplateList" :key="item.id" :label="item.templateName" :value="item.id">
              <span>{{ item.templateName }}</span>
              <span v-show="item.extra" class="tw-text-gray-400 tw-float-right">{{ `(${ item.extra })` }}</span>
            </el-option>
          </el-select>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.smsContent"
            placeholder="短信内容"
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div v-if="accountType === 0" class="item">
          <el-input
            v-model.trim="searchForm.smsShortLinkUrl"
            placeholder="短链地址"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div v-if="accountType === 0" class="item">
          <el-select
            v-model="searchForm.smsAccountNumber"
            placeholder="短信对接账号"
            filterable
            clearable
          >
            <el-option v-for="item in smsAccountList" :key="item.smsAccountNumber" :label="item.name" :value="item.smsAccountNumber"/>
          </el-select>
        </div>
        <div v-if="accountType === 0" class="item">
          <el-select
            v-model="searchForm.smsSupplierNumber"
            filterable
            placeholder="短信供应商"
            clearable
          >
            <el-option v-for="item in smsSupplierList" :key="item.supplierNumber" :label="item.name" :value="item.supplierNumber"/>
          </el-select>
        </div>
        <div v-if="accountType === 0" class="item">
          <el-input
            v-model.trim="searchForm.returnCode"
            placeholder="返回码"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.name"
            placeholder="姓名"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.company"
            placeholder="公司"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.remarks"
            placeholder="备注"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>

        <div class="item">
          <el-select v-model="searchForm.needOrder" placeholder="是否排序">
            <el-option label="排序" value="1"/>
            <el-option label="不排序" value="0"/>
          </el-select>
        </div>
      </div>
      <div class="tw-flex tw-justify-between tw-pt-[12px] tw-items-center tw-border-t-[1px] tw-h-[32px]">
        <div class="tw-float-left tw-leading-[32px]">
          <ColumnSetting
            :totalList="totalCols"
            :defaultList="defaultCols"
            :disabledList="disabledCols"
            :name="'sms-record'"
          />
        </div>
        <div class="tw-float-right tw-leading-[32px]">
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
          <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>
            收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon>
          </el-button>
          <el-button type="primary" v-else @click="isExpand=true" link>
            展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData||[]"
      ref="tableRef"
      v-loading="loading"
      :header-cell-style="{background:'#F7F8FA', color: '#646566'}"
      stripe
      class="tw-flex-grow"
      :row-key="(row: SmsRecord) =>row.id??''"
    >
      <el-table-column v-if="selectCols.includes('号码')" label="号码" align="left" fixed="left" min-width="170">
        <template #default="{ row, $index }">
          <div class="phone-msg" :class="currentIndex===$index ? 'tw-text-[#165DFF]':''">
            <span>{{ filterPhone(row.recordId) + ' ' + (row.operator || '') }}</span>
            <el-tooltip content="复制" placement="right" :show-after="500">
              <el-icon :size="14" class="hover:tw-text-[var(--el-color-primary)] tw-cursor-pointer" @click="copy(row.recordId)"><SvgIcon name="copy" color="inherit"></SvgIcon></el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('省市')" label="省市" align="left" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.province || row.city ? (filterProvinceName(row.province) || '') + ' ' + (row.city || '') : '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('姓名')" property="name" label="姓名" align="left" min-width="80" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('公司')" property="company" label="公司" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('备注')" property="remarks" label="备注" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <template v-if="accountType === 0">
        <el-table-column v-if="selectCols.includes('项目')" property="programName" label="项目" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #default="{ row }">
            {{ row.groupId ? projectMap.get(row.groupId)?.programName || '-' : '-' }}
          </template>
        </el-table-column>
        <el-table-column v-if="selectCols.includes('行业')" property="secondIndustryName" label="行业" align="left" min-width="120" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #default="{ row }">
            {{ row.groupId ? projectMap.get(row.groupId)?.secondIndustryName || '-' : '-' }}
          </template>
        </el-table-column>
        <el-table-column v-if="selectCols.includes('产品')" property="productName" label="产品" align="left" min-width="200" show-overflow-tooltip :formatter="formatterEmptyData">
          <template #default="{ row }">
            {{ row.groupId ? projectMap.get(row.groupId)?.productName || '-' : '-' }}
          </template>
        </el-table-column>
      </template>

      <el-table-column v-if="selectCols.includes('短信类型')" align="center" prop="messageType" label="短信类型" width="120">
        <template #default="{ row }">
          {{ findValueInEnum(row?.messageType, SmsTypeEnum) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('发送类型') && accountType === 0" prop="sendType" label="发送类型" align="center" width="120">
        <template #default="{ row }">
          {{ findValueInEnum(row?.sendType, SendTypeEnum) }}
        </template>
      </el-table-column>
      <el-table-column v-if="accountType === 0 && selectCols.includes('所属账号')" property="account" label="所属账号" align="left" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
      <el-table-column v-if="selectCols.includes('任务名称')" prop="taskName" label="任务名称" width="240" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('执行话术')" prop="scriptName" label="执行话术" width="240" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('接待坐席')" property="callSeatId" label="接待坐席" align="left" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ callSeatList?.find(item => item.id === row.callSeatId)?.account || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('所属坐席组')" property="callTeamName" label="所属坐席组" align="left" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
      </el-table-column>

      <el-table-column v-if="selectCols.includes('触发时间')" prop="triggerTime" label="触发时间" width="160" align="center"></el-table-column>
      <el-table-column v-if="selectCols.includes('提交状态')" prop="submitStatus" label="提交状态" width="120" align="center">
        <template #default="{ row }">
          {{ findValueInEnum(row?.submitStatus, SubmitStatusEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('提交时间')" prop="submitTime" label="提交时间" width="160" align="center" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('回执状态')" prop="receiptStatus" label="回执状态" width="120" align="center">
        <template #default="{ row }">
          {{ findValueInEnum(row?.receiptStatus, ReceiptStatusEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('回执时间')" prop="receiptTime" label="回执时间" width="160" align="center" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('回执超时')" prop="receiptTimeout" label="回执超时" width="120" align="center">
        <template v-slot="{ row }">
          {{ findValueInStatus(row?.receiptTimeout) || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('补偿状态')" prop="compensationStatus" label="补偿状态" width="180">
        <template v-slot="{ row }">
          {{ findValueInStatus(row?.compensationStatus) || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('短信状态')" prop="smsStatus" label="短信状态" width="180">
        <template #default="{ row }">
          {{ findValueInEnum(row?.smsStatus, SmsStatusEnum) }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('短信模板')" prop="smsTemplateName" label="短信模板" width="240" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('短信内容')" prop="smsContent" label="短信内容" width="240" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('短链ID')" prop="shortLinkOrdinaryNumbers" label="普通短链ID" width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('短链地址')" prop="shortLinkOrdinaryUrls" label="普通短链地址" width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('短链ID')" prop="shortLinkThousandNumbers" label="千人千链ID" width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('短链地址')" prop="shortLinkThousandUrls" label="千人千链地址" width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('业务类型')" prop="businessType" label="业务类型" width="120">
        <template #default="{ row }">
          {{ findValueInEnum(row?.businessType, BusinessTypeEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="selectCols.includes('失败原因')" prop="failureCause" label="失败原因" width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('短信对接账号')" prop="smsAccountName" label="短信对接账号" width="160" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('返回码')" prop="returnCode" label="返回码" width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('短信供应商')" prop="smsSupplierName" label="短信供应商" width="180" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('字数统计')" prop="wordCount" label="字数统计" width="120" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <el-table-column v-if="selectCols.includes('计费条数')" prop="costCount" label="计费条数" width="80" show-overflow-tooltip :formatter="formatterEmptyData"></el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      class="tw-grow-0"
      :pageSize="pageSize"
      :dynamic-total="dynamicTotal"
      :currentPage="currentPage"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="search(currentPage)"
      @update="updatePage"
    >
    </PaginationBox>
  </div>
</template>

<script lang="ts" setup>
import { aiOutboundTaskModel } from '@/api/ai-report'
import { reactive, computed, ref, watch, onUnmounted, } from 'vue'
import { ArrowUp, ArrowDown, } from '@element-plus/icons-vue'
import { OperatorEnum, } from '@/type/common'
import { useGlobalStore } from '@/store/globalInfo'
import { useUserStore } from '@/store/user'
import dayjs from 'dayjs'
import { SmsTypeEnum, SendTypeEnum, SubmitStatusEnum, ReceiptStatusEnum, SmsStatusEnum, SmsRecord,
  SmsSearchModal, SmsSearchOrigin, BusinessTypeEnum, SmsProviderStatusEnum, SmsAccountItem
 } from '@/type/sms'
import {
  copyText,
  filterPhone,
  formatterEmptyData,
  enum2Options,
  findValueInEnum,
  findValueInStatus,
} from '@/utils/utils'
import { useTaskStore } from '@/store/taskInfo'
import { ResponseData } from '@/axios/request/types'
import { SeatTeam, SeatMember, } from '@/type/seat'
import { SpeechCraftInfoItem, } from '@/type/speech-craft'
import to from 'await-to-js';
import { getColumnSettingByName, getCallTeamAndSeatOptions, filterProvinceName, } from './constants'
import { MerchantProjectItem } from '@/type/merchant'
import InputPhonesBox from '@/components/InputPhonesBox.vue'
import SelectPageBox from '@/components/SelectPageBox.vue'
import HeaderBox from '@/components/HeaderBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import PaginationBox from '@/components/PaginationBox.vue'
import ColumnSetting from '@/components/ColumnSetting.vue'
import { onBeforeRouteLeave } from 'vue-router'
import SelectBox from '@/components/SelectBox.vue'
import { TaskManageItem, TaskTypeEnum } from '@/type/task'
import { monitorStatisticModel } from '@/api/monitor-statistic'
import { merchantSmsTemplateModel, } from '@/api/merchant'
import { smsProviderModel, smsAccountModel, } from '@/api/sms'

const taskStore = useTaskStore()
const globalStore = useGlobalStore()
const userInfo = useUserStore()
const accountType = userInfo.accountType
const loading = ref(false)
const {totalCols, defaultCols, disabledCols} = getColumnSettingByName('sms-record')
const selectCols = computed(() => userInfo.colInfo['sms-record'] || [])
// 表格和分页
const tableData = ref<SmsRecord[] | null>([])
const pageSizeList = [20, 50, 100, 200]
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(pageSizeList[0])
const isExpand = ref(false)

const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search(currentPage.value, true)
}

const copy =(val: string) => {
  copyText(val || '')
}
// 筛选区：省份、城市
const city = ref<string | undefined>(undefined)
const province = ref<string | undefined>(undefined)
watch(province, () => {
  searchForm.province = province.value?.split(',')[1] || undefined
  city.value = undefined
})


// 搜索数据
const searchForm = reactive<SmsSearchModal>(new SmsSearchOrigin(accountType))
// 外呼时间变化，更新是否排序筛选项和任务列表
const handleTimeChange = () => {
  if (dayjs(searchForm.triggerTimeStart).isBefore(dayjs().startOf('day'))) {
    searchForm.needOrder = '0'
  } else {
    searchForm.needOrder = '1'
  }
  updateTaskList()
}

// 省份列表
const provinceList = ref<string[] | null>([])
const provinceAllMap = ref<{ [key: string]: string[] } | null>({})
// 城市列表
const cityList = ref<string[]>([])
const cityNum = ref(20)
const filterCity = (val: string = '') => {
  if (!provinceAllMap.value) return (cityList.value = [])
  const data = province.value ? (provinceAllMap.value[province.value] || []) : (Object.values(provinceAllMap.value).flat() || [])
  cityList.value = data.filter(item => !val || item.includes(val))
}

// 初始化数据
const callTeamList = ref<SeatTeam[] | null>([])
const callSeatList = ref<SeatMember[] | null>([])
const speechCraftList = ref<SpeechCraftInfoItem[] | null>([])
const taskList = ref<TaskManageItem[] | null>([])
const loadingTask = ref(false)
const taskNum = ref(0)
const smsStatusOption = enum2Options(SmsStatusEnum)
const smsTypeOption = enum2Options(SmsTypeEnum)
const sendTypeOption = enum2Options(SendTypeEnum)
const receiptStatusOption = enum2Options(ReceiptStatusEnum)
const submitStatusOption = enum2Options(SubmitStatusEnum)
const businessTypeEnumOption = enum2Options(BusinessTypeEnum)
const compensationStatusOption = [
  { name: '未补偿', value: '0' },
  { name: '已补偿', value: '1' },
]
const receiptTimeoutOption = [
  { name: '是', value: '1' },
  { name: '否', value: '0' },
]

const updateTaskList = async (val: string = '', startPage: number = 0, pageSize: number = 50) => {
  loadingTask.value = true
  const data = await aiOutboundTaskModel.findTaskList({
    ifFindAll: accountType === 0 ? '1' : undefined, // 查询所有
    startTime: searchForm.triggerTimeStart,
    endTime: searchForm.triggerTimeEnd,
    taskName: val ? val : undefined,
    startPage: startPage,
    pageNum: pageSize,
  })
  taskNum.value = data?.total || 0
  taskList.value = data?.data as {id: number, taskName: string}[] || []
  loadingTask.value = false
}

const masterAccountList = ref<{account: string, groupId: string}[] | null>([])
const updateMasterAccoutList = async () => {
  const [err, res] = await to(monitorStatisticModel.getAllMainAccount())
  masterAccountList.value = (res || []).filter(item => !!item.account && !!item.groupId)
}

const smsTemplateList = ref<{templateName: string, id: number, extra: string}[] | null>([])
const smsSupplierList = ref<{name: string, supplierNumber: string}[] | null>([])
const smsAccountList = ref<{name: string, smsAccountNumber: string}[] | null>([])
const updateSmsTemplateList = async () => {
  const [err, data] = await to(accountType === 0 ?
    merchantSmsTemplateModel.getTotalSmsTemplate()
    : merchantSmsTemplateModel.getSmsTemplate({
      groupId: userInfo.groupId,
    })
  )
  // @ts-ignore
  smsTemplateList.value = (data || []).flatMap(item => !!item.id && !!item.templateName ? [{ id: item.id, templateName: item.templateName, extra: item.businessType ? findValueInEnum(item.businessType, BusinessTypeEnum) || '' : '' }] : [])
}
const updateSmsSupplierList = async () => {
  const [err1, data1] = await to(smsProviderModel.findProviderList({status: SmsProviderStatusEnum['启用']}))
  smsSupplierList.value = (data1 || []).map(item => ({ name: item.supplierName!, supplierNumber: item.supplierNumber! }))
  const [err2, data2] = <[any, SmsAccountItem[]]>await to(smsAccountModel.findAllAccountList({}))
  smsAccountList.value = (data2 || []).map(item => ({ name: item.smsAccountName!, smsAccountNumber: item.smsAccountNumber! }))
}
const initData = async () => {
  // 初始化省份城市数据
  await globalStore.getProvinceInfo()
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []

  // 初始化表单收集信息缓存，仅在商户端，运营端获取使用的其他接口。
  if (accountType === 0) {
    updateProjectList()
    updateMasterAccoutList()
    updateSmsSupplierList()
  }
  updateSmsTemplateList()
  filterCity()
  // 初始化任务列表
  updateTaskList()
  speechCraftList.value = await taskStore.getAllScriptListOptions(true)
  const res = await getCallTeamAndSeatOptions()
  callTeamList.value = res?.callTeamList || []
  callSeatList.value = res?.callSeatList || []
}
const operatorList = OperatorEnum
const dynamicTotal = ref(false)
// 获取商户所有的项目list
const projectMap = ref<Map<string, MerchantProjectItem>>(new Map([]))
const updateProjectList = async () => {
  const data = await globalStore.updateProjectList()
  projectMap.value = new Map([])
  data.forEach(item => {
    item.id && projectMap.value.set(item.groupId + '', item)
  })
}
const search = async (page: number = 1, onlyPageChange: boolean = false) => {
  loading.value = true
  dynamicTotal.value = !onlyPageChange
  searchForm.groupId = accountType === 0 ? searchForm.groupId : userInfo.groupId
  searchForm.startPage = page > 1 ? page - 1 : 0
  currentPage.value = page
  searchForm.pageNum = pageSize.value
  const [_, res] = await to(aiOutboundTaskModel.findSmsRecordList(searchForm, accountType === 0))
  currentIndex.value = -1
  if (searchForm.phone) {
    tableData.value = (res?.data as SmsRecord[] || []).sort((a, b) => dayjs(a.triggerTime).isAfter(dayjs(b.triggerTime)) ? -1 : 1) || []
  } else {
    tableData.value = res?.data as SmsRecord[] || []
  }
  loading.value = false
  if (!onlyPageChange) {
    total.value = tableData.value.length === pageSize.value ? (res?.total || 0) : tableData.value.length
    const res2 = await aiOutboundTaskModel.getSmsRecordNum(searchForm) as ResponseData
    total.value = res2?.total || 0
    dynamicTotal.value = false
  }
}
const clearSearchForm = () => {
  Object.assign(searchForm, new SmsSearchOrigin(accountType))
}

// 表格区
const tableRef = ref()
const currentIndex = ref(-1)

initData()
search()

const clearAll = () => {
  tableRef.value = null
  tableData.value = null
  provinceList.value = null
  provinceAllMap.value = null
  callSeatList.value = null
  callTeamList.value = null
  masterAccountList.value = null
  speechCraftList.value = null
  smsAccountList.value = null
  smsTemplateList.value = null
  smsSupplierList.value = null
}
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})

</script>

<style scoped lang="postcss" type="text/postcss">
.module-container {
  width: 100%;
  min-width: 1080px;
}
.phone-msg {
  display: flex;
  align-items: center;
  span {
    width: 130px;
    flex-shrink: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-icon {
    display: none;
  }
  &:hover .el-icon {
    display: inline-block;
  }
}
</style>
