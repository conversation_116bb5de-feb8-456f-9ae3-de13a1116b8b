<template>
  <div class="search-box">
    <div class="tw-grid tw-grid-cols-5 tw-gap-[8px]" :class="isExpand ? 'tw-pb-[8px]':'tw-pb-[12px]'">
      <div class="item-col">
        <span class="label">号码/名单编号：</span>
        <InputPhonesBox
          v-model:value="searchForm.phone"
          @search="search()"
        />
      </div>
      <div v-if="props.groupType===0" class="item-col">
        <span class="label">坐席组：</span>
        <el-select v-model="searchForm.callTeamId" placeholder="请选择坐席组" clearable>
          <el-option v-for="item in callTeamList" :label="item.callTeamName" :value="item.id" :key="item.id"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class="label">坐席：</span>
        <el-select v-model="searchForm.callSeatId" placeholder="请选择坐席" clearable>
          <el-option v-for="item in callSeatList" :label="`${item.account||''}（${item.name||''}）`" :value="item.id" :key="item.id"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class="label">跟进状态：</span>
        <el-select v-model="searchForm.followUpStatus" placeholder="请选择跟进状态" clearable>
          <el-option v-for="item in followUpStatusOption" :label="item.name" :value="item.value" :key="item.value"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class=" label">分配时间：</span>
        <TimePickerBox
          class="tw-grow"
          v-model:start="searchForm.beAllocatedTimeStart"
          v-model:end="searchForm.beAllocatedTimeEnd"
          :maxRange="60*60*24*31*1000"
          :clearable="false"
        />
      </div>
      <div v-if="props.groupType===1" class="item-col">
        <span class="label">审核状态：</span>
        <el-select v-model="searchForm.examineStatus" placeholder="请选择审核状态" clearable>
          <el-option v-for="item in examineStatusOption" :label="item.name" :value="item.value" :key="item.value"/>
        </el-select>
      </div>
    </div>
    <div v-show="isExpand"  class="tw-grid tw-grid-cols-5 tw-gap-[8px] tw-pb-[12px]">
      <div v-if="props.groupType===0 && isExpand" class="item-col">
        <span class="label">审核状态：</span>
        <el-select v-model="searchForm.examineStatus" placeholder="请选择审核状态" clearable>
          <el-option v-for="item in examineStatusOption" :label="item.name" :value="item.value" :key="item.value"/>
        </el-select>
      </div>
      <div class="item-col ">
        <span class=" label">下发时间：</span>
        <TimePickerBox
          v-model:start="searchForm.beSendTimeStart"
          v-model:end="searchForm.beSendTimeEnd"
        />
      </div>
      <div class="item-col ">
        <span class=" label">导入时间：</span>
        <TimePickerBox
          v-model:start="searchForm.importTimeStart"
          v-model:end="searchForm.importTimeEnd"
        />
      </div>
      <div class="item-col ">
        <span class=" label">最近跟进时间：</span>
        <TimePickerBox
          v-model:start="searchForm.latestFollowUpTimeStart"
          v-model:end="searchForm.latestFollowUpTimeEnd"
        />
      </div>
      <div class="item-col ">
        <span class=" label">下次跟进时间：</span>
        <TimePickerBox
          v-model:start="searchForm.nextFollowUpTimeStart"
          v-model:end="searchForm.nextFollowUpTimeEnd"
        />
      </div>
      <div class="item-col ">
        <span class=" label">自动回收时间：</span>
        <TimePickerBox
          v-model:start="searchForm.autoRecoveredTimeStart"
          v-model:end="searchForm.autoRecoveredTimeEnd"
        />
      </div>
      <div class="item-col">
        <span class=" label">姓名：</span>
        <el-input
          v-model="searchForm.name"
          placeholder="请输入姓名"
          @keyup.enter="search()"
          clearable
        >
        </el-input>
      </div>
      <!-- <div class="item-col">
        <span class="label">公司：</span>
        <el-input
          v-model.trim="searchForm.company"
          placeholder="请输入公司"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div>
      <div class="item-col">
        <span class="label">备注：</span>
        <el-input
          v-model.trim="searchForm.comment"
          placeholder="请输入备注"
          clearable
          @keyup.enter="search()"
        >
        </el-input>
      </div> -->
      <div class="item-col">
        <span class=" label">省：</span>
        <el-select v-model="searchForm.provinceCode" placeholder="请选择省" filterable clearable @change="searchForm.cityCode=''">
          <el-option v-for="item in provinceList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class=" label">市：</span>
        <el-select v-model="searchForm.cityCode" placeholder="请选择市" filterable clearable>
          <el-option v-for="item in cityList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class=" label">最近通话状态：</span>
        <el-select
          v-model="searchForm.latestCallStatusList"
          placeholder="请选择最近通话状态"
          multiple
          collapse-tags
          :max-collapse-tags="1"
          collapse-tags-tooltip
          clearable
          class="tw-w-[200px]"
        >
          <el-option v-for="item in clueCallStatusOptions" :key="item.name" :label="item.name" :value="item.value"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class=" label">是否星标：</span>
        <el-select
          v-model="searchForm.isStar"
          placeholder="请选择是否星标"
          clearable
        >
          <el-option label="是" :value="true"/>
          <el-option label="否" :value="false"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class=" label">是否过期：</span>
        <el-select
          v-model="searchForm.isExpire"
          placeholder="请选择是否过期"
          clearable
        >
          <el-option label="是" :value="true"/>
          <el-option label="否" :value="false"/>
        </el-select>
      </div>
      <div class="item-col ">
        <span class="label">跟进：</span>
        <div class="tw-flex tw-items-center">
          <InputNumberBox v-model:value="searchForm.minFollowUpCount" :max="searchForm.maxFollowUpCount || Number.MAX_VALUE" placeholder="最低" style="width: 47%" append="次"/>
          <span class="tw-w-[14px] tw-shrink-0">-</span>
          <InputNumberBox v-model:value="searchForm.maxFollowUpCount" placeholder="最高" style="width: 47%" append="次" :min="searchForm.minFollowUpCount || 0"/>
        </div>
      </div>
      <div class="item-col ">
        <span class="label">通时：</span>
        <div class="tw-flex tw-items-center">
          <InputNumberBox v-model:value="searchForm.minCallDuration" :max="searchForm.maxCallDuration || Number.MAX_VALUE" placeholder="最低" style="width: 47%" append="秒"/>
          <span class="tw-w-[14px] tw-shrink-0">-</span>
          <InputNumberBox v-model:value="searchForm.maxCallDuration" placeholder="最高" style="width: 47%" append="秒" :min="searchForm.minCallDuration || 0"/>
        </div>
      </div>
      
    </div>

    <div class="tw-flex tw-justify-end tw-pt-[12px] tw-items-center tw-border-t-[1px] tw-h-[32px]">
      <div>
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search()" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
        <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon></el-button>
        <el-button type="primary" v-else @click="isExpand=true" link>展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon></el-button>
      </div>
    </div>
  </div>
  <div class="tw-float-right tw-pb-[8px] tw-px-[16px] tw-text-[13px] tw-h-[44px] tw-bg-white">
    <el-button v-if="props.groupType===1" :type="props.groupType===1 ? 'default': 'primary'" @click="handleRecovery">
      <span>线索回收</span>
    </el-button>
    <el-button v-if="props.groupType===1" type="primary" @click="handleAllocate">
      <span>重新分配</span>
    </el-button>
    <el-button v-if="props.groupType===0" type="primary" @click="handleArchive">
      <span>线索归档</span>
    </el-button>
  </div>
  <ClueTable :loading="loading" :tableData="tableData||[]" @update:table="search" v-model:selectClues="selectClues" :clueType="props.clueType" :groupType="props.groupType">
  </ClueTable>

  <AllocateDialog
    v-if="props.groupType===1"
    v-model:visible="allocateVisible"
    :type="1"
    :list="selectIds||[]"
    @confirm="updateClueData()"
  />
</template>

<script lang="ts" setup>
import { ClueItem, ClueSearchInfo, ClueStatusEnum, clueCallStatusOptions, SearchFormOrigin, FollowUpStatusEnum, ExamineStatusEnum } from '@/type/clue'
import { enum2Options } from '@/utils/utils'
import { SeatTeam, SeatMember, } from '@/type/seat'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
// api
import { clueManagerModel } from '@/api/clue'
// 组件
import InputNumberBox from '@/components/InputNumberBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import ClueTable from './ClueTable.vue'
import Confirm from '@/components/message-box'
import InputPhonesBox from '@/components/InputPhonesBox.vue'

// 插件、依赖等
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import { ArrowUp, ArrowDown, } from '@element-plus/icons-vue'
import { reactive, computed, ref, watch, onDeactivated, onActivated, defineAsyncComponent } from 'vue'
import { onBeforeRouteLeave } from 'vue-router';
import { trace } from '@/utils/trace';

const AllocateDialog = defineAsyncComponent({loader: () => import('./AllocateDialog.vue')})

const props = defineProps<{
  clueType: ClueStatusEnum;
  needRefresh?: boolean;
  groupType: number
}>();
const emits = defineEmits(['update:clue'])

const globalStore = useGlobalStore()
const taskStore = useTaskStore()
const loading = ref(false)
// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap[props.groupType === 1 ? '坐席组线索' : '线索管理'].id]

const isExpand = ref(false)


/** 搜索、列表等主体数据 */
const searchForm = reactive<ClueSearchInfo>(new SearchFormOrigin(userStore.groupId, props.clueType))
const provinceList = ref<string[]|null>([])
const provinceAllMap = ref<{ [key: string]: string[] } | null>({})
const cityList = computed(() => {
  if (!provinceAllMap.value) return []
  const provinces = Object.keys(provinceAllMap.value)
  const province = provinces.find(item => searchForm.provinceCode && item.includes(searchForm.provinceCode))
  return searchForm.provinceCode && province ? (provinceAllMap.value[province] || []) : (Object.values(provinceAllMap.value).flat() || [])
})
const tableData = ref<ClueItem[] | null>([])
const search = async () => {
  loading.value = true
  const params = JSON.parse(JSON.stringify(searchForm))
  const arr= params.latestCallStatusList?.join(',')
  params.latestCallStatusList = arr?.length > 0 ? arr.split(',') : undefined
  if (props.groupType === 1) {
    params.callTeamId = callTeamList.value?.find(item => item.leaderAccountId === userStore.userId)?.id || undefined
  }
  const res = await clueManagerModel.getBeDistributeClueList(params) || []
  tableData.value = res.sort((a,b) => dayjs(a.beAllocatedTime).isAfter(b.beAllocatedTime) ? -1 : 1)
  selectClues.value = []
  selectIds.value = []
  loading.value = false
}
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchFormOrigin(userStore.groupId, props.clueType))
}
/** 线索操作 【开始】 */
const selectClues = ref<ClueItem[]|null>([]) // 选中数据
const selectIds = ref<number[]|null>([])
// 重新分配
const allocateVisible = ref(false)
const handleAllocate = () => {
  if (selectClues.value && selectClues.value?.length > 0) {
    selectIds.value = selectClues.value.map(item => item.id!)
    allocateVisible.value = true
  } else {
    ElMessage({
      type: 'warning',
      message: '请至少勾选1条线索数据！'
    })
  }
}
// 回收
const handleRecovery = () => {
  if (selectClues.value && selectClues.value?.length > 0) {
    selectIds.value = selectClues.value.map(item => item.id!)
    Confirm({
      text: `您确认要回收这【${selectIds.value.length}】条线索`,
      type: 'warning',
      title: '操作确认',
      confirmText: '确认',
    }).then(async () => {
      await clueManagerModel.recoveryClues(selectIds.value!)
      trace({
        page: '线索管理-已分配-回收线索',
        params: selectIds.value
      })
      ElMessage({
        type: 'success',
        message: '回收成功！'
      })
      updateClueData()
    }).catch(() => {})
  } else {
    ElMessage({
      type: 'warning',
      message: '请至少勾选1条线索数据！'
    })
  }
}
// 归档
const handleArchive = () => {
  if (selectClues.value && selectClues.value?.length > 0) {
    selectIds.value = selectClues.value.map(item => item.id!)
    Confirm({
      text: `已选择【${selectIds.value.length}】条数据，您确认要归档吗？`,
      type: 'warning',
      title: '操作确认',
      confirmText: '确认',
    }).then(async () => {
      await clueManagerModel.batchArchiveClues(selectIds.value!)
      trace({
        page: '线索管理-已分配-归档线索',
        params: selectIds.value
      })
      ElMessage({
        type: 'success',
        message: '归档成功！'
      })
      updateClueData()
    }).catch(() => {})
  } else {
    ElMessage({
      type: 'warning',
      message: '请至少勾选1条线索数据！'
    })
  }
}

/** 更新线索数据和列表数据 */
const updateClueData = () => {
  search()
  emits('update:clue')
}

// const speechCraftList = ref<MerchantScriptInfo[]>([])
// const taskList = ref<TaskManageItem[]>([])
const callTeamList = ref<SeatTeam[]|null>([])
const callSeatList = ref<SeatMember[]|null>([])
const followUpStatusOption = enum2Options(FollowUpStatusEnum)
const examineStatusOption = enum2Options(ExamineStatusEnum)

const init = async () => {
  await globalStore.getProvinceInfo()
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
  callTeamList.value = await taskStore.getCallTeamListOptions()
  callSeatList.value = props.groupType === 0 ? await taskStore.getCallGroupSeatListOptions() :  await taskStore.getCallTeamSeatListOptions()
  search() 
}
// 表格区
onActivated(() => {
  init()
})

const clearData = () => {
  selectClues.value = null
  selectIds.value = null
  tableData.value = null
  provinceAllMap.value = null
  provinceList.value = null
  callTeamList.value = null
  callSeatList.value = null
}
onDeactivated(() => {
  clearData()
})
onBeforeRouteLeave(() => {
  clearData()
})
watch(() => props.needRefresh, n => {
  n && search()
})
watch([() => props.clueType, () => props.groupType], () => {
  clearSearchForm()
  search()
})

</script>

<style scoped lang="postcss" type="text/postcss">
.module-container-inner {
  .phone-msg {
    display: flex;
    align-items: center;
    span {
      width: 140px;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon {
      display: none;
    }
    &:hover .el-icon {
      display: inline-block;
    }
  }
  .audio-mode {
    position: fixed;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);
    z-index: 99;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
}
</style>
