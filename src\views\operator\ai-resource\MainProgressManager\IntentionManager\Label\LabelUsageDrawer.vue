<template>
  <el-drawer
    v-model="drawerVisible"
    size="800px"
    direction="rtl"
    @close="cancel"
  >
    <template #header>
      <div class="tw-flex tw-w-full tw-items-center tw-h-[48px]">
        <span class="tw-shrink-0 tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">
          【{{ props.data.labelName }}】使用详情
        </span>
      </div>
    </template>
    <el-tabs v-model="activeTab" class="tw-mb-[24px]">

      <el-tab-pane :label="`语料(${props.data.scriptCorpusList?.length || 0})`" name="corpus"></el-tab-pane>
      <el-tab-pane :label="`高级规则(${props.data.advancedRuleList?.length || 0})`" name="rule"></el-tab-pane>
    </el-tabs>

    <!-- 语料tab -->
    <el-table
      v-if="activeTab === 'corpus'"
      v-loading="props.loading"
      :data="props.data.scriptCorpusList || []"
      :header-cell-style="tableHeaderStyle"
      class="tw-grow"
      row-key="id"
    >
      <el-table-column align="left" prop="name" label="语料名称" min-width="120" show-overflow-tooltip />
      <el-table-column align="left" prop="corpusType" label="语料类型" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          {{ findValueInEnum(row.corpusType, CorpusTypeEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column v-if="!isChecked" align="right" label="操作" width="120">
       <template #default="{ row }">
          <el-button type="primary" link @click="replaceLabelInUse(row)">替换</el-button>
          <el-button type="danger" link @click="delLabelInUse(row)">去除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!props.data.scriptCorpusList || props.data.scriptCorpusList.length < 1" description="暂无数据" />
      </template>
    </el-table>

    <!-- 高级规则tab -->
    <el-table
      v-if="activeTab === 'rule'"
      v-loading="props.loading"
      :data="props.data.advancedRuleList || []"
      :header-cell-style="tableHeaderStyle"
      class="tw-grow"
      row-key="id"
    >
      <el-table-column align="left" prop="ruleName" label="规则名称" min-width="120" show-overflow-tooltip />
      <el-table-column v-if="!isChecked" align="right" label="操作" width="120">
       <template #default="{ row }">
          <el-button type="primary" link @click="replaceLabelInUse(row)">替换</el-button>
          <el-button type="danger" link @click="delLabelInUse(row)">去除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!props.data.advancedRuleList || props.data.advancedRuleList.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-drawer>

  <!-- 替换标签弹窗 -->
  <el-dialog
    v-model="replaceDialogVisible"
    width="480px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">替换标签</div>
    </template>
    <el-form label-width="80px">
      <el-form-item label="选择标签：">
        <el-select v-model="selectedLabelId" filterable placeholder="请选择意向标签" style="width: 100%">

          <el-option
            v-for="item in scriptStore.intentionTagOptions"
            :key="item.id"
            :label="item.labelName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="replaceDialogVisible = false">取消</el-button>
        <el-button :loading="props.loading" type="primary" @click="confirmReplace">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { findValueInEnum } from '@/utils/utils'
import { useScriptStore } from '@/store/script'
import { LabelItem, AdvancedRulesItem } from '@/type/IntentionType'
import { CorpusTypeEnum, ScriptCorpusItem } from '@/type/speech-craft'
import { scriptIntentionModel } from '@/api/speech-craft'
import to from 'await-to-js'
import { trace } from '@/utils/trace'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'

const emits = defineEmits(['update:visible', 'update:data', 'update:loading'])
const scriptStore = useScriptStore()
const isChecked = scriptStore.isChecked
const props = defineProps<{
  data: LabelItem,
  loading: boolean,
  visible: boolean,
}>();

const activeTab = ref<'corpus' | 'rule'>('corpus')


// 替换标签相关
const replaceDialogVisible = ref(false)
const selectedLabelId = ref<number | undefined>()
const currentReplaceRow = ref<ScriptCorpusItem | null>(null)

const replaceLabelInUse = (row: ScriptCorpusItem) => {
  currentReplaceRow.value = row
  selectedLabelId.value = undefined
  replaceDialogVisible.value = true
}

const delLabelInUse = async (row: Pick<ScriptCorpusItem, 'id' | 'name' | 'corpusType'> | Pick<AdvancedRulesItem, 'id' | 'ruleName'>) => {
  if (!props.data.id || !row.id) return
  const params = {
    id: props.data.id,
    targetId: row.id,
    type: activeTab.value,
  }
  const [confirmErr] = await to(Confirm({
    // @ts-ignore
    text: `您确定从【${row.ruleName || row.name}】中去除【${props.data.labelName}】吗?`,
    type: 'warning',
    title: '去除标签'
  }))
  if (confirmErr) return

  // 打开loading----请求去除标签----返回成功----更新外部列表----更新入参data，更新列表----关闭loading
  // 打开loading----请求去除标签----返回失败----关闭loading
  emits('update:loading', true)
  trace({
    page: `话术编辑-标签-去除标签使用(${scriptStore.id})`,
    params: params,
  })
  const [err, _] = await to(scriptIntentionModel.removeLabelInUse(params))
  if (!err) {
    emits('update:data')
    ElMessage.success('去除成功')
  } else {
    emits('update:loading', false)
  }
}

const confirmReplace = async () => {
  if (!selectedLabelId.value || !currentReplaceRow.value?.id || !props.data.id) {
    return ElMessage.warning('请选择标签')
  }
  const params = {
    targetId: currentReplaceRow.value.id,
    newId: selectedLabelId.value,
    oldId: props.data.id,
    type: activeTab.value,
  }
  emits('update:loading', true)
  trace({
    page: `话术编辑-标签-替换标签使用(${scriptStore.id})`,
    params: params,
  })
  // 打开loading----请求替换标签----返回成功----更新外部列表----更新入参data，更新列表----关闭loading
  // 打开loading----请求替换标签----返回失败----关闭loading
  const [err] = await to(scriptIntentionModel.replaceLabelInUse(params))
  if (!err) {
    emits('update:data')
    ElMessage.success('操作成功')
    replaceDialogVisible.value = false
  } else {
    emits('update:loading', false)
  }
}

const drawerVisible = ref(props.visible)
const cancel = () => {
  drawerVisible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, n => {
  drawerVisible.value = props.visible
  activeTab.value = 'corpus'
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.el-table {
  font-size: 13px;
  .caret-wrapper {
    display: none;
  }
  :deep(.cell) {
    padding: 0 8px;
  }
}
.el-tabs :deep(.el-tabs__item){
  padding: 10px auto;
  height: 40px;
  line-height: 40px;
  width: 100px;
}
:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  border: none;
  background-color: #e5e7eb;
}
:deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
  line-height: 40px;
  font-size: 16px;
}
:deep(.el-tabs__header) {
  margin-bottom: 0;
}
</style>
