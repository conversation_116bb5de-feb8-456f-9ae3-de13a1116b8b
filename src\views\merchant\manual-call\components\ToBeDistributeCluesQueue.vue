<template>
  <div class="search-box">
    <div class="tw-grid tw-grid-cols-5 tw-gap-[8px]" :class="isExpand ? 'tw-pb-[8px]':'tw-pb-[12px]'">
      <div class="item-col">
        <div class="label">号码/名单编号：</div>
        <InputPhonesBox
          v-model:value="searchForm.phone"
          @search="search()"
        />
      </div>
      <div class="item-col">
        <div class="label">姓名：</div>
        <el-input
          v-model="searchForm.name"
          placeholder="请输入姓名"
          @keyup.enter="search()"
          clearable
        >
        </el-input>
      </div>
      <div v-if="props.groupType===0" class="item-col">
        <div class="label">坐席组：</div>
        <el-select v-model="searchForm.callTeamId" placeholder="请选择坐席组" clearable>
          <el-option v-for="item in callTeamList" :label="item.callTeamName" :value="item.id" :key="item.id"/>
        </el-select>
      </div>
      <div class="item-col">
        <div class="label">线索来源：</div>
        <el-select v-model="searchForm.fromType" placeholder="请选择线索来源" clearable>
          <el-option v-for="item in clueFromOption" :label="item.name" :value="item.value" :key="item.value"/>
        </el-select>
      </div>
      <div class="item-col">
        <span class="label">下发时间：</span>
        <TimePickerBox
          v-model:start="searchForm.beSendTimeStart"
          v-model:end="searchForm.beSendTimeEnd"
          :maxRange="60*60*24*31*1000"
          :clearable="false"
        />
      </div>
      <div v-if="props.groupType===1" class="item-col">
          <span class="label">导入时间：</span>
          <TimePickerBox
            v-model:start="searchForm.importTimeStart"
            v-model:end="searchForm.importTimeEnd"
          />
        </div>
      </div>
      <div v-show="isExpand" class="tw-grid tw-grid-cols-5 tw-gap-[8px] tw-pb-[12px]">
        <div v-if="isExpand && props.groupType!==1" class="item-col">
          <span class="label">导入时间：</span>
          <TimePickerBox
            v-model:start="searchForm.importTimeStart"
            v-model:end="searchForm.importTimeEnd"
          />
        </div>
        <div class="item-col">
          <span class="label">省：</span>
          <el-select v-model="searchForm.provinceCode" placeholder="请选择省" filterable clearable @change="searchForm.cityCode=''">
            <el-option v-for="item in provinceList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
          </el-select>
        </div>
        <div class="item-col">
          <span class="label">市：</span>
          <el-select v-model="searchForm.cityCode" placeholder="请选择市" filterable clearable>
            <el-option v-for="item in cityList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item.split(',')[1]"/>
          </el-select>
        </div>
        <div class="item-col">
          <span class="label">外呼任务：</span>
          <SelectPageBox
            v-model:selectVal="searchForm.taskIds"
            :options="taskList||[]"
            name="taskName"
            val="id"
            placeholder="外呼任务"
            :filterable="true"
            class="tw-flex-grow"
            isRemote
            :loading="loadingTask"
            key="id"
            @update:options="updateTaskList"
            :total="taskNum"
            multiple
            canSelectAll
          >
          </SelectPageBox>
        </div>
        <div class="item-col">
          <span class="label">执行话术：</span>
          <SelectPageBox
            v-model:selectVal="searchForm.scriptStringIds"
            :options="speechCraftList||[]"
            name="scriptName"
            val="scriptStringId"
            placeholder="请选择执行话术"
            :filterable="true"
            class="tw-flex-grow"
            multiple
            canSelectAll
          >
          </SelectPageBox>
        </div>
        
        <div class="item-col">
          <span class="label">呼叫状态：</span>
          <el-select
            v-model="searchForm.callStatusCodes"
            placeholder="请选择呼叫状态"
            multiple
            collapse-tags
            :max-collapse-tags="1"
            collapse-tags-tooltip
            clearable
            class="tw-w-[200px]"
          >
            <el-option v-for="item in clueCallStatusOptions" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </div>
        <div class="item-col">
          <span class="label">分类结果：</span>
          <el-select
            v-model="searchForm.intentionClasses"
            placeholder="请选择分类结果"
            multiple
            collapse-tags
            :max-collapse-tags="2"
            collapse-tags-tooltip
            clearable
          >
            <el-option v-for="item in IntentionClassEnum" :key="item" :label="item" :value="item"/>
          </el-select>
        </div>
        <div class="item-col">
          <span class="label">标签：</span>
          <el-input
            v-model="searchForm.label"
            placeholder="请输入标签"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <!-- <div class="item-col">
          <span class="label">公司：</span>
          <el-input
            v-model.trim="searchForm.company"
            placeholder="请输入公司"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div>
        <div class="item-col">
          <span class="label">备注：</span>
          <el-input
            v-model.trim="searchForm.comment"
            placeholder="请输入备注"
            clearable
            @keyup.enter="search()"
          >
          </el-input>
        </div> -->
        <div class="item-col">
          <span class="label">呼出时间：</span>
          <TimePickerBox
            v-model:start="searchForm.callOutTimeStart"
            v-model:end="searchForm.callOutTimeEnd"
          />
        </div>
        <div class="item-col">
          <span class="label">通时：</span>
          <div class="tw-flex tw-items-center">
            <InputNumberBox v-model:value="searchForm.minCallDuration" :max="searchForm.maxCallDuration || Number.MAX_VALUE" placeholder="最低" style="width: 48%" append="秒"/>
            <span class="tw-w-[14px] tw-shrink-0">-</span>
            <InputNumberBox v-model:value="searchForm.maxCallDuration" placeholder="最高" style="width: 48%" append="秒" :min="searchForm.minCallDuration || 0"/>
          </div>
        </div>
        <div class="item-col tw-col-span-1">
          <span class="label">对话：</span>
          <div class="tw-flex tw-items-center">
            <InputNumberBox v-model:value="searchForm.minCycleCount" :max="searchForm.maxCycleCount || Number.MAX_VALUE" placeholder="最低" style="width: 48%" append="轮"/>
            <span class="tw-w-[14px] tw-shrink-0">-</span>
            <InputNumberBox v-model:value="searchForm.maxCycleCount" placeholder="最高" style="width: 48%" append="轮" :min="searchForm.minCycleCount || 0"/>
          </div>
        </div>
        <div class="item-col tw-col-span-1">
          <span class="label">说话：</span>
          <div class="tw-flex tw-items-center">
            <InputNumberBox v-model:value="searchForm.minSayCount" :max="searchForm.maxSayCount || Number.MAX_VALUE" placeholder="最低" style="width: 47%" append="次"/>
            <span class="tw-w-[14px] tw-shrink-0">-</span>
            <InputNumberBox v-model:value="searchForm.maxSayCount" placeholder="最高" style="width: 47%" append="次" :min="searchForm.minSayCount || 0"/>
          </div>
        </div>
      </div>
    <div class="tw-flex tw-justify-end tw-pt-[12px] tw-items-center tw-border-t-[1px] tw-h-[32px]">
      <div>
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search()" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="filter" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
        <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon></el-button>
        <el-button type="primary" v-else @click="isExpand=true" link>展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon></el-button>
      </div>
    </div>
  </div>
  <div class="tw-float-right tw-pb-[8px] tw-px-[16px] tw-text-[13px] tw-h-[44px] tw-bg-white">
    <el-button v-if="props.groupType===1" type="default" @click="openAutoAllocateSetting">
      <span
        class="tw-w-[5px] tw-h-[5px] tw-rounded-full"
        :class="autoStatus=='1' ? 'tw-bg-[var(--primary-green-color)]':'tw-bg-[var(--primary-gray-color)]'"
      ></span>
      <span class="tw-ml-[4px]">自动分配</span>
    </el-button>
    <el-button :type="props.groupType===1 ? 'default': 'primary'" @click="handleArchive">
      <span>线索归档</span>
    </el-button>
    <el-button v-if="props.groupType===1" type="primary" @click="handleAllocate">
      <span>线索分配</span>
    </el-button>
  </div>
  <ClueTable :tableData="tableData||[]" :loading="loading" @update:table="search" v-model:selectClues="selectClues" :clueType="props.clueType" :groupType="props.groupType">
  </ClueTable>

  <AllocateDialog
    v-if="props.groupType!==0"
    v-model:visible="allocateVisible"
    :list="selectIds||[]"
    :type="0"
    @confirm="updateClueData()"
  />
  <!-- 编辑自动分配弹窗 -->
  <AutoActionDrawer
    v-model:visible="autoAllocateVisible"
    :type="2"
    @update:visible="getAutoInfo"
  ></AutoActionDrawer>
</template>

<script lang="ts" setup>
import { ClueItem, ClueSearchInfo, ClueStatusEnum, clueCallStatusOptions, ClueFromTypeEnum, SearchFormOrigin,  } from '@/type/clue'
import { SpeechCraftInfoItem } from '@/type/speech-craft'
import { SeatTeam } from '@/type/seat'
import { OperatorEnum, IntentionClassEnum } from '@/type/common'
import { enum2Options } from '@/utils/utils'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
// api
import { clueManagerModel } from '@/api/clue'
import { trace } from '@/utils/trace'
import { aiOutboundTaskModel } from '@/api/ai-report'
// 组件
import InputNumberBox from '@/components/InputNumberBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import InputPhonesBox from '@/components/InputPhonesBox.vue'
import ClueTable from './ClueTable.vue'
import Confirm from '@/components/message-box'
import AllocateDialog from './AllocateDialog.vue'
import AutoActionDrawer from './auto/Index.vue'
// 插件、依赖等
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import to from 'await-to-js'
import { useGlobalStore } from '@/store/globalInfo'
import { useTaskStore } from '@/store/taskInfo'
import { ArrowUp, ArrowDown, } from '@element-plus/icons-vue'
import { reactive, computed, ref, watch, onDeactivated, onActivated } from 'vue'
import { onBeforeRouteLeave } from 'vue-router';

const props = defineProps<{
  clueType: ClueStatusEnum;
  needRefresh?: boolean;
  groupType: number, // 0: 线索管理； 1：团队管理
}>();
const emits = defineEmits(['update:clue'])

const globalStore = useGlobalStore()
const loading = ref(false)
const taskStore = useTaskStore()
// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap[props.groupType === 1 ? '坐席组线索' : '线索管理'].id]

const isExpand = ref(false)

const clueFromOption = enum2Options(ClueFromTypeEnum)
/** 搜索、列表等主体数据 */
const searchForm = reactive<ClueSearchInfo>(new SearchFormOrigin(userStore.groupId, props.clueType))
const provinceList = ref<string[]|null>([])
const provinceAllMap = ref<{ [key: string]: string[] } | null>({})
const cityList = computed(() => {
  if (!provinceAllMap.value) return []
  const provinces = Object.keys(provinceAllMap.value)
  const province = provinces.find(item => searchForm.provinceCode && item.includes(searchForm.provinceCode))
  return searchForm.provinceCode && province ? (provinceAllMap.value[province] || []) : (Object.values(provinceAllMap.value).flat() || [])
})
const tableData = ref<ClueItem[] | null>([])
const search = async () => {
  loading.value = true
  const params = JSON.parse(JSON.stringify(searchForm))
  params.callStatusCodes = params.callStatusCodes?.length > 0 ? params.callStatusCodes.join(',')?.split(',') : undefined
  if (props.groupType === 1) {
    params.callTeamId = callTeamList.value?.find(item => item.leaderAccountId === userStore.userId)?.id || undefined
  }
  const res = await clueManagerModel.getToBeDistributeClueList(params) || []
  tableData.value = res.sort((a,b) => dayjs(a.beSendTime).isAfter(b.beSendTime) ? -1 : 1)
  selectClues.value = []
  selectIds.value = []
  loading.value = false
}
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchFormOrigin(userStore.groupId, props.clueType))
}
/** 线索操作 【开始】 */
/** 自动分配 */
const autoAllocateVisible = ref(false)
const openAutoAllocateSetting = () => {
  autoAllocateVisible.value = true
}

const selectClues = ref<ClueItem[]|null>([]) // 选中数据
const selectIds = ref<number[]|null>([])
// 分配
const allocateVisible = ref(false)
const handleAllocate = () => {
  if (selectClues.value && selectClues.value?.length > 0) {
    selectIds.value = selectClues.value.map(item => item.id!)
    allocateVisible.value = true
  } else {
    ElMessage({
      type: 'warning',
      message: '请至少勾选1条线索数据！'
    })
  }
}
// 归档
const handleArchive = () => {
  if (selectClues.value && selectClues.value?.length > 0) {
    selectIds.value = selectClues.value.map(item => item.id!)
    Confirm({
      text: `已选择【${selectIds.value.length}】条数据，您确认要归档吗？`,
      type: 'warning',
      title: '操作确认',
      confirmText: '确认',
    }).then(async () => {
      await clueManagerModel.batchArchiveClues(selectIds.value!)
      trace({
        page: '线索管理-待分配-归档线索',
        params: selectIds.value
      })
      ElMessage({
        type: 'success',
        message: '归档成功！'
      })
      updateClueData()
    }).catch(() => {})
  } else {
    ElMessage({
      type: 'warning',
      message: '请至少勾选1条线索数据！'
    })
  }
}

/** 更新线索数据和列表数据 */
const updateClueData = () => {
  search()
  emits('update:clue')
}

const speechCraftList = ref<SpeechCraftInfoItem[]|null>([])
const taskList = ref<{
    id: number;
    taskName: string;
}[]|null>([])
const taskNum = ref(0)
const callTeamList = ref<SeatTeam[]|null>([])
const init = async () => {
  callTeamList.value = await taskStore.getCallTeamListOptions()
  await globalStore.getProvinceInfo()
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
  speechCraftList.value = await taskStore.getAllScriptListOptions() as SpeechCraftInfoItem[]
  updateTaskList()
  search()
  if (props.groupType===1) {
    getAutoInfo()
  }
}

// 获取自动分配规则状态
const autoStatus = ref<string>('0')
const getAutoInfo = async (visible: boolean = false) => {
  if (visible) return
  const res = await to(clueManagerModel.getAutoAllocateInfo({
    groupId: userStore.groupId,
  }))
  if (res[1]) {
    autoStatus.value = res[1].status || '0'
  } else {
    autoStatus.value = '0'
  }
}

const loadingTask = ref(false)
const updateTaskList = async (val: string = '', startPage: number = 0, pageSize: number = 30) => {
  loadingTask.value = true
  const data = await aiOutboundTaskModel.findTaskList({
    startTime: dayjs().add(-1, 'month').format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    taskName: val ? val : undefined,
    startPage: startPage,
    pageNum: pageSize,
  })
  taskNum.value = data?.total || 0
  taskList.value = data?.data as {id: number, taskName: string}[] || []
  loadingTask.value = false
}
// 表格区
onActivated(() => {
  init()
})

const clearData = () => {
  selectClues.value = null
  selectIds.value = null
  tableData.value = null
  provinceAllMap.value = null
  provinceList.value = null
  callTeamList.value = null
  speechCraftList.value = null
  taskList.value =  null
}
onDeactivated(() => {
  clearData()
})
onBeforeRouteLeave(() => {
  clearData()
})

watch(() => props.needRefresh, n => {
  n && search()
})
watch([() => props.clueType, () => props.groupType], () => {
  clearSearchForm()
  search()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.module-container-inner {
  .phone-msg {
    display: flex;
    align-items: center;
    span {
      width: 140px;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon {
      display: none;
    }
    &:hover .el-icon {
      display: inline-block;
    }
  }
  .audio-mode {
    position: fixed;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);
    z-index: 99;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
}
</style>
