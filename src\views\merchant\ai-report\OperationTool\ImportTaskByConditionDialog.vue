<template>
  <el-dialog
    v-model="dialogVisible"
    width="540px"
    class="dialog-form"
    align-center
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">导入任务</div>
    </template>
    <el-scrollbar :max-height="'calc(100vh - 160px)'" wrap-class="tw-pr-[12px]">
      <el-form
        :model="editData"
        :rules="rules"
        class="tw-pb-[12px]"
        label-width="102px"
        ref="editRef"
      >
        <el-form-item label="导入方式：" prop="loadType">
          <el-radio-group v-model="editData.loadType" class="tw-ml-[6px]">
            <el-radio :label="0">覆盖</el-radio>
            <el-radio :label="1">递增</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="任务类型：" prop="taskType">
          <el-select v-model="editData.taskType" placeholder="任务类型" class="tw-w-full" clearable>
            <el-option v-for="item in enum2Options(TaskTypeEnum)" :key="item.value" :label="item.name" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="任务创建时间：" prop="startTime">
          <TimePickerBox
            v-model:start="editData.startTime"
            v-model:end="editData.endTime"
            placeholder="任务创建时间"
            :disabled-date="disabledDate"
            class="tw-w-full"
            format="YYYY-MM-DD HH:mm:ss"
            :max-range="60*60*24*7*1000"
            :clearable="false"
            @change="updateTaskList"
          />
        </el-form-item>
        <el-form-item label="预期完成时间：" prop="expectedStartTime">
          <TimePickerBox
            v-model:start="editData.expectedStartTime"
            v-model:end="editData.expectedFinishTime"
            class="tw-w-full"
            type="timerange"
            format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item v-if="dialogVisible" label="任务名称：" prop="taskIds">
          <SelectBox
            v-model:selectVal="editData.taskIds"
            :options="taskList||[]"
            name="taskName"
            val="id"
            key="id"
            placeholder="外呼任务"
            filterable
            class="tw-grow"
            :loading="loadingTask"
            multiple
            canSelectAll
            canReverseSelectAll
          >
          </SelectBox>
        </el-form-item>
        <el-form-item label="任务状态：" prop="callStatus">
          <el-select v-model="editData.callStatus" multiple placeholder="任务状态" class="tw-w-full" clearable>
            <el-option v-for="item in taskStatusList" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
        <el-form-item label="执行话术：" prop="scriptStringIds">
          <SelectBox
            v-model:selectVal="editData.scriptStringIds"
            :options="speechCraftAllList||[]"
            name="scriptName"
            val="scriptStringId"
            key="scriptStringId"
            placeholder="执行话术"
            filterable
            class="tw-grow"
            isRemote
            @update:options="updateTaskList"
            multiple
            canSelectAll
          >
          </SelectBox>
        </el-form-item>
        <el-form-item label="外呼线路：" prop="scriptStringIds">
          <SelectBox
            v-model:selectVal="editData.lineCodes"
            :options="lineList"
            name="lineName"
            val="lineNumber"
            placeholder="外呼线路"
            filterable
            can-select-all
            class="tw-w-full"
            multiple
          />
        </el-form-item>
        <el-form-item label="是否补呼：" prop="ifAutoRecall">
          <el-select v-model="editData.ifAutoRecall" placeholder="是否补呼" class="tw-w-full" clearable>
            <el-option label="是" :value="1"/>
            <el-option label="否" :value="0"/>
          </el-select>
        </el-form-item>
        <el-form-item label="是否止损：" prop="ifAutoStop">
          <el-select v-model="editData.ifAutoStop" placeholder="是否止损" class="tw-w-full" clearable>
            <el-option label="是" :value="1"/>
            <el-option label="否" :value="0"/>
          </el-select>
        </el-form-item>
        <el-form-item label="短信通道状态：" prop="smsTemplateAbnormal">
          <el-select v-model="editData.smsTemplateAbnormal" placeholder="短信通道状态" class="tw-w-full" clearable>
            <el-option label="正常" :value="0"/>
            <el-option label="异常" :value="1"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="concurrentMin" label="锁定/理论并发：">
          <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
            <InputNumberBox
              v-model:value="editData.concurrentMin"
              placeholder="最小"
              :min="0"
              :max="editData.concurrentMax ?? Number.MAX_VALUE"
              style="width:45%"
            />
            <span>&nbsp;至&nbsp;</span>
            <InputNumberBox
              v-model:value="editData.concurrentMax"
              placeholder="最大"
              :min="editData.concurrentMin ?? 0"
              :max="Number.MAX_VALUE"
              style="width:45%"
            />
          </div>
        </el-form-item>
        <el-form-item prop="phoneNumMin" label="名单总量：">
          <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
            <InputNumberBox
              v-model:value="editData.phoneNumMin"
              placeholder="最小"
              :min="0"
              :max="editData.phoneNumMax ?? Number.MAX_VALUE"
              style="width:45%"
            />
            <span>&nbsp;至&nbsp;</span>
            <InputNumberBox
              v-model:value="editData.phoneNumMax"
              :min="editData.phoneNumMin ?? 0"
              :max="Number.MAX_VALUE"
              placeholder="最大"
              style="width:45%"
            />
          </div>
        </el-form-item>
        <el-form-item prop="remainingNumMin" label="剩余名单：">
          <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
            <InputNumberBox
              v-model:value="editData.remainingNumMin"
              placeholder="最小"
              :min="0"
              :max="editData.remainingNumMax ?? Number.MAX_VALUE"
              style="width:45%"
            />
            <span>&nbsp;至&nbsp;</span>
            <InputNumberBox
              v-model:value="editData.remainingNumMax"
              placeholder="最大"
              :min="editData.remainingNumMin ?? 0"
              :max="Number.MAX_VALUE"
              style="width:45%"
            />
          </div>
        </el-form-item>
        <el-form-item prop="callingPhoneNumMin" label="首呼剩余：">
          <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
            <InputNumberBox
              v-model:value="editData.callingPhoneNumMin"
              placeholder="最小"
              :min="0"
              :max="editData.callingPhoneNumMax ?? Number.MAX_VALUE"
              style="width:45%"
            />
            <span>&nbsp;至&nbsp;</span>
            <InputNumberBox
              v-model:value="editData.callingPhoneNumMax"
              placeholder="最大"
              :min="editData.callingPhoneNumMin ?? 0"
              :max="Number.MAX_VALUE"
              style="width:45%"
            />
          </div>
        </el-form-item>
        <el-form-item prop="recallingPhoneNumMin" label="补呼剩余：">
          <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
            <InputNumberBox
              v-model:value="editData.recallingPhoneNumMin"
              placeholder="最小"
               :min="0"
              :max="editData.recallingPhoneNumMax ?? Number.MAX_VALUE"
              style="width:45%"
            />
            <span>&nbsp;至&nbsp;</span>
            <InputNumberBox
              v-model:value="editData.recallingPhoneNumMax"
              placeholder="最大"
              :min="editData.recallingPhoneNumMin ?? 0"
              :max="Number.MAX_VALUE"
              style="width:45%"
            />
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible=false" :icon="CloseBold">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirm" :icon="Select">导入</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, defineAsyncComponent } from 'vue';
import { TaskStatusEnum, TaskTypeEnum, TaskFilterParamsInTool, TaskFilterParamsInToolOrigin, TaskManageItem } from '@/type/task'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js';
import type { FormInstance, } from 'element-plus'
import { merchantModel } from '@/api/merchant'
import { useUserStore } from '@/store/user'
import { MerchantScriptInfo, MerchantInfo, MerchantLineInfo, MerchantLineEnableStatusEnum } from '@/type/merchant'
import { enum2Options } from '@/utils/utils'
import { trace } from '@/utils/trace';
import dayjs, { Dayjs } from 'dayjs'
import TimePickerBox from '@/components/TimePickerBox.vue'
import InputNumberBox from '@/components/InputNumberBox.vue'

// 动态引入组件
const SelectBox = defineAsyncComponent(() => import('@/components/SelectBox.vue'))

const emits = defineEmits(['update:visible', 'confirm', 'export'])
const loading = ref(false)
const props = defineProps<{
  visible: boolean,
}>();

const dialogVisible = ref(props.visible)
const rules = {
  startTime: [
    { required: true, message: '请选择任务开始时间', trigger: ['change'] },
  ],
}

const editData = reactive<TaskFilterParamsInTool>(new TaskFilterParamsInToolOrigin())
const editRef = ref<FormInstance  | null>(null)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const speechCraftAllList = ref<MerchantScriptInfo[] | null>([])
const taskStatusList = Object.values(TaskStatusEnum).filter(item => item !== '全部')
const taskList = ref<{id: number, taskName: string}[] | null>([])
const lineList = ref<{lineName?: string, lineNumber?: string}[]>([]) // 可选的商户线路列表


const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      await trace({
        page: `外呼工具-点击导入任务-${editData.loadType === 0 ? '覆盖' : '递增'}`,
        params: editData,
      })
      const [err, res] = await to(aiOutboundTaskModel.batchImportTaskIdsInTool({
        ...editData, loadType: undefined,
      }))
      if (!err) {
        emits('confirm', res, editData.loadType)
        cancel()
      }
      loading.value = false
    }
  })
}

const loadingTask = ref(false)
const updateTaskList = async (val: string = '') => {
  if (!editData.startTime || !editData.endTime) return
  loadingTask.value = true
  const data = await aiOutboundTaskModel.search({
    startTime: editData.startTime,
    endTime: editData.endTime,
    taskName: val ? val : undefined,
  }) as [any, TaskManageItem[]]
  taskList.value = data || []
  loadingTask.value = false
}

const init = async () => {
  loading.value = true
  Object.assign(editData, new TaskFilterParamsInToolOrigin())

  // 话术列表
  const { groupId, tenantId } = useUserStore()
  const [err1, res1] = await to(merchantModel.getScriptList({ groupId: groupId || '', id: tenantId || undefined })) as [any, MerchantInfo]
  speechCraftAllList.value = ((res1 || []).relatedScriptList?.filter(item => item.active == 'ACTIVE') as MerchantScriptInfo[]) || []

  // 任务列表
  updateTaskList()

  // 商户线路列表
  const [err2, res2] = await to(merchantModel.getMerchantLineListByCondition({
    groupId: groupId || '',
    enableStatus: MerchantLineEnableStatusEnum['启用'],
  }))
  lineList.value = (res2 || []).filter(item => item.lineType === 'AI_OUTBOUND_CALL')

  loading.value = false
  clearValidate()
}

const disabledDate = (current: Dayjs): boolean => {
  return current < dayjs().subtract(1, 'M').startOf('d')
};

const clearValidate = () => {
  editRef.value?.clearValidate()
}

watch(() => props.visible, () => {
  dialogVisible.value = props.visible
  if (props.visible) {
    init()
  } else {
    Object.assign(editData, new TaskFilterParamsInToolOrigin())
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>

</style>
