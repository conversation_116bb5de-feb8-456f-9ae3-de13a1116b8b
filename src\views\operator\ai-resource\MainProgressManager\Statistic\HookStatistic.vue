<template>
  <div class="hook-statistic-container">
    <div class="tw-flex tw-h-[40px] tw-items-center tw-leading-[40px]">
      <el-radio-group v-model="hookType">
        <el-radio-button label="ALL">全部</el-radio-button>
        <el-radio-button label="ROBOT">AI挂机</el-radio-button>
        <el-radio-button label="HUMAN">客户挂机</el-radio-button>
      </el-radio-group>
    </div>
    <div class="title">主动流程</div>
    <div class="content">
      <el-scrollbar>
        <PercentBox 
          title="挂机分布"
          canShowDetails
          :tableList="masterDistributionTableList" 
          tips="挂机分布：以第一个主动流程节点的第一个语料触发次数（接通次数）作为分母，每个主动流程节点的挂机次数作为分子。统计不同主动流程节点实际挂机分布情况。"
          @show-details="row => showDetails(row, masterDistributionTableList, '挂机分布')"
        ></PercentBox>
      </el-scrollbar>
      <el-scrollbar>
        <PercentBox 
          title="挂机率"
          canShowDetails
          :tableList="masterRateTableList" 
          tips="挂机率：以每个主动流程节点的第一个语料触发次数作为分母，每一个主动流程节点的挂机次数作为分子，统计不同主动流程节点的挂机率情况。"
          @show-details="row => showDetails(row, masterRateTableList, '挂机率')"
        ></PercentBox>
      </el-scrollbar>
    </div>
    <div class="title">知识库和功能话术</div>
    <div class="tw-flex tw-justify-between tw-mb-1 tw-shrink-0 tw-grow-0">
      <span class="tw-flex tw-items-center">
        <span class="tw-w-[60px]">筛选：</span>
        <el-checkbox-group v-model="corpusTypes">
          <el-checkbox v-for="item in corpusTypeList" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </span>
      <span class="tw-flex">
        <el-input
          v-model="name"
          style="width:250px"
          placeholder="请输入语料名称（20字以内）"
          maxlength="20"
          clearable
          @keyup.enter="searchOther"
          @input="(val: string) => !val && searchOther()"
        >
        </el-input>
        <el-button type="primary" link class="tw-ml-1" @click="searchOther">
          <el-icon :size="16" class="tw-mr-[4px]"><SvgIcon name="search"></SvgIcon></el-icon>
          搜索
        </el-button>
      </span>
    </div>
    <div class="content">
      <el-scrollbar>
        <PercentBox 
          title="挂机分布" 
          tips="挂机分布：该节点挂机数量/该类型语料挂机数量之和"
          :tableList="otherDistributionTableList" 
          canShowDetails
          @show-details="row => showDetails(row, otherDistributionTableList, '挂机分布')"
        ></PercentBox>
      </el-scrollbar>
      <el-scrollbar>
        <PercentBox 
          title="挂机率" 
          tips="挂机率：:该节点挂机数量/该节点接通次数"
          :tableList="otherRateTableList"
          canShowDetails
          @show-details="row => showDetails(row, otherRateTableList, '挂机率')"
        ></PercentBox>
      </el-scrollbar>
    </div>
    <!-- <PaginationBox
      :pageSize="pageSize"
      :currentPage="currentPage"
      :pageSizeList="pageSizeList"
      :total="total"
      @search="searchOther"
      @update="updatePage"
    >
    </PaginationBox> -->
    <CorpusStatisticDetails
      v-model:visible="detailVisible"
      :dataList="currentList"
      :hangupType="hookType"
      :scriptId="scriptId"
      :title="currentTitle"
      :currentItem="currentItem"
    >
    </CorpusStatisticDetails>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, watch, computed } from 'vue'
import CorpusStatisticDetails from './CorpusStatisticDetails.vue'
import PercentBox from './PercentBox.vue'
import { scriptStatisticsModel } from '@/api/speech-craft'
import { ScriptStatisticParams, ScriptStatisticItem, CorpusTypeEnum, } from '@/type/speech-craft'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { ElMessage } from 'element-plus'
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const props = defineProps<{
  scriptId: number,
}>()
// 挂机类型：ALL, HUMAN, ROBOT
const hookType = ref('ALL')
// 主动流程-挂机分布、挂机率
const masterDistributionTableList = ref<ScriptStatisticItem[]>([])
const masterRateTableList = ref<ScriptStatisticItem[]>([])
const searchMaster = async () => {
  loading.value = true
  const params: ScriptStatisticParams = { scriptId: props.scriptId, type: 'master', hangupType: hookType.value}
  const data1 = await scriptStatisticsModel.getHangupDistributionList(params) || []
  const data2 = await scriptStatisticsModel.getHangupRateList(params) || []
  masterDistributionTableList.value = data1.sort((a, b) => (a.weight || 0) - (b.weight || 0))
  masterRateTableList.value = data2.sort((a, b) => (a.weight || 0) - (b.weight || 0))
  loading.value = false
}
// 非主动流程-命中分布、命中率
// 筛选参数
const corpusTypeList = ref([
  {name: '基本问答', value: CorpusTypeEnum['基本问答']},
  {name: '深层沟通', value: CorpusTypeEnum['深层沟通-普通语料']},
  {name: '沉默', value: CorpusTypeEnum['沉默语料']},
  {name: '重复', value: CorpusTypeEnum['重复语料']},
  {name: '最高优先', value: CorpusTypeEnum['最高优先']},
])
const corpusTypes = ref<CorpusTypeEnum[]>(corpusTypeList.value.map(item => item.value))
// 列表数据
const otherDistributionTableList = ref<ScriptStatisticItem[]>([])
const otherRateTableList = ref<ScriptStatisticItem[]>([])
const searchOther = async () => {
  loading.value = true
  const params: ScriptStatisticParams = {
    scriptId: props.scriptId,
    corpusTypes: corpusTypes.value,
    type: 'other',
    hangupType: hookType.value
  }
  const data1 = await scriptStatisticsModel.getHangupDistributionList(params) || []
  const data2 = await scriptStatisticsModel.getHangupRateList(params) || []
  otherDistributionTableList.value = data1?.filter(item => !!item.numerator && (!name.value.trim() || (item.corpusName?.includes(name.value) || item.canvasName?.includes(name.value)))).sort((a, b) => (a.weight || 0) - (b.weight || 0)) || []
  otherRateTableList.value = data2?.filter(item => !!item.numerator && (!name.value.trim() || (item.corpusName?.includes(name.value) || item.canvasName?.includes(name.value)))).sort((a, b) => (a.weight || 0) - (b.weight || 0)) || []
  loading.value = false
}
// 过滤名称\页码
const name = ref<string>('')
// 当前选中进入详情
class OriginStatisticItem {
  canvasId = null
  canvasName = ''
  corpusId = -1
  corpusType =  CorpusTypeEnum['主动流程-普通语料']
  corpusName = ''
  denominator = 0
  numerator = 0
}
const currentItem = reactive<ScriptStatisticItem>(new OriginStatisticItem())
const currentList = ref<ScriptStatisticItem[]>([])
const currentTitle = ref('')
const detailVisible = ref(false)
const showDetails = (row: ScriptStatisticItem, dataList: ScriptStatisticItem[], title: string) => {
  if (!row.canvasId || !row.corpusId) return
  Object.assign(currentItem, row)
   // 只将存在画布且画布存在头节点的列表带入
   currentList.value = dataList?.filter(item => item.canvasId && item.corpusId) || []
  detailVisible.value = true
  currentTitle.value = title
}

// 执行区
watch([() => props.scriptId, hookType], () => {
  searchMaster()
  searchOther()
}, {deep: true, immediate: true})
watch(corpusTypes, () => {
  searchOther()
}, {deep: true,})
</script>

<style scoped lang="postcss" type="text/postcss">
.hook-statistic-container {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  overflow-y: auto;
  height: calc(100vh - 230px);
  display: flex;
  padding: 16px;
  font-size: var(--el-font-size-base);
  flex-direction: column;
  .el-tabs :deep(.el-tabs__item){
    padding: 10px auto;
    height: 60px;
    line-height: 60px;
    width: 100px;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    border: none;
    background-color: #e5e7eb;
  }
  :deep(.el-tabs__nav-prev), :deep(.el-tabs__nav-next) {
    line-height: 60px;
    font-size: 16px;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
  .title {
    display: flex;
    font-size: 16px;
    font-weight: 600;
    line-height: 40px;
    flex-grow: 0;
    flex-shrink: 0;
  }
  .content {
    flex-grow: 1;
    flex-shrink: 0;
    height: 270px;
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    column-gap: 16px;
    width: 100%;
    background-color: #fff;
    .el-scrollbar {
      border-radius: 4px;
      background-color: #F9FAFC;
      border: 1px solid #F9FAFC;
      padding: 16px;
      :deep(.el-scrollbar__bar.is-vertical) {
        top: 18px;
      }
      &:hover {
        border-color: var(--primary-black-color-300);
      }
    }
    
  }
}

</style>