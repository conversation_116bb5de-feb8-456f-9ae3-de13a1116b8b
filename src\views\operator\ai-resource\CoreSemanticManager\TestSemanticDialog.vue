<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    class="semantic-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        语义测试
      </div>
    </template>

    <div v-loading="loadingConfirm" class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="top"
          label-width="90px"
        >
          <el-form-item label="请输入测试文本：" prop="text">
            <el-input
              v-model.trim="form.text"
              type="textarea"
              placeholder="请输入"
              clearable
              maxlength="200"
              show-word-limit
              :autosize="true"
              resize="none"
            />
          </el-form-item>

          <el-form-item>
            <div class="tw-flex tw-justify-end">
              <el-button @click="onClickClear">
                清空
              </el-button>
              <el-button type="primary" @click="onClickTest">
                测试
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="命中的语义：">
            <div v-if="!resultList.length">
              暂无数据
            </div>
            <div v-else>
              {{ resultStr || '' }}
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button type="primary" :icon="CloseBold" @click="onClickCancel">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { CloseBold } from '@element-plus/icons-vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { scriptCoreSemanticModel } from '@/api/speech-craft'
import to from 'await-to-js'
import { AiSemantics, SecondIndustrySemanticItem, SemanticsTestParam } from '@/type/core-semantic'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  industry: SecondIndustrySemanticItem | null,
}>(), {
  visible: false,
})
const emits = defineEmits([
  'update:visible',
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = () => ({
  text: '',
})
// 表单数据
const form = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  text: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('测试文本不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  // 表单DOM不存在
  if (!formRef.value) {
    return
  }
  // 表单DOM
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
        duration: 3000,
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: SemanticsTestParam = {
    secondIndustryId: props.industry.id,
    content: form.text || '',
  }

  // 请求接口
  const [err, res] = <[any, AiSemantics[]]>await to(scriptCoreSemanticModel.testSemantic(params))

  // 返回失败结果
  if (err) {
    ElMessage({
      message: '测试失败',
      type: 'error',
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }

  // 返回成功结果
  if (res) {
    resultList.value = res?.length ? res : []
  }
  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
  resultList.value = []
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 语义测试 开始 ----------------------------------------

const resultList = ref<AiSemantics[]>([])
const resultStr = computed(() => {
  if (!resultList.value.length) {
    return ''
  }
  return resultList.value.map((item: AiSemantics) => {
    return item.semantic || ''
  }).join('、') || ''
})

/**
 * 点击清空按钮
 */
const onClickClear = () => {
  resetForm()
}
const onClickTest = () => {
  validForm(submit)
}

// ---------------------------------------- 语义测试 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 重置表单
    resetForm()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
