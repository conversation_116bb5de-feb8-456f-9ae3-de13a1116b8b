<template>
  <div class="tw-h-[calc(100vh-130px)] tw-min-w-[1080px] tw-overflow-y-hidden tw-flex tw-flex-col">
    <div class="search-box tw-px-[12px]">
      <div class="tw-grid tw-grid-cols-5 tw-gap-[8px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.workOrderId"
            placeholder="请输入工单ID"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div class="item">
          <el-select v-model="searchForm.type" placeholder="请选择问题类型" filterable clearable>
            <el-option v-for="item in enum2Options(InspecteTypeEnum)" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.description"
            placeholder="请输入问题描述"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div class="item tw-col-span-2">
          <span class="tw-w-[66px] tw-shrink-0">提交时间：</span>
          <TimePickerBox
            v-model:start="searchForm.commitTimeStart"
            v-model:end="searchForm.commitTimeEnd"
            :disabledDate="disabledDate"
            :maxRange="60*60*24*1000*31"
            :clearable="false"
          />
        </div>
        <div class="item">
          <el-select v-model="searchForm.commitUser" placeholder="请选择提交人" filterable clearable>
            <el-option v-for="item in accountList" :key="item.account" :label="`${item.account}(${item.name})`" :value="item.account" />
          </el-select>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.phone"
            placeholder="请输入被叫号码"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div class="item">
          <el-select v-model="searchForm.phoneType" placeholder="请选择通话类型" clearable>
            <el-option v-for="item in enum2Options(RecordTypeEnum)" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </div>
        <div class="item tw-col-span-2">
          <span class="tw-w-[66px] tw-shrink-0">呼叫时间：</span>
          <TimePickerBox
            v-model:start="searchForm.callTimeStart"
            v-model:end="searchForm.callTimeEnd"
            :disabledDate="disabledDate"
            :maxRange="60*60*24*1000*31"
            clearable
          />
        </div>
        <div class="item">
          <el-select v-model="searchForm.principal" placeholder="请选择负责人" filterable clearable>
            <el-option v-for="item in accountList" :key="item.account" :label="`${item.account}(${item.name})`" :value="item.account" />
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.handleUser" placeholder="请选择处理人" filterable clearable>
            <el-option v-for="item in accountList" :key="item.account" :label="`${item.account}(${item.name})`" :value="item.account" />
          </el-select>
        </div>
        <div class="item">
          <el-select v-model="searchForm.status" placeholder="请选择处理状态" clearable>
            <el-option v-for="item in enum2Options(InspecteStatusEnum)" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.comment"
            placeholder="请输入处理备注"
            @keyup.enter="search()"
            clearable
          >
          </el-input>
        </div>
        <div class="item-btn">
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData"
      ref="tableRef"
      v-loading="loading"
      row-key="id"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column align="left" prop="id" label="工单ID" fixed="left" :formatter="formatterEmptyData" width="80" show-overflow-tooltip>
        <template #default="{ row, $index }">
          <span :class="currentIndex===$index ? 'tw-text-[#409EFF]' :''">{{ row.id || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="type" fixed="left" label="问题类型" width="120" :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ findValueInEnum(row.type, InspecteTypeEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="left" prop="description" fixed="left" label="问题描述" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column align="left" prop="callRecordId" label="被叫号码" width="120">
        <template #default="{ row }">
          <span v-if="!!row.callRecordId" class="tw-cursor-pointer" @click="copyText(row.callRecordId)">{{ filterPhone(row.callRecordId, 6, 4) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="phoneType" label="通话类型" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #default="{ row }">
          {{ findValueInEnum(row.phoneType, RecordTypeEnum) || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="left" prop="callTime" label="外呼时间" width="160" :formatter="formatterEmptyData"/>
      <el-table-column align="left" prop="commitUser" label="提交人" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column align="center" prop="createTime" label="提交时间" width="160" :formatter="formatterEmptyData">
        <template #default="{ row }">
          {{ row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </el-table-column>
      <el-table-column align="left" prop="principal" label="负责人" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column align="left" prop="handleUser" label="处理人" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column align="center" prop="status" label="处理状态" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="tw-mx-auto status-box-mini" :class="statusMap.get(row.status) || ''">
            {{ findValueInEnum(row.status, InspecteStatusEnum) || '-' }}
          </div>
          
        </template>
      </el-table-column>
      <el-table-column align="left" prop="comment" label="处理备注" min-width="160" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column align="center" prop="handleTime" label="处理时间" width="160" :formatter="formatterEmptyData"/>
      <el-table-column label="操作" width="80" align="right" fixed="right">
        <template #default="{ $index }">
          <div class="table-btn-box">
            <el-button type="primary" link @click="edit($index)">查看详情</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="pageSize"
      :pageSizeList="pageSizeList"
      :currentPage="currentPage"
      :total="total"
      @search="search()"
      @update="updateList"
    >
    </PaginationBox>
  </div>
  <OrderDetailsDrawer
    v-model:visible="drawerVisible"
    :tableData="tableData || []"
    :total="total"
    :readonly="!handlePermission"
    v-model:currentIndex="currentIndex"
    :searchForm="searchForm"
    @update:record="handleDetailsChange"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onUnmounted, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import PaginationBox from '@/components/PaginationBox.vue'
import OrderDetailsDrawer from './OrderDetailsDrawer.vue'
import { onBeforeRouteLeave } from 'vue-router'
import { tableHeaderStyle } from '@/assets/js/constant'
import { enum2Options, findValueInEnum, formatterEmptyData, copyText, filterPhone } from '@/utils/utils'
import { inspectionModel, } from '@/api/Inspection'
import { InspecteOrderSearchParams, InspecteTypeEnum, InspecteStatusEnum, InspecteOrderItem, InspecteOrderSearchParamsOrigin } from '@/type/Inspection'
import { aiTeamModel, } from '@/api/user'
import { AccountItem } from '@/type/user'
import { statusMap } from './constant'
import to from 'await-to-js'
import dayjs from 'dayjs'
import { RecordTypeEnum } from '@/type/task'
import { useUserStore } from '@/store/user'
import routeMap from '@/router/asyncRoute/route-map'

// loading信息
const loading = ref(false)

// 用户处理工单权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap['系统监控-业务巡检'].id]
const handlePermission = computed(() => {
  return permissions?.includes(routeMap['系统监控-业务巡检'].permissions['处理工单'])
})

// 搜索内容
const searchForm = reactive<Partial<InspecteOrderSearchParams>>(new InspecteOrderSearchParamsOrigin())

// 列表数据
const tableData = ref<InspecteOrderItem[] | null>([])

/** 分页 开始 */
const pageSizeList = [20, 50, 100, 200]
const pageSize = ref(pageSizeList[0])
const currentPage = ref(1)
const total = ref(0)
const updateList = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  search()
}

// 搜索
const search = async () =>{
  if (loading.value) return ElMessage.warning('请勿重复请求')
  loading.value = true
  const [_, data] = await to(inspectionModel.findInspectionWorkOrder(searchForm))
  total.value = data?.total || 0
  tableData.value = data?.data as InspecteOrderItem[] || []
  loading.value = false
}

const disabledDate = (time: Date) => {
  const _minTime = dayjs().add(-1, 'month').startOf('day').valueOf()
  const _maxTime = dayjs().endOf('day').valueOf()
  return time.getTime() > _maxTime || time.getTime() < _minTime
}

// 点击数据详情-展示抽屉
const drawerVisible = ref(false)
const tableRef = ref()
const currentIndex = ref(-1)
const readOnly = ref(true)
const edit = (index: number) => {
  if (index < 0) return ElMessage.warning('工单获取数据异常')
  currentIndex.value = index
  drawerVisible.value = true
}
// 抽屉内部更新了工单信息
const handleDetailsChange = (s: InspecteOrderSearchParams, index: number, t: number, data: InspecteOrderItem[]) => {
  Object.assign(searchForm, s)
  total.value = t || 0
  tableData.value = data || []
  tableRef.value?.scrollTo({top: currentIndex.value * 42, behavior: 'smooth', });
}

const accountList = ref<AccountItem[] | null>(null)
const init = async () => {
  accountList.value = await aiTeamModel.getMerchantAccountList({}) as AccountItem[] || []
  search()
}

/** vue生命周期 */
// 初始化
onMounted(() => {
  init()
})
const clearAllData = () => {
  tableData.value = null
  accountList.value = null
  currentIndex.value = -1
  drawerVisible.value = false
}
onUnmounted(() => { clearAllData() })
onBeforeRouteLeave(() => { clearAllData() })
</script>

<style scoped lang="postcss">
.el-table {
  font-size: 13px;
  :deep(.caret-wrapper) {
    display: none;
  }
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
