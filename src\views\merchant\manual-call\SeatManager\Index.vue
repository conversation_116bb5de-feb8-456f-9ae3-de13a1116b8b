<template>
  <!--模块标题-->
  <HeaderBox title="坐席管理" />

  <!--模块主体-->
  <el-scrollbar class="module-container">
    <!--顶部工具条-->
    <div class="tw-flex tw-flex-col">
      <el-button class="tw-flex-grow-0 tw-self-end" type="primary" @click="onClickAdd">
        <el-icon :size="18" color="#fff">
          <SvgIcon name="add1" color="inherit" />
        </el-icon>
        <span>新增坐席组</span>
      </el-button>

      <div class="tw-mt-[12px] tw-p-[16px] tw-bg-white tw-rounded-[4px] tw-text-left">
        <div class="tw-text-[13px] tw-text-[#969799]">
          坐席组名称：
        </div>
        <div class="tw-mt-[8px]">
          <el-input v-model.trim="seatTeamNameVal" clearable placeholder="请输入坐席组名称" style="width: 283px;" @keyup.enter="search" />
          <el-button class="tw-ml-[12px]" type="primary" link @click="search">
            <el-icon size="--el-font-size-base">
              <SvgIcon name="filter" color="var(--el-color-primary)" />
            </el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>

    <!--主体内容-->
    <div class="tw-overflow-hidden tw-mt-[16px] tw-rounded-[4px]">
      <!--表格-->
      <el-table
        v-loading="loadingAllList||loadingFilterList"
        :data="currentList"
        stripe
        :header-cell-style="tableHeaderStyle"
      >
        <template #empty>
          <el-empty />
        </template>

        <el-table-column label="坐席组名称" align="left" fixed="left" prop="callTeamName" min-width="150" show-overflow-tooltip>
          <template #default="scope:{row:{callTeamName:string}}">
            {{ scope?.row?.callTeamName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="坐席组长" align="left" prop="leaderAccount" min-width="150" show-overflow-tooltip>
          <template #default="scope:{row:{leaderAccount:SeatLeader}}">
            {{ scope?.row?.leaderAccount?.account || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="坐席人数" align="left" prop="callSeatIds" min-width="80" show-overflow-tooltip>
          <template #default="scope:{row:{callSeatIds:number[]}}">
            {{ scope?.row?.callSeatIds?.length ?? '-' }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="left" prop="creatTime" min-width="160" show-overflow-tooltip>
          <template #default="scope:{row:{createTime:string}}">
            {{ formatTime(scope?.row?.createTime || '-') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" fixed="right" min-width="60">
          <template #default="scope">
            <el-button link type="primary" @click="onClickEdit(scope)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页条-->
      <PaginationBox :currentPage="pageNum" :pageSize="pageSize" :pageSizeList="pageSizeList" :total="total" @search="updateList" @update="updateList" />
    </div>
  </el-scrollbar>

  <!--坐席组弹窗-->
  <DialogSeatTeam :visible="dialogSeatTeamVisible" :data="dialogSeatTeamData" :seatList="allList" @update="handleDialogSeatTeamUpdate" @close="handleDialogSeatTeamClose" />
</template>

<script lang="ts" setup>
import { computed, defineAsyncComponent, reactive, ref } from 'vue'
import { SeatLeader, SeatTeam } from '@/type/seat'
import { Throttle, updateCurrentPageList } from '@/utils/utils'
import dayjs from 'dayjs'
import { seatManagerModel } from '@/api/seat'
import { useUserStore } from '@/store/user'
import { tableHeaderStyle } from '@/assets/js/constant'

// 动态引入组件
const HeaderBox = defineAsyncComponent(() => import('@/components/HeaderBox.vue'))
const PaginationBox = defineAsyncComponent(() => import('@/components/PaginationBox.vue'))
const DialogSeatTeam = defineAsyncComponent(() => import('./DialogSeatTeam.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const userStore = useUserStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 坐席表格 开始 ----------------------------------------

// -------------------- 搜索条件 开始 --------------------

// 搜索框，坐席组名称
const seatTeamNameVal = ref('')

// -------------------- 搜索条件 结束 --------------------

// -------------------- 表格 开始 --------------------

// 全部列表，接口数据
const allList = ref<SeatTeam[]>([])
// 正在加载全部列表
const loadingAllList = ref<boolean>(false)
// 全部列表节流锁
const throttleAllList = new Throttle(loadingAllList)

// 搜索结果列表，全部的子集
const filterList = ref<SeatTeam[]>([])
// 全部列表，正在加载
const loadingFilterList = ref<boolean>(false)
// 全部列表，加载节流锁
const throttleFilterList = new Throttle(loadingFilterList)

// 当前页列表，页面展示，搜索结果的子集
const currentList = ref<SeatTeam[]>([])
// 当前页码
const pageNum = ref(1)
// 每页大小
const pageSize = ref(10)
// 可选页码大小
const pageSizeList = [10, 20, 50, 100]
// 总数
const total = computed(() => {
  return filterList.value.length ?? 0
})

/**
 * 更新列表 全部
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleAllList.check()) {
    return
  }
  throttleAllList.lock()

  try {
    // 请求接口
    const res = <SeatTeam[]>await seatManagerModel.getAllSeatTeam({
      groupId: userStore.groupId,
    })
    // 重置并更新全部列表
    allList.value = Array.isArray(res) ? res : []
    // 刷新搜索结果
    search()
  } catch (e) {
  } finally {
    // 节流锁解锁
    throttleAllList.unlock()
  }
}
/**
 * 更新列表 搜索
 */
const search = () => {
  // 节流锁上锁
  if (throttleFilterList.check()) {
    return
  }
  throttleFilterList.lock()

  // 按搜索条件筛选列表
  filterList.value = allList.value
  if (seatTeamNameVal.value) {
    filterList.value = filterList.value.filter((item: SeatTeam) => {
      return item?.callTeamName?.includes(seatTeamNameVal.value)
    })
  }
  updateList(pageNum.value, pageSize.value)

  // 节流锁解锁
  throttleFilterList.unlock()
}
/**
 * 更新列表 当前页码
 */
const updateList = (p?: number, s?: number) => {
  if (p || s) {
    // 如果参数指定了页码或/和每页大小，则按参数更新列表
    p && (pageNum.value = p)
    s && (pageSize.value = s)
    currentList.value = updateCurrentPageList(filterList.value, pageNum.value, pageSize.value)
  } else {
    // 如果参数都没有指定，则页码和页面大小保持不变，更新全部列表
    updateAllList()
  }
}
/**
 * 点击编辑按钮
 * @param scope 当前坐席组信息
 */
const onClickEdit = (scope: { row?: SeatTeam }) => {
  Object.assign(dialogSeatTeamData, scope?.row ?? { id: -1, callTeamId: undefined, })
  dialogSeatTeamVisible.value = true
}
/**
 * 点击新增按钮
 */
const onClickAdd = () => {
  Object.assign(dialogSeatTeamData, { id: -1, callTeamId: undefined, })
  dialogSeatTeamVisible.value = true
}

// -------------------- 表格 结束 --------------------

// ---------------------------------------- 坐席表格 结束 ----------------------------------------

// ---------------------------------------- 新增坐席组弹窗 开始 ----------------------------------------

// 是否显示新增坐席组弹窗
const dialogSeatTeamVisible = ref<boolean>(false)
// 坐席组弹窗表单数据
const dialogSeatTeamData = reactive<SeatTeam>({})

/**
 * 坐席组弹窗 组件通知更新数据
 */
const handleDialogSeatTeamUpdate = () => {
  updateAllList()
}
/**
 * 坐席组弹窗 组件通知关闭弹窗
 */
const handleDialogSeatTeamClose = () => {
  dialogSeatTeamVisible.value = false
  Object.keys(dialogSeatTeamData).forEach((prop: string) => {
    dialogSeatTeamData[<keyof SeatTeam>prop] = undefined
  })
}
/**
 * 将ISO日期时间转换成可读文本
 * @param str
 */
const formatTime = (str: string = '') => {
  return dayjs(str).isValid()
    ? dayjs(str).format('YYYY-MM-DD HH:mm:ss')
    : '-'
}

// ---------------------------------------- 新增坐席组弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

updateAllList()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
:deep(.el-table) {
  font-size: var(--el-font-size-base);
  color: #000;
}
</style>
