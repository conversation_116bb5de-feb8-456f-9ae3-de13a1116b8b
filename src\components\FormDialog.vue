<template>
  <!--表单弹窗组件-->
  <el-dialog
    v-model="dialogVisible"
    :width="props.dialogStyle.dialogWidth"
    align-center
    :close-on-click-modal="false"
    @opened="handleOpened"
    @closed="handleClosed"
  >
    <template #header>
      <div class="form-dialog-header">
        {{ props.id > -1 ? props.dialogText.editingTitle : props.dialogText.creatingTitle }}
      </div>
    </template>

    <el-scrollbar class="form-dialog-main" view-class="form-dialog-main-inner">
      <el-form
        ref="formRef"
        :label-width="props.dialogStyle.labelWidth"
        label-position="right"
        inline
        :model="form"
        :rules="rules"
        :disabled="loadingConfirm || props.readonly"
      >
        <slot></slot>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" :disabled="loadingConfirm" @click="handleCancel">
          {{ props.dialogText.cancelButtonText }}
        </el-button>
        <el-button type="primary" :icon="Select" :loading="loadingConfirm" :disabled="loadingConfirm" @click="handleConfirm">
          {{ props.dialogText.confirmButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, toRaw, watch } from 'vue'
import { ElMessage, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'

// 此组件中的实体是指任意可管理的数据对象，比如商户、供应商、账号、线路、话术等等

interface propType {
  // 弹窗显示隐藏
  visible: boolean,
  // 弹窗样式
  dialogStyle?: {
    // 弹窗宽度
    dialogWidth: string,
    // 表单项标签宽度
    labelWidth: string,
  },
  // 弹窗文本
  dialogText?: {
    // 编辑实体时的弹窗标题
    editingTitle: string,
    // 新建实体时的弹窗标题
    creatingTitle: string,
    // 取消按钮文本
    cancelButtonText: string,
    // 确定按钮文本
    confirmButtonText: string,
    // 编辑成功消息
    msgEditSuccessfully: string,
    // 创建成功消息
    msgCreateSuccessfully: string,
    // 编辑失败消息
    msgEditFailed: string,
    // 创建失败消息
    msgCreateFailed: string,
  },
  // 实体ID
  id: number,
  // 实体信息，通常也会包含实体ID
  content: { [keyName: string]: any },
  // 表单默认值，在成功提交或者清空表单后会使用此默认值
  contentDefault: { [keyName: string]: any },
  // 表单校验规则
  rules: FormRules,
  // 表单提交回调函数
  submitCallback: Function,
  // 是否只读（禁止编辑表单）
  readonly?: boolean,
}

const props = withDefaults(
  defineProps<propType>(),
  {
    visible: false,
    dialogStyle: () => {
      return {
        dialogWidth: '1000px',
        labelWidth: '80px',
      }
    },
    dialogText: () => {
      return {
        editingTitle: '编辑',
        creatingTitle: '新建',
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        msgEditSuccessfully: '编辑成功',
        msgCreateSuccessfully: '创建成功',
        msgEditFailed: '编辑失败',
        msgCreateFailed: '创建失败',
      }
    },
    id: -1,
    readonly: false,
  }
)
const emits = defineEmits([
  // 弹窗已打开
  'opened',
  // 关闭弹窗
  'close',
  // 通知父组件表单已提交
  'finish'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)
// 正在提交信息
const loadingConfirm = ref<boolean>(false)

// 表单DOM
const formRef = ref()
// 表单默认值
const formDefault = props.contentDefault
// 表单内容
const form = reactive({ ...formDefault })

// 表单校验规则
const rules = reactive<FormRules>(props.rules)

/**
 * 表单校验
 */
const validForm = () => {
  // 表单DOM不存在
  if (!formRef.value) {
    return
  }
  // 表单DOM
  // 回调函数里的参数valid是指表单校验是否通过，是UI组件传来的
  formRef.value.validate(async (valid: boolean) => {
    // 校验通过
    if (valid) {
      // 发送修改后的数据
      await submit()
    } else {
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        duration: 3000,
        type: 'warning',
      })
    }
  })
}

/**
 * 提交
 */
const submit = async () => {
  try {
    // 表单显示成正在提交的加载状态
    loadingConfirm.value = true

    // 调用父组件传来的请求接口回调函数
    await props.submitCallback(toRaw(form))

    ElMessage({
      message: props.id > -1 ? props.dialogText.msgEditSuccessfully : props.dialogText.msgCreateSuccessfully,
      duration: 3000,
      type: 'success',
    })

    setTimeout(() => {
      // 通知父组件表单已提交
      emits('finish')
      // 关闭弹窗
      closeDialog()
    }, 200)
  } catch (e) {
    ElMessage({
      message: props.id > -1 ? props.dialogText.msgEditFailed : props.dialogText.msgCreateFailed,
      duration: 3000,
      type: 'error',
    })
  } finally {
    setTimeout(() => {
      // 取消加载状态
      loadingConfirm.value = false
    }, 200)
  }
}

/**
 * 重置表单
 */
const resetForm = () => {
  // 清除表单的校验结果
  formRef.value.resetFields()
  // 重置表单数据
  Object.assign(form, JSON.parse(JSON.stringify(formDefault)))
}

/**
 * 弹窗显示动画结束后
 */
const handleOpened = () => {
  // 弹窗的显示动画播放结束，通知父组件同步状态
  emits('opened')
}

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
}

/**
 * 弹窗关闭动画结束后
 */
const handleClosed = () => {
  // 弹窗已经关闭，通知父组件同步状态
  emits('close')
  // 完全关闭弹窗后，重置表单数据，以备下次使用
  resetForm()
}

/**
 * 点击确定按钮
 */
const handleConfirm = async () => {
  if (props.readonly) {
    // 禁止编辑表单时，点击确定和点击取消都是关闭弹窗
    // 关闭弹窗
    closeDialog()
  } else {
    // 表单可以编辑时
    // 表单校验
    validForm()
  }
}

/**
 * 点击取消按钮
 */
const handleCancel = () => {
  // 关闭弹窗
  closeDialog()
}

watch(
  // 不能只监听props.visible，因为这个是组件内的组件，需要监听上层组件的整个表单数据
  props,
  (val) => {
    dialogVisible.value = val.visible
    // 每次显示弹窗时
    if (val.visible) {
      Object.assign(form, JSON.parse(JSON.stringify(val.content)))
    }
  }, {
    deep: true,
  }
)
</script>

<style lang="postcss" scoped>
</style>
