import { traceLogModel } from '@/api/trace';
import { TraceLogItem } from '@/type/trace';
import { filterEmptyParams } from '@/utils/utils';
import dayjs from 'dayjs';
import to from 'await-to-js'

export const trace = async (data: Partial<TraceLogItem>, thenFn?: Function, catchFn?: Function, finallyFn?: Function) => {
  const obj: Partial<TraceLogItem> = {
    submitTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    page: data.page || '',
  }
  if (data.params) {
    if (typeof data.params !== 'object') {
      obj.params = JSON.stringify(data.params);
    } else if (Object.keys(data.params).length === 0) {
      obj.params = null
    } else {
      const res: Record<string, any> = {}
      for (const key in data.params) {
        // @ts-ignore
        const value = data.params[key];
        // 跳过空值、空字符串和空数组
        if (value === null || value === undefined || value === '' || 
            (Array.isArray(value) && value.length === 0) || 
            (typeof value === 'object' && !Object.keys(value)?.length)) {
          continue;
        }
        // 处理 File 类型
        if (value instanceof File) {
          res[key] = {
            _isFile: true,
            name: value.name,
            size: value.size,
            type: value.type,
            lastModified: value.lastModified
          };
        } else {
          // 保留其他类型的值
          res[key] = value;
        }
      }
      obj.params = JSON.stringify(res);
    }
  } else {
    obj.params = null
  }
  const [err] = await to(traceLogModel.log(obj))
  if (err) {
    catchFn && catchFn()
  } else {
    thenFn && thenFn()
  }
  finallyFn && finallyFn()
}

/**
 * 记录调用接口前后埋点，并返回接口返回参数。按照串行执行
 * @param page 埋点page
 * @param fnParams 执行函数和埋点参数，务必保证是一致的
 * @param thenFn 务必保证该方法无需返回参数，仅返回成功或者失败
 * @returns Error | null
 */
export const traceApi = async <T>(
  page: string,
  fnParams: T,
  thenFn: Function
) => {
  if (!thenFn)  return new Error('获取执行函数失败')
  const params = fnParams
    ? (
      typeof fnParams === 'string'
      ? fnParams
      : ( Object.keys(fnParams).length > 0 ? JSON.stringify(filterEmptyParams(fnParams)) : null)
    ) : null
  await to(traceLogModel.log({
    submitTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    page: (page || '') + '：开始',
    params: params,
  }))
  const [err] = await to(thenFn(fnParams))
  await to(traceLogModel.log({
    submitTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    page: (page || '') + '：完成',
    params: err ? JSON.stringify(err) : null
  }))
  return err || null
}
