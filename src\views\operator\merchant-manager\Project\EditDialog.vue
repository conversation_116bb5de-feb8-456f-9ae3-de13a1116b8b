<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    class="merchant-dialog"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        项目设置
      </div>
    </template>

    <el-scrollbar class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="80px"
        >
          <el-form-item label="项目编号：" prop="id">
            <el-input
              v-model.trim="form.id"
              placeholder="系统自动生成"
              disabled
              style="width: 100%;"
            />
          </el-form-item>
          <el-form-item label="项目名称：" prop="programName">
            <el-input
              v-model.trim="form.programName"
              placeholder="不超过20个字符，请勿重复命名"
              clearable
              show-word-limit
              maxlength="20"
              style="width: 100%;"
            />
          </el-form-item>
          <el-form-item label="选择产品：" prop="productId">
            <el-select
              v-model="form.productId"
              placeholder="下拉选择"
              clearable
              filterable
              style="width: 100%;"
              @visible-change="onProductVisibleChange"
            >
              <el-option
                v-for="item in productOptions"
                :key="item.id"
                :label="item.productName"
                :value="item.id"
              >
                <div class="tw-flex tw-flex-row">
                  <div>{{ item.productName }}</div>
                  <div class="tw-ml-auto tw-text-[#969799]">{{ item.industrySecondFieldName }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :disabled="loadingConfirm" :icon="CloseBold" @click="onClickCancel">
          取消
        </el-button>
        <el-button :loading="loadingConfirm" type="primary" :icon="Select" @click="onClickConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import { MerchantProductOption, MerchantProjectItem } from '@/type/merchant'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { ProductItem } from '@/type/industry'
import { useMerchantStore } from '@/store/merchant'
import { merchantProjectModel } from '@/api/merchant'
import { useGlobalStore } from '@/store/globalInfo'
import to from 'await-to-js'
import { trace } from '@/utils/trace'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  data: MerchantProjectItem,
}>(), {
  visible: false,
  data: () => ({}),
})
const emits = defineEmits([
  'update:visible',
  'confirm',
])

const globalStore = useGlobalStore()
const merchantStore = useMerchantStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): MerchantProjectItem => {
  return {
    id: undefined,
    tenantId: merchantStore.currentMerchant?.id ? merchantStore.currentMerchant?.id + '' : undefined,
    groupId: undefined,
    programName: '',
    productId: '',
    productName: '',
    secondIndustryId: '',
    secondIndustryName: '',
  }
}
// 表单数据
const form: MerchantProjectItem = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  programName: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('项目名称不能为空'))
      } else if (value.length > 20) {
        callback(new Error('项目名称长度不超过20个字符'))
      } else {
        callback()
      }
    }
  },
  productId: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error('产品不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  // 表单DOM不存在
  if (!formRef.value) {
    return
  }
  // 表单DOM
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
        duration: 3000,
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: MerchantProjectItem = {
    id: (typeof form.id === 'number') ? form.id : undefined,
    programName: form.programName ?? '',
    productId: (form.productId || '') + '',
    productName: form.productName ?? '',
    secondIndustryId: form.secondIndustryId + '',
    secondIndustryName: form.secondIndustryName ?? '',
    tenantId: (form.tenantId || merchantStore.currentMerchant.id || '') + '',
    groupId: form.groupId ?? merchantStore.currentAccount.groupId ?? '',
  }

  trace({
    page: `商户管理-${typeof params.id === 'number' ? '-编辑' : '-新增'}项目`,
    params: params
  })

  const [err, _] = await to(typeof params.id === 'number'
    ? merchantProjectModel.editProject(params)
    : merchantProjectModel.addProject(params)
  )

  // 节流锁解锁
  throttleConfirm.unlock()
  if (!err) {
    ElMessage.success('保存成功')
    closeDialog()
    emits('confirm')
  }
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
}
/**
 * 更新表单
 * 从props里读取
 */
const updateForm = () => {
  Object.assign(form, JSON.parse(JSON.stringify(props.data)))
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const onClickConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const onClickCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 产品 开始 ----------------------------------------

// 产品列表
const productList = ref<ProductItem[]>([])
// 产品选项
const productOptions = ref<MerchantProductOption[]>([])

/**
 * 更新产品列表
 */
const updateProductList = async () => {
  try {
    await globalStore.updateProductList(true)
    productList.value = JSON.parse(JSON.stringify(globalStore.productList))
    productOptions.value = <MerchantProductOption[]><unknown>productList.value.map((item: ProductItem) => {
      return {
        ...item,
        id: item.id + '',
      }
    })
  } catch (e) {
    ElMessage.error('无法正确获取产品列表')
    productList.value = []
    productOptions.value = []
  } finally {
  }
}
/**
 * 产品列表 下拉选择框显示隐藏时
 * @param visible 显示隐藏状态
 */
const onProductVisibleChange = async (visible: boolean) => {
  // 下拉框从隐藏到显示时
  if (visible) {
    return
  }

  // 下拉框从显示到隐藏时
  await nextTick()

  // 如果清空
  if (!form.productId) {
    // 清空相关表单数据
    form.productName = ''
    form.secondIndustryId = ''
    form.secondIndustryName = ''
    return
  }

  // 如果选中
  // 从产品列表里找到当前选中产品
  const selectedProduct = productOptions.value.find((productItem: ProductItem) => {
    return productItem?.id === form.productId
  })
  // 找得到，把产品信息更新到表单里
  if (selectedProduct) {
    form.productName = selectedProduct.productName
    form.secondIndustryId = selectedProduct.industrySecondFieldId + ''
    form.secondIndustryName = selectedProduct.industrySecondFieldName
  } else {
    ElMessage.warning('产品信息不正确，无法选取')
    form.productId = ''
    form.productName = ''
    form.secondIndustryId = ''
    form.secondIndustryName = ''
  }
}

// ---------------------------------------- 产品 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  if (val) {
    // 显示弹窗时
    // 更新表单
    await nextTick()
    resetForm()
    updateForm()
    // 更新其他数据
    await updateProductList()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style lang="postcss" scoped>
</style>
