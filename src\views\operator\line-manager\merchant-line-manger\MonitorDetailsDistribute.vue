<template>
  <div class="tw-w-full tw-bg-white tw-pt-[12px] tw-rounded-[4px]">
    <div class="form-dialog-header tw-mb-[8px] tw-flex tw-items-center tw-px-[16px]">
      <div>数据内容</div>
      <el-tooltip :content="`商户线路在各供应线路下的数据分布详情`" placement="right" :show-after="500">
          <el-icon size="13" color="var(--primary-black-color-400)" class="tw-cursor-pointer tw-ml-0.5">
            <SvgIcon name="warning" color="inherit"/>
          </el-icon>
        </el-tooltip>
      <el-tooltip content="刷新" placement="right" :show-after="500">
        <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="search()">
          <SvgIcon name="reset" color="inherit"/>
        </el-icon>
      </el-tooltip>
    </div>
    <div class="tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-gap-3 tw-pb-[12px] tw-px-[16px]">
      <div class="item">
        <el-input
          v-model.trim="searchForm.supplyLineName"
          placeholder="供应线路名称"
          clearable
          @keyup.enter="search()"
        />
      </div>
      <div class="item">
        <el-select v-model="searchForm.enableStatus" placeholder="生效状态">
          <el-option
            v-for="lineStatusItem in Object.values(supplierLineStatusList)"
            :key="lineStatusItem.name"
            :value="lineStatusItem.val"
            :label="lineStatusItem.text"
          />
        </el-select>
      </div>
      <div class="item">
        <el-input
          v-model.trim="searchForm.supplierName"
          placeholder="供应商名称"
          clearable
          @keyup.enter="search()"
        />
      </div>
      <div class="item">
        <el-input
          v-model.trim="searchForm.supplierNumber"
          placeholder="供应商编号"
          clearable
          @keyup.enter="search()"
        />
      </div>
    </div>
    <div class="tw-flex tw-justify-between tw-items-center tw-py-[12px] tw-border-t-[1px] tw-px-[16px]">
      <div>
        <el-radio-group v-model="searchForm.recentMin"  @change="search(true)">
          <el-radio-button v-for="item in timeRangeList" :label="item.value" :key="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group>
      </div>
      <div>
        <ColumnSetting
          :totalList="totalCols"
          :defaultList="defaultCols"
          :disabledList="disabledCols"
          :name="'supply-monitor'"
        />
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search()" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
    <SupplyMonitorTable :table-data="tableData || []" isDetail :recentMin="searchForm.recentMin" @update:table="search()" showPagination :loading="loading">
      <template v-slot:btn="{rows}">
        <div>
          <el-button @click="changePriorityStatus(rows, true)">批量设置优先</el-button>
          <el-button @click="changePriorityStatus(rows, false)">批量取消优先</el-button>
          <el-button @click="batchChangePendingStatus(rows, true)">批量临停</el-button>
          <el-button @click="batchChangePendingStatus(rows, false)">批量取消临停</el-button>
        </div>
      </template>
      <template v-slot:prior="{row}">
        <el-switch
          v-model="row.isPriority"
          inline-prompt
          active-text="是"
          inactive-text="否"
          active-color="var(--primary-blue-color)"
          @click="changePriorityStatus(row ? [row] : [], row.isPriority)"
        />
      </template>
      <template v-slot:hold="{row}">
        <el-switch
          v-model="row.isTempStop"
          inline-prompt
          active-text="已临停"
          inactive-text="未临停"
          active-color="var(--primary-orange-color)"
          @click="changePendingStatus(row)"
        />
      </template>
      <template v-slot:operate="{row}">
        <el-button type="primary" link @click="goEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="goDetail(row)">详情</el-button>
        <el-button type="primary" link @click="sampleTenantSupplyLine(row)">号码检测</el-button>
        <el-button type="primary" link @click="goConcurrent(row)">并发趋势</el-button>
      </template>
    </SupplyMonitorTable>
  </div>
  <div class="tw-mt-[16px] tw-rounded-[4px] tw-overflow-hidden">
    <LineDurationBox :lineInfo="lineTotalInfo" :type="1" :recentMin="searchForm.recentMin" v-model:refresh="needDurationRefresh"/>
  </div>
  <div class="tw-mt-[16px] tw-rounded-[4px] tw-overflow-hidden">
    <LineConcurrentBox :lineInfo="lineTotalInfo?.tenantLineNumber||''" v-model:refresh="needConcurrentRefresh"/>
  </div>
  <el-drawer
    v-model="drawerVisible"
    :size="getDrawerWidth()"
    :with-header="true"
  >
    <template v-slot:header>
      <div class="tw-bg-white tw-h-[50px]  tw-flex tw-items-end tw-pb-[12px]">
        <div class="tw-font-semibold tw-text-left tw-text-[var(--primary-black-color-600)]">
          {{ lineCurrentInfo?.supplyLineName || '' }}
        </div>
        <div class="tw-text-[12px] tw-text-left tw-text-[var(--primary-black-color-600)] tw-ml-[4px]">
          下【{{ lineTotalInfo?.tenantLineName || '' }}】的数据分布
        </div>
        <el-tooltip content="刷新" placement="right" :show-after="500">
          <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5 tw-pb-1" @click="needRefresh=true">
            <SvgIcon name="reset" color="inherit"/>
          </el-icon>
        </el-tooltip>
      </div>
    </template>
    <el-scrollbar wrap-class="tw-p-[12px] tw-bg-[#f2f3f5]">
      <LineChartBox :lineInfo="lineCurrentInfo" :type="3" v-model:refresh="needRefresh"></LineChartBox>
    </el-scrollbar>
  </el-drawer>
  <TenantSupplyLineConcurrentDrawer v-model:visible="concurrentVisible" :lineInfo="concurrentLineInfo"/>
  <sampleTenantSupplyLineDialog v-model:visible="samplePhoneVisible" :lineInfo="concurrentLineInfo"/>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, onUnmounted, onActivated } from 'vue'
import { ElMessage, } from 'element-plus'
import LineChartBox from '../components/LineChartBox.vue'
import { supplierLineStatusList, } from '@/assets/js/map-supplier'
import { goSupplyLinDetail, } from '@/utils/line'
import SupplyMonitorTable from '../components/SupplyMonitorTable.vue'
import { SupplierMonitorInfoItem, TenantMonitorDetailsParams, MonitorInfoItem, SupplierLineInfoItem } from '@/type/line'
import { MerchantLineTypeEnum } from '@/type/merchant'
import { lineMerchantModel, } from '@/api/line'
import { onBeforeRouteLeave } from 'vue-router'
import { timeRangeList } from '../components/constant'
import LineConcurrentBox from '../components/LineConcurrentBox.vue'
import Confirm from '@/components/message-box'
import { merchantSupplyLineModel } from '@/api/merchant'
import to from 'await-to-js'
import { supplierModel } from '@/api/supplier'
import TenantSupplyLineConcurrentDrawer from '../components/TenantSupplyLineConcurrentDrawer.vue'
import LineDurationBox from '../components/LineDurationBox.vue'
import { getColumnSettingByName} from '../components/constant'
import ColumnSetting from '@/components/ColumnSetting.vue'
import { trace } from '@/utils/trace'
import sampleTenantSupplyLineDialog from '../components/sampleTenantSupplyLineDialog.vue'

const props = withDefaults(defineProps<{
  lineInfo: MonitorInfoItem | null,
  refresh: boolean,
}>(), {

})
const emits = defineEmits(['update:refresh'])
const loading = ref(false)
const {totalCols, defaultCols, disabledCols} = getColumnSettingByName('supply-monitor')

// 列表及分页
const tableData = ref<SupplierMonitorInfoItem[] | null>([])

// 表格选中线路信息
const lineTotalInfo = ref<MonitorInfoItem | null>(props.lineInfo)
const lineCurrentInfo = ref<MonitorInfoItem | null>(null)

// 选中、取消选中某条数据，更新底部图表数据
const goDetail = (row: SupplierMonitorInfoItem) => {
  if (row) {
    lineCurrentInfo.value = {
      ...row,
      tenantLineNumber: props.lineInfo?.tenantLineNumber  || '',
      tenantLineName: props.lineInfo?.tenantLineName  || ''
    }
  }
  drawerVisible.value = true
  needRefresh.value = true
}

/** 查看并发详情 */
const concurrentVisible = ref(false)
const concurrentLineInfo = ref<null | {
  tenantLineNumber: string,
  supplyLineNumber: string,
  tenantLineName?: string,
  supplyLineName?: string,
}>(null)
const goConcurrent = (row: SupplierMonitorInfoItem) => {
  concurrentVisible.value = true
  concurrentLineInfo.value = {
    tenantLineNumber: props.lineInfo?.tenantLineNumber  || '',
    supplyLineNumber : row.supplyLineNumber || ''
  }
}

/** 号码检测 */
const samplePhoneVisible = ref(false)
const sampleTenantSupplyLine = async (row: SupplierMonitorInfoItem) => {
  const [err] = await to(Confirm({
    text: `系统将从进行中的任务抽样一些号码，检测线路的可拨打能力。
    <p>商户线路：${props.lineInfo?.tenantLineName || ''}</p>
    <p>供应线路：${row.supplyLineName}</p>
    `,
    type: 'warning',
    title: `号码检测`,
  }))
  if (!!err) return
  samplePhoneVisible.value = true
  concurrentLineInfo.value = {
    tenantLineNumber: props.lineInfo?.tenantLineNumber  || '',
    supplyLineNumber : row.supplyLineNumber || '',
    tenantLineName: props.lineInfo?.tenantLineName  || '',
    supplyLineName: row.supplyLineName || ''
  }
}

/** 单挑线路统计详情抽屉 */ 
const drawerVisible = ref(false)
const getDrawerWidth = () => {
  return window.innerWidth > 1400 ? '75%' : '1080px'
}

// 编辑当前线路
const goEdit = async (row: SupplierMonitorInfoItem) => {
  goSupplyLinDetail({
    supplyLineNumber: row.supplyLineNumber,
    supplierNumber: row.callSupplierNumber,
  })
}

/** 修改优先状态 */
const changePriorityStatus = async (rows: SupplierMonitorInfoItem[] | null, isPriority: boolean) => {
  if (!rows || !rows.length) return ElMessage.warning('请选择需要操作的线路')
  const hasError = rows.some(row => {
    if (!row.concurrentLimit) {
      ElMessage.warning(`【${row.supplyLineName || ''}】请先设置最大可支配并发`)
      return true
    }
    if (row.supplyLineType === MerchantLineTypeEnum['人工直呼'] && isPriority) {
      ElMessage.warning('人工直呼线路无法设置优先线路')
      return true
    }
    return false
  })
  // 判断是否设置了最大可支配并发，如无则无法修改优先线路
  if (!!hasError && isPriority) {
    search()
    return
  }
  Confirm({
    text: `<p>您确定要${isPriority ? '设置' : '取消'}【${
        rows.length === 1 ? rows[0].supplyLineName || '' : (rows?.length + '个线路')
      }】为优先线路?</p>`,
    type: 'warning',
    title: `操作确认`,
  }).then(async () => {
    // 埋点
    trace({
      page: `线路运营-${isPriority ? '设置' : '取消'}优先线路-${rows?.length || 0}个`,
      params: {
        supplyLineNumber: rows.map(item => item.supplyLineNumber) || [],
        tenantLineNumber: props.lineInfo?.tenantLineNumber || '',
        isPriority: isPriority,
      }
    })
     const [err] = await to(Promise.all(rows.map(async (row) => {
      const params = {
        supplyLineNumber: row.supplyLineNumber || '',
        tenantLineNumber: props.lineInfo?.tenantLineNumber || '',
        isPriority: isPriority,
      }
      await to(merchantSupplyLineModel.switchPriorityStatus(params))
    })))
    !err && ElMessage.success('操作成功')
  }).catch(() => {
  }).finally(() => {
    search()
  })
}


/** 临停 */
const changePendingStatus = async (row: SupplierMonitorInfoItem) => {
  const [err1, info] = await to(supplierModel.getLineByNumber({
    supplyLineNumber: row.supplyLineNumber!
  })) as [any, SupplierLineInfoItem]
  if (err1) {
    ElMessage.warning('获取线路备注失败')
  }
  let notes = ''
  if (info?.notes) {
    let regex = /【(.+?)】/g;
    let options = info?.notes.match(regex)
    notes = options?.join('') || ''
  }
  Confirm({
    text: `<p>您确定要${row.isTempStop ? '临停' : '取消临停'}【${row?.supplyLineName || ''}】?</p>` + (
      notes ? `<p style="margin-top:6px;color:#E54B17;font-weight:600;">备注：${notes || ''}</p>` : ''
    ),
    type: 'warning',
    title: `${row.isTempStop ? '临停' : '取消临停'}确认`,
    confirmText: `${row.isTempStop ? '临停' : '确认'}`
  }).then(async () => {
    const params = {
      supplyLineNumber: row.supplyLineNumber || '',
      tenantLineNumber: props.lineInfo?.tenantLineNumber  || '',
      pendingStatus: row.isTempStop!,
    }
    trace({ page: `线路运营-单个${row.isTempStop ? '临停' : '取消临停'}`, params })
    const [err2, _] = await to(merchantSupplyLineModel.switchPendingStatus(params))
    !err2 && ElMessage({
      type: 'success',
      message: '操作成功',
    })
  }).catch(() => {
  }).finally(() => {
    search()
  })
}

/** 批量临停/取消临停
 * @param {SupplierMonitorInfoItem[]} rows
 * @param {boolean} isTempStop： true：临停 false：取消临停
 */
 const batchChangePendingStatus = async (rows: SupplierMonitorInfoItem[] | null, isTempStop: boolean) => {
  if (!rows || !rows.length) return ElMessage.warning('请选择需要操作的线路')
  Confirm({
    text: `<p>您确定要${isTempStop ? '临停' : '取消临停'}【${rows?.length || 0}条】线路嘛?</p>`,
    type: 'warning',
    title: `批量${isTempStop ? '临停' : '取消临停'}确认`,
    confirmText: `${isTempStop ? '临停' : '确认'}`
  }).then(async () => {
    const params = Object.fromEntries(rows?.map(item => ([
      `${searchForm.tenantLineNumber}_${item.supplyLineNumber}`, isTempStop
    ])))
    trace({ page: `线路运营-批量${isTempStop ? '临停' : '取消临停'}`, params })
    const [err2, _] = await to(merchantSupplyLineModel.batchSwitchPendingStatus(params))
    !err2 && ElMessage.success('操作成功')
  }).catch(() => {
  }).finally(() => {
    search()
  })
}

// 搜索
class searchOrigin {
  tenantLineNumber = props.lineInfo?.tenantLineNumber as string || undefined
  enableStatus = undefined
  supplierName = undefined
  supplierNumber = undefined
  supplyLineName = undefined
  recentMin = 5
}
const searchForm = reactive<TenantMonitorDetailsParams>(new searchOrigin())


// 重置搜索数据
const clearSearchForm = () => {
  Object.assign(searchForm, new searchOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}
// 搜索函数
const search = async (needUpdateDuration: boolean = false) => {
  loading.value = true
  needUpdateDuration && (needDurationRefresh.value = true)
  tableData.value = (await lineMerchantModel.getMonitorDetails({
    ...searchForm,
    recentMin: searchForm.recentMin ? searchForm.recentMin : undefined
  }) as SupplierMonitorInfoItem[] || []).sort((a, b) => b.currentlyCallNum - a.currentlyCallNum)
  loading.value = false
}

const needRefresh = ref(true) // 抽屉中的数据统计模块刷新
const needConcurrentRefresh = ref(false)
const needDurationRefresh = ref(false)
const refreshAction = () => {
  search(true)
  needConcurrentRefresh.value = true
  emits('update:refresh', false)
}

watch(() => props.refresh, () => {
  if (props.refresh && !!props.lineInfo) {
    lineCurrentInfo.value = null
    refreshAction()
  }
})
watch(() => props.lineInfo, () => {
  if (!!props.lineInfo) {
    searchForm.tenantLineNumber = props.lineInfo?.tenantLineNumber as string
    lineCurrentInfo.value = null
    lineTotalInfo.value = props.lineInfo
    refreshAction()
  }
})
onActivated(() => {
  !!props.lineInfo && refreshAction()
})
onUnmounted(() => {
  tableData.value = null
})
onBeforeRouteLeave(() => {
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.monitor-details-container {
  width: 100%;
  overflow-x: hidden;
  min-width: 1080px;
  box-sizing: border-box;
  padding: 16px;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .form-dialog-header {
    margin-bottom: 8px;
  }
}
</style>
