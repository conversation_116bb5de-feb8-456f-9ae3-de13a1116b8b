<template>
  <!--模块标题-->
  <HeaderBox title="黑名单" />
  <!--模块主体-->
  <div v-loading="loadingLeft" class="tw-flex tw-min-w-[1080px] tw-h-[calc(100%-56px)]">
    <!--左半部分，列表-->
    <div class="aside-list-box">
      <div>
        <el-button
          class="tw-w-[226px] tw-mx-[12px] tw-box-border"
          type="primary"
          @click="editLeftItemAction()"
        >
          新增黑名单分组
        </el-button>
      </div>
      <!--搜索框-->
      <div class="tw-w-full tw-flex tw-items-center tw-justify-between tw-mt-[16px] tw-mb-[6px] tw-px-[14px]">
        <el-input
          v-model="leftSearchForm[leftInputType]"
          @keyup.enter="searchLeftAction()"
          @clear="searchLeftAction()"
          clearable
          :placeholder="`请输入${leftInputType === 'phone' ? '搜索号码' : '分组名称'}`"
        >
          <template #prepend>
            <el-select v-model="leftInputType" class="tw-w-[95px]">
              <el-option v-for="item in leftInputTypeList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </template>
        </el-input>
      </div>
      <!-- 黑名单类型tab -->
      <!-- <TabsBox v-model:active="activeBlackListType" :tabList="blackListTypeList" @update:active="searchLeftAction"></TabsBox> -->
      <!--符合搜索条件的列表-->
      <el-scrollbar wrap-class="tw-pr-[14px]" class="tw-grow tw-pl-[16px]">
        <!--列表里的单项-->
        <div
          v-for="item in leftListTemp"
          :key="item.id"
          class="aside-normal-item"
          :class="[{'aside-active-item':item.id===currentItem?.id}]"
        >
          <!--内层容器-->
          <div class="aside-normal-item-inner" @click="clickLeftItem(item)">
            <!--名称-->
            <div
              class="tw-text-justify tw-break-words tw-truncate tw-text-[14px] tw-text-[var(--primary-black-color-600)] tw-font-[700]"
              :class="item.id===currentItem?.id ? 'tw-text-[var(--primary-blue-color)]' : undefined"
            >
              {{ item.groupName || '' }}
            </div>
            <!--日期时间-->
            <div class="tw-text-[13px] tw-break-words tw-text-[var(--primary-black-color-400)]">
              号码数量：{{ item.phoneCount ?? '-' }}
            </div>
            <!--日期时间-->
            <div class="tw-text-[13px] tw-break-words tw-text-[var(--primary-black-color-400)]">
              创建时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
          <!--按钮-->
          <div class="aside-item-btn-box">
            <el-tooltip content="编辑" placement="right" :show-after="500">
              <span class="tw-cursor-pointer" @click.stop="editLeftItemAction(item)"><el-icon :size="16" color="#fff"><SvgIcon name="edit" color="#fff"></SvgIcon></el-icon></span>
            </el-tooltip>
            <el-tooltip content="删除" placement="right" :show-after="500">
              <span class="tw-cursor-pointer" @click.stop="delLeftItem(item)"><el-icon :size="16" color="#fff"><SvgIcon name="delete" color="#fff"></SvgIcon></el-icon></span>
            </el-tooltip>
          </div>
        </div>
        <!--空数据提示-->
        <el-empty v-if="!leftList || leftList.length < 1" class="tw-m-auto"/>
      </el-scrollbar>
      <PaginationBox
        class="tw-border-t-[1px] tw-border-t-[#ebeef5] tw-mt-1"
        :pageSize="pageLeftSize"
        :pageSizeList="pageLeftSizeList"
        :currentPage="currentLeftPage"
        :total="leftList.length"
        :mini="true"
        @search="searchLeftAction()"
        @update="updateLeftList"
      >
      </PaginationBox>
    </div>

    <!--右半部分，内容-->
    <el-scrollbar class="tw-grow tw-p-[16px]" view-class="tw-flex tw-flex-col tw-h-full">
      <!--黑名单详情模块-->
      <template v-if="(currentItem?.id??-1)>=0">
          <!--按钮容器-->
          <div class="tw-mb-[12px] tw-flex tw-justify-end tw-w-full">
          <el-dropdown class="tw-mx-[12px]" @command="handleImportOperator">
            <el-button type="primary">
              <el-icon :size="16"><SvgIcon name="upload"></SvgIcon></el-icon>
              <span>导入号码</span>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="file">文件导入</el-dropdown-item>
                <el-dropdown-item command="single">单个导入</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" @click="updatePhoneCount">
            <el-icon :size="16"><SvgIcon name="reset3"></SvgIcon></el-icon>
            <span>更新数量</span>
          </el-button>
        </div>
          <!--黑名单基本信息-->
        <div class="card-box tw-flex tw-flex-col">
          <div class="tw-flex tw-items-between tw-w-full">
            <h5 class="tw-text-[14px] tw-text-[var(--primary-black-color-600)] tw-font-semibold">
              {{ currentItem?.groupName ?? '' }}
            </h5>
          </div>
          <div class="tw-flex tw-flex-col tw-w-full tw-mt-[12px]">
            <div class="tw-grid tw-grid-cols-4 tw-gap-x-[6px] tw-items-center tw-justify-between tw-w-full tw-pb-[4px]">
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">分组ID：</span>
                <span class="info-content">{{ currentItem?.id || '-' }}</span>
              </p>
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">号码数量：</span>
                <span class="info-content">{{ currentItem?.phoneCount ?? '-' }}</span>
              </p>
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">分组规则：</span>
                <span class="info-content">{{ currentItem?.targetComment || '-' }}</span>
              </p>
              
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">创建时间：</span>
                <span class="info-content">{{ dayjs(currentItem?.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
              </p>
            </div>
            <div class="tw-grid tw-grid-cols-4 tw-gap-x-[6px] tw-justify-between tw-w-full tw-pb-[4px]">
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">人群类别：</span>
                <span class="info-content">{{ findValueInEnum(currentItem?.targetType, BlackPersonTypeEnum) || '-' }}</span>
              </p>
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">人群等级：</span>
                <span class="info-content">{{ currentItem?.targetLevel || '-' }}</span>
              </p>
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">限制时长：</span>
                <span class="info-content">{{ currentItem?.limitDuration && currentItem?.limitDuration == -1 ? '永久生效' :  currentItem?.limitDuration + '天' }}</span>
              </p>
             
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">更新时间：</span>
                <span class="info-content">
                  {{dayjs(currentItem?.updateTime).format('YYYY-MM-DD HH:mm:ss') }}
                </span>
              </p>
            </div>
            
            <div class="tw-grid tw-grid-cols-4 tw-gap-x-[6px] tw-items-center tw-justify-between tw-w-full tw-pb-[4px]">
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">影响接通：</span>
                <span class="info-content">{{ currentItem?.putThroughComment || '-' }}</span>
              </p>
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">影响收益：</span>
                <span class="info-content">{{ currentItem?.benefitComment || '-' }}</span>
              </p>
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">影响本收：</span>
                <span class="info-content">{{ currentItem?.costBenefitComment || '-' }}</span>
              </p>
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">分组类型：</span>
                <span class="info-content">{{ findValueInEnum(currentItem?.groupType, BlackListTypeEnum) || '-' }}</span>
              </p>
            </div>
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full tw-pb-[4px]">
              <p class="tw-text-justify tw-break-all">
                <span class="info-title">备注：</span>
                <span class="info-content">
                  {{ currentItem?.comment || '-' }}
                </span>
              </p>
            </div>
          </div>
        </div>

        <div class="tw-rounded-[4px] tw-bg-white tw-flex tw-flex-col tw-grow tw-mt-[12px]">
          <TabsBox v-model:active="activeTab" :tabList="tabList" class="tw-w-full" @update:active="handleTabChange"></TabsBox>
          <div class="tw-flex tw-flex-col tw-px-[12px] tw-py-[16px] tw-grow">
            <template v-if="activeTab==='号码'">
              <SearchPhoneList
                :tableData="rightPhoneList"
                :loading="loadingRight"
                @search="searchPhoneAction"
                @edit="editRight"
                @del="delRight"
              />
            </template>
            <!--导入记录-->
            <template v-else>
              <div class="tw-flex tw-w-[540px] tw-mb-1 tw-items-center tw-gap-x-[6px]">
                <div class="item">
                  <span class="label">导入时间：</span>
                  <TimePickerBox
                    v-model:start="searchRightForm.startTime"
                    v-model:end="searchRightForm.endTime"
                    placeholder="导入时间"
                    separator="-"
                    type="daterange"
                    :clearable="false"
                    format="YYYY-MM-DD HH:mm:ss"
                    @change="searchRecordAction()"
                  />
                </div>
                <div class="item-btn">
                  <el-button type="primary" @click="searchRecordAction" link>
                    <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
                    <span>查询</span>
                  </el-button>
                </div>
              </div>
              <el-table
                :data="rightRecordListTemp"
                v-loading="loadingRight"
                class="tw-grow"
                row-key="id"
                :header-cell-style="tableHeaderStyle"
                stripe
              >
                <template #empty>
                  <el-empty v-if="!rightRecordList || rightRecordList.length < 1" description="暂无数据" />
                </template>
                <el-table-column label="文件名" property="fileName" align="left" :formatter="formatterEmptyData" min-width="160"></el-table-column>
                <el-table-column property="importType" label="导入类型" align="center" min-width="160" :formatter="formatterEmptyData">
                  <template #default="{ row }">
                    {{ findValueInEnum(row.importType, ImportTypeEnum) }}
                  </template>
                </el-table-column>
                <el-table-column property="importCount" label="号码数量" align="left" min-width="160" :formatter="formatterEmptyData">
                  <template #default="{ row }">
                    {{ row.importCount ? formatNumber(row.importCount) : '-' }}
                  </template>
                </el-table-column>
                <el-table-column property="importTime" label="导入时间" align="center" min-width="160" :formatter="formatterEmptyData">
                  <template #default="{ row }">
                    {{ row.importTime ? dayjs(row.importTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="60" align="right" fixed="right">
                  <template #default="{ row }">
                    <el-button v-if="row.status === '1'" type="primary" link @click="cancelRecordAction(row)">撤销导入</el-button>
                    <span v-else class="tw-text-[var(--primary-red-color)]">已撤销</span>
                  </template>
                </el-table-column>
              </el-table>
              <PaginationBox
                :pageSize="pageSize"
                :currentPage="currentPage"
                :total="total || 0"
                @search="searchRecordAction"
                @update="updatePage"
              >
              </PaginationBox>
            </template>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty class="tw-m-auto"/>
      </template>
    </el-scrollbar>
  </div>
  <EditGroupDialog
    v-model:visible="editLeftVisible"
    :editData="editLeftItem!"
    @confirm="searchLeftAction"
  />
  <ImportOrEditBlacklistPhoneDialog
    v-model:visible="rightPhoneVisible"
    :editData="currentRightPhone!"
    :groupId="(currentItem?.id||'') + ''"
    groupType="black"
    @confirm="handleConfirm()"
  />
  <ImportPhoneDialog
    v-model:visible="rightImportVisible"
    :groupId="currentItem?.id || -1"
    groupType="black"
    @confirm="handleConfirm"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, defineAsyncComponent } from 'vue'
import dayjs from 'dayjs'
import { copyText, findValueInEnum, formatterEmptyData, formatNumber, Throttle } from '@/utils/utils'
import { tableHeaderStyle } from '@/assets/js/constant'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import PaginationBox from '@/components/PaginationBox.vue'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
import { ElMessage } from 'element-plus'
import to from 'await-to-js';
import { blacklistModel } from '@/api/data-filter'
import ImportOrEditBlacklistPhoneDialog from '@/components/blacklist/ImportOrEditBlacklistPhoneDialog.vue'
import ImportPhoneDialog from '../components/ImportPhoneDialog.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { BlackListGroupItem, BlackPersonTypeEnum, WhiteBlackListItem, WhiteBlackListRecordItem, ImportTypeEnum, BlackListTypeEnum } from '@/type/dataFilter'
import TabsBox from '@/components/TabsBox.vue'
import EditGroupDialog from './EditGroupDialog.vue'

const SearchPhoneList = defineAsyncComponent({loader: () => import('@/components/blacklist/SearchPhoneList.vue')})

// 用户权限获取
const userStore = useUserStore();
const permissions = userStore.permissions[routeMap['黑名单'].id]
const blackListTypeList = ['全部', '普通', '供应商']

// loading信息
const loadingLeft = ref(false)
const loadingRight = ref(false)
const throttleLeft = new Throttle(loadingLeft)
const throttleRight = new Throttle(loadingRight)
/**
 * 左侧模块
 */
// 当前选中左侧元素
const currentItem = ref<BlackListGroupItem | null>(null)
// 左侧列表
const leftList = ref<BlackListGroupItem[]>([])
const leftListTemp = computed(() => {
  return leftList.value.slice(pageLeftSize.value * (currentLeftPage.value - 1), pageLeftSize.value * currentLeftPage.value)
})
// 左侧分页数据
const pageLeftSizeList = [100, 200]
const pageLeftSize = ref(pageLeftSizeList[0])
const currentLeftPage = ref(1)
// 搜索左侧
const leftSearchForm = reactive({
  groupName: '',
  phone: '',
})
const leftInputTypeList = [
  {name: '分组名称', value: 'groupName'},
  {name: '搜索号码', value: 'phone'}
]
const leftInputType = ref<keyof typeof leftSearchForm>('groupName')
const activeBlackListType = ref('全部')
const searchLeftAction = async (id?: number) => {
  loadingLeft.value = true
  const [_, res] = await to(blacklistModel.getGroupList({
    [leftInputType.value]: leftSearchForm[leftInputType.value],
    // @ts-ignore
    groupType: activeBlackListType.value === '全部' ? undefined : (BlackListTypeEnum[activeBlackListType.value] || ''),
  })) as [any, BlackListGroupItem[]]
  leftList.value = (res || []).sort((a,b) => dayjs(a.createTime).isAfter(dayjs(b.createTime)) ? -1 : 1);
  const cur = id ? leftList.value?.find(item => item.id === id) : leftList.value?.find(item => item.id === currentItem.value?.id)
  if (cur) {
    clickLeftItem(cur)
  } else {
    leftList.value?.length > 0 ? clickLeftItem(leftList.value[0]) : (currentItem.value = null)
  }
  loadingLeft.value = false
}
const updateLeftList = (p: number, s: number) => {
  currentLeftPage.value = p
  pageLeftSize.value = s
  searchLeftAction()
}
const clickLeftItem = (item: BlackListGroupItem) => {
  const isSame = currentItem.value?.id === item?.id
  currentItem.value = item
  if (activeTab.value === tabList[1]) {
    searchRecordAction()
  } else {
    !isSame && (rightPhoneList.value = [])
  }
}

// 新增左侧
const editLeftVisible = ref(false)
const editLeftItem = ref<BlackListGroupItem | null>(null)
const editLeftItemAction = (item?: BlackListGroupItem) => {
  editLeftItem.value = item || null
  editLeftVisible.value = true
}
// 删除黑名单组
const delLeftItem = async (row?: BlackListGroupItem) => {
  if (!row || !row.id) return ElMessage.warning('获取黑名单分组失败')
  if (throttleLeft.check()) {
    return
  }
  Confirm({ 
    text: `${row?.phoneCount || 0}条号码将被清空，确定要删除黑名单分组嘛？`,
    type: 'danger',
    title: `删除黑名单分组`,
    confirmText: '确认'
  }).then(async () => {
    throttleLeft.lock()
    const [err, res2] = await to(blacklistModel.deleteGroup({id: row?.id!}))
    !err && ElMessage.success('删除成功')
    searchLeftAction()
  }).catch(() => {}).finally(() => {
    throttleLeft.unlock()
  })
}

// 更新号码数
const updatePhoneCount = async () => {
  if (throttleLeft.check()) {
    return
  }
  if (currentItem.value && currentItem.value?.id) {
    throttleLeft.lock()
    const [err, data] = await to(blacklistModel.updateCount({id: currentItem.value?.id!}))
    currentItem.value.phoneCount = data ?? currentItem.value.phoneCount
    const index = leftList.value.findIndex(item => item.id === currentItem.value!.id)
    leftList.value[index].phoneCount = data ?? currentItem.value.phoneCount
    !err && ElMessage.success('更新号码数量成功')
    throttleLeft.unlock()
  }
}

/**
 * 右侧模块
 */
const tabList = ['号码', '导入记录']
const activeTab = ref(tabList[0])

/** 右侧列表 - 搜索号码 */
const currentSearchInfo = ref('')
const rightPhoneList = ref<WhiteBlackListItem[]>([])
const searchPhoneAction = async (phone: string) => {
  if (throttleRight.check()) {
    return
  }
  throttleRight.lock()
  currentSearchInfo.value = phone || ''
  const [_, res] = await to(blacklistModel.findPhoneList({
    phoneList: (phone || '').split(','),
    groupId: currentItem.value?.id + '',
  })) as [any, WhiteBlackListItem[]]
  rightPhoneList.value = res || [];
  throttleRight.unlock()
}
const delRight = (row: WhiteBlackListItem) => {
  Confirm({ 
    text: `您确定要删除【${row?.phone||''}】?`,
    type: 'danger',
    title: `黑名单删除确认`,
    confirmText: '删除'
  }).then(async () => {
    const [err, _] = await to(blacklistModel.deletePhone({id: row.id!, groupId: row.groupId!}))
    !err && ElMessage.success('删除成功')
    searchPhoneAction(currentSearchInfo.value)
  }).catch(() => {})
}

 // 右侧弹窗-导入\编辑弹窗
const currentRightPhone = ref<WhiteBlackListItem | null>(null)
const rightPhoneVisible = ref(false)
const editRight = (row: WhiteBlackListItem) => {
  currentRightPhone.value = row
  rightPhoneVisible.value = true
}
const handleConfirm = (num?: number) => {
  if (activeTab.value === tabList[1]) {
    searchRecordAction()
  } else {
    searchPhoneAction(currentSearchInfo.value||'')
  }
  // 更新号码
  if (num && currentItem.value) {
    currentItem.value.phoneCount = num ?? currentItem.value.phoneCount
    const index = leftList.value.findIndex(item => item.id === currentItem.value!.id)
    leftList.value[index].phoneCount = num ?? currentItem.value.phoneCount
  }
}
const handleImportOperator = (type?: string) => {
  if (type === 'single') {
    importSingleRight()
  }
  if (type === 'file') {
    importBatchRight()
  }
}
// 右侧弹窗-导入弹窗
const rightImportVisible = ref(false)
const importBatchRight = () => {
  rightImportVisible.value = true
}

const importSingleRight = () => {
  rightPhoneVisible.value = true
  currentRightPhone.value = null
}
/** 右侧列表 - 搜索号码 结束*/ 

// 右侧列表： 导入记录
const searchRightForm = reactive<{
  startTime?: string,
  endTime?: string,
}>({
  startTime: dayjs().add(-7, 'd').format('YYYY-MM-DD HH:mm:ss'),
  endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
})
const rightRecordList = ref<WhiteBlackListRecordItem[]>([])
const searchRecordAction = async () => {
  if (throttleRight.check()) {
    return
  }
  throttleRight.lock()
  const [_, res] = await to(blacklistModel.getRecordList({
    groupId: currentItem.value?.id!,
    ...searchRightForm
  })) as [any, WhiteBlackListRecordItem[]]
  rightRecordList.value = res || [];
  total.value = res?.length || 0
  throttleRight.unlock()
}
const cancelRecordAction = async (row: WhiteBlackListRecordItem) => {
  let warningInfo = ''
  if (row.importCount && row.importCount >= 1000000) {
    warningInfo = `<div class="tw-text-[#E54B17] tw-font-[600]">注意：您撤销的数据量为【${row.importCount}】，可能会处理较长时间，请谨慎操作！</div>`
  } else if (row.importCount && row.importCount >= 100000) {
    warningInfo = `<div class="tw-text-[#FFCC00] tw-font-[600]">注意：您撤销的数据量为【${row.importCount}】，可能会处理较长时间，请谨慎操作！</div>`
  }
  Confirm({ 
    text: `<div>您确定要撤销导入${row?.fileName ? '【' + row?.fileName + '】': ''}吗?</div>
    ` + warningInfo,
    type: 'warning',
    title: `撤销导入确认`,
    confirmText: '确认'
  }).then(async () => {
    loadingRight.value = true
    const [err, _] = await to(blacklistModel.cancelRecord({
      importHistoryId: row.id!
    }))
    if (!err) ElMessage.success('撤销导入成功')
    loadingRight.value = false
  }).catch(() => {}).finally(() => {
    searchLeftAction(currentItem.value?.id)
  })
}
// // 对于导入记录，每次切换tab后刷新
const handleTabChange = () => {
  if (activeTab.value == tabList[1]) {
    searchRecordAction()
  }
}
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
}
const rightRecordListTemp = computed(() => {
  return rightRecordList.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})
// 初始化
const init = async () => {
  searchLeftAction()
}
init()
</script>

<style scoped lang="postcss">
.form-dialog-header {
  font-size: 14px;
}
.el-table {
  font-size: 13px;
  color: var(--primary-black-color-600);
}
.search-box {
  padding: 0 16px 16px;
  display: grid;
}
.item {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
}
.info-title {
  width: 5em;
  display: inline-block;
  text-align: right;
}
.info-content {
  word-break: break-all;
}
/** :deep(.tab-box) {
  height: 36px;
  .normal-tab {
    height: 31px;
    font-size: 13px;
    line-height: 28px;
  }
} */
</style>
