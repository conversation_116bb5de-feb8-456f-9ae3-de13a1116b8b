<template>
  <div class="tw-flex tw-w-full tw-h-full tw-overflow-hidden tw-rounded-[4px] tw-border-[1px] tw-border-[#E1E3E6]">
    <div class=" tw-flex tw-flex-col tw-border-r-[1px] tw-bg-[#f0f2f5] tw-h-full" :style="{'width': props.width*10 + '%'}">
      <div class="transfer-item tw-justify-between">
        <span class="info-title">{{ props.leftTitle }}</span>
        <el-button v-if="checkAllVisible" link type="primary" class="tw-mr-auto tw-ml-[12px]" @click="checkAll">
          全部添加
        </el-button>
        <span class="number">{{ leftList?.length || 0 }}<span v-if="unit">{{ unit }}</span></span>
      </div>
      <el-scrollbar class="tw-flex-grow" wrap-class="tw-pr-[8px]">
        <div
          v-for="item in leftList"
          :key="item[props.value]"
          class="transfer-item info-title-deep tw-cursor-pointer"
          @click="handleTransferChange(item)"
        >
          <span class="tw-truncate tw-w-[calc(100%-16px)] tw-text-left" :class="{'tw-text-[#C8C9CC]': !!item.disabled}">{{ props.name ? item[props.name] : item }}</span>
          <el-icon v-if="!item.disabled" class="tw-ml-[6px]" color="var(--primary-black-color-500)"><SvgIcon name="add1" /></el-icon>
        </div>
      </el-scrollbar>
    </div>
    <div class=" tw-flex tw-flex-col tw-h-full" :style="{'width': 10*(10-width) + '%'}">
      <div class="transfer-item tw-justify-between">
        <span class="info-title">{{ props.rightTitle }}</span>
        <el-button v-if="checkAllVisible" link type="primary" class="tw-mr-auto tw-ml-[12px]" @click="uncheckAll">
          全部取消
        </el-button>
        <span class="number">{{ rightList?.length || 0 }}<span v-if="unit">{{ unit }}</span></span>
      </div>
      <el-scrollbar class="tw-flex-grow" wrap-class="tw-pr-[8px]">
        <div
          v-for="item in rightList"
          :key="item[props.value]"
          class="transfer-item info-title-deep tw-cursor-pointer"
          @click="handleTransferChange(item, true)"
        >
          <span class="tw-truncate tw-w-[calc(100%-16px)] tw-text-left" :class="{'tw-text-[#C8C9CC]': !!item.disabled}">{{ props.name ? item[props.name] : item }}</span>
          <el-icon v-if="!item.disabled" class="tw-ml-[6px]" color="var(--primary-black-color-500)"><Close /></el-icon>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, watch } from 'vue'
import { Close } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
const props = withDefaults(
  defineProps<{
    leftTitle: string,
    leftList: any[] | null,
    rightTitle: string,
    rightList: any[] | null,
    width?: number, // 左侧布局宽度，取值0-10，默认一半
    name?: string, // leftList、rightList作为label的字段
    value?: string, // leftList、rightList作为key的字段
    unit?: string, // 量词
    checkAllVisible?: boolean, // 显示全选按钮
  }>(), {
    name: 'name',
    value: 'id',
    width: 5,
    unitVisible: true,
    unit: '人',
    checkAllVisible: false,
  }
)
// emit
const emits = defineEmits([
  'update:leftList', 'update:rightList',
])
const leftList = ref(props.leftList || [])
const rightList = ref(props.rightList || [])

const handleTransferChange = (val: any, isRight?: boolean) => {
  if (!!val.disabled) return
  if (isRight) {
    leftList.value.push(val)
    rightList.value = rightList.value.filter(item => props.value ? item[props.value]!==val[props.value] : item !== val)
  } else {
    rightList.value.push(val)
    leftList.value = leftList.value.filter(item => props.value ? item[props.value]!==val[props.value] : item !== val)
  }
  emits('update:leftList', leftList.value)
  emits('update:rightList', rightList.value)
}

watch([() => props.leftList, () => props.rightList], () => {
  leftList.value = props.leftList || []
  rightList.value = props.rightList || []
}, {deep: true})
/**
 * 全选
 */
const checkAll = () => {
  const arr: typeof leftList.value = []
  leftList.value.forEach((item) => {
    !item.disabled ? rightList.value.push(item) : arr.push(item)
  })
  leftList.value = arr
  emits('update:leftList', leftList.value)
  emits('update:rightList', rightList.value)
}
/**
 * 取消全选
 */
const uncheckAll = () => {
  const arr: typeof rightList.value = []
  rightList.value.forEach((item) => {
    !item.disabled ? leftList.value.push(item) : arr.push(item)
  })
  rightList.value = arr
  emits('update:leftList', leftList.value)
  emits('update:rightList', rightList.value)
}
</script>

<style lang="postcss" type="text/postcss" scoped>
.transfer-item {
  padding: 8px 12px;
  height: 36px;
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--primary-black-color-200);
  .el-icon {
    cursor: pointer;
  }
  .number {
    color:  var(--primary-black-color-600);
    font-size: 13px;
    font-weight: 600;
  }
}
</style>
