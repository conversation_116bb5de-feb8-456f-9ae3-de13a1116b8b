<template>
  <!--模块标题-->
  <div class="module-title">
    行业管理
  </div>

  <!--模块主体-->
  <div v-loading="loading" class="industry-container">
    <div v-for="item in allIndustryList" v-if="allIndustryList && allIndustryList?.length > 0" :key="item.id" class="tw-mt-[16px] first:tw-mt-0">
      <div class="industry-title">
        {{ `${item.primaryIndustry}(共${Object.keys(item.secondaryIndustriesIdMap)?.length || 0}类)` }}
      </div>
      <ul>
        <li
          v-for="(secInsId, secInsName) in item.secondaryIndustriesIdMap"
          :key="secInsId"
        >
          {{ secInsName }}
        </li>
      </ul>
    </div>
    <el-empty v-else class="tw-items-center" />
  </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import { onMounted, ref, onUnmounted } from 'vue'
import { IndustryItem } from '@/type/industry'

const globalStore = useGlobalStore()

// 正在加载
const { loading } = storeToRefs(globalStore)

// 行业列表
const allIndustryList = ref<IndustryItem[] | null>([])

onMounted(async () => {
  await globalStore.getAllIndustryList()
  allIndustryList.value = globalStore.allIndustryList
})
onUnmounted(() => {
  allIndustryList.value = null
})
</script>

<style scoped lang="postcss">
.industry-container {
  margin: 16px;
  width: 100%;
  min-width: 1032px;
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  .industry-title {
    font-weight: 600;
    font-size: 14px;
    text-align: left;
    margin-bottom: 8px;
  }
  ul {
    background-color: inherit;
    display: grid;
    flex-wrap: wrap;
    overflow: hidden;
    column-gap: 12px;
    row-gap: 8px;
    grid-template-columns: repeat(6, minmax(0, 1fr));
    @media (min-width: 1636px) {
      grid-template-columns: repeat(9, minmax(0, 1fr));
    }
    li {
      min-width: 160px;
      height: 36px;
      border-radius: 4px;
      background-color: #fff;
      padding: 8px 12px;
      font-size: 13px;
      box-sizing: border-box;
      flex-shrink: 0;
    }
  }
}
</style>
