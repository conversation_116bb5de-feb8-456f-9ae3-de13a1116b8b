<template>
  <el-dialog
    :model-value="props.visible"
    width="600px"
    class="restriction-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        批量添加{{ props.tab }}
      </div>
    </template>

    <div class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form ref="formRef" :model="form" :rules="rules">
          <el-upload
            v-show="!form.file?.has('file')"
            ref="uploadRef"
            drag
            class="upload-box"
            :multiple="uploadInfo.multiple"
            :show-file-list="uploadInfo.showFileList"
            :accept="uploadInfo.accept"
            :limit="uploadInfo.limit"
            :auto-upload="uploadInfo.autoUpload"
            :on-change="uploadInfo.onChange"
          >
            <div ref="uploadButtonRef" class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon size="24px" color="#969799" class="upload-icon">
                <SvgIcon name="upload2" color="inherit" />
              </el-icon>
              <div class="upload-hint">点击或拖拽文件到此处上传</div>
            </div>
          </el-upload>

          <div v-show="form.file?.has('file')" class="upload-wrapper">
            <div class="upload-result">
              <div class="upload-filename">
                <div class="upload-filename-text">
                  {{ form.fileName || '（未选中文件）' }}
                </div>
              </div>

              <div class="upload-button-box">
                <el-button link type="primary" @click="onClickUploadRetry">
                  重新上传
                </el-button>
                <el-button link type="primary" @click="onClickUploadDelete">
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-form>

        <div class="tw-flex tw-flex-col tw-items-end tw-mt-[8px]">
          <el-button type="primary" link @click="onClickDownloadTemplate">
            下载模板
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" :disabled="loadingConfirm" @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" :loading="loadingConfirm" :icon="Select" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { Throttle } from '@/utils/utils'
import { ElMessage, FormRules, UploadFile, UploadFiles } from 'element-plus'
import to from 'await-to-js'
import { forbiddenWordUrlModel, forbiddenWordVariableModel } from '@/api/data-filter'
import { exportExcel } from '@/utils/export'
import { ForbiddenWordTabEnum } from '@/type/dataFilter'
import SvgIcon from '@/components/SvgIcon.vue'
import * as _XLSX from 'xlsx'

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  visible: boolean,
  tab: ForbiddenWordTabEnum,
}>(), {
  visible: false,
  tab: ForbiddenWordTabEnum.VARIABLE,
})
const emits = defineEmits([
  'update:visible',
  'confirm'
])

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

interface FormInfo {
  file?: FormData
  fileName?: string
}

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()
// 表单默认数据，用函数返回值达到深拷贝效果
const formDefault = (): FormInfo => {
  return {
    file: new FormData(),
    fileName: '',
  }
}
// 表单数据
const form: FormInfo = reactive(formDefault())
// 表单校验规则
const rules: FormRules = reactive({
  file: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule: any, value: FormData, callback: any) => {
      if (!value.has('file')) {
        callback(new Error('文件不能为空'))
      } else {
        callback()
      }
    }
  },
})

/**
 * 表单校验
 * @param callback 表单校验通过后的回调函数
 */
const validForm = (callback?: Function) => {
  formRef.value && formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 校验通过
      // 执行回调函数
      typeof callback === 'function' && await callback()
    } else {
      // 校验不通过
      // 提示用户检查表单
      ElMessage({
        message: '请按提示正确填写信息',
        type: 'warning',
      })
    }
  })
}
/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const data = form.file ?? new FormData()

  // 请求接口
  let err: any = null
  if (props.tab === ForbiddenWordTabEnum.VARIABLE) {
    // 变量违禁词
    const [e, _] = await to(forbiddenWordVariableModel.addBatch(data))
    err = e
  } else if (props.tab === ForbiddenWordTabEnum.URL) {
    // 短链违禁词
    const [e, _] = await to(forbiddenWordUrlModel.addBatch(data))
    err = e
  }

  // 返回失败结果
  if (err) {
    ElMessage({
      type: 'error',
      message: '批量添加失败'
    })
    // 节流锁解锁
    throttleConfirm.unlock()
    return
  }

  // 返回成功结果
  ElMessage({
    message: '批量添加成功',
    type: 'success',
  })
  emits('confirm')
  // 关闭弹窗
  closeDialog()

  // 节流锁解锁
  throttleConfirm.unlock()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, formDefault())
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清除其他内容
  resetUploadFiles()
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  emits('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 200)
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  validForm(submit)
}
/**
 * 点击取消按钮
 */
const handleCancel = () => {
  closeDialog()
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 上传文件 开始 ----------------------------------------

// 上传组件DOM
const uploadRef = ref()
// 上传按钮DOM
const uploadButtonRef = ref()
// 上传组件参数
const uploadInfo = {
  // 是否支持多选文件
  multiple: false,
  // 是否显示已上传文件列表
  showFileList: false,
  // 接受上传的文件类型
  accept: '.xls,.xlsx',
  // 允许上传文件的最大数量
  limit: 1,
  // 是否自动上传文件
  autoUpload: false,
  // 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
  onChange: async (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
    if (!uploadFiles?.length) {
      // 上传文件列表为空
      return
    }
    // 上传文件列表不为空
    if (!uploadFile.name.endsWith('.xls') && !uploadFile.name.endsWith('.xlsx')) {
      ElMessage({
        message: '上传的文件必须为Excel (*.xls, *.xlsx)',
        type: 'error',
      })
      return
    }

    form.file = new FormData()
    if (uploadFile.raw) {
      form.file.append('file', uploadFile.raw)
    }
    form.fileName = uploadFile.name

    // 判断xlsx文件内容是否含有正确的表头
    const ab = await uploadFile.raw?.arrayBuffer()
    const workbook = _XLSX.read(ab)
    const sheet = workbook.Sheets[workbook.SheetNames[0]]
    if (sheet['A1'].v !== '违禁词' || sheet['B1'].v !== '是否正则' || sheet['C1'].v !== '备注') {
      ElMessage({
        message: '表格格式不正确，请按照模板格式上传正确的表格文件',
        type: 'warning',
      })
      // 清空当前选中的文件
      resetUploadFiles()
    }
  },
}
/**
 * 清空当前文件队列
 */
const resetUploadFiles = () => {
  uploadRef.value?.clearFiles && uploadRef.value.clearFiles()
  form.file = new FormData()
  form.fileName = ''
}
/**
 * 点击重新上传按钮
 */
const onClickUploadRetry = () => {
  resetUploadFiles()
  // 帮用户点击一下选择文件按钮
  uploadButtonRef.value?.click && uploadButtonRef.value.click()
}
/**
 * 点击删除按钮
 */
const onClickUploadDelete = () => {
  resetUploadFiles()
}

// ---------------------------------------- 上传文件 结束 ----------------------------------------

// ---------------------------------------- 下载模板 开始 ----------------------------------------

/**
 * 点击下载模板按钮
 */
const onClickDownloadTemplate = async () => {
  const list: { [prop: string]: any }[] = [
    {
      '违禁词': '',
      '是否正则': '',
      '备注': '',
    },
  ]

  try {
    exportExcel(
      list,
      '违禁词管理-模板.xlsx',
      '违禁词管理'
    )
  } catch (e) {
    ElMessage.error('模板下载失败：' + e)
    console.error('模板下载失败', e)
  }
}

// ---------------------------------------- 下载模板 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => props.visible, async (val) => {
  // 每次显示弹窗时
  if (val) {
    // 更新表单数据
    await nextTick()
    resetForm()
  }
})

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.upload-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  height: 96px;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
}
.upload-result {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  align-self: flex-start;
  margin: 0 16px;
  text-align: left;
}
.upload-filename {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #313233;
  line-height: 22px;
}
</style>
