<template>
  <div class="tw-h-[calc(100vh-130px)] tw-min-w-[1080px] tw-overflow-y-hidden tw-flex tw-flex-col">
    <div class="search-box tw-px-[12px]">
      <div class="tw-grid tw-grid-cols-4 tw-gap-[12px]">
        <div class="item">
          <el-select v-model="searchForm.account" placeholder="请选择账号" @change="search()" filterable clearable>
            <el-option v-for="item in accountList" :key="item.account" :label="`${item.account}（${item.name}）`" :value="item.account" />
          </el-select>
        </div>
        <div class="item tw-col-span-2">
          <TimePickerBox
            v-model:start="searchForm.timeStart"
            v-model:end="searchForm.timeEnd"
            :disabledDate="disabledDate"
            :maxRange="60*60*24*1000"
            :clearable="false"
            @change="search"
          />
        </div>
        <div class="item-btn">
          <el-button type="primary" @click="search()" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData"
      v-loading="loading"
      ref="tableRef"
      row-key="supplyLineName"
      class="tw-grow"
      :header-cell-style="tableHeaderStyle"
      stripe
    >
      <el-table-column align="left" prop="account" label="账号" fixed="left" :formatter="formatterEmptyData" min-width="200" show-overflow-tooltip/>
      <el-table-column align="left" prop="roleName" label="所属角色" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip>
      </el-table-column>
      <el-table-column align="left" prop="checkCallTimes" label="巡检通话数" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column align="left" prop="checkPlayTimes" label="巡检录音数" min-width="120" :formatter="formatterEmptyData" show-overflow-tooltip/>
      <el-table-column label="操作" width="120" align="right" fixed="right">
        <template #default="{ row }">
          <div class="table-btn-box">
            <el-button type="primary" link @click="goDetails(row)">详情</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      :pageSize="searchForm.limit"
      :pageSizeList="pageSizeList"
      :currentPage="searchForm.page"
      :total="total"
      @search="search()"
      @update="updateList"
    >
    </PaginationBox>
  </div>

  <!-- 线路数据详情（异常通话记录） -->
  <RecordDetailsDrawer
    v-model:visible="drawerVisible"
    :currentAccount="currentAccount"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onUnmounted, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import PaginationBox from '@/components/PaginationBox.vue'
import RecordDetailsDrawer from './RecordDetailsDrawer.vue'
import { onBeforeRouteLeave } from 'vue-router'
import { tableHeaderStyle } from '@/assets/js/constant'
import { formatterEmptyData, } from '@/utils/utils'
import { inspectionModel, } from '@/api/Inspection'
import { InspectionRecordItem } from '@/type/Inspection'
import { aiTeamModel, } from '@/api/user'
import { RoleResponse, AccountItem } from '@/type/user'
import TimePickerBox from '@/components/TimePickerBox.vue'
import to from 'await-to-js'
import dayjs from 'dayjs'

// loading信息
const loading = ref(false)

// 用户权限获取
// const userStore = useUserStore();
// const permissions = userStore.permissions[routeMap['供应平台-线路数据'].id]

// 搜索内容
const searchForm = reactive<{
  account?: string,
  timeStart: string,
  timeEnd: string,
  page: number,
  limit: number,
}>({
  account: undefined,
  timeStart: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  timeEnd: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
  page: 1,
  limit: 20,
})

// 列表数据
const tableData = ref<InspectionRecordItem[] | null>([])

/** 分页 开始 */
const pageSizeList = [20, 50, 100, 200]
const total = ref(0)
const updateList = (p: number, s: number) => {
  searchForm.page = p
  searchForm.limit = s
  search()
}

// 搜索
const search = async () =>{
  if (loading.value) return ElMessage.warning('请勿重复请求')
  loading.value = true
  const [_, data] = await to(inspectionModel.findInspectionRecord(searchForm))
  total.value = data?.total || 0
  tableData.value = data?.data as InspectionRecordItem[] || []
  loading.value = false
}

const disabledDate = (time: Date) => {
  const _minTime = dayjs().add(-1, 'month').startOf('day').valueOf()
  const _maxTime = dayjs().endOf('day').valueOf()
  return time.getTime() > _maxTime || time.getTime() < _minTime
}

// 点击数据详情-展示抽屉
const drawerVisible = ref(false)
const currentAccount = ref<{
  account: string,
  timeStart: string,
  timeEnd: string
} | null>(null)
const goDetails = (row: InspectionRecordItem) => {
  if (!row || !row.account) return
  currentAccount.value = {
    account: row.account,
    timeStart: searchForm.timeStart,
    timeEnd: searchForm.timeEnd,
  }
  drawerVisible.value = true
}

const roleList = ref<RoleResponse[] | null>(null)
const accountList = ref<AccountItem[] | null>(null)
const init = async () => {
  roleList.value = await aiTeamModel.searchRoleList() as RoleResponse[]
  accountList.value = await aiTeamModel.getMerchantAccountList({}) as AccountItem[] || []
  search()
}

/** vue生命周期 */
// 初始化
onMounted(() => {
  init()
})
const clearAllData = () => {
  tableData.value = null
  roleList.value = null
  accountList.value = null
  currentAccount.value = null
  drawerVisible.value = false
}
onUnmounted(() => { clearAllData() })
onBeforeRouteLeave(() => { clearAllData() })
</script>

<style scoped lang="postcss">
.el-table {
  font-size: 13px;
  :deep(.caret-wrapper) {
    display: none;
  }
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
