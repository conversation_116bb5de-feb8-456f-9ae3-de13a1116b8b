import { BusinessTypeEnum, SmsTemplateTypeEnum, SmsAccountItem, SmsAccountServiceProviderEnum, VariableSmsPojoItem } from '@/type/sms'
import { SupplierLineInfo } from '@/type/supplier'
import { CallStatusEnum } from '@/type/task'

export enum MerchantStatusEnum {
  '启用' = 1,
  '禁用' = 0,
}

export enum MerchantCallbackTypeEnum {
  '三方通用接口' = '三方通用接口',
  '内部客户接口' = '内部客户接口',
  '蚂蚁1接口' = '蚂蚁1接口',
  '蚂蚁2接口' = '蚂蚁2接口',
  '蚂蚁3接口' = '蚂蚁3接口',
  '火山接口' = '火山接口',
  '冰鉴接口' = '冰鉴接口',
}
/**
 * 商户管理
 */
// 商户信息
export interface MerchantInfo {
  id?: number | null
  tenantNo?: string
  tenantName?: string
  tenantShortName?: string
  contactName?: string
  contactPhone?: string
  contactMail?: string
  contactAddress?: string
  callbackType?: MerchantCallbackTypeEnum
  remark?: string
  status?: MerchantStatusEnum
  createTime?: string
  updateTime?: string
  relatedScriptList?: MerchantScriptInfo[]
}

// 商户账号信息
export interface MerchantAccountInfo {
  id?: number | null
  account?: string
  password?: string
  passwordConfirmed?: string
  name?: string
  phone?: string
  email?: string
  address?: string
  note?: string
  createTime?: string
  tenantId?: number
  roleId?: number
  accountType?: number
  accountEnable?: boolean
  gender?: string
  department?: string
  groupId?: string
  isForEncryptionPhones?: boolean // true表示加密， false表示普通
  isForEncryptionAgain?: boolean // 是否`加解密`
}

// 商户线路 启用状态 枚举
export enum MerchantLineEnableStatusEnum {
  '停用' = 'DISABLE',
  '启用' = 'ENABLE',
}

// 商户线路 线路类型 枚举
export enum MerchantLineTypeEnum {
  'AI外呼' = 'AI_OUTBOUND_CALL',
  '人工直呼' = 'MANUAL_DIRECT_CALL',
}

// 商户线路信息
export interface MerchantLineInfo {
  // V4已有
  id?: number | null
  lineNumber?: string
  lineName?: string
  enableStatus?: MerchantLineEnableStatusEnum
  concurrentLimit?: number | string
  industry?: string[]
  supplyLineNumbers?: string[]
  notes?: string
  tenantId?: number
  tenantName?: string
  tenantNo?: string
  lineRemainConcurrent?: number
  createTime?: string
  updateTime?: string
  groupId?: string
  secondIndustry?: string
  secondIndustries?: string[]
  supplyLineGroups?: MerchantLineConstituteParams[]
  tenantLineNumber?: string
  adminId?: number

  // V5新增
  lineType?: MerchantLineTypeEnum // 线路类型，V5版本时区分为AI外呼（人机协同也包含在AI外呼线路里）和人工直呼
}

// 商户线路信息的接口参数
export interface MerchantLineInfoParams {
  // V4接口
  id?: string | number | null
  lineNumber?: string | number | null
  lineName?: string | null
  supplyLineNumbers?: string[] | string
  tenantId?: number
  adminId?: number
  enableStatus?: MerchantLineEnableStatusEnum
  concurrentLimit?: number
  notes?: string
  lineRemainConcurrent?: number
  tenantLineId?: number
  groupId?: string
  secondIndustries?: string[]
  supplyLineGroups?: MerchantLineConstituteParams[]
  tenantLineNumber?: string

  // V5接口
  lineType?: string
}

// 商户话术信息
export interface MerchantScriptInfo {
  tenantId?: number | string
  relatedScriptId?: number | string
  secondIndustry?: string
  smsTriggerNames?: string // 需要使用JSON.parse转为数组
  scriptId?: number | string
  scriptName?: string
  active?: string
  version?: number
  scriptStringId?: string
  createTime?: string
}

// 商户话术信息的接口参数
export interface MerchantScriptInfoParams {
  id?: number | null
  groupId?: string
}

// 可添加话术
export interface MerchantScriptAvailableInfo {
  id: number | null
  scriptName?: string
  scriptStringId?: string
  status: string
}

// 商户模块映射表单项
export interface MerchantMapItemType {
  name: string
  val: any
  text: string
  tabText?: string
  searchText?: string
}

// 商户模块映射表
export interface MerchantMapType {
  [propName: string]: MerchantMapItemType
}

// 商户线路支持地区信息 请求参数
export interface MerchantLineRegionApiParams {
  date: string
  tenantLineNumber: string
  type: string
  operators: string[]
  minutes: number[]
}

// 商户线路 线路组成 单个线路组 接口数据
export interface MerchantLineConstituteParams {
  id?: number | null
  serviceProvider?: string
  cityCodes?: string[]
  maxConcurrentLimit?: number
  supplyLineNumbers?: string[]
  tenantLineNumber?: string
  supplyLineList?: Pick<SupplierLineInfo, 'lineName' | 'lineNumber' | 'concurrentLimit' | 'pending'>[]
  supplyLineNameList?: string[]
  cityCountStrList?: string[]
}

// 商户 项目 信息
export interface MerchantProjectItem {
  id?: number | null,
  programName?: string | null,
  productId?: string | null,
  productName?: string | null,
  secondIndustryId?: string | null,
  secondIndustryName?: string | null,
  tenantId?: string | null,
  groupId?: string | null,
}

// 商户 项目 产品 选项
export interface MerchantProductOption {
  id?: number,
  createTime?: string,
  updateTime?: string,
  industrySecondFieldId?: number,
  industrySecondFieldName?: string,
  productName?: string,
  name?: string,
}

export enum RecordTypeEnum {
  'AI外呼' = 'PURE_AI',
  '人机协同' = 'HUMAN_MACHINE',
  '人工直呼' = 'MANUAL_DIRECT',
}

// 商户设置信息：AK、回调等
export interface MerchantSetting {
  accountId: number,
  salt?: string,
  aes?: string,
  dataStatisticRange?: RecordTypeEnum[], // 查询范围
  taskCallbackUrl?: string,
  callBackRange?: RecordTypeEnum[],
  callBackUrl?: string, // 回调地址（旧）
  callSmsCallbackUrl?: string, // 短信接口
  callMCallbackUrl?: string, // M短信接口
  callDataCallbackUrl?: string, // 数据接口
  callUpdateCallbackUrl?: string, // 晚上更新接口
  smsCallbackUrl?: string,
  smsMoCallbackUrl?: string, // 上行短信回调地址
  callbackFieldConfig?: string[], // 回调字段
  callbackStatusConfig?: string[], // 回调通话状态
  whiteIps: string[],
}

export class MerchantSettingOrigin implements MerchantSetting {
  accountId
  salt = undefined
  aes = undefined
  dataStatisticRange = []
  taskCallbackUrl = undefined
  callBackRange = []
  callBackUrl = undefined
  callSmsCallbackUrl = undefined
  callDataCallbackUrl = undefined
  smsCallbackUrl = undefined
  smsMoCallbackUrl = undefined
  whiteIps = []
  callbackFieldConfig = []
  callbackStatusConfig = []

  constructor(id: number) {
    this.accountId = id
  }

}

// 商户 短信模板 启用状态
export enum SmsTemplateStatusEnum {
  '禁用' = 'DISABLE',
  '启用' = 'ENABLE',
}

// 商户 短信配置 补偿范围 枚举
export enum SmsConfigCompensateRangeEnum {
  // 提交失败
  SUBMIT_FAIL = 'SUBMIT_FAIL',
  // 发送失败
  SEND_FAIL = 'SEND_FAIL',
  // 回执超时
  RECEIPT_TIMEOUT = 'RECEIPT_TIMEOUT',
}

// 商户 短信配置 补偿范围 表单值
export const SmsConfigCompensateRangeValList = {
  // 启用 回执超时
  DISABLE_RECEIPT: [
    SmsConfigCompensateRangeEnum.SUBMIT_FAIL,
    SmsConfigCompensateRangeEnum.SEND_FAIL,
    SmsConfigCompensateRangeEnum.RECEIPT_TIMEOUT,
  ],
  // 禁用 回执超时
  ENABLE_RECEIPT: [
    SmsConfigCompensateRangeEnum.SUBMIT_FAIL,
    SmsConfigCompensateRangeEnum.SEND_FAIL,
  ],
}

// 商户 短信配置
export interface SmsConfigItem {
  businessType?: BusinessTypeEnum; // 业务类型
  smsTemplateType?: SmsTemplateTypeEnum;
  comments?: string; // 备注
  compensateCount?: number | null; // 补偿次数
  compensateEffectivePeriod?: number; // 补偿时效
  compensateEndTime?: string; // 补偿时段，结束时间
  compensateMessage?: boolean; // 短信补偿
  compensateRange?: SmsConfigCompensateRangeEnum[]; // 补偿范围
  compensateStartTime?: string; // 补偿时段，开始时间
  createTime?: string;
  groupId?: string;
  id?: number; // 模板编号
  messageContent?: string; // 短信内容
  messageSign?: string; // 短信签名
  volcanoAccountId?: string; // 火山账号ID
  volcanoSmsTemplateId?: string; // 火山短信模板ID
  secondIndustry?: string; // 适用行业
  shortLinkOrdinaryNumbers?: string[]; // 普通短链，编号列表
  shortLinkThousandNumbers?: string[]; // 千人千链，编号列表
  templateName?: string; // 模板名称
  templateStatus?: string;
  tenantId?: number;
  templateId?: number;
  // 通道配置
  tenantSmsChannel?: {
    channelAccountWarnOpen?: boolean;
    channelRelateTaskOpen?: boolean;
    channelWarnOpen?: boolean;
    createTime?: string;
    id?: number;
    isPending?: boolean; // 挂起状态
    receiptCount?: number;
    receiptFailCount?: number;
    sendCount?: number;
    sendFailCount?: number;
    smsAccountGroups?: SmsAccountGroupItem[];
    submitCount?: number;
    submitFailCount?: number;
  }
}

// 商户 短信模板
export interface MerchantSmsTemplateItem {
  variableUsed?: SmsVariableItem[] | VariableSmsPojoItem[],
  secondIndustry?: string;
  messageSign?: string; // 短信签名
  id?: number; // 模板编号
  templateName?: string; // 模板
  templateStatus?: SmsTemplateStatusEnum | '',
  businessType?: BusinessTypeEnum,
  isPending?: boolean // 原运营端tenantSmsChannel.isPending字段
}
export interface SmsTemplateItem extends SmsConfigItem, MerchantSmsTemplateItem {
  // 商户ID
  tenantId?: number,
  // 账号ID
  groupId?: string,
  // 模板启用状态
  templateStatus?: SmsTemplateStatusEnum | '',
  createTime?: string,
  updateTime?: string,
  variableUsed?: SmsVariableItem[] | VariableSmsPojoItem[],
}

// 商户 短信模板 接口参数
export interface SmsTemplateParams extends SmsTemplateItem {
  isChannelPending?: boolean | null,
}

// 商户 短信模板 筛选参数
export interface SmsTemplateFilter {
  templateName?: string,
  id?: number,
  secondIndustry?: string,
  templateStatus?: SmsTemplateStatusEnum | '',
  isChannelPending?: boolean | null,
}

export interface SmsAccountGroupItem {
  cityCodes?: string[];
  id?: number;
  smsAccountGroupEntities?: {
    pending?: boolean;
    smsAccount?: SmsAccountItem;
  }[],
  smsAccountNumbers?: string[];
  smsServiceProvider?: SmsAccountServiceProviderEnum;
  tenantSmsTemplateId?: number;
}

// 通道预警
export interface ChannelWarnConfig {
  channelAccountWarnOpen?: boolean;
  channelRelateTaskOpen?: boolean;
  channelWarnOpen?: boolean;
  sendFailCount?: number | null;
  sendCount?: number | null;
  receiptCount?: number | null;
  receiptFailCount?: number | null;
  tenantSmsTemplateId?: number;
}

// 商户 短信模板-短信通道信息
export interface SmsChannelItem extends ChannelWarnConfig {
  smsAccountGroups: SmsAccountGroupItem[]
  tenantSmsTemplateId?: number;
}

// 商户 短信模板 短信通道 切换挂起状态 接口参数
export interface SmsChannelStatusParams {
  status: boolean,
  templateId?: number,
}

// 商户 短信变量 类型 枚举
export enum SmsVariableTypeEnum {
  // '系统内置' = null,
  '自定义' = 'CUS',
}

// 商户 短信变量 字段类型 枚举
export enum SmsVariableColumnTypeEnum {
  "数字" = 'NUMBER',
  "文本" = 'TEXT',
  "日期" = 'DATE',
  "时间" = 'TIME',
  "姓氏" = 'SURNAME',
  "城市" = 'CITY',
  "金额" = 'AMOUNT',
}

// 商户 短信变量
export interface SmsVariableItem {
  id?: number,
  tenantId?: number,
  groupId?: string,
  variableName?: string,
  variableComment?: string,
  // 变量类型 系统内置 null 自定义 CUS
  variableType?: string | null,
  // 字段类型
  columnType?: SmsVariableColumnTypeEnum,
  createTime?: string,
  updateTime?: string,
}

// 商户 短信变量 接口参数
export interface SmsVariableParams extends SmsVariableItem {
  groupId?: string,
}

export interface MerchantSupplyLineParams {
  tenantLineNumber?: string
  supplyLineNumber?: string
  pendingStatus?: boolean
  isPriority?: boolean
  supplyLimit?: number
}

// 商户 短信 系统内置变量 枚举
export enum SystemVariableEnum {
  '手机尾号' = 'sysLastFourDigits',
  '归属城市' = 'sysCity',
  '下发日期' = 'sysIssueDate',
}
