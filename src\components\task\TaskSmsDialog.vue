<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    align-center
    class="dialog-form"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">
        {{ dialogTitle[props.type] || '触发短信' }}
      </div>
    </template>
    <!--  话术触发短信弹窗 -->
    <el-table
      v-if="props.type === 0"
      v-loading="loading"
      :data="props.data?.scriptSms || []"
      :header-cell-style="tableHeaderStyle"
      class="tw-grow"
      row-key="triggerName"
    >
      <el-table-column property="triggerName" label="触发点" align="left" :width="160"></el-table-column>
      <el-table-column property="smsTemplateId" label="短信模板" align="left" :min-width="240" show-overflow-tooltip>
        <template #default="{ row }">
          {{  row.smsTemplateId ? smsTemplateMap?.get(row.smsTemplateId) : '-' }}
        </template>
      </el-table-column>
    </el-table>

    <!--  挂机短信弹窗 -->
    <el-table
      v-if="props.type === 1"
      v-loading="loading"
      :data="props.data?.hangUpSms || []"
      :header-cell-style="tableHeaderStyle"
      class="tw-grow"
    >
      <el-table-column property="intentionType" label="意向分类" align="left" :min-width="120"></el-table-column>
      <el-table-column property="labelIds" label="标签" align="left" :min-width="240" show-overflow-tooltip>
        <template #default="{ row }">
          {{  row.labelIds ? row.labelIds?.map((item: number) => (item &&labelMap?.get(item)) || '')?.join('、')  || '-' : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="smsTemplateId" label="短信模板" align="left" :min-width="240" show-overflow-tooltip>
        <template #default="{ row }">
          {{  row.smsTemplateId ? smsTemplateMap?.get(row.smsTemplateId) || '-' : '-' }}
        </template>
      </el-table-column>
    </el-table>

     <!--  通话记录触发短信弹窗 -->
     <el-table
      v-if="props.type === 2"
      v-loading="loading"
      :data="tableData"
      :header-cell-style="tableHeaderStyle"
      class="tw-grow"
    >
      <el-table-column property="messageType" label="触发类型" align="left" :min-width="160">
        <template #default="{ row }">
          {{  findValueInEnum(row.messageType, SmsTypeEnum) }}
        </template>
      </el-table-column>
      <el-table-column property="smsTemplateId" label="短信模板" align="left" :min-width="240" show-overflow-tooltip>
        <template #default="{ row }">
          {{  row.smsTemplateId ? smsTemplateMap?.get(row.smsTemplateId) || '-' : '-' }}
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, } from 'vue'
import { aiOutboundTaskModel } from '@/api/ai-report'
import to from 'await-to-js'
import { merchantSmsTemplateModel, } from '@/api/merchant'
import { useUserStore } from '@/store/user'
import { scriptIntentionModel } from '@/api/speech-craft'
import { tableHeaderStyle } from '@/assets/js/constant'
import { findValueInEnum } from '@/utils/utils'
import { SmsTypeEnum, SmsRecord } from '@/type/sms'

const emits = defineEmits(['update:visible', 'confirm'])
const loading = ref(false)
const props = defineProps<{
  data: {
    // 挂机短信：type === 0
    hangUpSms?: {
      intentionType?: string,
      labelIds: string[],
      smsTemplateId?: number,
      triggerOrder: number
    }[]

    // 话术触发短信：type === 1
    scriptSms?: {
      smsTemplateId?: number,
      // triggerId: number,
      triggerName: string
    }[],
    speechCraftId?: number,

    // 通话记录触发短信需要的时间和recordId：type === 2
    callRecordId?: string,
    callOutTime?: string,
  } | null,
  type: number, // [0] 触发短信弹窗 [1] 挂机短信弹窗 [2]通话记录触发短信
  visible: boolean
}>();

const dialogTitle = ['触发短信', '挂机短信', '触发短信']

const smsTemplateMap = ref<Map<number, string> | null>(null)
const labelMap = ref<Map<number, string> | null>(null)
const tableData = ref<SmsRecord[] | null>([])
const userInfo = useUserStore()
const accountType = userInfo.accountType

const initData = async () => {
  loading.value = true
  // 获取短信模板Map
  if (!smsTemplateMap.value ||smsTemplateMap.value?.size === 0) {
    const [err, data] = await to(accountType === 0 ? 
      merchantSmsTemplateModel.getTotalSmsTemplate() // 运营端通话记录 | 运营端账号管理-任务详情
      : merchantSmsTemplateModel.getSmsTemplate({
        groupId: userInfo.groupId,
      })
    )
    smsTemplateMap.value = new Map([]);
    (data || []).map(item => {
      if(!!item.id && !!item.templateName) {
        smsTemplateMap.value?.set(item.id, item.templateName)
      }
    })
  }
  // 如果是挂机短信，则获取标签列表
  if(props.type === 1) {
    labelMap.value = new Map([]);
    const [err1, data2] = await to(scriptIntentionModel.findLabelList(props.data?.speechCraftId!));
    (data2 || []).map(item => {
      if(!!item.id && !!item.labelName) {
        labelMap.value?.set(item.id, item.labelName)
      }
    })
  }

  // 如果是短信触发记录
  if(props.type === 2 && props.data?.callRecordId && props.data?.callOutTime) {
    const [err2, data2] = await to(aiOutboundTaskModel.findSmsRecordByRecordId({
      callRecordId: props.data.callRecordId!,
      callOutTime: props.data.callOutTime!,
    }))
    tableData.value = data2 || []
  }
  loading.value = false
}

const dialogVisible = ref(props.visible)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

watch(() => props.visible, n => {
  dialogVisible.value = props.visible
  n && initData()
})

</script>

<style lang="postcss" type="text/postcss" scoped>
.el-table {
  font-size: 13px;
  .caret-wrapper {
    display: none;
  }
  :deep(.cell) {
    padding: 0 8px;
  }
}
</style>
