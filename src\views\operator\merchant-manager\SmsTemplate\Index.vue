<template>
  <!--搜索条-->
  <div class="search-box">
    <div class="tw-grid tw-grid-cols-5 tw-gap-[8px] tw-border-b-[1px] tw-pb-[8px]">
      <div class="item-col">
        <span class="label">模板名称：</span>
        <el-input
          v-model="searchForm.templateName"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter="onClickSearch"
        >
        </el-input>
      </div>
      <div class="item-col">
        <span class="label">模板编号：</span>
        <el-input
          v-model.number="searchForm.id"
          placeholder="请输入模板编号"
          clearable
          @keyup.enter="onClickSearch"
        >
        </el-input>
      </div>
      <div class="item-col">
        <span class="label">适用行业：</span>
        <el-cascader
          v-model="searchForm.secondIndustry"
          :options="industryOptions"
          :props="industryProps"
          clearable
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="2"
          :show-all-levels="false"
          placeholder="请输入适用行业"
          class="tw-w-full"
        />
      </div>
      <div class="item-col">
        <span class="label">启用状态：</span>
        <el-select
          v-model="searchForm.templateStatus"
          clearable
          placeholder="请选择启用状态"
        >
          <el-option
            v-for="statusItem in Object.entries(SmsTemplateStatusEnum)"
            :key="statusItem[1]"
            :value="statusItem[1]"
            :label="statusItem[0]"
          />
        </el-select>
      </div>
      <div class="item-col">
        <span class="label">通道状态：</span>
        <el-select
          v-model="searchForm.isChannelPending"
          clearable
          placeholder="请选择通道状态"
        >
          <el-option :value="false" label="未挂起" />
          <el-option :value="true" label="已挂起" />
        </el-select>
      </div>
    </div>
    <div class="tw-flex tw-justify-end tw-pt-[8px] tw-items-center tw-h-[32px]">
      <div>
        <el-button type="primary" link @click="onClickReset">
          <el-icon size="--el-font-size-base">
            <SvgIcon name="reset" color="var(--el-color-primary)" />
          </el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" link @click="onClickSearch">
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)">
            <SvgIcon name="filter" color="none" />
          </el-icon>
          <span>查询</span>
        </el-button>
      </div>
    </div>
    <div v-if="!!merchantStore.currentAccount?.accountEnable" class="tw-mt-[6px] tw-ml-auto">
      <el-dropdown placement="top" trigger="click">
        <el-button type="primary" :icon="Plus">
          新增短信模板
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="onClickAdd">新建模板</el-dropdown-item>
            <el-dropdown-item @click="onClickCopy">复制模板</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>

  <!--表格-->
  <el-table
    v-loading="loadingList"
    :header-cell-style="tableHeaderStyle"
    :data="currentList"
    border
  >
    <el-table-column align="left" prop="id" label="模板编号" min-width="100" show-overflow-tooltip />
    <el-table-column align="left" prop="templateName" label="模板名称" min-width="100" show-overflow-tooltip />
    <el-table-column align="left" prop="businessType" label="业务类型" min-width="100">
      <template #default="{row}:{row:SmsTemplateItem}">
        {{ getBusinessTypeText(row.businessType) }}
      </template>
    </el-table-column>
    <el-table-column align="left" prop="smsTemplateType" label="短信类型" min-width="100">
      <template #default="{row}:{row:SmsTemplateItem}">
        {{ findValueInEnum(row.smsTemplateType, SmsTemplateTypeEnum) || '-' }}
      </template>
    </el-table-column>
    <el-table-column align="left" prop="messageContent" label="短信内容" :formatter="formatMessageContent" min-width="200" show-overflow-tooltip />
    <el-table-column align="center" prop="templateStatus" label="启用状态" min-width="100">
      <template #default="{row}:{row:SmsTemplateItem}">
        <span class="status-box-mini tw-m-auto" :class="getTemplateStatusClass(row.templateStatus)">
          {{ getTemplateStatusText(row.templateStatus) }}
        </span>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="secondIndustry" label="适用行业" min-width="100" show-overflow-tooltip>
      <template #default="{row}:{row:SmsTemplateItem}">
        {{ row.secondIndustry || '-' }}
      </template>
    </el-table-column>
    <template v-if="!!merchantStore.currentAccount?.accountEnable" >
      <el-table-column align="center" prop="channelStatus" label="通道状态" min-width="100" show-overflow-tooltip>
        <template #default="{row}:{row:SmsTemplateItem}">
          <template v-if="typeof row.tenantSmsChannel?.isPending === 'boolean'">
            <el-switch
              v-model="row.tenantSmsChannel.isPending"
              inline-prompt
              active-text="已挂起"
              inactive-text="未挂起"
              :active-value="true"
              :inactive-value="false"
              @click="onSwitchChannelStatus(row)"
            />
          </template>
          <template v-else>
            -
          </template>
        </template>
      </el-table-column>
      <el-table-column align="right" fixed="right" label="操作" width="190">
        <template #default="{row}:{row:SmsTemplateItem}">
          <el-button v-show="row.templateStatus === SmsTemplateStatusEnum['启用']" type="primary" link @click="onClickEdit(row)">
            短信配置
          </el-button>
          <el-button v-show="row.templateStatus === SmsTemplateStatusEnum['启用']" type="primary" link @click="onClickEditSmsChannel(row)">
            通道配置
          </el-button>
          <el-button :type="row.templateStatus === SmsTemplateStatusEnum['启用']?'danger':'primary'" link @click="onClickSwitch(row)">
            {{ row.templateStatus === SmsTemplateStatusEnum['启用'] ? '停用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </template>
  </el-table>

  <!--分页条-->
  <PaginationBox
    :currentPage="pageNum"
    :pageSize="pageSize"
    :pageSizeList="pageSizeList"
    :total="total"
    @search="updateAllList"
    @update="updateList"
  />

  <!--短信模板复制弹窗-->
  <CopyDialog v-model:visible="copyDialogVisible" />
</template>

<script setup lang="ts">
import { findValueInEnum, Throttle, updateCurrentPageList } from '@/utils/utils'
import SvgIcon from '@/components/SvgIcon.vue'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import to from 'await-to-js'
import { CascaderProps, ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import { merchantSmsChannelModel, merchantSmsTemplateModel } from '@/api/merchant'
import {
  MerchantAccountInfo,
  SmsChannelStatusParams,
  SmsTemplateFilter,
  SmsTemplateItem,
  SmsTemplateParams,
  SmsTemplateStatusEnum
} from '@/type/merchant'
import { useGlobalStore } from '@/store/globalInfo'
import { tableHeaderStyle } from '@/assets/js/constant'
import PaginationBox from '@/components/PaginationBox.vue'
import router from '@/router'
import { useMerchantStore } from '@/store/merchant'
import { Plus } from '@element-plus/icons-vue'
import CopyDialog from './CopyDialog.vue'
import { BusinessTypeEnum, SmsTemplateTypeEnum } from '@/type/sms'
import { formatSmsContent } from '@/views/operator/merchant-manager/SmsTemplate/Sms/common'
import dayjs from 'dayjs'
import { trace } from '@/utils/trace'

// ---------------------------------------- 通用 开始 ----------------------------------------

const globalStore = useGlobalStore()
const merchantStore = useMerchantStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 搜索条 开始 ----------------------------------------

// 搜索条件，行业，级联选择器，数据内容
const industryOptions = ref<any[]>([])
// 搜索条件，行业，级联选择器，配置信息
const industryProps: CascaderProps = {
  multiple: false,
  emitPath: false,
  value: 'name',
  label: 'name',
  children: 'secondaryIndustries',
}

/**
 * 获取行业列表
 */
const getIndustryList = async () => {
  // console.log('获取行业列表')
  try {
    await globalStore.getAllIndustryList()
    industryOptions.value = globalStore.getIndustryOption
  } catch (e) {
    ElMessage.error('无法正确获取行业列表')
  } finally {
  }
}

// 搜索条件，默认值
const searchFormDefault = (): SmsTemplateFilter => ({
  templateName: '',
  id: undefined,
  secondIndustry: '',
  templateStatus: '',
  isChannelPending: null,
})
// 搜索条件，表单值
const searchForm: SmsTemplateFilter = reactive(searchFormDefault())

/**
 * 搜索条，重置表单
 */
const resetSearchForm = () => {
  // 表单数据恢复默认值
  Object.assign(searchForm, searchFormDefault())
}
/**
 * 搜索条，点击重置按钮
 */
const onClickReset = () => {
  resetSearchForm()
}
/**
 * 搜索条，点击查询按钮
 */
const onClickSearch = () => {
  updateAllList()
}

// ---------------------------------------- 搜索条 结束 ----------------------------------------

// ---------------------------------------- 按钮组 开始 ----------------------------------------

/**
 * 点击新建按钮
 */
const onClickAdd = () => {
  // 缓存数据
  merchantStore.editingSmsTemplate = {}
  // 切换路由
  router.push({ name: 'SmsTemplateDetail' })
}
/**
 * 点击复制按钮
 */
const onClickCopy = () => {
  // 显示短信模板复制弹窗
  copyDialogVisible.value = true
}

// ---------------------------------------- 按钮组 结束 ----------------------------------------

// ---------------------------------------- 列表 开始 ----------------------------------------

// 列表，正在加载
const loadingList = ref<boolean>(false)
// 列表，加载节流锁
const throttleList = new Throttle(loadingList)

// 列表，全部，接口数据
const allList = ref<SmsTemplateItem[]>([])

// 列表，筛选，全部的子集
const filterList = ref<any[]>([])

// 列表，当前页，筛选的子集
const currentList = ref<SmsTemplateItem[]>([])
// 列表，当前页，页码
const pageNum = ref(1)
// 列表，当前页，每页大小
const pageSize = ref(20)
// 列表，当前页，每页大小可选数值
const pageSizeList: number[] = [10, 20, 50, 100]

// 列表，表格展示总数
const total = computed(() => {
  return filterList.value.length ?? 0
})

/**
 * 更新全部列表
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleList.check()) {
    return
  }
  throttleList.lock()

  // 处理参数
  const params: SmsTemplateParams = {
    groupId: merchantStore.currentAccount.groupId ?? '',
    templateName: searchForm.templateName || undefined,
    id: typeof searchForm.id === 'number' ? searchForm.id : undefined,
    secondIndustry: searchForm.secondIndustry || undefined,
    templateStatus: searchForm.templateStatus || undefined,
    isChannelPending: searchForm.isChannelPending ?? undefined,
  }

  // 请求接口
  const [err, res] = <[any, SmsTemplateItem[]]>await to(merchantSmsTemplateModel.getSmsTemplateForOperation(params))

  // 返回失败结果
  if (err) {
    ElMessage.error('无法获取短信模板列表')
    allList.value = []
    // 节流锁解锁
    throttleList.unlock()
    return
  }

  // 返回成功结果
  // 更新全部列表
  allList.value = res?.length ? res.sort((a, b) => dayjs(a.createTime).isAfter(dayjs(b.createTime)) ? -1 : 1) : []
  // 更新筛选列表
  updateFilterList()
  // 更新当前页列表
  updateList(pageNum.value, pageSize.value)
  // 节流锁解锁
  throttleList.unlock()
}
/**
 * 更新筛选列表
 */
const updateFilterList = () => {
  // console.log('筛选条件', JSON.parse(JSON.stringify(searchForm)))
  filterList.value = JSON.parse(JSON.stringify(allList.value ?? []))
}
/**
 * 更新当前页列表
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateList = (p?: number, s?: number) => {
  // console.log('updateList', p, s)
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined, null, 0等逻辑假值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    pageNum.value = p || 1
    pageSize.value = s || 20
  }
  // 更新当前页列表
  currentList.value = updateCurrentPageList(filterList.value, pageNum.value, pageSize.value)
}
/**
 * 点击列表行短信配置按钮
 * @param row 点击的列表行
 */
const onClickEdit = (row: SmsTemplateItem) => {
  // 缓存数据
  merchantStore.editingSmsTemplate = row
  // 切换路由
  router.push({ name: 'SmsTemplateDetail' })
}
/**
 * 点击列表行通道配置按钮
 * @param row 点击的列表行
 */
const onClickEditSmsChannel = (row: SmsTemplateItem) => {
  // 缓存数据
  merchantStore.editingSmsTemplate = row
  // 切换路由
  router.push({ name: 'SmsChannelDetail' })
}
/**
 * 获取表格列文本，业务类型
 * @param val 业务类型
 */
const getBusinessTypeText = (val?: BusinessTypeEnum | '') => {
  const entity = Object.entries(BusinessTypeEnum).find(([, value]) => {
    return value === val
  }) ?? []
  return entity.at(0) ?? val ?? ''
}
/**
 * 获取表格列文本，启用状态
 * @param val 启用状态
 */
const getTemplateStatusText = (val?: SmsTemplateStatusEnum | '') => {
  const entity = Object.entries(SmsTemplateStatusEnum).find(([, value]) => {
    return value === val
  }) ?? []
  return entity.at(0) ?? val ?? ''
}
/**
 * 获取表格列样式，启用状态
 * @param val 启用状态
 */
const getTemplateStatusClass = (val?: SmsTemplateStatusEnum | '') => {
  if (val === SmsTemplateStatusEnum['启用']) {
    return 'green-status'
  } else if (val === SmsTemplateStatusEnum['禁用']) {
    return 'red-status'
  } else {
    return ''
  }
}
/**
 * 点击启用状态切换开关
 * @param row 选中的行
 */
const onClickSwitch = (row: SmsTemplateItem) => {
  Confirm({
    text: `确定要${row?.templateStatus === SmsTemplateStatusEnum['启用'] ? '禁用' : '启用'}【${row?.templateName}】吗？`,
    type: 'danger',
    title: '启用状态切换确认'
  }).then(async () => {
    // 切换

    // 处理参数
    if (typeof row?.id !== 'number') {
      ElMessage({
        message: 'ID不正确',
        type: 'warning',
      })
      return
    }
    const params: SmsTemplateParams = {
      id: row.id ?? undefined,
    }

    trace({
      page: `商户管理-${row?.templateStatus === SmsTemplateStatusEnum['启用'] ? '禁用' : '启用'}【${row?.templateName}】`,
      params: params
    })

    // 请求接口
    let error: any
    if (row?.templateStatus === SmsTemplateStatusEnum['启用']) {
      // 禁用
      const [err, _] = <[any, SmsTemplateItem]>await to(merchantSmsTemplateModel.disableSmsTemplate(params))
      error = err
    } else {
      // 启用
      const [err, _] = <[any, SmsTemplateItem]>await to(merchantSmsTemplateModel.enableSmsTemplate(params))
      error = err
    }

    // 返回失败结果
    if (error) {
      ElMessage({
        message: `【${row?.templateName}】无法${row?.templateStatus === SmsTemplateStatusEnum['启用'] ? '禁用' : '启用'}`,
        type: 'error',
      })
      // 更新列表
      await updateAllList()
      return
    }

    // 返回成功结果
    ElMessage({
      message: `【${row?.templateName}】${row?.templateStatus === SmsTemplateStatusEnum['启用'] ? '禁用' : '启用'}成功`,
      type: 'success',
    })
    // 更新列表
    await updateAllList()

  }).catch(() => {
    // 取消
  }).finally(() => {
  })
}
/**
 * 切换通道状态
 * @param row 选中的列表行
 */
const onSwitchChannelStatus = (row: SmsTemplateItem) => {
  if (typeof row.id !== 'number') {
    ElMessage({
      message: 'ID不正确',
      type: 'warning',
    })
    return
  }

  // 期望的挂起状态
  // 此处点击事件回调里，挂起状态已经变成了期望的挂起状态，所以直接使用
  const expectPending = row.tenantSmsChannel?.isPending ?? false
  const expectPendingStr = `${expectPending ? '' : '取消'}挂起`
  Confirm({
    text: `您确定要${expectPendingStr}【${row.templateName ?? ''}】的通道吗？`,
    type: 'warning',
    title: `${expectPendingStr}确认`,
    confirmText: `${expectPendingStr}`,
  }).then(async () => {
    try {
      // 处理参数
      const params: SmsChannelStatusParams = {
        status: expectPending,
        templateId: row.id ?? undefined,
      }
      trace({
        page: `商户管理-${expectPendingStr}【${row.templateName ?? ''}】`,
        params: params
      })
      // 请求接口
      await merchantSmsChannelModel.switchSmsChannelStatus(params)
      // 返回成功结果
      ElMessage({
        message: `【${row.templateName ?? ''}】通道${expectPendingStr}成功`,
        type: 'success',
      })
    } catch (err) {
      ElMessage({
        message: `【${row.templateName ?? ''}】通道${expectPendingStr}失败`,
        type: 'error',
      })
    }
  }).catch(() => {
    // 取消
  }).finally(() => {
    // 更新全部列表
    updateAllList()
  })
}

// ---------------------------------------- 列表 结束 ----------------------------------------

// ---------------------------------------- 短信内容 开始 ----------------------------------------

/**
 * 格式化短信内容
 * 因为自定义变量的具体值未知（短信预览时手动临时输入），
 * 所以这里仅格式化短信签名和短信内容的系统变量
 * @param row 行数据，当前短信模板
 * @param col 列数据，element表格列
 * @param cell 单元格数据，短信内容字符串
 * @param index 行索引
 * @return {string} 格式化后的短信内容
 */
const formatMessageContent = (row: SmsTemplateItem, col: any, cell: string, index: number): string => {
  return formatSmsContent(row.messageSign, cell, [])
}

// ---------------------------------------- 短信内容 结束 ----------------------------------------

// ---------------------------------------- 短信模板复制弹窗 开始 ----------------------------------------

// 短信模板复制弹窗，是否显示
const copyDialogVisible = ref<boolean>(false)

// ---------------------------------------- 短信模板复制弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => merchantStore.currentAccount, async (val: MerchantAccountInfo | null | undefined) => {
  if (typeof val?.id === 'number') {
    await nextTick()
    resetSearchForm()
    await updateAllList()
    await getIndustryList()
  }
}, { immediate: true, deep: true })

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
/* 文本框内的标签 */
:deep(.el-cascader__tags .el-tag) {
  margin: 0 0 0 4px;
}
</style>
