<template>
  <!--搜索条-->
  <div class="tw-flex tw-m-0 tw-pt-[16px] tw-pb-[12px] tw-px-[16px] tw-bg-white">
    <div>
      <div class="tw-mb-[8px] tw-text-[13px] tw-text-[#969799] tw-text-left">变量名称：</div>
      <el-input
        v-model.trim="searchForm.variableName"
        placeholder="请输入变量名称"
        clearable
        style="width: 300px;"
        @keyup.enter="updateAllList"
      />
    </div>

    <div class="tw-flex tw-items-end tw-ml-[12px] tw-pb-[8px]">
      <el-button type="primary" link @click="onClickReset">
        <el-icon size="--el-font-size-base">
          <SvgIcon name="reset" color="var(--el-color-primary)" />
        </el-icon>
        <span>重置</span>
      </el-button>
      <el-button type="primary" link @click="onClickSearch">
        <el-icon size="--el-font-size-base">
          <SvgIcon name="search" color="var(--el-color-primary)" />
        </el-icon>
        <span>查询</span>
      </el-button>
    </div>
  </div>

  <!--按钮组-->
  <div v-if="!!merchantStore.currentAccount?.accountEnable" class="tw-flex tw-justify-end tw-m-0 tw-px-[16px] tw-pb-[12px] tw-bg-white">
    <el-button type="primary" :icon="Plus" @click="onClickAdd">
      新增变量
    </el-button>
  </div>

  <!--表格-->
  <el-table
    v-loading="loadingVariable"
    :header-cell-style="tableHeaderStyle"
    :data="currentList"
  >
    <el-table-column align="left" prop="variableName" label="变量名称" min-width="100" show-overflow-tooltip />
    <el-table-column align="left" prop="variableComment" label="变量说明" min-width="100" show-overflow-tooltip />
    <el-table-column align="left" prop="variableType" label="变量类型" min-width="100" show-overflow-tooltip :formatter="formatVariableType" />
    <el-table-column align="left" prop="columnType" label="字段类型" min-width="100" show-overflow-tooltip :formatter="formatColumnType" />
    <el-table-column v-if="!!merchantStore.currentAccount?.accountEnable" align="right" fixed="right" label="操作" width="100">
      <template #default="{row}:{row:SmsVariableItem}">
        <el-button type="primary" link @click="onClickEdit(row)">
          编辑
        </el-button>
        <el-button type="danger" link @click="onClickDelete(row)">
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>

  <!--分页条-->
  <PaginationBox
    :currentPage="pageNum"
    :pageSize="pageSize"
    :pageSizeList="pageSizeList"
    :total="total"
    @search="updateAllList"
    @update="updateList"
  />

  <!--编辑弹窗-->
  <EditDialog
    v-model:visible="editDialogVisible"
    :data="editDialogData"
    :isEdit="editDialogIsEdit"
    @confirm="onEditDialogConfirm"
  />
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import PaginationBox from '@/components/PaginationBox.vue'
import { Throttle, updateCurrentPageList } from '@/utils/utils'
import SvgIcon from '@/components/SvgIcon.vue'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import to from 'await-to-js'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import Confirm from '@/components/message-box'
import { merchantSmsVariableModel } from '@/api/merchant'
import {
  MerchantAccountInfo,
  SmsVariableColumnTypeEnum,
  SmsVariableItem,
  SmsVariableParams,
  SmsVariableTypeEnum
} from '@/type/merchant'
import EditDialog from './EditDialog.vue'
import { useMerchantStore } from '@/store/merchant'

// ---------------------------------------- 通用 开始 ----------------------------------------

const merchantStore = useMerchantStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 搜索条 开始 ----------------------------------------

// 搜索条件，默认值
const searchFormDefault = () => ({
  variableName: '',
})
// 搜索条件，表单值
const searchForm: SmsVariableItem = reactive(searchFormDefault())

/**
 * 搜索条，重置表单
 */
const resetSearchForm = () => {
  // 表单数据恢复默认值
  Object.assign(searchForm, searchFormDefault())
}
/**
 * 搜索条，点击重置按钮
 */
const onClickReset = () => {
  resetSearchForm()
}
/**
 * 搜索条，点击查询按钮
 */
const onClickSearch = () => {
  updateAllList()
}

// ---------------------------------------- 搜索条 结束 ----------------------------------------

// ---------------------------------------- 按钮组 开始 ----------------------------------------

/**
 * 点击添加按钮
 */
const onClickAdd = () => {
  editDialogData.value = {}
  editDialogIsEdit.value = false
  editDialogVisible.value = true
}

// ---------------------------------------- 按钮组 结束 ----------------------------------------

// ---------------------------------------- 列表 开始 ----------------------------------------

// 列表，正在加载
const loadingVariable = ref<boolean>(false)
// 列表，加载节流锁
const throttleVariable = new Throttle(loadingVariable)

// 列表，全部，接口数据
const allList = ref<SmsVariableItem[]>([])

// 列表，筛选，全部的子集
const filterList = ref<any[]>([])

// 列表，当前页，筛选的子集
const currentList = ref<SmsVariableItem[]>([])
// 列表，当前页，页码
const pageNum = ref(1)
// 列表，当前页，每页大小
const pageSize = ref(20)
// 列表，当前页，每页大小可选数值
const pageSizeList: number[] = [10, 20, 50, 100]

// 列表，表格展示总数
const total = computed(() => {
  return filterList.value.length ?? 0
})

/**
 * 更新全部列表
 */
const updateAllList = async () => {
  // 节流锁上锁
  if (throttleVariable.check()) {
    return
  }
  throttleVariable.lock()

  // 处理参数
  const params: SmsVariableParams = {
    groupId: merchantStore.currentAccount.groupId ?? '',
  }

  // 请求接口
  const [err, res] = <[any, SmsVariableItem[]]>await to(merchantSmsVariableModel.getSmsVariable(params))

  // 返回失败结果
  if (err) {
    ElMessage.error('无法获取短信变量列表')
    allList.value = []
    // 节流锁解锁
    throttleVariable.unlock()
    return
  }

  // 返回成功结果
  // 更新全部列表
  allList.value = res?.length ? res : []
  // 更新筛选列表
  updateFilterList()
  // 更新当前页列表
  updateList(pageNum.value, pageSize.value)
  // 节流锁解锁
  throttleVariable.unlock()
}
/**
 * 更新筛选列表
 */
const updateFilterList = () => {
  filterList.value = allList.value.filter((item: SmsVariableItem) => {
    return item.variableName?.includes(searchForm.variableName ?? '')
  })
}
/**
 * 更新当前页列表
 * @param {number} p 新页码
 * @param {number} s 新分页大小
 */
const updateList = (p?: number, s?: number) => {
  // console.log('updateList', p, s)
  // 如果参数指定了页码或/和每页大小，则按参数更新列表；
  // 否则保持当前页码和大小不变直接更新。
  // 这里的判断条件相当于排除了undefined, null, 0等逻辑假值，
  // 另外，页码为0或者分页大小为0，本身就是逻辑错误的，不应该更新分页。
  if (p || s) {
    pageNum.value = p || 1
    pageSize.value = s || 20
  }
  // 更新当前页列表
  currentList.value = updateCurrentPageList(filterList.value, pageNum.value, pageSize.value)
}
/**
 * 删除列表行 请求接口
 * @param row 列表行，限制信息
 */
const deleteRow = async (row: SmsVariableItem) => {
  // 处理参数
  if (typeof row?.id !== 'number') {
    ElMessage({
      message: '当前短信变量的ID不正确',
      type: 'warning',
    })
    return
  }
  const params: SmsVariableParams = {
    id: row.id ?? undefined,
  }

  // 请求接口
  const [err, _] = <[any, SmsVariableItem]>await to(merchantSmsVariableModel.deleteSmsVariable(params))

  // 返回失败结果
  if (err) {
    ElMessage({
      message: `【${row?.variableName}】删除失败`,
      type: 'error',
    })
    return
  }

  // 返回成功结果
  ElMessage({
    message: `【${row?.variableName}】删除成功`,
    type: 'success',
  })
  // 更新全部列表
  await updateAllList()
}
/**
 * 点击列表行删除按钮
 * @param row 点击的列表行
 */
const onClickDelete = (row: SmsVariableItem) => {
  // 显示删除确认
  Confirm({
    text: `确定要删除【${row?.variableName ?? ''}】吗？`,
    type: 'danger',
    title: '删除确认'
  }).then(() => {
    // 删除
    deleteRow(row)
  }).catch(() => {
    // 取消
  })
}
/**
 * 点击列表行编辑按钮
 * @param row 点击的列表行
 */
const onClickEdit = (row: SmsVariableItem) => {
  editDialogData.value = row
  editDialogIsEdit.value = true
  editDialogVisible.value = true
}
/**
 * 格式化变量类型
 * @param row 行
 * @param column 列
 * @param cellValue 单元格
 * @param index 索引
 */
const formatVariableType = (row: SmsVariableItem, column: any, cellValue: any, index: number) => {
  if (Object.values(SmsVariableTypeEnum).includes(cellValue))
    if (cellValue === 'CUS') {
      return '自定义'
    } else if (cellValue === null) {
      return '系统内置'
    }
  return cellValue ?? ''
}
/**
 * 格式化字段类型
 * @param row 行
 * @param column 列
 * @param cellValue 单元格
 * @param index 索引
 */
const formatColumnType = (row: SmsVariableItem, column: any, cellValue: SmsVariableColumnTypeEnum, index: number) => {
  const entity = Object.entries(SmsVariableColumnTypeEnum).find(([key, value]) => {
    return value === cellValue
  }) ?? []
  return entity.at(0) ?? ''
}

// ---------------------------------------- 列表 结束 ----------------------------------------

// ---------------------------------------- 编辑弹窗 开始 ----------------------------------------

// 编辑弹窗，是否显示
const editDialogVisible = ref<boolean>(false)
// 编辑弹窗，表单数据
const editDialogData = ref<SmsVariableItem>({})
// 编辑弹窗，是否是编辑模式，是——编辑，否——新建
const editDialogIsEdit = ref<boolean>(false)

/**
 * 编辑弹窗，提交表单
 */
const onEditDialogConfirm = () => {
  updateAllList()
}

// ---------------------------------------- 编辑弹窗 结束 ----------------------------------------

// ---------------------------------------- 立即执行 开始 ----------------------------------------

watch(() => merchantStore.currentAccount, async (val: MerchantAccountInfo | null | undefined) => {
  if (typeof val?.id === 'number') {
    await nextTick()
    resetSearchForm()
    await updateAllList()
  }
}, { immediate: true, deep: true })

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
</style>
