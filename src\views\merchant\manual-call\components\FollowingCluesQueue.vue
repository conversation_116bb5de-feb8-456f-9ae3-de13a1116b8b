<template>
  <!--筛选项容器-->
  <div class="search-box">
    <div class="tw-grid tw-grid-cols-4 tw-gap-[12px]">
      <div class="item-col">
        <div class="label">号码/名单编号：</div>
        <el-input
          v-model="searchForm.phone"
          placeholder="输入号码/名单编号搜索"
          clearable
          @blur="formatPhone(searchForm.phone)"
          @keyup.enter="search()"
        >
          <template #suffix>
            <el-button :icon="FullScreen" type="primary" link @click="showPhone" />
          </template>
        </el-input>
      </div>

      <div class="item-col">
        <div class="label">星标：</div>
        <el-select v-model="searchForm.isStar" placeholder="全部" clearable @change="search()">
          <!--<el-option label="全部" value="" />-->
          <el-option label="&#9733; 标记" :value="true">
            <el-icon style="height: 100%;" :size="14" color="#FFAA3D">
              <SvgIcon name="star-active" />
            </el-icon>
            标记
          </el-option>
          <el-option label="&#9734; 未标记" :value="false">
            <el-icon style="height: 100%;" :size="14" color="#FFAA3D">
              <SvgIcon name="star-inactive" />
            </el-icon>
            未标记
          </el-option>
        </el-select>
      </div>

      <div class="item-col">
        <div class="label">分配时间：</div>
        <TimePickerBox
          v-model:start="searchForm.beAllocatedTimeStart"
          v-model:end="searchForm.beAllocatedTimeEnd"
        />
      </div>

      <div class="item-col">
        <div class="label">姓名：</div>
        <el-input
          v-model="searchForm.name"
          placeholder="输入姓名"
          clearable
          @keyup.enter="search()"
        />
      </div>
    </div>

    <!--展开更多-->
    <div v-show="isExpand" class="tw-grid tw-grid-cols-4 tw-gap-[12px] tw-mt-[12px]">
      <div class="item-col">
        <div class="label">省：</div>
        <el-select v-model="searchForm.provinceCode" placeholder="省" filterable clearable>
          <el-option
            v-for="item in provinceList"
            :key="item.split(',')[0]"
            :label="item.split(',')[0]"
            :value="item.split(',')[1]"
          />
        </el-select>
      </div>

      <div class="item-col">
        <div class="label">市：</div>
        <el-select v-model="searchForm.cityCode" placeholder="市" filterable clearable>
          <el-option
            v-for="item in cityList"
            :key="item.split(',')[0]"
            :label="item.split(',')[0]"
            :value="item.split(',')[1]"
          />
        </el-select>
      </div>

      <div class="item-col">
        <span class="label">跟进次数：</span>
        <div class="item">
          <InputNumberBox
            v-model:value="searchForm.minFollowUpCount"
            placeholder="最低"
            :max="searchForm.maxFollowUpCount || Number.MAX_VALUE"
            append="次"
          />
          <span class="tw-w-[14px] tw-shrink-0">-</span>
          <InputNumberBox
            v-model:value="searchForm.maxFollowUpCount"
            placeholder="最高"
            :min="searchForm.minFollowUpCount || 0"
            append="次"
          />
        </div>
      </div>

      <div class="item-col">
        <span class="label">最近通话时长：</span>
        <div class="item">
          <InputNumberBox
            v-model:value="searchForm.minLatestCallDuration"
            placeholder="最低"
            :max="searchForm.maxLatestCallDuration || Number.MAX_VALUE"
            append="秒"
          />
          <span class="tw-w-[14px] tw-shrink-0">-</span>
          <InputNumberBox
            v-model:value="searchForm.maxLatestCallDuration"
            placeholder="最高"
            :min="searchForm.minLatestCallDuration || 0"
            append="秒"
          />
        </div>
      </div>

      <div class="item-col">
        <span class="label">最近跟进时间：</span>
        <TimePickerBox
          v-model:start="searchForm.latestFollowUpTimeStart"
          v-model:end="searchForm.latestFollowUpTimeEnd"
        />
      </div>

      <div class="item-col">
        <span class="label">最近通话状态：</span>
        <el-select v-model="searchForm.latestCallStatus" placeholder="最近通话状态" clearable>
          <el-option
            v-for="item in clueCallStatusOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </div>

      <div class="item-col">
        <span class="label">跟进备注：</span>
        <el-input v-model="searchForm.followUpLogNote" placeholder="跟进备注" clearable @keyup.enter="search()" />
      </div>

      <div class="item-col">
        <span class="label">下次跟进时间：</span>
        <TimePickerBox
          v-model:start="searchForm.nextFollowUpTimeStart"
          v-model:end="searchForm.nextFollowUpTimeEnd"
        />
      </div>

      <div class="item-col">
        <span class="label">是否过期：</span>
        <el-select v-model="searchForm.isExpire" placeholder="全部" clearable>
          <!--<el-option label="全部" value=""></el-option>-->
          <el-option label="否" :value="false"></el-option>
          <el-option label="是" :value="true"></el-option>
        </el-select>
      </div>

      <div class="item-col ">
        <span class=" label">自动回收时间：</span>
        <TimePickerBox
          v-model:start="searchForm.autoRecoveryTimeStart"
          v-model:end="searchForm.autoRecoveryTimeEnd"
        />
      </div>
    </div>

    <!--功能按钮-->
    <div class="tw-flex tw-justify-end tw-items-center tw-h-[32px] tw-mt-[12px] tw-pt-[12px] tw-border-t-[1px]">
      <el-button type="primary" link @click="clearSearchForm">
        <el-icon size="--el-font-size-base">
          <SvgIcon name="reset" color="var(--el-color-primary)" />
        </el-icon>
        <span>重置</span>
      </el-button>

      <el-button type="primary" link @click="search()">
        <el-icon size="--el-font-size-base" color="var(--el-color-primary)">
          <SvgIcon name="filter" color="none" />
        </el-icon>
        <span>查询</span>
      </el-button>

      <el-button v-if="isExpand" type="primary" link @click="isExpand=false">
        收起
        <el-icon size="--el-font-size-base">
          <ArrowUp />
        </el-icon>
      </el-button>

      <el-button v-else type="primary" link @click="isExpand=true">
        展开
        <el-icon size="--el-font-size-base">
          <ArrowDown />
        </el-icon>
      </el-button>
    </div>
  </div>

  <!--表格-->
  <ClueFollowUpTable
    v-model:selectClues="selectClues"
    v-model:tableData="tableData"
    :loading="loading"
    :followUpStatus="FollowUpStatusEnum['跟进中']"
    @search="updateTable"
  >
    <!--操作列按钮-->
    <template v-slot:operate="{ row }">
      <el-button type="primary" link @click="goDetail(row)">详情</el-button>
      <el-button type="primary" link @click="call(row)">呼叫</el-button>
    </template>
  </ClueFollowUpTable>

  <!--批量导入名单弹窗-->
  <el-dialog v-model="phoneDialogVisible" width="480px" :close-on-click-modal="false" align-center>
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">号码/名单编号</div>
    </template>
    <div class="tw-grid tw-grid-cols-2 tw-gaps-1 tw-p-[12px]">
      <div>
        <el-input
          v-model="tempPhone"
          :rows="10"
          type="textarea"
          placeholder="请输入号码/名单编号"
          @blur="formatPhone(tempPhone)"
        />
      </div>
      <div class="tw-ml-[12px]">
        <div class="tw-text-left tw-mb-[6px] tw-text-[14px]">号码/名单编号：</div>
        <div v-for="p in tempPhone?.split(',') || []" v-show="!!p" class="tw-text-left tw-list-decimal tw-my-[2px]">
          {{ p }}
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmPhone">确认</el-button>
        <el-button @click="cancelPhone">关闭</el-button>
      </span>
    </template>
  </el-dialog>

  <!--线索详情-->
  <ClueDetailsDrawer
    v-model:visible="clueDrawerVisible"
    :clueData="currentClue"
    :permission="permissions"
    :transferRecordVisible="false"
    :fromWorkbench="true"
    @update:data="updateTable"
  />
</template>

<script lang="ts" setup>
// 插件、依赖等
import { computed, defineAsyncComponent, onActivated, onDeactivated, reactive, ref, watch } from 'vue'
import { useGlobalStore } from '@/store/globalInfo'
import { useUserStore } from "@/store/user";
import { ArrowDown, ArrowUp, FullScreen } from '@element-plus/icons-vue'
import {
  clueCallStatusOptions,
  ClueItem,
  ClueStatusEnum,
  FollowUpStatusEnum,
  SearchFormOrigin,
  WorkbenchClueSearchInfo
} from '@/type/clue'
import routeMap from '@/router/asyncRoute/route-map'
// api
import { seatWorkbenchClueModel } from '@/api/seat'
import { useSeatPhoneStore } from '@/store/seat-phone'
import to from 'await-to-js'
import { SeatLogActionEnum } from '@/type/seat'
import { useSeatInfoStore } from '@/store/seat/seat-info'
import { storeToRefs } from "pinia"

// 动态引入组件
const ClueFollowUpTable = defineAsyncComponent(() => import('./ClueFollowUpTable.vue'))
const ClueDetailsDrawer = defineAsyncComponent(() => import('./ClueDetailsDrawer.vue'))
const TimePickerBox = defineAsyncComponent(() => import('@/components/TimePickerBox.vue'))
const InputNumberBox = defineAsyncComponent(() => import('@/components/InputNumberBox.vue'))

const props = defineProps<{
  followUpStatus: FollowUpStatusEnum;
  needRefresh?: boolean;
}>();
const emits = defineEmits(['update:needRefresh'])

const globalStore = useGlobalStore()
const loading = ref(false)
const userStore = useUserStore()
const seatPhoneStore = useSeatPhoneStore()
const seatInfoStore = useSeatInfoStore()
const { seatOnline, seatTaskList } = storeToRefs(seatInfoStore)

const permissions = computed(() => {
  const curPermissions = userStore.permissions[routeMap['坐席工作台'].id]
  const res: Record<string, boolean> = {
    '修改跟进状态': true,
    '修改星标': true
  }
  if (curPermissions?.includes(routeMap['坐席工作台'].permissions['表单编辑'])) {
    res['编辑表单'] = true
  }
  if (curPermissions?.includes(routeMap['坐席工作台'].permissions['查看明文'])) {
    res['查看明文'] = true
  }
  return res
})

const isExpand = ref(false)

/** 搜索、列表等主体数据 */
const searchForm = reactive<WorkbenchClueSearchInfo>(new SearchFormOrigin(userStore.groupId, ClueStatusEnum['已分配']))
searchForm.importTimeStart = undefined
searchForm.importTimeEnd = undefined
const provinceList = ref<string[]>([])
const provinceAllMap = ref<{ [key: string]: string[] }>({})
const cityList = computed(() => {
  const provinces = Object.keys(provinceAllMap.value)
  const province = provinces.find(item => searchForm.provinceCode && item.includes(searchForm.provinceCode))
  return searchForm.provinceCode && province ? (provinceAllMap.value[province] || []) : (Object.values(provinceAllMap.value).flat() || [])
})
const tableData = ref<ClueItem[]>([])
const search = async () => {
  loading.value = true
  const params = JSON.parse(JSON.stringify(searchForm))
  Object.keys(params).forEach((key: string) => {
    if (params[<keyof typeof params>key] === '') {
      params[<keyof typeof params>key] = undefined
    }
  })
  const [_, res] = <[any, ClueItem[]]>await to(seatWorkbenchClueModel.getFollowingClueList(params))
  tableData.value = Array.isArray(res) ? res : []
  selectClues.value = []
  loading.value = false
  startCheckNewClueTimer()
}
const updateTable = () => {
  search()
  emits('update:needRefresh', true)
}
const clearSearchForm = () => {
  Object.assign(searchForm, new SearchFormOrigin(userStore.groupId))
  searchForm.importTimeStart = undefined
  searchForm.importTimeEnd = undefined
}

// 检查新线索定时器
let checkNewClueTimer: number | null = null
// 检查新线索时间间隔，单位秒
const CHECK_NEW_CLUE_SECOND = 60
/**
 * 处理 检查新线索定时器
 */
const handleCheckNewClueTimer = async () => {
  // console.log('处理 检查新线索定时器')
  // 获取最新的线索列表，
  // 和现有的比较，用set进行比较，
  // 如果有剩余数据，说明有新线索，提示坐席更新列表，
  // 如果没有剩余数据，说明没有新线索，不需要任何操作。

  // 处理参数
  const params = JSON.parse(JSON.stringify(searchForm))
  // 空参数不传
  Object.keys(params).forEach((key: string) => {
    if (params[<keyof typeof params>key] === '') {
      params[<keyof typeof params>key] = undefined
    }
  })
  // 请求接口
  const [_, res] = <[any, ClueItem[]]>await to(seatWorkbenchClueModel.getFollowingClueList(params))

  // 缓存接口返回的线索的ID列表
  const tempIdList: number[] = (res?.length ? res : []).map((item: ClueItem) => (item?.id ?? -1))
  const tempIdSet = new Set(tempIdList)
  // 去除无效ID
  tempIdSet.delete(-1)

  // 缓存表格现有的线索的ID列表
  const localIdList: number[] = tableData.value.map((item: ClueItem) => (item?.id ?? -1))
  const localIdSet = new Set(localIdList)
  // 去除无效ID
  localIdSet.delete(-1)

  // 在接口返回的线索ID里剔除表格现有的
  Array.from(tempIdSet.values()).forEach((id: number) => {
    // 需要剔除
    if (localIdSet.has(id)) {
      tempIdSet.delete(id)
    }
  })
  // console.log('tempIdSet', tempIdSet)

  // 判断接口返回的线索ID集合，剩余数据是否为空
  if (tempIdSet.size) {
    // 弹窗通知
    seatPhoneStore.notifyNewClue(tempIdSet.size)
    console.log('获取到新分配的线索', tempIdSet.size, '个')
  }

  // 重置定时器
  startCheckNewClueTimer()
}
/**
 * 启动 检查新线索定时器
 * 支持防抖，重复调用就以最新一次重新计时
 */
const startCheckNewClueTimer = () => {
  // 清除旧的定时器
  stopCheckNewClueTimer()
  // 只有上线后并且人工直呼工作模式才需要
  if (seatOnline.value && !seatTaskList.value.length) {
    // console.log('启动 检查新线索定时器')
    // 开启新的定时器
    checkNewClueTimer = <number><unknown>setTimeout(handleCheckNewClueTimer, CHECK_NEW_CLUE_SECOND * 1000)
  }
}
/**
 * 关闭 检查新线索定时器
 */
const stopCheckNewClueTimer = () => {
  try {
    if (checkNewClueTimer) {
      clearTimeout(<number>checkNewClueTimer)
      // console.log('关闭 检查新线索定时器')
    }
    checkNewClueTimer = null
  } catch (e) {
    console.warn('stopCheckNewClueTimer', e)
  }
}

const currentClue = ref<ClueItem>({ id: undefined, })

/** 线索操作 【开始】 */
const selectClues = ref<ClueItem[]>([]) // 选中数据

const clueDrawerVisible = ref(false)

/**
 * 点击操作列的详情按钮
 * @param {ClueItem} row 当前线索
 */
const goDetail = (row: ClueItem) => {
  clueDrawerVisible.value = true
  currentClue.value = row
}
/**
 * 点击操作列的呼叫按钮
 * @param {ClueItem} row 当前线索
 */
const call = async (row: ClueItem) => {
  // 发起呼叫
  await seatPhoneStore.launchCall(row)
}

/** 号码、名单编号批量输入显示弹窗 开始 */
const phoneDialogVisible = ref(false) // 名单编号弹窗Visible
const tempPhone = ref() // 名单编号弹窗 数据
// 点击icon打开名单编号弹窗
const showPhone = () => {
  phoneDialogVisible.value = true
  tempPhone.value = searchForm.phone
}
// 点击名单编号弹窗确认
const confirmPhone = () => {
  searchForm.phone = tempPhone.value
  phoneDialogVisible.value = false
  tempPhone.value = ''
}
// 点击名单编号弹窗取消
const cancelPhone = () => {
  phoneDialogVisible.value = false
  tempPhone.value = ''
}
// 弹窗-失焦后，格式化名单编号
const formatPhone = (val?: string) => {
  if (!val || !val?.trim()) {
    tempPhone.value = ''
  } else {
    const str = val?.trim()?.replace(/\n/g, ',') || ''
    tempPhone.value = [...new Set(str.split(',').map(item => item.trim()).filter(item => !!item))].join(',')
  }
}
const init = async () => {
  await globalStore.getProvinceInfo()
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
}
// 表格区
onActivated(() => {
  init()
  updateTable()
})
watch(() => props.needRefresh, n => {
  n && updateTable()
}, {
  deep: true
})
watch(() => props.followUpStatus, () => {
  clearSearchForm()
  updateTable()
}, {
  deep: true
})
/**
 * 重置
 */
const reset = () => {
  stopCheckNewClueTimer()
}
onActivated(() => {
  console.log('onActivated')
  window.addEventListener('beforeunload', reset)
})
onDeactivated(() => {
  console.log('onDeactivated')
  reset()
  window.removeEventListener('beforeunload', reset)
})
watch(() => seatOnline.value, (val) => {
  // 坐席上线后定时查询线索列表，下线后关闭定时查询
  if (val) {
    startCheckNewClueTimer()
  } else {
    stopCheckNewClueTimer()
    seatPhoneStore.closeNotifyNewClue()
  }
})
watch(() => seatTaskList.value, (val) => {
  // 根据是否签入任务确定是否需要定时查询新分配的线索
  if (val?.length) {
    // 人机协同工作模式，不需要定时查询
    // 立马清空通知
    seatPhoneStore.closeNotifyNewClue()
    // 关闭定时器
    stopCheckNewClueTimer()
  } else {
    // 开启定时器
    startCheckNewClueTimer()
  }
})
watch(() => seatPhoneStore.needCallNext, async (val) => {
  // 先更新线索列表
  await search()
  emits('update:needRefresh', true)

  if (!val) {
    // 不需要拨打下一个
    return
  }

  // 再从线索列表里找到跟进次数为0的线索，发起呼叫
  // 如果线索列表里没有跟进次数为0的线索，停止呼叫
  const nextClue = seatPhoneStore.clueList.find((item: ClueItem) => {
    return item?.id !== currentClue.value.id && !item?.followCount
  })
  console.log('下一条线索 nextClue', nextClue)
  if (!nextClue) {
    // ElMessage({
    //   type: 'warning',
    //   message: '没有可以拨打的线索，请刷新线索列表',
    //   duration: 5000,
    // })
  } else {
    seatPhoneStore.report({
      action: SeatLogActionEnum['人工直呼-拨打下一个'],
      desc: '人工直呼 ' + (seatPhoneStore.needCallNext ?? '手动') + '拨打下一个',
      phone: nextClue.phone ?? '',
    })
    // 重置状态
    seatPhoneStore.needCallNext = ''
    await seatPhoneStore.launchCall(nextClue)
  }
})
</script>

<style scoped lang="postcss">
</style>
