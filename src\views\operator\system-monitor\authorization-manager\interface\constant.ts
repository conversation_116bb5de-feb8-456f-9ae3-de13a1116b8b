import { InterfaceItem, InterfaceTypeEnum, ServiceEnum } from '@/type/authorization'
export class InterfaceItemOrigin implements Partial<InterfaceItem> {
  serviceId = ServiceEnum['AiSpeech']
  name = undefined
  identifier = undefined
  type = InterfaceTypeEnum['TOKEN类']
  id = undefined
  remark = undefined
}

export const typeClassObj: Record<InterfaceTypeEnum, string> = {
  [InterfaceTypeEnum['TOKEN类']]: 'blue-status',
  [InterfaceTypeEnum['API类']]: 'orange-status',
  [InterfaceTypeEnum['登录类']]: 'green-status',
}
