// websocket 配置
export interface WebSocketConfig {
  // 服务器地址
  url: string,
  // 自定义协议
  protocol?: string,
  // 四个事件监听处理
  onOpen: (this: WebSocket, ev: Event) => any,
  onError: (this: WebSocket, ev: Event) => any,
  onClose: (this: WebSocket, ev: CloseEvent) => any,
  onMessage: (this: WebSocket, ev: MessageEvent) => any,
  // 是否启用心跳保活
  keepalive?: boolean,
  // 心跳保活间隔时长，单位秒
  keepaliveSecond?: number,
  // 处理心跳保活定时任务
  handleKeepalive?: Function,
}
